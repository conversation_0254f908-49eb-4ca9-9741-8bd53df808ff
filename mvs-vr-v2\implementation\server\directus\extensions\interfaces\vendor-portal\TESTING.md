# Testing Guide for Vendor Portal Extension

This document provides guidance on how to run and write tests for the Vendor Portal extension.

## Testing Setup

The Vendor Portal extension uses Vitest as the testing framework. Vitest is a modern, fast, and feature-rich testing framework that is compatible with Vue.js and provides a Jest-compatible API.

### Key Features

- **Fast execution**: Vitest is built on top of Vite, which provides fast HMR and bundling
- **Vue.js support**: Vitest works well with Vue components through the vite-plugin-vue2 plugin
- **Jest-compatible API**: Vitest provides a Jest-compatible API, making it easy to migrate from Jest
- **Watch mode**: Vitest provides a watch mode for continuous testing during development
- **Coverage reporting**: Vitest provides built-in coverage reporting

## Running Tests

### Running All Tests

To run all tests:

```bash
npm test
```

### Running Specific Tests

To run specific tests:

```bash
npm run test:vitest
```

This will run only the Vitest-specific tests that have been migrated from Jest.

### Watch Mode

To run tests in watch mode (tests will re-run when files change):

```bash
npm run test:watch
```

### Coverage Reporting

To generate a coverage report:

```bash
npm run test:coverage
```

### UI Mode

To run tests in UI mode:

```bash
npm run test:ui
```

## Writing Tests

### Service Tests

Service tests should be placed in the `src/services/tests` directory with a `.vitest.js` extension.

Example:

```javascript
// src/services/tests/MyService.vitest.js
import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios from 'axios';
import MyService from '../MyService';

describe('MyService', () => {
  let service;
  let mockAxios;

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
    
    // Mock axios.create to return our mockAxios
    mockAxios = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn()
    };
    
    // Mock the axios.create function to return our mockAxios
    vi.mocked(axios.create).mockReturnValue(mockAxios);
    
    // Create a new instance of the service
    service = new MyService();
  });

  describe('myMethod', () => {
    it('should do something', async () => {
      // Arrange
      const mockResponse = { data: { result: 'success' } };
      mockAxios.get.mockImplementation(() => Promise.resolve(mockResponse));

      // Act
      const result = await service.myMethod();

      // Assert
      expect(mockAxios.get).toHaveBeenCalledWith('/some/endpoint');
      expect(result).toEqual(mockResponse.data);
    });
  });
});
```

### Component Tests

Component tests should be placed in the `src/components/*/tests` directory with a `.vitest.js` extension.

Example:

```javascript
// src/components/MyComponent/tests/MyComponent.vitest.js
import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import MyComponent from '../MyComponent.vue';

describe('MyComponent', () => {
  let wrapper;

  beforeEach(() => {
    // Reset the wrapper before each test
    wrapper = null;
  });

  it('renders correctly with default props', () => {
    // Arrange
    wrapper = mount(MyComponent, {
      propsData: {
        title: 'Test Title'
      }
    });

    // Assert
    expect(wrapper.find('.title').text()).toBe('Test Title');
  });

  it('emits an event when button is clicked', async () => {
    // Arrange
    wrapper = mount(MyComponent, {
      propsData: {
        title: 'Test Title'
      }
    });

    // Act
    await wrapper.find('button').trigger('click');

    // Assert
    expect(wrapper.emitted('click')).toBeTruthy();
  });
});
```

## Mocking

### Mocking Axios

Axios is mocked globally in the `vitest.setup.js` file:

```javascript
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
      put: vi.fn(),
      delete: vi.fn()
    }))
  }
}));
```

### Mocking Vue Components

To mock a Vue component, create a mock component in the `tests` directory:

```javascript
// src/components/MyComponent/tests/MyComponent.mock.vue
<template>
  <div class="my-component-mock">
    <div class="title">{{ title }}</div>
    <button @click="$emit('click')">Click Me</button>
  </div>
</template>

<script>
export default {
  name: 'MyComponent',
  props: {
    title: {
      type: String,
      required: true
    }
  }
};
</script>
```

Then import the mock component in your test:

```javascript
import MyComponent from './MyComponent.mock.vue';
```

## Best Practices

1. **Use the AAA pattern**: Arrange, Act, Assert
2. **Test one thing per test**: Each test should test one specific behavior
3. **Use descriptive test names**: Test names should describe what the test is testing
4. **Mock external dependencies**: Mock external dependencies like API calls
5. **Use beforeEach for setup**: Use beforeEach for common setup code
6. **Clean up after tests**: Reset mocks and state after each test
7. **Test edge cases**: Test edge cases like empty arrays, null values, etc.
8. **Test error handling**: Test error handling code
9. **Keep tests fast**: Tests should run quickly
10. **Keep tests independent**: Tests should not depend on each other
