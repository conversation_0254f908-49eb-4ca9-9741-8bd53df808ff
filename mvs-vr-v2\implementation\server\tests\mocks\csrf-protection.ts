/**
 * Mock CSRF Protection Module
 *
 * This is a mock implementation of the CSRF protection middleware for testing.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { vi } from 'vitest';
import { getIronSession } from 'iron-session';

// Mock functions
export const generateCsrfToken = vi.fn(() => 'generated-token');
export const getOrCreateCsrfToken = vi.fn((_req: NextApiRequest, _res: NextApiResponse) => {
  // This will call getIronSession internally
  vi.mocked(getIronSession).mockImplementation(() => {
    return Promise.resolve({
      csrfToken: 'test-token',
      save: vi.fn().mockResolvedValue(undefined),
    });
  });
  return Promise.resolve('test-token');
});
export const validateCsrfToken = vi.fn(
  (_req: NextApiRequest, _res: NextApiResponse, token: string) => {
    // This will call getIronSession internally
    vi.mocked(getIronSession).mockImplementation(() => {
      return Promise.resolve({
        csrfToken: 'test-token',
        save: vi.fn().mockResolvedValue(undefined),
      });
    });
    return Promise.resolve(token === 'test-token');
  },
);

// CSRF protection middleware
export function csrfProtection(
  options: {
    ignorePaths?: string[];
    ignoreMethods?: string[];
  } = {},
) {
  const { ignorePaths = [], ignoreMethods = ['GET', 'HEAD', 'OPTIONS'] } = options;

  return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    // Call getIronSession to make the mock work
    vi.mocked(getIronSession).mockImplementation(() => {
      return Promise.resolve({
        csrfToken: 'test-token',
        save: vi.fn().mockResolvedValue(undefined),
      });
    });

    // Skip CSRF check for non-mutating methods
    if (ignoreMethods.includes(req.method || '')) {
      next();
      return;
    }

    // Skip CSRF check for ignored paths
    if (ignorePaths.some(path => (req.url || '').startsWith(path))) {
      next();
      return;
    }

    // Get token from request
    const token = (req.headers['x-csrf-token'] as string) || req.body?._csrf;

    if (!token) {
      res.status(403).json({
        success: false,
        error: {
          code: 'CSRF_TOKEN_MISSING',
          message: 'CSRF token is missing',
        },
      });
      return;
    }

    // Validate token
    const isValid = await validateCsrfToken(req, res, token);

    if (!isValid) {
      res.status(403).json({
        success: false,
        error: {
          code: 'CSRF_TOKEN_INVALID',
          message: 'CSRF token is invalid or expired',
        },
      });
      return;
    }

    next();
  };
}

// CSRF token generator middleware
export function csrfTokenGenerator() {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    // Call getIronSession to make the mock work
    vi.mocked(getIronSession).mockImplementation(() => {
      return Promise.resolve({
        csrfToken: 'test-token',
        save: vi.fn().mockResolvedValue(undefined),
      });
    });

    // Generate token and add to response locals
    const token = await getOrCreateCsrfToken(req, res);
    const resWithLocals = res as NextApiResponse & { locals?: { csrfToken?: string } };
    resWithLocals.locals = resWithLocals.locals || {};
    resWithLocals.locals.csrfToken = token;
    next();
  };
}

export default {
  csrfProtection,
  csrfTokenGenerator,
  generateCsrfToken,
  getOrCreateCsrfToken,
  validateCsrfToken,
};
