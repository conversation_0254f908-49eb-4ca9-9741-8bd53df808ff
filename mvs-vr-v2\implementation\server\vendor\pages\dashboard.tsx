import React, { useEffect, useState } from "react";
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Grid,
  Paper,
  Typography,
} from "@mui/material";
import {
  BarChart as AnalyticsIcon,
  CloudUpload as AssetsIcon,
  People as MembersIcon,
} from "@mui/icons-material";
import VendorLayout from "../components/VendorLayout";
import ProtectedRoute from "../components/ProtectedRoute";
import { useAuth } from "../utils/auth/AuthContext";
import { assetService } from "../utils/api/asset-service";
import { memberService } from "../utils/api/member-service";
import { analyticsService } from "../utils/api/analytics-service";

interface DashboardStats {
  totalAssets: number;
  totalMembers: number;
  totalViews: number;
  recentActivity: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
  }>;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        if (!user) {
          console.error("No user found in session");
          setLoading(false);
          return;
        }

        // Get today's date and 30 days ago for analytics
        const endDate = new Date().toISOString().split("T")[0];
        const startDate =
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split(
            "T",
          )[0];

        // Fetch assets count (first page with size 1 to just get count)
        const assetsResponse = await assetService.getAssets(0, 1);
        const totalAssets = assetsResponse.totalCount;

        // Fetch members count (first page with size 1 to just get count)
        const membersResponse = await memberService.getMembers(0, 1);
        const totalMembers = membersResponse.totalCount;

        // Fetch analytics data
        const analyticsData = await analyticsService.getAnalyticsData(
          startDate,
          endDate,
        );
        const totalViews = analyticsData.totalViews;

        // Fetch recent activity
        // This would typically come from a dedicated activity service
        // For now, we'll simulate it with mock data based on real assets
        const recentActivity = [
          {
            id: "1",
            type: "asset_upload",
            description: "New asset uploaded: Living Room Model",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
              .toLocaleString(),
          },
          {
            id: "2",
            type: "member_invite",
            description: "Team member invited: <EMAIL>",
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000)
              .toLocaleString(),
          },
          {
            id: "3",
            type: "asset_view",
            description: "Asset viewed 10 times: Kitchen Cabinets",
            timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
              .toLocaleString(),
          },
          {
            id: "4",
            type: "report_generated",
            description: "Monthly analytics report generated",
            timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
              .toLocaleString(),
          },
          {
            id: "5",
            type: "asset_update",
            description: "Asset updated: Bathroom Fixtures",
            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
              .toLocaleString(),
          },
        ];

        setStats({
          totalAssets,
          totalMembers,
          totalViews,
          recentActivity,
        });
      } catch (error) {
        console.error("Error fetching dashboard stats:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchDashboardStats();
    }
  }, [user]);

  if (loading) {
    return (
      <VendorLayout title="Dashboard">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }

  return (
    <VendorLayout title="Dashboard">
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                bgcolor: "primary.light",
                color: "primary.contrastText",
              }}
            >
              <AssetsIcon sx={{ fontSize: 48, mb: 1 }} />
              <Typography component="h2" variant="h4">
                {stats?.totalAssets || 0}
              </Typography>
              <Typography variant="subtitle1">Total Assets</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                bgcolor: "secondary.light",
                color: "secondary.contrastText",
              }}
            >
              <MembersIcon sx={{ fontSize: 48, mb: 1 }} />
              <Typography component="h2" variant="h4">
                {stats?.totalMembers || 0}
              </Typography>
              <Typography variant="subtitle1">Team Members</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                bgcolor: "success.light",
                color: "success.contrastText",
              }}
            >
              <AnalyticsIcon sx={{ fontSize: 48, mb: 1 }} />
              <Typography component="h2" variant="h4">
                {stats?.totalViews || 0}
              </Typography>
              <Typography variant="subtitle1">Total Views</Typography>
            </Paper>
          </Grid>

          {/* Recent Activity */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Recent Activity" />
              <CardContent>
                {stats?.recentActivity && stats.recentActivity.length > 0
                  ? (
                    stats.recentActivity.map((activity: {
                      id: string;
                      type: string;
                      description: string;
                      timestamp: string;
                    }) => (
                      <Box
                        key={activity.id}
                        sx={{ mb: 2, p: 1, borderBottom: "1px solid #eee" }}
                      >
                        <Typography variant="subtitle1">
                          {activity.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {activity.type} • {activity.timestamp}
                        </Typography>
                      </Box>
                    ))
                  )
                  : <Typography variant="body1">No recent activity</Typography>}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </VendorLayout>
  );
};

// Wrap the Dashboard component with ProtectedRoute
const ProtectedDashboard: React.FC = () => {
  return (
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  );
};

export default ProtectedDashboard;
