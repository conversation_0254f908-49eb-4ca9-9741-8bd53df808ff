# Jest to Vitest Migration Guide

This guide provides instructions for migrating tests from <PERSON><PERSON> to Vitest in the MVS-VR project.

## Common Replacements

| Jest | Vitest |
|------|--------|
| `jest.mock()` | `vi.mock()` |
| `jest.fn()` | `vi.fn()` |
| `jest.spyOn()` | `vi.spyOn()` |
| `jest.clearAllMocks()` | `vi.clearAllMocks()` |
| `jest.resetAllMocks()` | `vi.resetAllMocks()` |
| `jest.restoreAllMocks()` | `vi.restoreAllMocks()` |
| `jest.useFakeTimers()` | `vi.useFakeTimers()` |
| `jest.useRealTimers()` | `vi.useRealTimers()` |
| `jest.advanceTimersByTime()` | `vi.advanceTimersByTime()` |
| `jest.runAllTimers()` | `vi.runAllTimers()` |
| `jest.Mock` | `ReturnType<typeof vi.fn>` |

## Import Changes

```javascript
// Jest
import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Vitest
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
```

## Mocking Modules

```javascript
// Jest
jest.mock('module-name', () => ({
  default: jest.fn(),
  namedExport: jest.fn(),
}));

// Vitest
vi.mock('module-name', () => ({
  default: vi.fn(),
  namedExport: vi.fn(),
}));
```

For ES modules that return a default export:

```javascript
// Vitest - for modules with default export
vi.mock('module-name', () => {
  return { default: vi.fn() };
});
```

## Mocking Classes and Constructors

```javascript
// Jest
jest.mock('redis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
  }));
});

// Vitest
vi.mock('redis', () => {
  const RedisMock = vi.fn().mockImplementation(() => ({
    get: vi.fn(),
    set: vi.fn(),
  }));
  return { default: RedisMock };
});
```

## Type Casting

```javascript
// Jest
(someFunction as jest.Mock).mockReturnValue('value');

// Vitest
(someFunction as ReturnType<typeof vi.fn>).mockReturnValue('value');
```

## File Extensions

When importing local modules, add the `.js` extension:

```javascript
// Jest
import { myFunction } from '../../path/to/module';

// Vitest
import { myFunction } from '../../path/to/module.js';
```

## Common Issues and Solutions

### Issue: "vi.mock() is not returning an object"

**Solution**: Return an object with a `default` property:

```javascript
// Incorrect
vi.mock('module-name', () => {
  return vi.fn().mockImplementation(() => ({}));
});

// Correct
vi.mock('module-name', () => {
  const MockClass = vi.fn().mockImplementation(() => ({}));
  return { default: MockClass };
});
```

### Issue: "Cannot find name 'jest'"

**Solution**: Replace all `jest` references with `vi`:

```javascript
// Before
jest.clearAllMocks();
const mockFn = jest.fn();

// After
vi.clearAllMocks();
const mockFn = vi.fn();
```

### Issue: Type errors with mocks

**Solution**: Use proper type casting:

```javascript
// Before
(myFunction as jest.Mock).mockReturnValue('test');

// After
(myFunction as ReturnType<typeof vi.fn>).mockReturnValue('test');
```

## Testing Commands

```bash
# Run all tests
npm test

# Run unit tests
npm run test:unit

# Run a specific test file
npx vitest run path/to/test.ts

# Run tests in watch mode
npm run test:watch
```

## Additional Resources

- [Vitest Documentation](https://vitest.dev/guide/)
- [Vitest API Reference](https://vitest.dev/api/)
- [Jest to Vitest Migration](https://vitest.dev/guide/migration.html)
