#!/bin/bash

# Ensure script fails on any error
set -e

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Setting up test environment...${NC}"

# Check for required tools
required_tools=("node" "deno" "npm")
missing_tools=()

for tool in "${required_tools[@]}"; do
  if ! command_exists "$tool"; then
    missing_tools+=("$tool")
  fi
done

if [ ${#missing_tools[@]} -ne 0 ]; then
  echo -e "${RED}Error: Missing required tools: ${missing_tools[*]}${NC}"
  exit 1
fi

# Create necessary directories
echo -e "${YELLOW}Creating test directories...${NC}"
mkdir -p .deno/types
mkdir -p coverage
mkdir -p dist/tests

# Install dependencies if package.json exists and node_modules is missing
if [ -f "package.json" ] && [ ! -d "node_modules" ]; then
  echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
  npm install
fi

# Generate type definitions for Deno compatibility
echo -e "${YELLOW}Generating type definitions...${NC}"
deno types > .deno/types/lib.deno.d.ts

# Set up environment variables
echo -e "${YELLOW}Setting up test environment variables...${NC}"
if [ ! -f ".env.test" ]; then
  echo -e "${YELLOW}Creating .env.test file...${NC}"
  cat > .env.test << EOF
NODE_ENV=test
VITEST_SEGFAULT_RETRY=3
VITEST_MAX_THREADS=4
VITEST_MIN_THREADS=1
VITEST_POOL_OPTIONS={"threads":{"singleThread":true}}
EOF
fi

# Generate tsconfig.test.json if it doesn't exist
if [ ! -f "tests/tsconfig.json" ]; then
  echo -e "${YELLOW}Creating test TypeScript configuration...${NC}"
  cat > tests/tsconfig.json << EOF
{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "types": ["vitest/globals", "@testing-library/jest-dom", "node"],
    "paths": {
      "@/*": ["../src/*"],
      "@tests/*": ["./*"],
      "@shared/*": ["../shared/*"],
      "@services/*": ["../services/*"]
    }
  },
  "include": [
    "./**/*.ts",
    "../src/types/*.d.ts",
    "../types/*.d.ts"
  ]
}
EOF
fi

# Clear test cache
echo -e "${YELLOW}Clearing test cache...${NC}"
rm -rf ./node_modules/.vitest
rm -rf ./.deno/.vitest

# Run type checking
echo -e "${YELLOW}Running type checking...${NC}"
deno check **/*.ts

# Success message
echo -e "${GREEN}Test environment setup complete!${NC}"
echo -e "${YELLOW}You can now run tests using:${NC}"
echo -e "  ${GREEN}npm test${NC} - Run tests"
echo -e "  ${GREEN}npm run test:watch${NC} - Run tests in watch mode"
echo -e "  ${GREEN}npm run test:coverage${NC} - Run tests with coverage"