/**
 * Audit API Endpoints for Validation
 * 
 * This script audits API endpoints to identify those with insufficient validation.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const readdirAsync = promisify(fs.readdir);
const statAsync = promisify(fs.stat);
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  apiDir: path.join(__dirname, '../../api'),
  routesDir: path.join(__dirname, '../../routes'),
  outputPath: path.join(__dirname, '../../logs/api-validation-audit.json'),
  validationPatterns: [
    'validateQuery',
    'validateBody',
    'validateParams',
    'validate',
    'schema.parse',
    'schema.parseAsync',
    'z.object',
    'Joi.validate',
    'check(',
    'validationResult'
  ]
};

/**
 * Find all JavaScript files in a directory recursively
 * @param {string} dir - Directory to search
 * @returns {Promise<Array<string>>} Array of file paths
 */
async function findJsFiles(dir) {
  const files = [];
  
  async function traverse(currentDir) {
    const entries = await readdirAsync(currentDir);
    
    for (const entry of entries) {
      const entryPath = path.join(currentDir, entry);
      const stats = await statAsync(entryPath);
      
      if (stats.isDirectory()) {
        await traverse(entryPath);
      } else if (stats.isFile() && (entry.endsWith('.js') || entry.endsWith('.ts'))) {
        files.push(entryPath);
      }
    }
  }
  
  await traverse(dir);
  return files;
}

/**
 * Parse route definitions from a file
 * @param {string} filePath - Path to file
 * @returns {Promise<Array<Object>>} Array of route objects
 */
async function parseRoutes(filePath) {
  const content = await readFileAsync(filePath, 'utf8');
  const routes = [];
  
  // Parse the file
  const ast = parse(content, {
    sourceType: 'module',
    plugins: ['jsx', 'typescript']
  });
  
  // Find route definitions
  traverse(ast, {
    CallExpression(path) {
      const callee = path.node.callee;
      const args = path.node.arguments;
      
      // Check for router.METHOD calls (e.g., router.get, router.post)
      if (
        callee.type === 'MemberExpression' &&
        (callee.object.name === 'router' || callee.object.name === 'app') &&
        ['get', 'post', 'put', 'patch', 'delete'].includes(callee.property.name) &&
        args.length >= 2
      ) {
        const method = callee.property.name.toUpperCase();
        let route = '';
        
        // Get route path
        if (args[0].type === 'StringLiteral') {
          route = args[0].value;
        }
        
        // Check for validation middleware
        const hasValidation = args.slice(1).some(arg => {
          if (arg.type === 'Identifier') {
            return config.validationPatterns.some(pattern => arg.name.includes(pattern));
          } else if (arg.type === 'CallExpression') {
            const calleeStr = content.substring(arg.callee.start, arg.callee.end);
            return config.validationPatterns.some(pattern => calleeStr.includes(pattern));
          }
          return false;
        });
        
        // Get line number
        const lineNumber = content.substring(0, path.node.start).split('\n').length;
        
        routes.push({
          method,
          route,
          hasValidation,
          file: filePath,
          line: lineNumber
        });
      }
    }
  });
  
  return routes;
}

/**
 * Audit API endpoints for validation
 * @returns {Promise<Object>} Audit results
 */
async function auditApiValidation() {
  logger.info('Starting API validation audit...');
  
  // Find all JavaScript files in API and routes directories
  const apiFiles = await findJsFiles(config.apiDir);
  const routesFiles = await findJsFiles(config.routesDir);
  const allFiles = [...apiFiles, ...routesFiles];
  
  logger.info(`Found ${allFiles.length} files to audit`);
  
  // Parse routes from all files
  const allRoutes = [];
  
  for (const file of allFiles) {
    try {
      const routes = await parseRoutes(file);
      allRoutes.push(...routes);
    } catch (error) {
      console.error(`Error parsing file ${file}:`, error);
    }
  }
  
  logger.info(`Found ${allRoutes.length} routes`);
  
  // Group routes by validation status
  const routesWithValidation = allRoutes.filter(route => route.hasValidation);
  const routesWithoutValidation = allRoutes.filter(route => !route.hasValidation);
  
  logger.info(`Routes with validation: ${routesWithValidation.length}`);
  logger.info(`Routes without validation: ${routesWithoutValidation.length}`);
  
  // Group routes by method
  const routesByMethod = {};
  
  for (const route of allRoutes) {
    if (!routesByMethod[route.method]) {
      routesByMethod[route.method] = [];
    }
    routesByMethod[route.method].push(route);
  }
  
  // Prepare audit results
  const auditResults = {
    timestamp: new Date().toISOString(),
    summary: {
      totalRoutes: allRoutes.length,
      routesWithValidation: routesWithValidation.length,
      routesWithoutValidation: routesWithoutValidation.length,
      validationCoverage: allRoutes.length > 0 ? (routesWithValidation.length / allRoutes.length) * 100 : 0
    },
    byMethod: Object.keys(routesByMethod).reduce((acc, method) => {
      const routes = routesByMethod[method];
      acc[method] = {
        total: routes.length,
        withValidation: routes.filter(route => route.hasValidation).length,
        withoutValidation: routes.filter(route => !route.hasValidation).length
      };
      return acc;
    }, {}),
    routesWithoutValidation: routesWithoutValidation.map(route => ({
      method: route.method,
      route: route.route,
      file: path.relative(path.join(__dirname, '../..'), route.file),
      line: route.line
    }))
  };
  
  // Sort routes without validation by risk (POST/PUT/PATCH first, then GET/DELETE)
  auditResults.routesWithoutValidation.sort((a, b) => {
    const riskOrder = { POST: 0, PUT: 1, PATCH: 2, DELETE: 3, GET: 4 };
    return riskOrder[a.method] - riskOrder[b.method];
  });
  
  // Save audit results
  await writeFileAsync(config.outputPath, JSON.stringify(auditResults, null, 2));
  
  logger.info(`Audit completed. Results saved to ${config.outputPath}`);
  
  return auditResults;
}

// If script is run directly, run audit
if (require.main === module) {
  auditApiValidation()
    .then(results => {
      logger.info('Audit summary:');
      logger.info(`Total routes: ${results.summary.totalRoutes}`);
      logger.info(`Routes with validation: ${results.summary.routesWithValidation}`);
      logger.info(`Routes without validation: ${results.summary.routesWithoutValidation}`);
      logger.info(`Validation coverage: ${results.summary.validationCoverage.toFixed(2);}%`);
      
      // Print high-risk routes without validation
      const highRiskRoutes = results.routesWithoutValidation.filter(route => 
        ['POST', 'PUT', 'PATCH'].includes(route.method)
      );
      
      if (highRiskRoutes.length > 0) {
        logger.info('\nHigh-risk routes without validation:');
        highRiskRoutes.forEach(route => {
          logger.info(`${route.method} ${route.route} (${route.file}:${route.line});`);
        });
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  auditApiValidation
};
