import apiClient from './api-client';

/**
 * Interface for asset
 */
export interface Asset {
  id: string;
  name: string;
  type: string;
  size: number;
  created_at: string;
  updated_at: string;
  status: string;
  version: number;
  metadata?: Record<string, any>;
}

/**
 * Interface for asset list response
 */
export interface AssetListResponse {
  assets: Asset[];
  totalCount: number;
  page: number;
  pageSize: number;
}

/**
 * Interface for asset upload request
 */
export interface AssetUploadRequest {
  file: File;
  name?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface for asset update request
 */
export interface AssetUpdateRequest {
  name?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface for asset version
 */
export interface AssetVersion {
  id: string;
  version: number;
  created_at: string;
  created_by: string;
  changes: string;
}

/**
 * Asset service for vendor portal
 */
class AssetService {
  /**
   * Get list of assets with pagination
   */
  public async getAssets(page: number = 0, pageSize: number = 10): Promise<AssetListResponse> {
    try {
      const response = await apiClient.get<AssetListResponse>('/vendor/assets', {
        params: { page, pageSize }
      });
      return response;
    } catch (error) {
      console.error('Failed to get assets:', error);
      throw error;
    }
  }

  /**
   * Get asset by ID
   */
  public async getAsset(id: string): Promise<Asset> {
    try {
      const response = await apiClient.get<Asset>(`/vendor/assets/${id}`);
      return response;
    } catch (error) {
      console.error(`Failed to get asset ${id}:`, error);
      throw error;
    }
  }

  /**
   * Upload a new asset
   */
  public async uploadAsset(
    request: AssetUploadRequest,
    onProgress?: (percentage: number) => void
  ): Promise<Asset> {
    try {
      // First, create a metadata record
      const metadata = {
        name: request.name || request.file.name,
        type: request.file.type,
        size: request.file.size,
        ...request.metadata
      };
      
      const metadataResponse = await apiClient.post<{ uploadUrl: string; assetId: string }>(
        '/vendor/assets/prepare-upload',
        metadata
      );
      
      // Then upload the file
      await apiClient.uploadFile(
        metadataResponse.uploadUrl,
        request.file,
        onProgress
      );
      
      // Finally, get the asset details
      return this.getAsset(metadataResponse.assetId);
    } catch (error) {
      console.error('Failed to upload asset:', error);
      throw error;
    }
  }

  /**
   * Update asset metadata
   */
  public async updateAsset(id: string, request: AssetUpdateRequest): Promise<Asset> {
    try {
      const response = await apiClient.put<Asset>(`/vendor/assets/${id}`, request);
      return response;
    } catch (error) {
      console.error(`Failed to update asset ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an asset
   */
  public async deleteAsset(id: string): Promise<void> {
    try {
      await apiClient.delete(`/vendor/assets/${id}`);
    } catch (error) {
      console.error(`Failed to delete asset ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get asset versions
   */
  public async getAssetVersions(id: string): Promise<AssetVersion[]> {
    try {
      const response = await apiClient.get<AssetVersion[]>(`/vendor/assets/${id}/versions`);
      return response;
    } catch (error) {
      console.error(`Failed to get versions for asset ${id}:`, error);
      throw error;
    }
  }

  /**
   * Revert to a specific version
   */
  public async revertToVersion(assetId: string, versionId: string): Promise<Asset> {
    try {
      const response = await apiClient.post<Asset>(`/vendor/assets/${assetId}/revert`, {
        versionId
      });
      return response;
    } catch (error) {
      console.error(`Failed to revert asset ${assetId} to version ${versionId}:`, error);
      throw error;
    }
  }

  /**
   * Download asset
   */
  public async getDownloadUrl(id: string): Promise<string> {
    try {
      const response = await apiClient.get<{ downloadUrl: string }>(`/vendor/assets/${id}/download`);
      return response.downloadUrl;
    } catch (error) {
      console.error(`Failed to get download URL for asset ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create asset bundle
   */
  public async createBundle(assetIds: string[], name: string): Promise<any> {
    try {
      const response = await apiClient.post('/vendor/assets/bundles', {
        assetIds,
        name
      });
      return response;
    } catch (error) {
      console.error('Failed to create bundle:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const assetService = new AssetService();
export default assetService;
