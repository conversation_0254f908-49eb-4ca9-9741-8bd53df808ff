# Enhanced User Analysis

The Enhanced User Analysis system provides advanced user behavior prediction, churn prediction, and personalization impact measurement for the MVS-VR platform.

## Components

### 1. User Behavior Predictor

The User Behavior Predictor analyzes user activity patterns and predicts future actions. It provides:

- Next action prediction based on historical patterns
- Session pattern analysis (time of day, day of week)
- Engagement scoring
- Personalized recommendations

### 2. Churn Predictor

The Churn Predictor analyzes user engagement patterns and predicts churn risk. It provides:

- Churn risk assessment (low, medium, high, very high)
- Churn probability calculation
- Engagement trend analysis
- Retention strategy recommendations

### 3. Personalization Impact Analyzer

The Personalization Impact Analyzer measures the impact of personalization on user engagement and business metrics. It provides:

- Engagement impact measurement (session duration, page views, interaction rate, return rate)
- Business impact measurement (conversion rate, revenue, user retention, customer satisfaction)
- Personalization type effectiveness analysis
- Personalization strategy recommendations

## API Endpoints

### User Behavior Predictor

#### `GET /api/user/:userId/next-actions`

Get predicted next actions for a user.

**Response:**

```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "predictions": [
    {
      "action": "view_product",
      "count": 25,
      "probability": 0.5
    },
    {
      "action": "add_to_cart",
      "count": 15,
      "probability": 0.3
    },
    ...
  ],
  "confidence": 0.8,
  "nextLikelyActions": [
    {
      "action": "view_product",
      "count": 25,
      "probability": 0.5
    },
    ...
  ]
}
```

#### `GET /api/user/:userId/session-patterns`

Get session patterns for a user.

**Response:**

```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "sessionCount": 10,
  "avgSessionDuration": 1200,
  "timeOfDayDistribution": [0.1, 0.2, 0.3, ...],
  "dayOfWeekDistribution": [0.1, 0.2, 0.3, ...]
}
```

### Churn Predictor

#### `GET /api/user/:userId/churn-prediction`

Get churn prediction for a user.

**Response:**

```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "churnRisk": "medium",
  "churnProbability": 0.35,
  "engagementScore": 65,
  "trendScore": -0.2,
  "activityTrend": [10, 8, 7, 6],
  "retentionStrategies": [
    "Send usage tips and best practices",
    "Highlight unused features",
    "Offer training session",
    "Request feedback on product"
  ]
}
```

#### `GET /api/users/high-churn-risk`

Get users with high churn risk.

**Response:**

```json
{
  "users": [
    {
      "user": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "email": "<EMAIL>",
        "name": "John Doe",
        "created_at": "2023-01-01T00:00:00Z"
      },
      "prediction": {
        "churnRisk": "high",
        "churnProbability": 0.75,
        "engagementScore": 35,
        "trendScore": -0.5,
        "activityTrend": [10, 8, 5, 3],
        "retentionStrategies": [
          "Offer moderate discount on renewal",
          "Personalized feature highlight email",
          "Webinar invitation for advanced usage",
          "Check-in call from customer success"
        ]
      }
    },
    ...
  ]
}
```

### Personalization Impact Analyzer

#### `GET /api/personalization/impact`

Get personalization impact analysis.

**Response:**

```json
{
  "engagementImpact": {
    "sessionDuration": 25.5,
    "pageViews": 15.2,
    "interactionRate": 30.1,
    "returnRate": 20.3
  },
  "businessImpact": {
    "conversionRate": 15.5,
    "revenue": 20.2,
    "userRetention": 10.1,
    "customerSatisfaction": 5.3
  },
  "personalizationTypes": [
    {
      "type": "content_recommendation",
      "count": 1250,
      "engagementImpact": 35.5,
      "businessImpact": 25.2,
      "overallImpact": 30.35
    },
    ...
  ],
  "recommendations": [
    "Increase \"content_recommendation\" personalization which shows the highest impact (30.35%)",
    "Personalization increases session duration by 25.50%",
    "Personalization increases page views by 15.20%",
    "Strong correlation between personalization and conversion rate (0.75)"
  ]
}
```

#### `GET /api/personalization/types`

Get personalization type effectiveness.

**Response:**

```json
{
  "types": [
    {
      "type": "content_recommendation",
      "count": 1250,
      "engagementImpact": 35.5,
      "businessImpact": 25.2,
      "overallImpact": 30.35
    },
    ...
  ]
}
```

#### `GET /api/personalization/recommendations`

Get personalization recommendations.

**Response:**

```json
{
  "recommendations": [
    "Increase \"content_recommendation\" personalization which shows the highest impact (30.35%)",
    "Personalization increases session duration by 25.50%",
    "Personalization increases page views by 15.20%",
    "Strong correlation between personalization and conversion rate (0.75)"
  ]
}
```

## Database Schema

### `user_activities` Table

Stores user activity data:

- `id` (uuid): Primary key
- `user_id` (uuid): Foreign key to the `users` table
- `action_type` (text): The type of action (e.g., page_view, interaction)
- `action_data` (jsonb): Additional data about the action
- `created_at` (timestamp): When the action was performed

### `user_sessions` Table

Stores user session data:

- `id` (uuid): Primary key
- `user_id` (uuid): Foreign key to the `users` table
- `duration` (integer): Session duration in seconds
- `page_views` (integer): Number of page views in the session
- `interactions` (integer): Number of interactions in the session
- `created_at` (timestamp): When the session started

### `personalization_events` Table

Stores personalization events:

- `id` (uuid): Primary key
- `user_id` (uuid): Foreign key to the `users` table
- `type` (text): The type of personalization (e.g., content_recommendation)
- `content` (jsonb): The personalized content
- `created_at` (timestamp): When the personalization was delivered

### `business_metrics` Table

Stores business metrics:

- `id` (uuid): Primary key
- `date` (date): The date of the metrics
- `active_users` (integer): Number of active users
- `new_users` (integer): Number of new users
- `conversion_rate` (float): Conversion rate
- `revenue` (float): Revenue
- `retention_rate` (float): Retention rate
- `satisfaction_score` (float): Customer satisfaction score
- `created_at` (timestamp): When the metrics were recorded

### `user_predictions` Table

Stores user predictions:

- `id` (uuid): Primary key
- `user_id` (uuid): Foreign key to the `users` table
- `prediction_type` (text): The type of prediction (e.g., churn, next_action)
- `prediction_data` (jsonb): The prediction data
- `confidence` (float): The confidence of the prediction
- `created_at` (timestamp): When the prediction was made

## Configuration

The Enhanced User Analysis system can be configured with the following environment variables:

- `USER_BEHAVIOR_PREDICTOR_PORT`: The port for the User Behavior Predictor (default: 9103)
- `CHURN_PREDICTOR_PORT`: The port for the Churn Predictor (default: 9104)
- `PERSONALIZATION_IMPACT_ANALYZER_PORT`: The port for the Personalization Impact Analyzer (default: 9105)
- `SUPABASE_URL`: The URL of the Supabase instance
- `SUPABASE_KEY`: The service key for the Supabase instance

## Deployment

The Enhanced User Analysis system is deployed as Docker containers and integrated with the existing monitoring system.

### Docker Compose

```yaml
# User Behavior Predictor
user-behavior-predictor:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.user-behavior-predictor
  container_name: mvs-vr-user-behavior-predictor
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - USER_BEHAVIOR_PREDICTOR_PORT=9103
  ports:
    - "9103:9103"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus

# Churn Predictor
churn-predictor:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.churn-predictor
  container_name: mvs-vr-churn-predictor
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - CHURN_PREDICTOR_PORT=9104
  ports:
    - "9104:9104"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus

# Personalization Impact Analyzer
personalization-impact-analyzer:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.personalization-impact-analyzer
  container_name: mvs-vr-personalization-impact-analyzer
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - PERSONALIZATION_IMPACT_ANALYZER_PORT=9105
  ports:
    - "9105:9105"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus
```

### Prometheus Configuration

```yaml
# User Behavior Predictor
- job_name: "user-behavior-predictor"
  static_configs:
    - targets: ["user-behavior-predictor:9103"]

# Churn Predictor
- job_name: "churn-predictor"
  static_configs:
    - targets: ["churn-predictor:9104"]

# Personalization Impact Analyzer
- job_name: "personalization-impact-analyzer"
  static_configs:
    - targets: ["personalization-impact-analyzer:9105"]
```
