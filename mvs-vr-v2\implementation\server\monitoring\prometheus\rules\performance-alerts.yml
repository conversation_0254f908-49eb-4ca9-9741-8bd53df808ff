groups:
  - name: performance_degradation_alerts
    rules:
      # API Response Time Degradation
      - alert: ApiResponseTimeDegradation
        expr: (avg_over_time(mvs_vr_http_request_duration_seconds{quantile="0.95"}[1h]) / avg_over_time(mvs_vr_http_request_duration_seconds{quantile="0.95"}[1d] offset 1d)) > 1.3
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "API response time degradation for {{ $labels.endpoint }}"
          description: "API response time has increased by {{ $value | humanizePercentage }} compared to the baseline"
          dashboard_id: "api-performance"
          ml_context: "true"

      # Frontend Rendering Time Degradation
      - alert: FrontendRenderingDegradation
        expr: (avg_over_time(mvs_vr_frontend_render_time_ms{quantile="0.95"}[1h]) / avg_over_time(mvs_vr_frontend_render_time_ms{quantile="0.95"}[1d] offset 1d)) > 1.4
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "Frontend rendering time degradation for {{ $labels.component }}"
          description: "Frontend rendering time has increased by {{ $value | humanizePercentage }} compared to the baseline"
          dashboard_id: "frontend-performance"
          ml_context: "true"

      # Database Query Performance Degradation
      - alert: DatabaseQueryDegradation
        expr: (avg_over_time(mvs_vr_db_query_duration_seconds{quantile="0.95"}[1h]) / avg_over_time(mvs_vr_db_query_duration_seconds{quantile="0.95"}[1d] offset 1d)) > 1.8
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "Database query performance degradation for {{ $labels.query_type }}"
          description: "Database query performance has degraded by {{ $value | humanizePercentage }} compared to the baseline"
          dashboard_id: "database-performance"
          ml_context: "true"

      # Asset Processing Time Degradation
      - alert: AssetProcessingDegradation
        expr: (avg_over_time(mvs_vr_asset_processing_duration_seconds{quantile="0.95"}[1h]) / avg_over_time(mvs_vr_asset_processing_duration_seconds{quantile="0.95"}[1d] offset 1d)) > 1.5
        for: 10m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "Asset processing time degradation for {{ $labels.asset_type }}"
          description: "Asset processing time has increased by {{ $value | humanizePercentage }} compared to the baseline"
          dashboard_id: "asset-processing"
          ml_context: "true"

  - name: high_error_rate_alerts
    rules:
      # High API Error Rate
      - alert: HighApiErrorRate
        expr: sum(rate(mvs_vr_http_requests_total{status=~"5.."}[5m])) by (endpoint) / sum(rate(mvs_vr_http_requests_total[5m])) by (endpoint) > 0.03
        for: 3m
        labels:
          severity: error
          category: errors
        annotations:
          summary: "High API error rate for {{ $labels.endpoint }}"
          description: "API error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          dashboard_id: "api-errors"
          ml_context: "true"

      # Authentication Failures Spike
      - alert: AuthFailuresSpike
        expr: sum(rate(mvs_vr_auth_failures_total[5m])) by (auth_method) / sum(rate(mvs_vr_auth_attempts_total[5m])) by (auth_method) > 0.15
        for: 3m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Authentication failures spike for {{ $labels.auth_method }}"
          description: "Authentication failure rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          dashboard_id: "auth-security"
          ml_context: "true"

      # Asset Processing Failures
      - alert: AssetProcessingFailures
        expr: sum(rate(mvs_vr_asset_processing_errors_total[15m])) by (asset_type) / sum(rate(mvs_vr_asset_processing_total[15m])) by (asset_type) > 0.08
        for: 10m
        labels:
          severity: error
          category: errors
        annotations:
          summary: "High asset processing failure rate for {{ $labels.asset_type }}"
          description: "Asset processing failure rate is {{ $value | humanizePercentage }} for the last 15 minutes"
          dashboard_id: "asset-processing"
          ml_context: "true"

      # Database Connection Errors
      - alert: DatabaseConnectionErrors
        expr: sum(increase(mvs_vr_db_connection_errors_total[5m])) by (database) > 3
        for: 3m
        labels:
          severity: critical
          category: errors
        annotations:
          summary: "Database connection errors for {{ $labels.database }}"
          description: "{{ $value }} database connection errors in the last 5 minutes"
          dashboard_id: "database-health"
          ml_context: "true"
