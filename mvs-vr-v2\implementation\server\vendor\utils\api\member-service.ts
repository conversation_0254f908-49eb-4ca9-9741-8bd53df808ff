import apiClient from './api-client';

/**
 * Interface for team member
 */
export interface Member {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
  created_at: string;
  last_login: string | null;
  status: 'invited' | 'active' | 'suspended';
}

/**
 * Interface for member list response
 */
export interface MemberListResponse {
  members: Member[];
  totalCount: number;
  page: number;
  pageSize: number;
}

/**
 * Interface for create member request
 */
export interface CreateMemberRequest {
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
}

/**
 * Interface for update member request
 */
export interface UpdateMemberRequest {
  id: string;
  name?: string;
  role?: 'admin' | 'editor' | 'viewer';
  status?: 'invited' | 'active' | 'suspended';
}

/**
 * Team member service for vendor portal
 */
class MemberService {
  /**
   * Get list of team members with pagination
   */
  public async getMembers(page: number = 0, pageSize: number = 10): Promise<MemberListResponse> {
    try {
      const response = await apiClient.get<MemberListResponse>('/vendor/members', {
        params: { page, pageSize },
      });
      return response;
    } catch (error) {
      console.error('Failed to get team members:', error);
      throw error;
    }
  }

  /**
   * Get member by ID
   */
  public async getMember(id: string): Promise<Member> {
    try {
      const response = await apiClient.get<Member>(`/vendor/members/${id}`);
      return response;
    } catch (error) {
      console.error(`Failed to get team member ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new team member
   */
  public async createMember(request: CreateMemberRequest): Promise<Member> {
    try {
      const response = await apiClient.post<Member>('/vendor/members', request);
      return response;
    } catch (error) {
      console.error('Failed to create team member:', error);
      throw error;
    }
  }

  /**
   * Update team member
   */
  public async updateMember(request: UpdateMemberRequest): Promise<Member> {
    try {
      if (!request.id) {
        throw new Error('Member ID is required for update');
      }

      const { id, ...updateData } = request;
      const response = await apiClient.put<Member>(`/vendor/members/${id}`, updateData);
      return response;
    } catch (error) {
      console.error(`Failed to update team member ${request.id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a team member
   */
  public async deleteMember(id: string): Promise<void> {
    try {
      await apiClient.delete(`/vendor/members/${id}`);
    } catch (error) {
      console.error(`Failed to delete team member ${id}:`, error);
      throw error;
    }
  }

  /**
   * Resend invitation to a team member
   */
  public async resendInvitation(id: string): Promise<void> {
    try {
      await apiClient.post(`/vendor/members/${id}/resend-invitation`);
    } catch (error) {
      console.error(`Failed to resend invitation to team member ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get current member's profile
   */
  public async getCurrentMemberProfile(): Promise<Member> {
    try {
      const response = await apiClient.get<Member>('/vendor/members/me');
      return response;
    } catch (error) {
      console.error('Failed to get current member profile:', error);
      throw error;
    }
  }

  /**
   * Update current member's profile
   */
  public async updateCurrentMemberProfile(request: { name?: string }): Promise<Member> {
    try {
      const response = await apiClient.put<Member>('/vendor/members/me', request);
      return response;
    } catch (error) {
      console.error('Failed to update current member profile:', error);
      throw error;
    }
  }

  /**
   * Change current member's password
   */
  public async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/vendor/members/change-password', {
        currentPassword,
        newPassword,
      });
    } catch (error) {
      console.error('Failed to change password:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const memberService = new MemberService();
export default memberService;
