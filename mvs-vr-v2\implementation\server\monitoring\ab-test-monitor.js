/**
 * A/B Test Monitor
 *
 * This service integrates the A/B testing framework with the monitoring system to:
 * - Track performance metrics for each test variant
 * - Compare error rates between variants
 * - Monitor user engagement metrics across variants
 * - Analyze performance impact of different variants
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Create Express app
const app = express();
app.use(express.json());

// Create a Registry for A/B test metrics
const register = new promClient.Registry();

// Create custom metrics for A/B tests
const metrics = {
  // Test variant performance metrics
  variantApiLatency: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_api_latency_ms',
    help: 'API latency by test variant in milliseconds',
    labelNames: ['test_id', 'variant_id', 'endpoint', 'optimization_strategy'],
  }),

  variantPageLoadTime: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_page_load_time_ms',
    help: 'Page load time by test variant in milliseconds',
    labelNames: ['test_id', 'variant_id', 'page', 'optimization_strategy'],
  }),

  variantErrorRate: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_error_rate_percent',
    help: 'Error rate by test variant in percent',
    labelNames: ['test_id', 'variant_id', 'error_type', 'optimization_strategy'],
  }),

  // Test variant engagement metrics
  variantSessionDuration: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_session_duration_seconds',
    help: 'Average session duration by test variant in seconds',
    labelNames: ['test_id', 'variant_id', 'optimization_strategy'],
  }),

  variantBounceRate: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_bounce_rate_percent',
    help: 'Bounce rate by test variant in percent',
    labelNames: ['test_id', 'variant_id', 'optimization_strategy'],
  }),

  variantConversionRate: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_conversion_rate_percent',
    help: 'Conversion rate by test variant in percent',
    labelNames: ['test_id', 'variant_id', 'conversion_type', 'optimization_strategy'],
  }),

  // Test variant business metrics
  variantRevenuePerUser: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_revenue_per_user',
    help: 'Average revenue per user by test variant',
    labelNames: ['test_id', 'variant_id', 'optimization_strategy'],
  }),

  // Statistical significance
  variantSignificance: new promClient.Gauge({
    name: 'mvs_vr_ab_variant_significance_percent',
    help: 'Statistical significance of test variant results in percent',
    labelNames: ['test_id', 'variant_id', 'metric', 'optimization_strategy'],
  }),

  // Optimization strategy comparison metrics
  strategyPerformanceImprovement: new promClient.Gauge({
    name: 'mvs_vr_ab_strategy_performance_improvement_percent',
    help: 'Performance improvement percentage by optimization strategy',
    labelNames: ['test_id', 'optimization_strategy', 'metric'],
  }),

  strategyEngagementImprovement: new promClient.Gauge({
    name: 'mvs_vr_ab_strategy_engagement_improvement_percent',
    help: 'Engagement improvement percentage by optimization strategy',
    labelNames: ['test_id', 'optimization_strategy', 'metric'],
  }),

  strategyConversionImprovement: new promClient.Gauge({
    name: 'mvs_vr_ab_strategy_conversion_improvement_percent',
    help: 'Conversion improvement percentage by optimization strategy',
    labelNames: ['test_id', 'optimization_strategy', 'conversion_type'],
  }),

  strategyResourceUsage: new promClient.Gauge({
    name: 'mvs_vr_ab_strategy_resource_usage',
    help: 'Resource usage by optimization strategy',
    labelNames: ['test_id', 'optimization_strategy', 'resource_type'],
  }),

  strategyOverallScore: new promClient.Gauge({
    name: 'mvs_vr_ab_strategy_overall_score',
    help: 'Overall score for optimization strategy (0-100)',
    labelNames: ['test_id', 'optimization_strategy'],
  }),
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Get active A/B tests
 *
 * @returns {Array} - Active tests
 */
async function getActiveTests() {
  try {
    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from('ab_tests')
      .select(
        `
        id,
        name,
        description,
        start_date,
        end_date,
        traffic_percentage,
        optimization_strategy,
        variants (
          id,
          name,
          description,
          traffic_weight,
          optimization_details
        )
      `,
      )
      .lte('start_date', now)
      .gte('end_date', now)
      .eq('status', 'active');

    if (error) {
      logger.error('Error fetching active A/B tests', { error: error.message });
      return [];
    }

    return data || [];
  } catch (error) {
    logger.error('Error in getActiveTests', { error: error.message });
    return [];
  }
}

/**
 * Get test variant assignments
 *
 * @returns {Object} - User variant assignments
 */
async function getVariantAssignments() {
  try {
    const { data, error } = await supabase.from('ab_test_assignments').select(`
        user_id,
        test_id,
        variant_id,
        assigned_at
      `);

    if (error) {
      logger.error('Error fetching variant assignments', { error: error.message });
      return {};
    }

    // Group assignments by user
    return (data || []).reduce((acc, assignment) => {
      if (!acc[assignment.user_id]) {
        acc[assignment.user_id] = {};
      }

      acc[assignment.user_id][assignment.test_id] = assignment.variant_id;
      return acc;
    }, {});
  } catch (error) {
    logger.error('Error in getVariantAssignments', { error: error.message });
    return {};
  }
}

/**
 * Analyze A/B test performance
 */
async function analyzeTestPerformance() {
  try {
    // Get active tests
    const activeTests = await getActiveTests();

    if (activeTests.length === 0) {
      logger.debug('No active A/B tests found');
      return;
    }

    // Get variant assignments
    const variantAssignments = await getVariantAssignments();

    // Get API performance data
    const { data: apiMetrics, error: apiError } = await supabase
      .from('api_metrics')
      .select(
        `
        user_id,
        endpoint,
        response_time,
        status_code,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (apiError) {
      logger.error('Error fetching API metrics for A/B test analysis', { error: apiError.message });
      return;
    }

    // Get page performance data
    const { data: pageMetrics, error: pageError } = await supabase
      .from('page_metrics')
      .select(
        `
        user_id,
        page,
        load_time,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (pageError) {
      logger.error('Error fetching page metrics for A/B test analysis', {
        error: pageError.message,
      });
      return;
    }

    // Get session data
    const { data: sessions, error: sessionsError } = await supabase
      .from('user_sessions')
      .select(
        `
        user_id,
        duration,
        bounce,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (sessionsError) {
      logger.error('Error fetching session data for A/B test analysis', {
        error: sessionsError.message,
      });
      return;
    }

    // Get conversion data
    const { data: conversions, error: conversionsError } = await supabase
      .from('conversions')
      .select(
        `
        user_id,
        type,
        value,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (conversionsError) {
      logger.error('Error fetching conversion data for A/B test analysis', {
        error: conversionsError.message,
      });
      return;
    }

    // Process metrics by test and variant
    for (const test of activeTests) {
      // Initialize metrics for each variant
      const variantMetrics = {};

      for (const variant of test.variants) {
        variantMetrics[variant.id] = {
          apiLatency: {},
          pageLoadTime: {},
          errors: { count: 0, total: 0 },
          sessions: { duration: 0, count: 0, bounces: 0 },
          conversions: {},
        };
      }

      // Process API metrics
      for (const metric of apiMetrics) {
        const userId = metric.user_id;
        const variantId = variantAssignments[userId]?.[test.id];

        if (!variantId || !variantMetrics[variantId]) {
          continue;
        }

        // Track API latency
        if (!variantMetrics[variantId].apiLatency[metric.endpoint]) {
          variantMetrics[variantId].apiLatency[metric.endpoint] = {
            total: 0,
            count: 0,
          };
        }

        variantMetrics[variantId].apiLatency[metric.endpoint].total += metric.response_time;
        variantMetrics[variantId].apiLatency[metric.endpoint].count += 1;

        // Track errors
        const isError = metric.status_code >= 400;
        if (isError) {
          variantMetrics[variantId].errors.count += 1;
        }

        variantMetrics[variantId].errors.total += 1;
      }

      // Process page metrics
      for (const metric of pageMetrics) {
        const userId = metric.user_id;
        const variantId = variantAssignments[userId]?.[test.id];

        if (!variantId || !variantMetrics[variantId]) {
          continue;
        }

        // Track page load time
        if (!variantMetrics[variantId].pageLoadTime[metric.page]) {
          variantMetrics[variantId].pageLoadTime[metric.page] = {
            total: 0,
            count: 0,
          };
        }

        variantMetrics[variantId].pageLoadTime[metric.page].total += metric.load_time;
        variantMetrics[variantId].pageLoadTime[metric.page].count += 1;
      }

      // Process session data
      for (const session of sessions) {
        const userId = session.user_id;
        const variantId = variantAssignments[userId]?.[test.id];

        if (!variantId || !variantMetrics[variantId]) {
          continue;
        }

        // Track session duration
        variantMetrics[variantId].sessions.duration += session.duration;
        variantMetrics[variantId].sessions.count += 1;

        // Track bounces
        if (session.bounce) {
          variantMetrics[variantId].sessions.bounces += 1;
        }
      }

      // Process conversion data
      for (const conversion of conversions) {
        const userId = conversion.user_id;
        const variantId = variantAssignments[userId]?.[test.id];

        if (!variantId || !variantMetrics[variantId]) {
          continue;
        }

        // Track conversions
        if (!variantMetrics[variantId].conversions[conversion.type]) {
          variantMetrics[variantId].conversions[conversion.type] = {
            count: 0,
            value: 0,
          };
        }

        variantMetrics[variantId].conversions[conversion.type].count += 1;
        variantMetrics[variantId].conversions[conversion.type].value += conversion.value || 0;
      }

      // Update metrics
      for (const variant of test.variants) {
        const variantData = variantMetrics[variant.id];

        // Get optimization strategy for this variant
        const optimizationStrategy = test.optimization_strategy || 'unknown';

        // Update API latency metrics
        Object.entries(variantData.apiLatency).forEach(([endpoint, data]) => {
          if (data.count > 0) {
            const avgLatency = data.total / data.count;
            metrics.variantApiLatency.set(
              {
                test_id: test.id,
                variant_id: variant.id,
                endpoint,
                optimization_strategy: optimizationStrategy,
              },
              avgLatency,
            );
          }
        });

        // Update page load time metrics
        Object.entries(variantData.pageLoadTime).forEach(([page, data]) => {
          if (data.count > 0) {
            const avgLoadTime = data.total / data.count;
            metrics.variantPageLoadTime.set(
              {
                test_id: test.id,
                variant_id: variant.id,
                page,
                optimization_strategy: optimizationStrategy,
              },
              avgLoadTime,
            );
          }
        });

        // Update error rate metrics
        if (variantData.errors.total > 0) {
          const errorRate = (variantData.errors.count / variantData.errors.total) * 100;
          metrics.variantErrorRate.set(
            {
              test_id: test.id,
              variant_id: variant.id,
              error_type: 'api',
              optimization_strategy: optimizationStrategy,
            },
            errorRate,
          );
        }

        // Update session metrics
        if (variantData.sessions.count > 0) {
          // Session duration
          const avgDuration = variantData.sessions.duration / variantData.sessions.count;
          metrics.variantSessionDuration.set(
            {
              test_id: test.id,
              variant_id: variant.id,
              optimization_strategy: optimizationStrategy,
            },
            avgDuration,
          );

          // Bounce rate
          const bounceRate = (variantData.sessions.bounces / variantData.sessions.count) * 100;
          metrics.variantBounceRate.set(
            {
              test_id: test.id,
              variant_id: variant.id,
              optimization_strategy: optimizationStrategy,
            },
            bounceRate,
          );
        }

        // Update conversion metrics
        Object.entries(variantData.conversions).forEach(([type, data]) => {
          if (variantData.sessions.count > 0) {
            const conversionRate = (data.count / variantData.sessions.count) * 100;
            metrics.variantConversionRate.set(
              {
                test_id: test.id,
                variant_id: variant.id,
                conversion_type: type,
                optimization_strategy: optimizationStrategy,
              },
              conversionRate,
            );

            if (type === 'purchase' && data.count > 0) {
              const revenuePerUser = data.value / data.count;
              metrics.variantRevenuePerUser.set(
                {
                  test_id: test.id,
                  variant_id: variant.id,
                  optimization_strategy: optimizationStrategy,
                },
                revenuePerUser,
              );
            }
          }
        });
      }

      // Calculate statistical significance
      calculateStatisticalSignificance(test, variantMetrics);

      // Analyze optimization strategies
      analyzeOptimizationStrategies(test, variantMetrics);
    }

    logger.info('A/B test performance analysis completed');
  } catch (error) {
    logger.error('Error in analyzeTestPerformance', { error: error.message });
  }
}

/**
 * Calculate statistical significance of test results
 *
 * @param {Object} test - Test object
 * @param {Object} variantMetrics - Variant metrics
 */
function calculateStatisticalSignificance(test, variantMetrics) {
  try {
    // Find control variant
    const controlVariant = test.variants.find(v => v.name.toLowerCase() === 'control');

    if (!controlVariant) {
      logger.debug(`No control variant found for test ${test.id}`);
      return;
    }

    const controlData = variantMetrics[controlVariant.id];

    // Calculate significance for each variant
    for (const variant of test.variants) {
      if (variant.id === controlVariant.id) {
        continue;
      }

      const variantData = variantMetrics[variant.id];

      // Calculate conversion rate significance
      Object.entries(variantData.conversions).forEach(([type, data]) => {
        if (
          !controlData.conversions[type] ||
          controlData.sessions.count === 0 ||
          variantData.sessions.count === 0
        ) {
          return;
        }

        const controlRate = controlData.conversions[type].count / controlData.sessions.count;
        const variantRate = data.count / variantData.sessions.count;

        // Simple z-test for proportions
        const pooledProportion =
          (controlData.conversions[type].count + data.count) /
          (controlData.sessions.count + variantData.sessions.count);

        const standardError = Math.sqrt(
          pooledProportion *
            (1 - pooledProportion) *
            (1 / controlData.sessions.count + 1 / variantData.sessions.count),
        );

        if (standardError === 0) {
          return;
        }

        const zScore = Math.abs((variantRate - controlRate) / standardError);

        // Convert z-score to p-value (simplified)
        const pValue = 1 - Math.min(0.9999, Math.max(0.0001, Math.erf(zScore / Math.sqrt(2))));

        // Convert p-value to confidence level
        const confidenceLevel = (1 - pValue) * 100;

        metrics.variantSignificance.set(
          {
            test_id: test.id,
            variant_id: variant.id,
            metric: `conversion_${type}`,
            optimization_strategy: test.optimization_strategy || 'unknown',
          },
          confidenceLevel,
        );
      });

      // Calculate bounce rate significance
      if (controlData.sessions.count > 0 && variantData.sessions.count > 0) {
        const controlRate = controlData.sessions.bounces / controlData.sessions.count;
        const variantRate = variantData.sessions.bounces / variantData.sessions.count;

        // Simple z-test for proportions
        const pooledProportion =
          (controlData.sessions.bounces + variantData.sessions.bounces) /
          (controlData.sessions.count + variantData.sessions.count);

        const standardError = Math.sqrt(
          pooledProportion *
            (1 - pooledProportion) *
            (1 / controlData.sessions.count + 1 / variantData.sessions.count),
        );

        if (standardError === 0) {
          return;
        }

        const zScore = Math.abs((variantRate - controlRate) / standardError);

        // Convert z-score to p-value (simplified)
        const pValue = 1 - Math.min(0.9999, Math.max(0.0001, Math.erf(zScore / Math.sqrt(2))));

        // Convert p-value to confidence level
        const confidenceLevel = (1 - pValue) * 100;

        metrics.variantSignificance.set(
          {
            test_id: test.id,
            variant_id: variant.id,
            metric: 'bounce_rate',
            optimization_strategy: test.optimization_strategy || 'unknown',
          },
          confidenceLevel,
        );
      }
    }
  } catch (error) {
    logger.error('Error calculating statistical significance', { error: error.message });
  }
}

// Metrics endpoint
app.get('/metrics', async (_req, res) => {
  try {
    // Collect latest metrics before responding
    await analyzeTestPerformance();

    // Return metrics in Prometheus format
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving A/B test metrics', { error: error.message });
    res.status(500).send('Error collecting A/B test metrics');
  }
});

// Start server
const PORT = process.env.AB_TEST_MONITOR_PORT || 9097;
app.listen(PORT, () => {
  logger.info(`A/B Test Monitor listening on port ${PORT}`);
});

// Schedule regular updates
const UPDATE_INTERVAL_MS = 15 * 60 * 1000; // 15 minutes
setInterval(analyzeTestPerformance, UPDATE_INTERVAL_MS);

// Initial data collection
analyzeTestPerformance().catch(error => {
  logger.error('Error in initial A/B test performance analysis', { error: error.message });
});

/**
 * Analyze optimization strategies
 *
 * @param {Object} test - Test object
 * @param {Object} variantMetrics - Variant metrics
 */
function analyzeOptimizationStrategies(test, variantMetrics) {
  try {
    // Skip if no optimization strategy is defined
    if (!test.optimization_strategy) {
      return;
    }

    const optimizationStrategy = test.optimization_strategy;

    // Find control variant
    const controlVariant = test.variants.find(v => v.name.toLowerCase() === 'control');

    if (!controlVariant) {
      logger.debug(`No control variant found for test ${test.id}`);
      return;
    }

    const controlData = variantMetrics[controlVariant.id];

    // Calculate performance improvements
    let totalApiLatencyImprovement = 0;
    let apiLatencyCount = 0;

    // API latency improvements
    Object.entries(controlData.apiLatency).forEach(([endpoint, controlEndpointData]) => {
      if (controlEndpointData.count === 0) return;

      const controlAvgLatency = controlEndpointData.total / controlEndpointData.count;

      // Compare with other variants
      test.variants.forEach(variant => {
        if (variant.id === controlVariant.id) return;

        const variantData = variantMetrics[variant.id];
        const variantEndpointData = variantData.apiLatency[endpoint];

        if (!variantEndpointData || variantEndpointData.count === 0) return;

        const variantAvgLatency = variantEndpointData.total / variantEndpointData.count;

        // Calculate improvement percentage (negative means improvement)
        const improvementPercent =
          ((variantAvgLatency - controlAvgLatency) / controlAvgLatency) * 100 * -1;

        // Record specific endpoint improvement
        metrics.strategyPerformanceImprovement.set(
          {
            test_id: test.id,
            optimization_strategy: optimizationStrategy,
            metric: `api_latency_${endpoint}`,
          },
          improvementPercent,
        );

        // Add to total for average calculation
        totalApiLatencyImprovement += improvementPercent;
        apiLatencyCount++;
      });
    });

    // Calculate average API latency improvement
    if (apiLatencyCount > 0) {
      const avgApiLatencyImprovement = totalApiLatencyImprovement / apiLatencyCount;
      metrics.strategyPerformanceImprovement.set(
        {
          test_id: test.id,
          optimization_strategy: optimizationStrategy,
          metric: 'api_latency_avg',
        },
        avgApiLatencyImprovement,
      );
    }

    // Page load time improvements
    let totalPageLoadImprovement = 0;
    let pageLoadCount = 0;

    Object.entries(controlData.pageLoadTime).forEach(([page, controlPageData]) => {
      if (controlPageData.count === 0) return;

      const controlAvgLoadTime = controlPageData.total / controlPageData.count;

      // Compare with other variants
      test.variants.forEach(variant => {
        if (variant.id === controlVariant.id) return;

        const variantData = variantMetrics[variant.id];
        const variantPageData = variantData.pageLoadTime[page];

        if (!variantPageData || variantPageData.count === 0) return;

        const variantAvgLoadTime = variantPageData.total / variantPageData.count;

        // Calculate improvement percentage (negative means improvement)
        const improvementPercent =
          ((variantAvgLoadTime - controlAvgLoadTime) / controlAvgLoadTime) * 100 * -1;

        // Record specific page improvement
        metrics.strategyPerformanceImprovement.set(
          {
            test_id: test.id,
            optimization_strategy: optimizationStrategy,
            metric: `page_load_${page}`,
          },
          improvementPercent,
        );

        // Add to total for average calculation
        totalPageLoadImprovement += improvementPercent;
        pageLoadCount++;
      });
    });

    // Calculate average page load time improvement
    if (pageLoadCount > 0) {
      const avgPageLoadImprovement = totalPageLoadImprovement / pageLoadCount;
      metrics.strategyPerformanceImprovement.set(
        { test_id: test.id, optimization_strategy: optimizationStrategy, metric: 'page_load_avg' },
        avgPageLoadImprovement,
      );
    }

    // Engagement improvements (session duration, bounce rate)
    test.variants.forEach(variant => {
      if (variant.id === controlVariant.id) return;

      const variantData = variantMetrics[variant.id];

      // Session duration improvement
      if (controlData.sessions.count > 0 && variantData.sessions.count > 0) {
        const controlAvgDuration = controlData.sessions.duration / controlData.sessions.count;
        const variantAvgDuration = variantData.sessions.duration / variantData.sessions.count;

        // Calculate improvement percentage (positive means improvement for duration)
        const durationImprovementPercent =
          ((variantAvgDuration - controlAvgDuration) / controlAvgDuration) * 100;

        metrics.strategyEngagementImprovement.set(
          {
            test_id: test.id,
            optimization_strategy: optimizationStrategy,
            metric: 'session_duration',
          },
          durationImprovementPercent,
        );

        // Bounce rate improvement
        const controlBounceRate = controlData.sessions.bounces / controlData.sessions.count;
        const variantBounceRate = variantData.sessions.bounces / variantData.sessions.count;

        // Calculate improvement percentage (negative means improvement for bounce rate)
        const bounceImprovementPercent =
          ((variantBounceRate - controlBounceRate) / controlBounceRate) * 100 * -1;

        metrics.strategyEngagementImprovement.set(
          { test_id: test.id, optimization_strategy: optimizationStrategy, metric: 'bounce_rate' },
          bounceImprovementPercent,
        );
      }
    });

    // Conversion improvements
    Object.entries(controlData.conversions).forEach(([type, controlConvData]) => {
      if (controlData.sessions.count === 0 || controlConvData.count === 0) return;

      const controlConvRate = controlConvData.count / controlData.sessions.count;

      // Compare with other variants
      test.variants.forEach(variant => {
        if (variant.id === controlVariant.id) return;

        const variantData = variantMetrics[variant.id];
        const variantConvData = variantData.conversions[type];

        if (!variantConvData || variantData.sessions.count === 0) return;

        const variantConvRate = variantConvData.count / variantData.sessions.count;

        // Calculate improvement percentage (positive means improvement)
        const improvementPercent = ((variantConvRate - controlConvRate) / controlConvRate) * 100;

        metrics.strategyConversionImprovement.set(
          { test_id: test.id, optimization_strategy: optimizationStrategy, conversion_type: type },
          improvementPercent,
        );
      });
    });

    // Calculate overall strategy score (weighted average of improvements)
    const weights = {
      performance: 0.3, // 30% weight for performance metrics
      engagement: 0.3, // 30% weight for engagement metrics
      conversion: 0.4, // 40% weight for conversion metrics
    };

    let performanceScore = 0;
    let engagementScore = 0;
    let conversionScore = 0;

    // Get performance metrics
    const performanceMetrics = register.getMetricsAsJSON();

    for (const metric of performanceMetrics) {
      if (metric.name === 'mvs_vr_ab_strategy_performance_improvement_percent') {
        let totalImprovement = 0;
        let count = 0;

        metric.values.forEach(value => {
          if (
            value.labels.test_id === test.id &&
            value.labels.optimization_strategy === optimizationStrategy
          ) {
            totalImprovement += value.value;
            count++;
          }
        });

        if (count > 0) {
          performanceScore = Math.min(100, Math.max(0, 50 + totalImprovement / count));
        }
      } else if (metric.name === 'mvs_vr_ab_strategy_engagement_improvement_percent') {
        let totalImprovement = 0;
        let count = 0;

        metric.values.forEach(value => {
          if (
            value.labels.test_id === test.id &&
            value.labels.optimization_strategy === optimizationStrategy
          ) {
            totalImprovement += value.value;
            count++;
          }
        });

        if (count > 0) {
          engagementScore = Math.min(100, Math.max(0, 50 + totalImprovement / count));
        }
      } else if (metric.name === 'mvs_vr_ab_strategy_conversion_improvement_percent') {
        let totalImprovement = 0;
        let count = 0;

        metric.values.forEach(value => {
          if (
            value.labels.test_id === test.id &&
            value.labels.optimization_strategy === optimizationStrategy
          ) {
            totalImprovement += value.value;
            count++;
          }
        });

        if (count > 0) {
          conversionScore = Math.min(100, Math.max(0, 50 + totalImprovement / count));
        }
      }
    }

    // Calculate weighted score
    const overallScore =
      performanceScore * weights.performance +
      engagementScore * weights.engagement +
      conversionScore * weights.conversion;

    // Record overall strategy score
    metrics.strategyOverallScore.set(
      { test_id: test.id, optimization_strategy: optimizationStrategy },
      overallScore,
    );

    logger.info('Optimization strategy analysis completed', {
      test_id: test.id,
      strategy: optimizationStrategy,
      score: overallScore,
    });
  } catch (error) {
    logger.error('Error analyzing optimization strategies', { error: error.message });
  }
}

module.exports = {
  analyzeTestPerformance,
  getActiveTests,
  analyzeOptimizationStrategies,
};
