/**
 * Migration to create API key usage table
 */

exports.up = function(knex) {
  return knex.schema
    .createTable('api_key_usage', table => {
      table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      table.uuid('api_key_id').notNullable().references('id').inTable('api_keys').onDelete('CASCADE');
      table.string('endpoint').notNullable();
      table.string('method', 10).notNullable();
      table.string('ip_address').notNullable();
      table.integer('status_code').notNullable();
      table.boolean('is_rotated_key').notNullable().defaultTo(false);
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
      
      // Indexes
      table.index('api_key_id');
      table.index('created_at');
      table.index(['api_key_id', 'created_at']);
    });
};

exports.down = function(knex) {
  return knex.schema
    .dropTableIfExists('api_key_usage');
};
