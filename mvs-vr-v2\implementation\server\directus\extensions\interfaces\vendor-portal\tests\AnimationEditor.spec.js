import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AnimationEditor from '../src/components/VisualEditors/AnimationEditor.vue';

// Mock API
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

describe('AnimationEditor.vue', () => {
  let wrapper;
  
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock API responses
    mockApi.get.mockImplementation((url) => {
      if (url.includes('/items/animations')) {
        return Promise.resolve({
          data: {
            data: [
              {
                id: 'anim1',
                name: 'Test Animation',
                duration: 5,
                tracks: [
                  {
                    id: 'track1',
                    name: 'Position',
                    type: 'transform',
                    keyframes: [
                      { id: 'kf1', time: 0, value: { x: 0, y: 0, z: 0 } },
                      { id: 'kf2', time: 2.5, value: { x: 10, y: 5, z: 0 } },
                      { id: 'kf3', time: 5, value: { x: 0, y: 0, z: 0 } }
                    ]
                  }
                ]
              }
            ]
          }
        });
      }
      return Promise.resolve({ data: { data: [] } });
    });
    
    // Create wrapper
    wrapper = mount(AnimationEditor, {
      props: {
        vendorId: 'vendor1',
        animationId: null
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  it('renders the component correctly', () => {
    expect(wrapper.find('.animation-editor').exists()).toBe(true);
    expect(wrapper.find('.editor-title').text()).toBe('Animation Editor');
  });

  it('loads animations on mount', async () => {
    await wrapper.vm.$nextTick();
    expect(mockApi.get).toHaveBeenCalledWith(expect.stringContaining('/items/animations'));
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.animations.length).toBeGreaterThan(0);
  });

  it('creates a new animation', async () => {
    // Setup mock for post
    mockApi.post.mockResolvedValue({
      data: {
        data: {
          id: 'new-anim',
          name: 'New Animation',
          duration: 3,
          tracks: []
        }
      }
    });
    
    // Call the create method
    await wrapper.vm.createAnimation();
    
    // Check if API was called
    expect(mockApi.post).toHaveBeenCalledWith(
      '/items/animations',
      expect.objectContaining({
        vendor_id: 'vendor1',
        name: expect.any(String),
        duration: expect.any(Number)
      })
    );
    
    // Check if the new animation was added to the list
    expect(wrapper.vm.animations.some(a => a.id === 'new-anim')).toBe(true);
  });

  it('selects an animation when clicked', async () => {
    // Wait for animations to load
    await wrapper.vm.$nextTick();
    
    // Set animations manually to ensure they exist
    wrapper.vm.animations = [
      {
        id: 'anim1',
        name: 'Test Animation',
        duration: 5,
        tracks: []
      }
    ];
    
    // Select animation
    await wrapper.vm.selectAnimation('anim1');
    
    // Check if the animation is selected
    expect(wrapper.vm.selectedAnimationId).toBe('anim1');
  });

  it('adds a new track to the selected animation', async () => {
    // Wait for animations to load
    await wrapper.vm.$nextTick();
    
    // Set animations and select one
    wrapper.vm.animations = [
      {
        id: 'anim1',
        name: 'Test Animation',
        duration: 5,
        tracks: []
      }
    ];
    await wrapper.vm.selectAnimation('anim1');
    
    // Add a new track
    await wrapper.vm.addTrack();
    
    // Check if a track was added
    expect(wrapper.vm.selectedAnimation.tracks.length).toBe(1);
    expect(wrapper.vm.selectedAnimation.tracks[0].name).toBe('New Track');
  });

  it('saves animations correctly', async () => {
    // Setup mock for patch
    mockApi.patch.mockResolvedValue({
      data: {
        data: {
          id: 'anim1',
          name: 'Updated Animation',
          duration: 5,
          tracks: []
        }
      }
    });
    
    // Set animations and select one
    wrapper.vm.animations = [
      {
        id: 'anim1',
        name: 'Test Animation',
        duration: 5,
        tracks: []
      }
    ];
    await wrapper.vm.selectAnimation('anim1');
    
    // Update animation name
    wrapper.vm.selectedAnimation.name = 'Updated Animation';
    
    // Save animations
    await wrapper.vm.saveAnimations();
    
    // Check if API was called
    expect(mockApi.patch).toHaveBeenCalledWith(
      '/items/animations/anim1',
      expect.objectContaining({
        name: 'Updated Animation'
      })
    );
  });

  it('handles animation playback controls', async () => {
    // Set animations and select one
    wrapper.vm.animations = [
      {
        id: 'anim1',
        name: 'Test Animation',
        duration: 5,
        tracks: [
          {
            id: 'track1',
            name: 'Position',
            type: 'transform',
            keyframes: [
              { id: 'kf1', time: 0, value: { x: 0, y: 0, z: 0 } },
              { id: 'kf2', time: 5, value: { x: 10, y: 0, z: 0 } }
            ]
          }
        ]
      }
    ];
    await wrapper.vm.selectAnimation('anim1');
    
    // Start playback
    wrapper.vm.playAnimation();
    expect(wrapper.vm.isPlaying).toBe(true);
    
    // Stop playback
    wrapper.vm.stopAnimation();
    expect(wrapper.vm.isPlaying).toBe(false);
    
    // Set current time
    wrapper.vm.setCurrentTime(2.5);
    expect(wrapper.vm.currentTime).toBe(2.5);
  });
});
