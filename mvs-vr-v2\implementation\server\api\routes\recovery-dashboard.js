/**
 * Recovery Dashboard API Routes
 *
 * This file defines the API routes for the recovery dashboard.
 */

const express = require('express');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const existsAsync = promisify(fs.exists);
const { runOrchestratedRecovery } = require('../../scripts/recovery/recovery-orchestrator');
const {
  generateDependencyVisualization,
  generateRecoveryVisualizationFromLog,
} = require('../../scripts/recovery/recovery-visualizer');
const logger = require('../../utils/logger').getLogger('recovery-dashboard');

const router = express.Router();

// Configuration
const config = {
  recoveryLogPath: path.join(__dirname, '../../logs/recovery-orchestration.json'),
  visualizationDir: path.join(__dirname, '../../public/recovery'),
};

/**
 * Load recovery log
 * @returns {Promise<Object>} Recovery log
 */
async function loadRecoveryLog() {
  try {
    if (await existsAsync(config.recoveryLogPath)) {
      const data = await readFileAsync(config.recoveryLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    logger.error('Error loading recovery log:', error);
  }

  return {
    lastRun: null,
    recoveries: [],
  };
}

/**
 * Get recovery dashboard data
 */
router.get('/', async (req, res) => {
  try {
    const log = await loadRecoveryLog();

    // Get latest recovery
    const latestRecovery =
      log.recoveries.length > 0 ? log.recoveries[log.recoveries.length - 1] : null;

    // Get recovery statistics
    const totalRecoveries = log.recoveries.length;
    const successfulRecoveries = log.recoveries.filter(r => r.success).length;
    const failedRecoveries = totalRecoveries - successfulRecoveries;
    const successRate = totalRecoveries > 0 ? (successfulRecoveries / totalRecoveries) * 100 : 0;

    // Get component statistics
    const componentStats = {};

    for (const recovery of log.recoveries) {
      for (const [id, component] of Object.entries(recovery.components)) {
        if (!componentStats[id]) {
          componentStats[id] = {
            id,
            name: component.name,
            totalRecoveries: 0,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            averageDuration: 0,
            totalDuration: 0,
          };
        }

        componentStats[id].totalRecoveries++;

        if (component.success) {
          componentStats[id].successfulRecoveries++;
        } else {
          componentStats[id].failedRecoveries++;
        }

        if (component.duration) {
          componentStats[id].totalDuration += component.duration;
          componentStats[id].averageDuration =
            componentStats[id].totalDuration / componentStats[id].totalRecoveries;
        }
      }
    }

    // Get recent recoveries (last 10)
    const recentRecoveries = log.recoveries
      .slice(-10)
      .reverse()
      .map(recovery => ({
        startTime: recovery.startTime,
        endTime: recovery.endTime,
        duration: recovery.duration,
        success: recovery.success,
        componentCount: Object.keys(recovery.components).length,
        successfulComponents: Object.values(recovery.components).filter(c => c.success).length,
      }));

    res.json({
      lastRun: log.lastRun,
      latestRecovery,
      statistics: {
        totalRecoveries,
        successfulRecoveries,
        failedRecoveries,
        successRate,
      },
      componentStats: Object.values(componentStats),
      recentRecoveries,
    });
  } catch (error) {
    logger.error('Error getting recovery dashboard data:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get recovery history
 */
router.get('/history', async (req, res) => {
  try {
    const log = await loadRecoveryLog();

    // Get all recoveries with basic info
    const recoveries = log.recoveries
      .map(recovery => ({
        startTime: recovery.startTime,
        endTime: recovery.endTime,
        duration: recovery.duration,
        success: recovery.success,
        componentCount: Object.keys(recovery.components).length,
        successfulComponents: Object.values(recovery.components).filter(c => c.success).length,
      }))
      .reverse();

    res.json(recoveries);
  } catch (error) {
    logger.error('Error getting recovery history:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get recovery details
 */
router.get('/details/:timestamp', async (req, res) => {
  try {
    const { timestamp } = req.params;
    const log = await loadRecoveryLog();

    // Find recovery by timestamp
    const recovery = log.recoveries.find(r => r.startTime === timestamp);

    if (!recovery) {
      return res.status(404).json({ error: 'Recovery not found' });
    }

    res.json(recovery);
  } catch (error) {
    logger.error('Error getting recovery details:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get dependency graph visualization
 */
router.get('/visualization/dependency', async (req, res) => {
  try {
    const visualizationPath = await generateDependencyVisualization();
    const relativePath = path.relative(path.join(__dirname, '../../public'), visualizationPath);

    res.json({
      visualizationUrl: `/recovery/${path.basename(visualizationPath)}`,
    });
  } catch (error) {
    logger.error('Error generating dependency visualization:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get recovery process visualization
 */
router.get('/visualization/recovery/:timestamp?', async (req, res) => {
  try {
    const { timestamp } = req.params;
    const visualizationPath = await generateRecoveryVisualizationFromLog(timestamp);
    const relativePath = path.relative(path.join(__dirname, '../../public'), visualizationPath);

    res.json({
      visualizationUrl: `/recovery/${path.basename(visualizationPath)}`,
    });
  } catch (error) {
    logger.error('Error generating recovery visualization:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Start recovery process
 */
router.post('/start', async (req, res) => {
  try {
    const { components, test } = req.body;

    // Start recovery in background
    const recoveryPromise = runOrchestratedRecovery({
      components,
      test: test === true,
    });

    // Return immediately with status
    res.json({
      status: 'started',
      message: 'Recovery process started',
      components: components || 'all',
      test: test === true,
    });

    // Log when recovery completes
    recoveryPromise
      .then(results => {
        logger.info(
          `Recovery process completed with status: ${results.success ? 'SUCCESS' : 'FAILURE'}`,
        );
      })
      .catch(error => {
        logger.error('Error during recovery process:', error);
      });
  } catch (error) {
    logger.error('Error starting recovery process:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get recovery status
 */
router.get('/status', async (req, res) => {
  try {
    const log = await loadRecoveryLog();

    // Get latest recovery
    const latestRecovery =
      log.recoveries.length > 0 ? log.recoveries[log.recoveries.length - 1] : null;

    // Check if recovery is in progress
    const inProgress = latestRecovery && !latestRecovery.endTime;

    res.json({
      inProgress,
      lastRun: log.lastRun,
      latestRecovery: latestRecovery
        ? {
            startTime: latestRecovery.startTime,
            endTime: latestRecovery.endTime,
            duration: latestRecovery.duration,
            success: latestRecovery.success,
            componentCount: Object.keys(latestRecovery.components).length,
            successfulComponents: Object.values(latestRecovery.components).filter(c => c.success)
              .length,
            components: Object.entries(latestRecovery.components).map(([id, component]) => ({
              id,
              name: component.name,
              success: component.success,
              duration: component.duration,
              retries: component.retries,
            })),
          }
        : null,
    });
  } catch (error) {
    logger.error('Error getting recovery status:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
