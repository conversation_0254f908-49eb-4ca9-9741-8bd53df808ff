/**
 * Simple Vitest Test
 * 
 * This is a basic test file to verify Vitest setup
 */

import { describe, it, expect, vi } from 'vitest';

describe('Simple Vitest Test', () => {
  it('should pass a basic test', () => {
    expect(1 + 1).toBe(2);
  });

  it('should mock a function', () => {
    const mockFn = vi.fn().mockReturnValue(42);
    expect(mockFn()).toBe(42);
    expect(mockFn).toHaveBeenCalled();
  });
});
