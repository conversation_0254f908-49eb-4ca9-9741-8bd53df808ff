{"extends": "./tsconfig.base.json", "compilerOptions": {"composite": true, "module": "ESNext", "moduleResolution": "NodeNext", "allowSyntheticDefaultImports": true, "types": ["node"], "lib": ["ESNext"], "paths": {"node:*": ["./node_modules/@types/*"]}}, "include": ["vite.config.*", "vitest.config.*", "playwright.config.*", "package.json", "deno.json", "scripts/**/*.ts", "tests/setup/**/*.ts"], "exclude": ["node_modules", ".deno", "dist", "coverage", "**/*.spec.ts", "**/*.test.ts"]}