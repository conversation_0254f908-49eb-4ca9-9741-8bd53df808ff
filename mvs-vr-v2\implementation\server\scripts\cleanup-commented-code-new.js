/**
 * Cleanup Commented Code Script
 *
 * This script finds and removes commented-out code.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const rootDir = path.resolve(__dirname, '..');
const fileExtensions = ['.js', '.ts', '.jsx', '.tsx'];
const excludeDirs = ['node_modules', 'dist', 'build', '.next', 'tests', '__tests__'];
const excludeFiles = ['cleanup-commented-code.js', 'cleanup-commented-code-new.js'];

// Simple logger
const customLogger = {
  info: message => console.log(message),
  error: message => console.error(message),
  warn: message => console.warn(message),
};

// Function to check if a file should be processed
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  const relativePath = path.relative(rootDir, filePath);

  // Check file extension
  if (!fileExtensions.includes(ext)) {
    return false;
  }

  // Check excluded directories
  if (excludeDirs.some(dir => relativePath.includes(dir))) {
    return false;
  }

  // Check excluded files
  if (excludeFiles.some(file => relativePath.endsWith(file))) {
    return false;
  }

  return true;
}

// Function to find all files recursively
function findFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);

  for (const file of list) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Skip excluded directories
      if (!excludeDirs.some(excludeDir => file === excludeDir)) {
        results = results.concat(findFiles(filePath));
      }
    } else if (shouldProcessFile(filePath)) {
      results.push(filePath);
    }
  }

  return results;
}

// Function to process a file
function processFile(filePath) {
  customLogger.info(`Processing ${path.relative(rootDir, filePath)}`);

  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;

  // Check if file contains commented-out code
  const commentedCodeRegex = /^\s*\/\/\s*[a-zA-Z0-9_$]+.*[;{}]/gm;
  if (!commentedCodeRegex.test(content)) {
    return false;
  }

  // Reset regex
  commentedCodeRegex.lastIndex = 0;

  // Find all commented-out code
  const matches = content.match(commentedCodeRegex) || [];

  // Skip comments that are not code
  const validCommentedCode = matches.filter(match => {
    // Skip comments that are just descriptions or TODOs
    if (
      match.includes('TODO:') ||
      match.includes('FIXME:') ||
      match.includes('NOTE:') ||
      match.includes('Description:') ||
      match.includes('Params:') ||
      match.includes('Returns:') ||
      match.includes('Example:')
    ) {
      return false;
    }

    // Skip comments that don't look like code
    if (
      !match.includes('=') &&
      !match.includes('(') &&
      !match.includes(')') &&
      !match.includes('{') &&
      !match.includes('}') &&
      !match.includes(';')
    ) {
      return false;
    }

    return true;
  });

  if (validCommentedCode.length === 0) {
    return false;
  }

  customLogger.info(
    `Found ${validCommentedCode.length} commented-out code blocks in ${path.relative(rootDir, filePath)}`,
  );

  // Remove commented-out code
  for (const commentedCode of validCommentedCode) {
    content = content.replace(commentedCode, '');
  }

  // Clean up empty lines
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

  // Write the updated content back to the file if changes were made
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }

  return false;
}

// Main function
function main() {
  customLogger.info('Finding files...');
  const files = findFiles(rootDir);
  customLogger.info(`Found ${files.length} files to process.`);

  let changedFiles = 0;

  for (const file of files) {
    if (processFile(file)) {
      changedFiles++;
    }
  }

  customLogger.info(`Processed ${files.length} files, updated ${changedFiles} files.`);
}

// Run the script
main();
