/**
 * Recovery Time Objective (RTO) Definitions
 * 
 * This file defines the Recovery Time Objectives (RTOs) for all system components.
 * RTOs represent the maximum acceptable time for recovery after a failure.
 */

const rtoDefinitions = {
  // Database components
  database: {
    main: {
      rto: 15, // minutes
      description: 'Main application database',
      priority: 'critical',
      dependencies: [],
      justification: 'Core database containing all application data. Critical for all operations.'
    },
    analytics: {
      rto: 30, // minutes
      description: 'Analytics database',
      priority: 'high',
      dependencies: ['database.main'],
      justification: 'Required for analytics and reporting. Not critical for core operations.'
    },
    cache: {
      rto: 10, // minutes
      description: 'Redis cache',
      priority: 'high',
      dependencies: [],
      justification: 'Required for optimal performance. System can operate with degraded performance without it.'
    }
  },

  // Application components
  application: {
    api: {
      rto: 20, // minutes
      description: 'API services',
      priority: 'critical',
      dependencies: ['database.main', 'database.cache'],
      justification: 'Core API services required for all client operations.'
    },
    admin: {
      rto: 30, // minutes
      description: 'Admin portal',
      priority: 'high',
      dependencies: ['application.api', 'database.main'],
      justification: 'Required for administrative operations. Not critical for end-user operations.'
    },
    vendor: {
      rto: 25, // minutes
      description: 'Vendor portal',
      priority: 'critical',
      dependencies: ['application.api', 'database.main'],
      justification: 'Required for vendor operations. Critical for business continuity.'
    },
    worker: {
      rto: 40, // minutes
      description: 'Background workers',
      priority: 'medium',
      dependencies: ['database.main', 'storage.assets'],
      justification: 'Required for asynchronous processing. System can operate with queued tasks.'
    }
  },

  // Infrastructure components
  infrastructure: {
    loadBalancer: {
      rto: 15, // minutes
      description: 'Load balancer',
      priority: 'critical',
      dependencies: [],
      justification: 'Required for routing traffic to application instances.'
    },
    cdn: {
      rto: 20, // minutes
      description: 'Content Delivery Network',
      priority: 'high',
      dependencies: [],
      justification: 'Required for optimal asset delivery. System can operate with direct asset delivery.'
    },
    monitoring: {
      rto: 45, // minutes
      description: 'Monitoring infrastructure',
      priority: 'medium',
      dependencies: [],
      justification: 'Required for system visibility. Not critical for core operations.'
    }
  },

  // Storage components
  storage: {
    assets: {
      rto: 30, // minutes
      description: 'Asset storage',
      priority: 'high',
      dependencies: [],
      justification: 'Required for asset delivery. Critical for showroom operations.'
    },
    backups: {
      rto: 60, // minutes
      description: 'Backup storage',
      priority: 'medium',
      dependencies: [],
      justification: 'Required for recovery operations. Not critical for normal operations.'
    }
  },

  // Complete system
  system: {
    complete: {
      rto: 60, // minutes
      description: 'Complete system',
      priority: 'critical',
      dependencies: [
        'database.main',
        'database.cache',
        'application.api',
        'application.vendor',
        'infrastructure.loadBalancer',
        'storage.assets'
      ],
      justification: 'Complete system recovery with all critical components.'
    }
  }
};

module.exports = rtoDefinitions;
