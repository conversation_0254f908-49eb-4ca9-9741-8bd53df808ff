FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/business-impact-predictor.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV BUSINESS_IMPACT_PREDICTOR_PORT=9111

# Expose port
EXPOSE 9111

# Start the service
CMD ["node", "monitoring/business-impact-predictor.js"]
