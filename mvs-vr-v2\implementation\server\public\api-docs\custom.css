/**
 * Custom styles for API documentation
 */

/* General styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  color: #333;
}

/* Header styles */
.api-docs-header {
  background-color: #1a202c;
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.api-docs-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.api-docs-header .links {
  display: flex;
  gap: 1rem;
}

.api-docs-header .links a {
  color: white;
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.api-docs-header .links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Stoplight Elements customization */
elements-api {
  --theme-color: #4a5568;
  --theme-color-dark: #2d3748;
  --theme-color-light: #e2e8f0;
  --theme-text: #1a202c;
  --theme-border: #e2e8f0;
  --theme-background: #f7fafc;
}

/* Code snippets */
pre {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  border: 1px solid #e2e8f0;
}

code {
  font-family: 'Fira Code', 'Courier New', Courier, monospace;
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .api-docs-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .api-docs-header .links {
    flex-wrap: wrap;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a202c;
    color: #e2e8f0;
  }
  
  pre {
    background-color: #2d3748;
    border-color: #4a5568;
  }
  
  elements-api {
    --theme-color: #4a5568;
    --theme-color-dark: #2d3748;
    --theme-color-light: #4a5568;
    --theme-text: #e2e8f0;
    --theme-border: #4a5568;
    --theme-background: #1a202c;
  }
}
