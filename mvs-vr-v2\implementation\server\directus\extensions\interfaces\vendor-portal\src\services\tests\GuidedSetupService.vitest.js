/**
 * Vitest tests for GuidedSetupService
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios from 'axios';
import GuidedSetupService from './GuidedSetupService.mock.js';

// Mock axios is already set up in vitest.setup.js

describe('GuidedSetupService', () => {
  let service;
  let mockAxios;

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();

    // Mock axios.create to return our mockAxios
    mockAxios = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
    };

    // Mock the axios.create function to return our mockAxios
    vi.mocked(axios.create).mockReturnValue(mockAxios);

    // Create a new instance of the service
    service = new GuidedSetupService();
  });

  describe('constructor', () => {
    it('should create an axios instance with the correct configuration', () => {
      // Assert
      expect(axios.create).toHaveBeenCalledWith({
        baseURL: globalThis.directus.url,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${globalThis.directus.auth.token}`,
        },
      });
    });
  });

  describe('getOnboardingStatus', () => {
    it('should return onboarding status when it exists', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              vendor_id: vendorId,
              is_completed: false,
              progress_data: '{"step1":true,"step2":false}',
            },
          ],
        },
      };

      // Setup the mock to return our response
      mockAxios.get.mockImplementation(() => Promise.resolve(mockResponse));

      // Act
      const result = await service.getOnboardingStatus(vendorId);

      // Assert
      expect(mockAxios.get).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      expect(result).toEqual(mockResponse.data.data[0]);
    });

    it('should return null when onboarding status does not exist', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockResponse = {
        data: {
          data: [],
        },
      };

      // Setup the mock to return our response
      mockAxios.get.mockImplementation(() => Promise.resolve(mockResponse));

      // Act
      const result = await service.getOnboardingStatus(vendorId);

      // Assert
      expect(mockAxios.get).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      expect(result).toBeNull();
    });

    it('should throw an error when the API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockError = new Error('API error');

      // Setup the mock to reject with our error
      mockAxios.get.mockImplementation(() => Promise.reject(mockError));

      // Act & Assert
      await expect(service.getOnboardingStatus(vendorId)).rejects.toThrow('API error');
      expect(mockAxios.get).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
    });
  });

  // Additional tests for other methods would follow the same pattern
});
