/**
 * Vitest tests for GuidedSetupService
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import GuidedSetupService from '../GuidedSetupService.js'; // Use the real service

// Mock axios is already set up in vitest.setup.js
// Remove the local axios mock setup

describe('GuidedSetupService', () => {
  let service;
  // Remove mockAxios as it's handled in setupFiles
  // let mockAxios;

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();

    // Create a new instance of the service
    service = new GuidedSetupService();
  });

  // No need to test constructor if axios mock is in setupFiles
  // describe('constructor', () => {
  //   it('should create an axios instance with the correct configuration', () => {
  //     // Assert
  //     expect(axios.create).toHaveBeenCalledWith({
  //       baseURL: globalThis.directus.url,
  //       headers: {
  //         'Content-Type': 'application/json',
  //         Authorization: `Bearer ${globalThis.directus.auth.token}`,
  //       },
  //     });
  //   });
  // });

  describe('getOnboardingStatus', () => {
    it('should return onboarding status when it exists', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              vendor_id: vendorId,
              is_completed: false,
              progress_data: '{"step1":true,"step2":false}',
            },
          ],
        },
      };

      // Setup the mock to return our response
      vi.mocked(axios.create().get).mockImplementation(() => Promise.resolve(mockResponse));

      // Act
      const result = await service.getOnboardingStatus(vendorId);

      // Assert
      expect(vi.mocked(axios.create().get)).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      expect(result).toEqual(mockResponse.data.data[0]);
    });

    it('should return null when onboarding status does not exist', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockResponse = {
        data: {
          data: [],
        },
      };

      // Setup the mock to return our response
      vi.mocked(axios.create().get).mockImplementation(() => Promise.resolve(mockResponse));

      // Act
      const result = await service.getOnboardingStatus(vendorId);

      // Assert
      expect(vi.mocked(axios.create().get)).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      expect(result).toBeNull();
    });

    it('should throw an error when the API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockError = new Error('API error');

      // Setup the mock to reject with our error
      vi.mocked(axios.create().get).mockImplementation(() => Promise.reject(mockError));

      // Act & Assert
      await expect(service.getOnboardingStatus(vendorId)).rejects.toThrow('API error');
      expect(vi.mocked(axios.create().get)).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
    });
  });

  describe('saveOnboardingStatus', () => {
    it('should update existing onboarding status', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = true;
      const progressData = { step1: true, step2: true };
      const existingStatus = {
        id: 1,
        vendor_id: vendorId,
        is_completed: false,
        progress_data: '{"step1":true,"step2":false}',
      };
      const mockResponse = {
        data: {
          data: {
            ...existingStatus,
            is_completed: isCompleted,
            progress_data: JSON.stringify(progressData),
          },
        },
      };

      vi.spyOn(service, 'getOnboardingStatus').mockResolvedValue(existingStatus);
      vi.mocked(axios.create().patch).mockResolvedValue(mockResponse);

      // Act
      const result = await service.saveOnboardingStatus(vendorId, isCompleted, progressData);

      // Assert
      expect(service.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/items/vendor_onboarding/${existingStatus.id}`,
        {
          is_completed: isCompleted,
          progress_data: JSON.stringify(progressData),
          updated_at: expect.any(String), // Check for presence of updated_at
        },
      );
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should create new onboarding status if none exists', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = false;
      const progressData = { step1: true };
      const mockResponse = {
        data: {
          data: {
            id: 2,
            vendor_id: vendorId,
            is_completed: isCompleted,
            progress_data: JSON.stringify(progressData),
          },
        },
      };

      vi.spyOn(service, 'getOnboardingStatus').mockResolvedValue(null);
      vi.mocked(axios.create().post).mockResolvedValue(mockResponse);

      // Act
      const result = await service.saveOnboardingStatus(vendorId, isCompleted, progressData);

      // Assert
      expect(service.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(vi.mocked(axios.create().post)).toHaveBeenCalledWith('/items/vendor_onboarding', {
        vendor_id: vendorId,
        is_completed: isCompleted,
        progress_data: JSON.stringify(progressData),
        created_at: expect.any(String), // Check for presence of created_at
        updated_at: expect.any(String), // Check for presence of updated_at
      });
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should throw an error if getOnboardingStatus fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = true;
      const progressData = { step1: true, step2: true };
      const mockError = new Error('Get status error');

      vi.spyOn(service, 'getOnboardingStatus').mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        service.saveOnboardingStatus(vendorId, isCompleted, progressData),
      ).rejects.toThrow('Get status error');
      expect(service.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(vi.mocked(axios.create().patch)).not.toHaveBeenCalled();
      expect(vi.mocked(axios.create().post)).not.toHaveBeenCalled();
    });

    it('should throw an error if update API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = true;
      const progressData = { step1: true, step2: true };
      const existingStatus = {
        id: 1,
        vendor_id: vendorId,
        is_completed: false,
        progress_data: '{"step1":true,"step2":false}',
      };
      const mockError = new Error('Update API error');

      vi.spyOn(service, 'getOnboardingStatus').mockResolvedValue(existingStatus);
      vi.mocked(axios.create().patch).mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        service.saveOnboardingStatus(vendorId, isCompleted, progressData),
      ).rejects.toThrow('Update API error');
      expect(service.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalled();
      expect(vi.mocked(axios.create().post)).not.toHaveBeenCalled();
    });

    it('should throw an error if create API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = false;
      const progressData = { step1: true };
      const mockError = new Error('Create API error');

      vi.spyOn(service, 'getOnboardingStatus').mockResolvedValue(null);
      vi.mocked(axios.create().post).mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        service.saveOnboardingStatus(vendorId, isCompleted, progressData),
      ).rejects.toThrow('Create API error');
      expect(service.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(vi.mocked(axios.create().patch)).not.toHaveBeenCalled();
      expect(vi.mocked(axios.create().post)).toHaveBeenCalled();
    });
  });

  describe('saveCompanyProfile', () => {
    it('should update vendor profile and upload logo if provided', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const profileData = {
        companyName: 'Test Company',
        industry: 'Tech',
        description: 'A test company',
        contactEmail: '<EMAIL>',
        contactPhone: '************',
        website: 'https://test.com',
        address: '123 Test St',
        logo: { file: new File(['logo'], 'logo.png', { type: 'image/png' }) },
      };
      const mockPatchResponse = {
        data: { data: { id: vendorId, company_name: profileData.companyName } },
      };
      const mockFileUploadResponse = {
        data: { data: { id: 'file-id', filename_disk: 'logo.png' } },
      };

      vi.mocked(axios.create().patch).mockResolvedValue(mockPatchResponse);
      vi.spyOn(service, 'uploadCompanyLogo').mockResolvedValue(mockFileUploadResponse.data.data);

      // Act
      const result = await service.saveCompanyProfile(vendorId, profileData);

      // Assert
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(`/items/vendors/${vendorId}`, {
        company_name: profileData.companyName,
        industry: profileData.industry,
        description: profileData.description,
        contact_email: profileData.contactEmail,
        contact_phone: profileData.contactPhone,
        website: profileData.website,
        address: profileData.address,
      });
      expect(service.uploadCompanyLogo).toHaveBeenCalledWith(vendorId, profileData.logo.file);
      expect(result).toEqual(mockPatchResponse.data.data);
    });

    it('should update vendor profile without uploading logo if not provided', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const profileData = {
        companyName: 'Test Company',
        industry: 'Tech',
        description: 'A test company',
        contactEmail: '<EMAIL>',
        contactPhone: '************',
        website: 'https://test.com',
        address: '123 Test St',
        // No logo provided
      };
      const mockPatchResponse = {
        data: { data: { id: vendorId, company_name: profileData.companyName } },
      };

      vi.mocked(axios.create().patch).mockResolvedValue(mockPatchResponse);
      vi.spyOn(service, 'uploadCompanyLogo'); // Spy but expect not to be called

      // Act
      const result = await service.saveCompanyProfile(vendorId, profileData);

      // Assert
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(`/items/vendors/${vendorId}`, {
        company_name: profileData.companyName,
        industry: profileData.industry,
        description: profileData.description,
        contact_email: profileData.contactEmail,
        contact_phone: profileData.contactPhone,
        website: profileData.website,
        address: profileData.address,
      });
      expect(service.uploadCompanyLogo).not.toHaveBeenCalled();
      expect(result).toEqual(mockPatchResponse.data.data);
    });

    it('should throw an error if update API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const profileData = { companyName: 'Test Company' };
      const mockError = new Error('Update profile error');

      vi.mocked(axios.create().patch).mockRejectedValue(mockError);
      vi.spyOn(service, 'uploadCompanyLogo');

      // Act & Assert
      await expect(service.saveCompanyProfile(vendorId, profileData)).rejects.toThrow(
        'Update profile error',
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalled();
      expect(service.uploadCompanyLogo).not.toHaveBeenCalled();
    });

    it('should throw an error if logo upload fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const profileData = {
        companyName: 'Test Company',
        logo: { file: new File(['logo'], 'logo.png', { type: 'image/png' }) },
      };
      const mockPatchResponse = {
        data: { data: { id: vendorId, company_name: profileData.companyName } },
      };
      const mockError = new Error('Upload logo error');

      vi.mocked(axios.create().patch).mockResolvedValue(mockPatchResponse);
      vi.spyOn(service, 'uploadCompanyLogo').mockRejectedValue(mockError);

      // Act & Assert
      await expect(service.saveCompanyProfile(vendorId, profileData)).rejects.toThrow(
        'Upload logo error',
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalled();
      expect(service.uploadCompanyLogo).toHaveBeenCalledWith(vendorId, profileData.logo.file);
    });
  });

  describe('uploadCompanyLogo', () => {
    it('should upload the logo file and update vendor with file ID', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const logoFile = new File(['logo'], 'logo.png', { type: 'image/png' });
      const mockFileUploadResponse = {
        data: { data: { id: 'file-id', filename_disk: 'logo.png' } },
      };
      const mockVendorUpdateResponse = { data: { data: { id: vendorId, logo: 'file-id' } } };

      vi.mocked(axios.create().post).mockResolvedValue(mockFileUploadResponse);
      vi.mocked(axios.create().patch).mockResolvedValue(mockVendorUpdateResponse);

      // Act
      const result = await service.uploadCompanyLogo(vendorId, logoFile);

      // Assert
      expect(vi.mocked(axios.create().post)).toHaveBeenCalledWith(
        '/files',
        expect.any(FormData), // Check if FormData is passed
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(`/items/vendors/${vendorId}`, {
        logo: 'file-id',
      });
      expect(result).toEqual(mockFileUploadResponse.data.data);
    });

    it('should throw an error if file upload fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const logoFile = new File(['logo'], 'logo.png', { type: 'image/png' });
      const mockError = new Error('File upload error');

      vi.mocked(axios.create().post).mockRejectedValue(mockError);
      vi.mocked(axios.create().patch); // Spy but expect not to be called

      // Act & Assert
      await expect(service.uploadCompanyLogo(vendorId, logoFile)).rejects.toThrow(
        'File upload error',
      );
      expect(vi.mocked(axios.create().post)).toHaveBeenCalled();
      expect(vi.mocked(axios.create().patch)).not.toHaveBeenCalled();
    });

    it('should throw an error if vendor update fails after file upload', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const logoFile = new File(['logo'], 'logo.png', { type: 'image/png' });
      const mockFileUploadResponse = {
        data: { data: { id: 'file-id', filename_disk: 'logo.png' } },
      };
      const mockError = new Error('Vendor update error');

      vi.mocked(axios.create().post).mockResolvedValue(mockFileUploadResponse);
      vi.mocked(axios.create().patch).mockRejectedValue(mockError);

      // Act & Assert
      await expect(service.uploadCompanyLogo(vendorId, logoFile)).rejects.toThrow(
        'Vendor update error',
      );
      expect(vi.mocked(axios.create().post)).toHaveBeenCalled();
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalled();
    });
  });

  describe('saveUserAccounts', () => {
    it('should update admin user and create/update team members', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const accountData = {
        adminUser: {
          id: 'admin-id',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          role: 'admin',
        },
        teamMembers: [
          {
            id: 'member1-id',
            firstName: 'Team',
            lastName: 'Member 1',
            email: '<EMAIL>',
            role: 'editor',
          }, // Existing member
          { firstName: 'Team', lastName: 'Member 2', email: '<EMAIL>', role: 'viewer' }, // New member
        ],
      };
      const mockAdminUpdateResponse = {
        data: { data: { ...accountData.adminUser, first_name: 'Admin' } },
      };
      const mockMember1UpdateResponse = {
        data: { data: { ...accountData.teamMembers[0], first_name: 'Team' } },
      };
      const mockMember2CreateResponse = {
        data: { data: { id: 'member2-id', ...accountData.teamMembers[1], status: 'invited' } },
      };

      vi.mocked(axios.create().patch).mockImplementation((url, data) => {
        if (url.includes(accountData.adminUser.id)) return Promise.resolve(mockAdminUpdateResponse);
        if (url.includes(accountData.teamMembers[0].id))
          return Promise.resolve(mockMember1UpdateResponse);
        return Promise.reject(new Error('Unexpected patch call'));
      });
      vi.mocked(axios.create().post).mockImplementation((url, data) => {
        if (url === '/users' && data.email === accountData.teamMembers[1].email)
          return Promise.resolve(mockMember2CreateResponse);
        return Promise.reject(new Error('Unexpected post call'));
      });

      // Act
      const result = await service.saveUserAccounts(vendorId, accountData);

      // Assert
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/users/${accountData.adminUser.id}`,
        {
          first_name: accountData.adminUser.firstName,
          last_name: accountData.adminUser.lastName,
          email: accountData.adminUser.email,
          role: accountData.adminUser.role,
        },
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/users/${accountData.teamMembers[0].id}`,
        {
          first_name: accountData.teamMembers[0].firstName,
          last_name: accountData.teamMembers[0].lastName,
          email: accountData.teamMembers[0].email,
          role: accountData.teamMembers[0].role,
        },
      );
      expect(vi.mocked(axios.create().post)).toHaveBeenCalledWith('/users', {
        first_name: accountData.teamMembers[1].firstName,
        last_name: accountData.teamMembers[1].lastName,
        email: accountData.teamMembers[1].email,
        role: accountData.teamMembers[1].role,
        status: 'invited',
        provider: 'default',
      });
      expect(result.adminUser).toEqual(mockAdminUpdateResponse.data.data);
      expect(result.teamMembers).toHaveLength(2);
      expect(result.teamMembers).toContainEqual(mockMember1UpdateResponse.data.data);
      expect(result.teamMembers).toContainEqual(mockMember2CreateResponse.data.data);
    });

    it('should throw an error if admin user update fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const accountData = {
        adminUser: {
          id: 'admin-id',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          role: 'admin',
        },
        teamMembers: [],
      };
      const mockError = new Error('Admin update error');

      vi.mocked(axios.create().patch).mockRejectedValue(mockError);
      vi.mocked(axios.create().post);

      // Act & Assert
      await expect(service.saveUserAccounts(vendorId, accountData)).rejects.toThrow(
        'Admin update error',
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalled();
      expect(vi.mocked(axios.create().post)).not.toHaveBeenCalled();
    });

    it('should throw an error if team member update fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const accountData = {
        adminUser: {
          id: 'admin-id',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          role: 'admin',
        },
        teamMembers: [
          {
            id: 'member1-id',
            firstName: 'Team',
            lastName: 'Member 1',
            email: '<EMAIL>',
            role: 'editor',
          },
        ],
      };
      const mockAdminUpdateResponse = {
        data: { data: { ...accountData.adminUser, first_name: 'Admin' } },
      };
      const mockError = new Error('Team member update error');

      vi.mocked(axios.create().patch).mockImplementation((url, data) => {
        if (url.includes(accountData.adminUser.id)) return Promise.resolve(mockAdminUpdateResponse);
        if (url.includes(accountData.teamMembers[0].id)) return Promise.reject(mockError);
        return Promise.reject(new Error('Unexpected patch call'));
      });
      vi.mocked(axios.create().post);

      // Act & Assert
      await expect(service.saveUserAccounts(vendorId, accountData)).rejects.toThrow(
        'Team member update error',
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/users/${accountData.adminUser.id}`,
        expect.any(Object),
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/users/${accountData.teamMembers[0].id}`,
        expect.any(Object),
      );
      expect(vi.mocked(axios.create().post)).not.toHaveBeenCalled();
    });

    it('should throw an error if team member creation fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const accountData = {
        adminUser: {
          id: 'admin-id',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          role: 'admin',
        },
        teamMembers: [
          { firstName: 'Team', lastName: 'Member 2', email: '<EMAIL>', role: 'viewer' },
        ],
      };
      const mockAdminUpdateResponse = {
        data: { data: { ...accountData.adminUser, first_name: 'Admin' } },
      };
      const mockError = new Error('Team member create error');

      vi.mocked(axios.create().patch).mockResolvedValue(mockAdminUpdateResponse);
      vi.mocked(axios.create().post).mockRejectedValue(mockError);

      // Act & Assert
      await expect(service.saveUserAccounts(vendorId, accountData)).rejects.toThrow(
        'Team member create error',
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/users/${accountData.adminUser.id}`,
        expect.any(Object),
      );
      expect(vi.mocked(axios.create().post)).toHaveBeenCalledWith('/users', expect.any(Object));
    });
  });

  describe('saveBranding', () => {
    it('should update vendor branding', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const brandingData = {
        primaryColor: '#FFFFFF',
        secondaryColor: '#000000',
        accentColor: '#FF0000',
        fontPrimary: 'Arial',
        fontSecondary: 'Times New Roman',
        logoPlacement: 'top-left',
        themeMode: 'light',
      };
      const mockResponse = {
        data: { data: { vendor_id: vendorId, primary_color: brandingData.primaryColor } },
      };

      vi.mocked(axios.create().patch).mockResolvedValue(mockResponse);

      // Act
      const result = await service.saveBranding(vendorId, brandingData);

      // Assert
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalledWith(
        `/items/vendor_branding?filter[vendor_id][_eq]=${vendorId}`,
        {
          primary_color: brandingData.primaryColor,
          secondary_color: brandingData.secondaryColor,
          accent_color: brandingData.accentColor,
          font_primary: brandingData.fontPrimary,
          font_secondary: brandingData.fontSecondary,
          logo_placement: brandingData.logoPlacement,
          theme_mode: brandingData.themeMode,
        },
      );
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should throw an error if update API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const brandingData = { primaryColor: '#FFFFFF' };
      const mockError = new Error('Update branding error');

      vi.mocked(axios.create().patch).mockRejectedValue(mockError);

      // Act & Assert
      await expect(service.saveBranding(vendorId, brandingData)).rejects.toThrow(
        'Update branding error',
      );
      expect(vi.mocked(axios.create().patch)).toHaveBeenCalled();
    });
  });

  describe('trackWizardAnalytics', () => {
    it('should post analytics data', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const analyticsData = { eventType: 'step_completed', eventData: { step: 1 } };
      const mockResponse = {
        data: { data: { vendor_id: vendorId, event_type: analyticsData.eventType } },
      };

      vi.mocked(axios.create().post).mockResolvedValue(mockResponse);

      // Act
      const result = await service.trackWizardAnalytics(vendorId, analyticsData);

      // Assert
      expect(vi.mocked(axios.create().post)).toHaveBeenCalledWith('/items/wizard_analytics', {
        vendor_id: vendorId,
        event_type: analyticsData.eventType,
        event_data: JSON.stringify(analyticsData.eventData),
        timestamp: expect.any(String), // Check for presence of timestamp
      });
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should return null and log error if API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const analyticsData = { eventType: 'step_completed', eventData: { step: 1 } };
      const mockError = new Error('Analytics API error');

      vi.mocked(axios.create().post).mockRejectedValue(mockError);
      vi.spyOn(console, 'error');

      // Act
      const result = await service.trackWizardAnalytics(vendorId, analyticsData);

      // Assert
      expect(vi.mocked(axios.create().post)).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith('Error tracking wizard analytics:', mockError);
      expect(result).toBeNull();
    });
  });
});
