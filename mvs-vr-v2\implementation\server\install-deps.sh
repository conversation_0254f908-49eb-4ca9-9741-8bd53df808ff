#!/bin/bash

# Script to install all required dependencies for both Node.js and Deno environments

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Installing dependencies for both Node.js and Deno environments...${NC}"

# Function to check command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check for required package managers
if ! command_exists "npm"; then
  echo -e "${RED}Error: npm is not installed${NC}"
  exit 1
fi

if ! command_exists "deno"; then
  echo -e "${RED}Error: deno is not installed${NC}"
  exit 1
fi

# Install Node.js dependencies
echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
npm install

# Add Deno dependencies
echo -e "${YELLOW}Adding Deno dependencies...${NC}"

# Testing framework
deno add npm:vitest
deno add npm:vitest/globals
deno add npm:@testing-library/jest-dom
deno add npm:@testing-library/dom
deno add npm:jsdom
deno add npm:@vitejs/plugin-vue
deno add npm:@vue/test-utils

# Node.js compatibility
deno add npm:@types/node

# Standard library modules
echo -e "${YELLOW}Adding Deno standard library modules...${NC}"
deno add https://deno.land/std@0.181.0/path/mod.ts
deno add https://deno.land/std@0.181.0/fs/mod.ts
deno add https://deno.land/std@0.181.0/testing/mock.ts

# Update import map
echo -e "${YELLOW}Updating import map...${NC}"
cat > import_map.json << EOF
{
  "imports": {
    "vitest": "npm:vitest",
    "vitest/": "npm:vitest/",
    "@testing-library/": "npm:@testing-library/",
    "node:": "./node_modules/",
    "@/": "./src/",
    "@directus/": "./directus/extensions/",
    "@shared/": "./shared/",
    "@services/": "./services/",
    "@tests/": "./tests/",
    "@setup/": "./tests/setup/",
    "path": "https://deno.land/std@0.181.0/path/mod.ts",
    "fs": "https://deno.land/std@0.181.0/fs/mod.ts"
  }
}
EOF

# Create Deno configuration
echo -e "${YELLOW}Creating Deno configuration...${NC}"
cat > deno.json << EOF
{
  "importMap": "./import_map.json",
  "compilerOptions": {
    "allowJs": true,
    "lib": ["dom", "dom.iterable", "dom.asynciterable", "deno.ns", "deno.window"],
    "types": ["vitest/globals"]
  }
}
EOF

# Set permissions
chmod +x scripts/*.sh
chmod +x scripts/*.js

echo -e "${GREEN}Dependencies installed successfully!${NC}"
echo -e "${YELLOW}You can now run:${NC}"
echo -e "  ${GREEN}npm test${NC} - Run tests with Node.js"
echo -e "  ${GREEN}deno test${NC} - Run tests with Deno"