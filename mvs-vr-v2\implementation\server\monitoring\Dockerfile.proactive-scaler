FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/proactive-scaler.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV PROACTIVE_SCALER_PORT=9110

# Expose port
EXPOSE 9110

# Start the service
CMD ["node", "monitoring/proactive-scaler.js"]
