import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import VendorLayout from "../../components/VendorLayout";
import ProtectedRoute from "../../components/ProtectedRoute";
import {
  CreateMemberRequest,
  Member,
  memberService,
  UpdateMemberRequest,
} from "../../utils/api/member-service";

// Member interface is imported from member-service

const Members: React.FC = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [newMember, setNewMember] = useState<CreateMemberRequest>({
    email: "",
    name: "",
    role: "viewer",
  });
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch members with pagination using the member service
      const response = await memberService.getMembers(page, rowsPerPage);

      setMembers(response.members);
      setTotalCount(response.totalCount);
    } catch (err: unknown) {
      console.error("Error fetching members:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch members");
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMembers();
  }, [page, rowsPerPage]);

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<{ value: string }>,
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleCloseError = () => {
    setShowError(false);
  };

  const handleOpenAddDialog = () => {
    setOpenAddDialog(true);
  };

  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
    setNewMember({
      email: "",
      name: "",
      role: "viewer",
    });
  };

  const handleOpenEditDialog = (member: Member) => {
    setSelectedMember(member);
    setOpenEditDialog(true);
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
    setSelectedMember(null);
  };

  const handleAddMember = async () => {
    try {
      setError(null);

      // Create new member using the member service
      await memberService.createMember(newMember);

      // Close dialog and refresh members list
      handleCloseAddDialog();
      fetchMembers();
    } catch (err: unknown) {
      console.error("Error adding member:", err);
      setError(err instanceof Error ? err.message : "Failed to add member");
      setShowError(true);
    }
  };

  const handleUpdateMember = async () => {
    if (!selectedMember) return;

    try {
      setError(null);

      // Update member using the member service
      const updateRequest: UpdateMemberRequest = {
        id: selectedMember.id,
        name: selectedMember.name,
        role: selectedMember.role,
      };

      await memberService.updateMember(updateRequest);

      // Close dialog and refresh members list
      handleCloseEditDialog();
      fetchMembers();
    } catch (err: unknown) {
      console.error("Error updating member:", err);
      setError(err instanceof Error ? err.message : "Failed to update member");
      setShowError(true);
    }
  };

  const handleDeleteMember = async (id: string) => {
    if (confirm("Are you sure you want to remove this team member?")) {
      try {
        setError(null);

        // Delete member using the member service
        await memberService.deleteMember(id);

        // Refresh the members list
        fetchMembers();
      } catch (err: unknown) {
        console.error("Error deleting member:", err);
        setError(
          err instanceof Error ? err.message : "Failed to delete member",
        );
        setShowError(true);
      }
    }
  };

  if (loading && members.length === 0) {
    return (
      <VendorLayout title="Team Members">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }

  // Error snackbar
  const errorSnackbar = (
    <Snackbar
      open={showError}
      autoHideDuration={6000}
      onClose={handleCloseError}
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
    >
      <Alert onClose={handleCloseError} severity="error" sx={{ width: "100%" }}>
        {error}
      </Alert>
    </Snackbar>
  );

  return (
    <VendorLayout title="Team Members">
      {/* Error snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseError}
          severity="error"
          sx={{ width: "100%" }}
        >
          {error}
        </Alert>
      </Snackbar>

      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h4" component="h1">
            Team Members
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenAddDialog}
          >
            Add Team Member
          </Button>
        </Box>

        <Paper sx={{ width: "100%", overflow: "hidden" }}>
          <TableContainer sx={{ maxHeight: 440 }}>
            <Table stickyHeader aria-label="members table">
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Joined</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {members.map((member: Member) => (
                  <TableRow hover key={member.id}>
                    <TableCell>{member.name}</TableCell>
                    <TableCell>{member.email}</TableCell>
                    <TableCell>{member.role}</TableCell>
                    <TableCell>{member.status}</TableCell>
                    <TableCell>
                      {new Date(member.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      {member.last_login
                        ? new Date(member.last_login).toLocaleString()
                        : "Never"}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        color="secondary"
                        onClick={() =>
                          handleOpenEditDialog(member)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() =>
                          handleDeleteMember(member.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {members.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Box sx={{ py: 3 }}>
                        <Typography variant="body1" gutterBottom>
                          No team members found
                        </Typography>
                        <Button
                          variant="outlined"
                          startIcon={<AddIcon />}
                          onClick={handleOpenAddDialog}
                          sx={{ mt: 1 }}
                        >
                          Add your first team member
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={totalCount}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Box>

      {/* Add Member Dialog */}
      <Dialog open={openAddDialog} onClose={handleCloseAddDialog}>
        <DialogTitle>Add Team Member</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Enter the details of the new team member. An invitation will be sent
            to their email address.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newMember.name}
            onChange={(e: React.ChangeEvent<{ value: string }>) =>
              setNewMember({ ...newMember, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            id="email"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={newMember.email}
            onChange={(e: React.ChangeEvent<{ value: string }>) =>
              setNewMember({ ...newMember, email: e.target.value })}
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth>
            <InputLabel id="role-label">Role</InputLabel>
            <Select
              labelId="role-label"
              id="role"
              value={newMember.role}
              label="Role"
              onChange={(e: SelectChangeEvent) =>
                setNewMember({ ...newMember, role: e.target.value })}
            >
              <MenuItem value="admin">Admin</MenuItem>
              <MenuItem value="editor">Editor</MenuItem>
              <MenuItem value="viewer">Viewer</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddDialog}>Cancel</Button>
          <Button onClick={handleAddMember} variant="contained" color="primary">
            Add
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Member Dialog */}
      <Dialog open={openEditDialog} onClose={handleCloseEditDialog}>
        <DialogTitle>Edit Team Member</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="edit-name"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={selectedMember?.name || ""}
            onChange={(e: React.ChangeEvent<{ value: string }>) =>
              selectedMember &&
              setSelectedMember({ ...selectedMember, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            id="edit-email"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={selectedMember?.email || ""}
            disabled
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth>
            <InputLabel id="edit-role-label">Role</InputLabel>
            <Select
              labelId="edit-role-label"
              id="edit-role"
              value={selectedMember?.role || ""}
              label="Role"
              onChange={(e: SelectChangeEvent) => selectedMember &&
                setSelectedMember({ ...selectedMember, role: e.target.value })}
            >
              <MenuItem value="admin">Admin</MenuItem>
              <MenuItem value="editor">Editor</MenuItem>
              <MenuItem value="viewer">Viewer</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Cancel</Button>
          <Button
            onClick={handleUpdateMember}
            variant="contained"
            color="primary"
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </VendorLayout>
  );
};

// Wrap the Members component with ProtectedRoute
const ProtectedMembers: React.FC = () => {
  return (
    <ProtectedRoute>
      <Members />
    </ProtectedRoute>
  );
};

export default ProtectedMembers;
