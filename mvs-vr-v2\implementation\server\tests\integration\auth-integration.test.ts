/**
 * Integration tests for the authentication integration
 */

import request from 'supertest';
import express from 'express';
import cookieParser from 'cookie-parser';
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock dependencies
vi.mock('@supabase/supabase-js');
vi.mock('ioredis');

// Mock environment variables
process.env.DIRECTUS_URL = 'http://localhost:8055';
process.env.DIRECTUS_SECRET = 'test-secret';
process.env.REDIS_URL = 'redis://localhost:6379';
process.env.NODE_ENV = 'test';

// Mock Redis client
const mockRedis = {
  get: vi.fn(),
  set: vi.fn(),
  exists: vi.fn(),
  incr: vi.fn(),
  expire: vi.fn(),
  lpush: vi.fn(),
  ltrim: vi.fn(),
  call: vi.fn(),
};

// Mock Supabase client
const mockSupabase = {
  auth: {
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
    refreshSession: vi.fn(),
    resetPasswordForEmail: vi.fn(),
    verifyOtp: vi.fn(),
    getUser: vi.fn(),
    mfa: {
      enroll: vi.fn(),
      challenge: vi.fn(),
    },
  },
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  single: vi.fn(),
};

// Setup mocks
vi.mocked(Redis).mockImplementation(() => mockRedis as any);
vi.mocked(createClient).mockReturnValue(mockSupabase as any);

// Create a simple rate limiter for testing
const simpleRateLimiter = (_req: any, _res: any, next: any) => {
  next(); // Just pass through in tests
};

// Mock auth-middleware to avoid Redis issues
vi.mock('../../api/middleware/auth-middleware', () => {
  return {
    authenticate:
      (options = {}) =>
      (req: any, res: any, next: any) => {
        // Simple mock implementation
        if (req.headers.authorization) {
          req.user = { id: 'test-user', role: 'admin' };
          next();
        } else {
          res.status(401).json({ error: 'Unauthorized' });
        }
      },
    apiLimiter: (_req: any, _res: any, next: any) => next(),
    csrfProtection: (_req: any, _res: any, next: any) => next(),
  };
});

// Import middleware
import * as authMiddleware from '../../api/middleware/auth-middleware';
const authenticate = authMiddleware.authenticate;

// Create test app
const app = express();
app.use(express.json());
app.use(cookieParser());

// Add test routes
app.post('/auth-integration/login', simpleRateLimiter, async (req, res) => {
  try {
    const { email, password } = req.body;

    // Mock successful login
    if (email === '<EMAIL>' && password === 'Password123!') {
      // Mock Supabase response
      const mockSession = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_at: new Date(Date.now() + 3600000).toISOString(),
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: 'admin',
        vendor_id: null,
      };

      // Set secure cookies
      res.cookie('refresh_token', mockSession.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      res.cookie('access_token', mockSession.access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 1000, // 1 hour
      });

      // Return tokens and user data
      return res.json({
        data: {
          directus: {
            access_token: 'mock-directus-token',
            refresh_token: null,
          },
          supabase: mockSession,
          user: mockUser,
        },
      });
    }

    // Invalid credentials
    return res.status(401).json({
      errors: [
        {
          message: 'Invalid email or password',
        },
      ],
    });
  } catch (error: any) {
    return res.status(500).json({
      errors: [
        {
          message: error.message,
        },
      ],
    });
  }
});

app.post('/auth-integration/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;

    // Mock successful refresh
    if (refresh_token === 'mock-refresh-token') {
      // Mock Supabase response
      const mockSession = {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        expires_at: new Date(Date.now() + 3600000).toISOString(),
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: 'admin',
        vendor_id: null,
      };

      // Set secure cookies
      res.cookie('refresh_token', mockSession.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      res.cookie('access_token', mockSession.access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 1000, // 1 hour
      });

      // Return tokens and user data
      return res.json({
        data: {
          directus: {
            access_token: 'new-directus-token',
            refresh_token: null,
          },
          supabase: mockSession,
          user: mockUser,
        },
      });
    }

    // Invalid refresh token
    return res.status(401).json({
      errors: [
        {
          message: 'Invalid refresh token',
        },
      ],
    });
  } catch (error: any) {
    return res.status(500).json({
      errors: [
        {
          message: error.message,
        },
      ],
    });
  }
});

// Protected route
app.get('/protected', authenticate({ required: true }), (req, res) => {
  res.json({ message: 'Access granted', user: (req as any).user });
});

describe('Authentication Integration', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
  });

  describe('Login', () => {
    it('should login successfully with valid credentials', async () => {
      const response = await request(app).post('/auth-integration/login').send({
        email: '<EMAIL>',
        password: 'Password123!',
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.supabase).toBeDefined();
      expect(response.body.data.directus).toBeDefined();
      expect(response.body.data.user).toBeDefined();
      expect(response.headers['set-cookie']).toBeDefined();
    });

    it('should reject login with invalid credentials', async () => {
      const response = await request(app).post('/auth-integration/login').send({
        email: '<EMAIL>',
        password: 'wrong-password',
      });

      expect(response.status).toBe(401);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Token Refresh', () => {
    it('should refresh tokens successfully with valid refresh token', async () => {
      const response = await request(app).post('/auth-integration/refresh').send({
        refresh_token: 'mock-refresh-token',
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.supabase).toBeDefined();
      expect(response.body.data.directus).toBeDefined();
      expect(response.body.data.user).toBeDefined();
      expect(response.headers['set-cookie']).toBeDefined();
    });

    it('should reject refresh with invalid refresh token', async () => {
      const response = await request(app).post('/auth-integration/refresh').send({
        refresh_token: 'invalid-refresh-token',
      });

      expect(response.status).toBe(401);
      expect(response.body.errors).toBeDefined();
    });
  });

  // Add more tests for other endpoints...
});
