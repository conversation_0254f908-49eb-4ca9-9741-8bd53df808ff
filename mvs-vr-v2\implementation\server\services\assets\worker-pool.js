/**
 * Worker Pool for Asset Processing
 * 
 * This module provides a worker pool for parallel asset processing.
 */

const { Worker } = require('worker_threads');
const os = require('os');
const path = require('path');
const { EventEmitter } = require('events');
const { Logger } = require('../integration/logger');
const { Gauge } = require('prom-client');

// Create logger
const logger = new Logger();

// Create metrics
const workerPoolSize = new Gauge({
  name: 'worker_pool_size',
  help: 'Size of the worker pool',
  labelNames: ['pool_name']
});

const workerPoolActive = new Gauge({
  name: 'worker_pool_active',
  help: 'Number of active workers in the pool',
  labelNames: ['pool_name']
});

const workerPoolIdle = new Gauge({
  name: 'worker_pool_idle',
  help: 'Number of idle workers in the pool',
  labelNames: ['pool_name']
});

const workerPoolQueue = new Gauge({
  name: 'worker_pool_queue',
  help: 'Number of tasks in the queue',
  labelNames: ['pool_name']
});

/**
 * Worker pool class
 */
class WorkerPool extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Pool options
   */
  constructor(options = {}) {
    super();

    const {
      name = 'default',
      size = Math.max(1, Math.floor(os.cpus().length * 0.75)),
      workerScript = path.join(__dirname, 'worker.js'),
      maxQueueSize = 1000,
      taskTimeout = 60000 // 1 minute
    } = options;

    this.name = name;
    this.size = size;
    this.workerScript = workerScript;
    this.maxQueueSize = maxQueueSize;
    this.taskTimeout = taskTimeout;

    this.workers = [];
    this.idleWorkers = [];
    this.taskQueue = [];
    this.taskMap = new Map();
    this.nextTaskId = 1;

    // Initialize metrics
    workerPoolSize.set({ pool_name: this.name }, this.size);
    workerPoolActive.set({ pool_name: this.name }, 0);
    workerPoolIdle.set({ pool_name: this.name }, 0);
    workerPoolQueue.set({ pool_name: this.name }, 0);

    logger.info(`Created worker pool "${name}"`, {
      size,
      workerScript,
      maxQueueSize,
      taskTimeout
    });
  }

  /**
   * Initialize the worker pool
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info(`Initializing worker pool "${this.name}"`);

    // Create workers
    for (let i = 0; i < this.size; i++) {
      this.createWorker();
    }

    // Update metrics
    workerPoolSize.set({ pool_name: this.name }, this.size);
    workerPoolActive.set({ pool_name: this.name }, 0);
    workerPoolIdle.set({ pool_name: this.name }, this.size);
    workerPoolQueue.set({ pool_name: this.name }, 0);

    logger.info(`Worker pool "${this.name}" initialized with ${this.size} workers`);
  }

  /**
   * Create a worker
   * @returns {Worker} Worker
   */
  createWorker() {
    const worker = new Worker(this.workerScript);
    
    // Handle messages from worker
    worker.on('message', (result) => {
      const taskId = worker.taskId;
      
      if (taskId && this.taskMap.has(taskId)) {
        const { resolve, reject, timeout } = this.taskMap.get(taskId);
        
        // Clear timeout
        if (timeout) {
          clearTimeout(timeout);
        }
        
        // Remove task from map
        this.taskMap.delete(taskId);
        
        // Handle result
        if (result.success) {
          resolve(result);
        } else {
          reject(new Error(result.error || 'Task failed'));
        }
      }
      
      // Mark worker as idle
      worker.taskId = null;
      this.idleWorkers.push(worker);
      
      // Update metrics
      workerPoolActive.set({ pool_name: this.name }, this.workers.length - this.idleWorkers.length);
      workerPoolIdle.set({ pool_name: this.name }, this.idleWorkers.length);
      
      // Process next task in queue
      this.processNextTask();
    });
    
    // Handle errors
    worker.on('error', (error) => {
      logger.error(`Worker error in pool "${this.name}"`, { error });
      
      const taskId = worker.taskId;
      
      if (taskId && this.taskMap.has(taskId)) {
        const { reject, timeout } = this.taskMap.get(taskId);
        
        // Clear timeout
        if (timeout) {
          clearTimeout(timeout);
        }
        
        // Remove task from map
        this.taskMap.delete(taskId);
        
        // Reject task
        reject(error);
      }
      
      // Remove worker from pool
      const index = this.workers.indexOf(worker);
      
      if (index !== -1) {
        this.workers.splice(index, 1);
      }
      
      // Remove from idle workers
      const idleIndex = this.idleWorkers.indexOf(worker);
      
      if (idleIndex !== -1) {
        this.idleWorkers.splice(idleIndex, 1);
      }
      
      // Create a new worker to replace the failed one
      this.createWorker();
      
      // Update metrics
      workerPoolActive.set({ pool_name: this.name }, this.workers.length - this.idleWorkers.length);
      workerPoolIdle.set({ pool_name: this.name }, this.idleWorkers.length);
    });
    
    // Handle exit
    worker.on('exit', (code) => {
      logger.info(`Worker exited in pool "${this.name}"`, { code });
      
      // Remove worker from pool
      const index = this.workers.indexOf(worker);
      
      if (index !== -1) {
        this.workers.splice(index, 1);
      }
      
      // Remove from idle workers
      const idleIndex = this.idleWorkers.indexOf(worker);
      
      if (idleIndex !== -1) {
        this.idleWorkers.splice(idleIndex, 1);
      }
      
      // Update metrics
      workerPoolActive.set({ pool_name: this.name }, this.workers.length - this.idleWorkers.length);
      workerPoolIdle.set({ pool_name: this.name }, this.idleWorkers.length);
    });
    
    // Add worker to pool
    this.workers.push(worker);
    this.idleWorkers.push(worker);
    
    return worker;
  }

  /**
   * Execute a task
   * @param {Object} task - Task to execute
   * @returns {Promise<Object>} Task result
   */
  async executeTask(task) {
    // If queue is full, reject
    if (this.taskQueue.length >= this.maxQueueSize) {
      throw new Error(`Task queue is full (${this.maxQueueSize} tasks)`);
    }
    
    // Create task promise
    return new Promise((resolve, reject) => {
      const taskId = this.nextTaskId++;
      
      // Create timeout
      const timeout = setTimeout(() => {
        if (this.taskMap.has(taskId)) {
          const { worker } = this.taskMap.get(taskId);
          
          // Remove task from map
          this.taskMap.delete(taskId);
          
          // Terminate worker and create a new one
          if (worker) {
            worker.terminate();
            
            // Remove worker from pool
            const index = this.workers.indexOf(worker);
            
            if (index !== -1) {
              this.workers.splice(index, 1);
            }
            
            // Create a new worker
            this.createWorker();
            
            // Update metrics
            workerPoolActive.set({ pool_name: this.name }, this.workers.length - this.idleWorkers.length);
            workerPoolIdle.set({ pool_name: this.name }, this.idleWorkers.length);
          }
          
          // Reject task
          reject(new Error(`Task timed out after ${this.taskTimeout}ms`));
        }
      }, this.taskTimeout);
      
      // Add task to map
      this.taskMap.set(taskId, { resolve, reject, timeout, task });
      
      // Add task to queue
      this.taskQueue.push(taskId);
      
      // Update metrics
      workerPoolQueue.set({ pool_name: this.name }, this.taskQueue.length);
      
      // Process task if workers are available
      this.processNextTask();
    });
  }

  /**
   * Process next task in queue
   */
  processNextTask() {
    // If no tasks in queue or no idle workers, return
    if (this.taskQueue.length === 0 || this.idleWorkers.length === 0) {
      return;
    }
    
    // Get next task
    const taskId = this.taskQueue.shift();
    
    // Update metrics
    workerPoolQueue.set({ pool_name: this.name }, this.taskQueue.length);
    
    // If task doesn't exist, process next task
    if (!this.taskMap.has(taskId)) {
      this.processNextTask();
      return;
    }
    
    // Get task
    const { task } = this.taskMap.get(taskId);
    
    // Get idle worker
    const worker = this.idleWorkers.pop();
    
    // Update metrics
    workerPoolActive.set({ pool_name: this.name }, this.workers.length - this.idleWorkers.length);
    workerPoolIdle.set({ pool_name: this.name }, this.idleWorkers.length);
    
    // Assign task to worker
    worker.taskId = taskId;
    this.taskMap.get(taskId).worker = worker;
    
    // Send task to worker
    worker.postMessage(task);
  }

  /**
   * Shutdown the worker pool
   * @returns {Promise<void>}
   */
  async shutdown() {
    logger.info(`Shutting down worker pool "${this.name}"`);
    
    // Terminate all workers
    const terminatePromises = this.workers.map(worker => worker.terminate());
    
    // Wait for all workers to terminate
    await Promise.all(terminatePromises);
    
    // Clear arrays
    this.workers = [];
    this.idleWorkers = [];
    
    // Reject all pending tasks
    for (const [taskId, { reject, timeout }] of this.taskMap.entries()) {
      // Clear timeout
      if (timeout) {
        clearTimeout(timeout);
      }
      
      // Reject task
      reject(new Error('Worker pool is shutting down'));
      
      // Remove task from map
      this.taskMap.delete(taskId);
    }
    
    // Clear task queue
    this.taskQueue = [];
    
    // Update metrics
    workerPoolSize.set({ pool_name: this.name }, 0);
    workerPoolActive.set({ pool_name: this.name }, 0);
    workerPoolIdle.set({ pool_name: this.name }, 0);
    workerPoolQueue.set({ pool_name: this.name }, 0);
    
    logger.info(`Worker pool "${this.name}" shut down`);
  }

  /**
   * Get pool statistics
   * @returns {Object} Pool statistics
   */
  getStats() {
    return {
      name: this.name,
      size: this.size,
      active: this.workers.length - this.idleWorkers.length,
      idle: this.idleWorkers.length,
      queued: this.taskQueue.length,
      maxQueueSize: this.maxQueueSize,
      taskTimeout: this.taskTimeout
    };
  }
}

// Default worker pool
let defaultPool = null;

/**
 * Get the default worker pool
 * @returns {WorkerPool} Default worker pool
 */
function getDefaultPool() {
  if (!defaultPool) {
    defaultPool = new WorkerPool();
    defaultPool.initialize();
  }
  
  return defaultPool;
}

module.exports = {
  WorkerPool,
  getDefaultPool
};
