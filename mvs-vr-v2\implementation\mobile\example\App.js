/**
 * MVS-VR Mobile App Example
 */

import React, { useEffect, useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
  ActivityIndicator,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { performance } from 'react-native-performance';

// Import MVS Metrics SDK
import MVSMetricsSDK from '../MVSMetricsSDK';

// Create stack navigator
const Stack = createStackNavigator();

// Home screen
const HomeScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    // Mark screen load time
    performance.mark('screen_load_Home');
    
    // Record screen load time
    const loadTime = performance.now();
    MVSMetricsSDK.recordScreenLoadTime('Home', loadTime);
    
    // Simulate API request
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const startTime = Date.now();
        
        // Simulate API request
        const response = await fetch('https://api.mvs-vr.com/api/showrooms');
        const data = await response.json();
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // Record API request
        MVSMetricsSDK.recordApiRequest({
          endpoint: 'api/showrooms',
          method: 'GET',
          responseTime,
          status: response.status,
        });
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data', error);
        
        // Record error
        MVSMetricsSDK.recordError('api', 'home', 'error');
        
        setLoading(false);
      }
    };
    
    fetchData();
    
    // Cleanup
    return () => {
      // Record interaction latency
      const interactionTime = performance.now();
      MVSMetricsSDK.recordInteractionLatency('home_screen_view', interactionTime);
    };
  }, []);
  
  const handleShowroomPress = () => {
    // Record interaction latency
    const startTime = performance.now();
    
    // Navigate to showroom
    navigation.navigate('Showroom');
    
    const endTime = performance.now();
    const latency = endTime - startTime;
    
    // Record interaction latency
    MVSMetricsSDK.recordInteractionLatency('navigate_to_showroom', latency);
  };
  
  const handleARPress = () => {
    // Record interaction latency
    const startTime = performance.now();
    
    // Navigate to AR view
    navigation.navigate('ARView');
    
    const endTime = performance.now();
    const latency = endTime - startTime;
    
    // Record interaction latency
    MVSMetricsSDK.recordInteractionLatency('navigate_to_ar', latency);
  };
  
  const handleErrorPress = () => {
    // Simulate error
    try {
      throw new Error('Test error');
    } catch (error) {
      // Record error
      MVSMetricsSDK.recordError('javascript', 'home', 'error');
      
      alert('Error recorded');
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>MVS-VR Mobile</Text>
      
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={handleShowroomPress}>
            <Text style={styles.buttonText}>View Showroom</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={handleARPress}>
            <Text style={styles.buttonText}>AR View</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={handleErrorPress}>
            <Text style={styles.buttonText}>Simulate Error</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

// Showroom screen
const ShowroomScreen = () => {
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Mark screen load time
    performance.mark('screen_load_Showroom');
    
    // Record screen load time
    const loadTime = performance.now();
    MVSMetricsSDK.recordScreenLoadTime('Showroom', loadTime);
    
    // Simulate asset loading
    const loadAssets = async () => {
      try {
        setLoading(true);
        
        // Simulate texture loading
        const textureStartTime = Date.now();
        await new Promise(resolve => setTimeout(resolve, 500));
        const textureEndTime = Date.now();
        const textureLoadTime = (textureEndTime - textureStartTime) / 1000; // in seconds
        
        // Record asset load time
        MVSMetricsSDK.recordAssetLoadTime('texture', textureLoadTime);
        
        // Simulate model loading
        const modelStartTime = Date.now();
        await new Promise(resolve => setTimeout(resolve, 1000));
        const modelEndTime = Date.now();
        const modelLoadTime = (modelEndTime - modelStartTime) / 1000; // in seconds
        
        // Record asset load time
        MVSMetricsSDK.recordAssetLoadTime('model', modelLoadTime);
        
        // Record asset cache hit rate
        MVSMetricsSDK.recordAssetCacheHitRate('texture', 75);
        MVSMetricsSDK.recordAssetCacheHitRate('model', 60);
        
        setLoading(false);
      } catch (error) {
        console.error('Error loading assets', error);
        
        // Record error
        MVSMetricsSDK.recordError('asset', 'showroom', 'error');
        
        setLoading(false);
      }
    };
    
    loadAssets();
    
    // Cleanup
    return () => {
      // Record interaction latency
      const interactionTime = performance.now();
      MVSMetricsSDK.recordInteractionLatency('showroom_screen_view', interactionTime);
    };
  }, []);
  
  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Showroom</Text>
      
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <View style={styles.showroomContainer}>
          <Image
            source={{ uri: 'https://via.placeholder.com/300' }}
            style={styles.showroomImage}
          />
          
          <Text style={styles.showroomText}>
            This is a sample showroom view. In a real app, this would display 3D models and
            interactive elements.
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

// AR view screen
const ARViewScreen = () => {
  useEffect(() => {
    // Mark screen load time
    performance.mark('screen_load_ARView');
    
    // Record screen load time
    const loadTime = performance.now();
    MVSMetricsSDK.recordScreenLoadTime('ARView', loadTime);
    
    // Simulate AR session start
    if (global.NativeModules?.ARTracker) {
      global.NativeModules.ARTracker.startARSession();
    }
    
    // Cleanup
    return () => {
      // Simulate AR session end
      if (global.NativeModules?.ARTracker) {
        global.NativeModules.ARTracker.endARSession();
      }
      
      // Record interaction latency
      const interactionTime = performance.now();
      MVSMetricsSDK.recordInteractionLatency('ar_screen_view', interactionTime);
    };
  }, []);
  
  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>AR View</Text>
      
      <View style={styles.arContainer}>
        <Image
          source={{ uri: 'https://via.placeholder.com/300' }}
          style={styles.arImage}
        />
        
        <Text style={styles.arText}>
          This is a sample AR view. In a real app, this would display AR content using
          ARKit or ARCore.
        </Text>
      </View>
    </SafeAreaView>
  );
};

// App component
const App = () => {
  useEffect(() => {
    // Initialize MVS Metrics SDK
    MVSMetricsSDK.initialize({
      apiUrl: 'https://api.mvs-vr.com',
      appId: 'mvs-vr-mobile-example',
      enableDetailedMetrics: true,
      enableARTracking: true,
    });
    
    // Cleanup
    return () => {
      MVSMetricsSDK.cleanup();
    };
  }, []);
  
  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" />
      
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Showroom" component={ShowroomScreen} />
        <Stack.Screen name="ARView" component={ARViewScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5FCFF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    margin: 20,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 5,
    margin: 10,
    width: 200,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  showroomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  showroomImage: {
    width: 300,
    height: 300,
    marginBottom: 20,
  },
  showroomText: {
    fontSize: 16,
    textAlign: 'center',
  },
  arContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  arImage: {
    width: 300,
    height: 300,
    marginBottom: 20,
  },
  arText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default App;
