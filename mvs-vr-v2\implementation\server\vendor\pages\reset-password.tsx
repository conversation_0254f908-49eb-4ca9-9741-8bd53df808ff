import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  TextField,
  Typo<PERSON>,
  Link as <PERSON>i<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>per,
} from '@mui/material';
import { authService } from '../utils/api/auth-service';
import Link from 'next/link';
import Image from 'next/image';

/**
 * Password reset page for vendor portal
 */
const ResetPassword: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const steps = ['Request Reset', 'Enter New Password', 'Complete'];

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate email
    if (!email) {
      setError('Email is required');
      setShowError(true);
      return;
    }

    // Submit request
    try {
      setLoading(true);
      await authService.requestPasswordReset({ email });
      setSuccess('Password reset link has been sent to your email');
      setShowSuccess(true);
      setActiveStep(1);
    } catch (err: unknown) {
      console.error('Password reset request failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to request password reset');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (!token) {
      setError('Reset token is required');
      setShowError(true);
      return;
    }

    if (!password) {
      setError('Password is required');
      setShowError(true);
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setShowError(true);
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      setShowError(true);
      return;
    }

    // Submit update
    try {
      setLoading(true);
      await authService.updatePassword({ password, token });
      setSuccess('Password has been reset successfully');
      setShowSuccess(true);
      setActiveStep(2);
    } catch (err: unknown) {
      console.error('Password update failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to update password');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseError = () => {
    setShowError(false);
  };

  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  return (
    <Container maxWidth="sm">
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        py={4}
      >
        {/* Error snackbar */}
        <Snackbar
          open={showError}
          autoHideDuration={6000}
          onClose={handleCloseError}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
            {error}
          </Alert>
        </Snackbar>

        {/* Success snackbar */}
        <Snackbar
          open={showSuccess}
          autoHideDuration={6000}
          onClose={handleCloseSuccess}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
            {success}
          </Alert>
        </Snackbar>

        {/* Logo */}
        <Box mb={4} display="flex" justifyContent="center">
          <Image
            src="/logo.png"
            alt="MVS-VR Logo"
            width={200}
            height={80}
            priority
          />
        </Box>

        <Card sx={{ width: '100%' }}>
          <CardContent>
            <Typography variant="h4" component="h1" align="center" gutterBottom>
              Reset Password
            </Typography>

            <Box sx={{ width: '100%', mb: 4 }}>
              <Stepper activeStep={activeStep} alternativeLabel>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
            </Box>

            {activeStep === 0 && (
              <>
                <Typography variant="body1" color="textSecondary" align="center" sx={{ mb: 4 }}>
                  Enter your email address to receive a password reset link
                </Typography>

                <form onSubmit={handleRequestReset}>
                  <TextField
                    label="Email"
                    type="email"
                    fullWidth
                    margin="normal"
                    variant="outlined"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    autoComplete="email"
                    autoFocus
                  />
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    color="primary"
                    size="large"
                    disabled={loading}
                    sx={{ mt: 3 }}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Request Reset Link'}
                  </Button>
                </form>
              </>
            )}

            {activeStep === 1 && (
              <>
                <Typography variant="body1" color="textSecondary" align="center" sx={{ mb: 4 }}>
                  Enter the reset token from your email and your new password
                </Typography>

                <form onSubmit={handleUpdatePassword}>
                  <TextField
                    label="Reset Token"
                    type="text"
                    fullWidth
                    margin="normal"
                    variant="outlined"
                    value={token}
                    onChange={(e) => setToken(e.target.value)}
                    autoFocus
                  />
                  <TextField
                    label="New Password"
                    type="password"
                    fullWidth
                    margin="normal"
                    variant="outlined"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    autoComplete="new-password"
                  />
                  <TextField
                    label="Confirm New Password"
                    type="password"
                    fullWidth
                    margin="normal"
                    variant="outlined"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    autoComplete="new-password"
                  />
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    color="primary"
                    size="large"
                    disabled={loading}
                    sx={{ mt: 3 }}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Reset Password'}
                  </Button>
                </form>
              </>
            )}

            {activeStep === 2 && (
              <>
                <Typography variant="body1" color="textSecondary" align="center" sx={{ mb: 4 }}>
                  Your password has been reset successfully
                </Typography>

                <Box display="flex" justifyContent="center">
                  <Link href="/login" passHref>
                    <Button variant="contained" color="primary" size="large">
                      Return to Login
                    </Button>
                  </Link>
                </Box>
              </>
            )}
          </CardContent>
        </Card>

        <Box mt={4} textAlign="center">
          <Link href="/login" passHref>
            <MuiLink variant="body2" color="primary" sx={{ cursor: 'pointer' }}>
              Back to Login
            </MuiLink>
          </Link>
        </Box>
      </Box>
    </Container>
  );
};

export default ResetPassword;
