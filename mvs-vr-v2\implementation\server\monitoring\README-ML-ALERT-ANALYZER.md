# ML Alert Analyzer

The ML Alert Analyzer is a service that uses machine learning to analyze alert patterns, reduce noise, add context to alerts, and correlate related alerts.

## Features

### 1. Alert Importance Prediction

The ML Alert Analyzer predicts the importance of alerts based on historical data, including:

- **Actionability**: How often alerts of this type lead to actions
- **False Positive Rate**: How often alerts of this type are false positives
- **Frequency**: How often alerts of this type occur
- **Time Anomaly**: Whether the alert occurred at an unusual time

### 2. Alert Correlation

The ML Alert Analyzer correlates related alerts to reduce alert fatigue, including:

- **Same Component**: Alerts from the same component
- **Same Service**: Alerts from the same service
- **Resource Exhaustion Cascade**: Related resource exhaustion alerts
- **Network Issues Cascade**: Related network issue alerts
- **Database Issues Cascade**: Related database issue alerts

### 3. Context-Aware Alerts

The ML Alert Analyzer adds context to alerts, including:

- **System State**: CPU, memory, disk usage, network metrics, etc.
- **Active Users**: Number of active users at the time of the alert
- **Recent Deployments**: Whether a deployment occurred recently
- **Maintenance Mode**: Whether the system is in maintenance mode

## API Endpoints

### `GET /api/alert-patterns`

Get alert patterns based on historical data.

**Response:**

```json
{
  "patterns": {
    "highCpuUsage": {
      "frequency": 5.2,
      "hourDistribution": [...],
      "dayDistribution": [...],
      "avgResolutionTime": 1200000,
      "medianResolutionTime": 900000,
      "totalAlerts": 120,
      "falsePositives": 15,
      "actionability": 0.85
    },
    ...
  }
}
```

### `GET /api/alert-context/:id`

Get context for a specific alert.

**Response:**

```json
{
  "alert": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "alertId": "highCpuUsage",
    "created_at": "2023-06-01T12:00:00Z",
    "context": {
      "importance": 0.85,
      "confidence": 0.92,
      "reasons": [
        "High actionability (85%)",
        "Low false positive rate (12%)",
        "Moderate frequency (5.2 per day)"
      ],
      "correlatedAlerts": [
        {
          "alert": {
            "id": "123e4567-e89b-12d3-a456-426614174001",
            "alertId": "highMemoryUsage",
            ...
          },
          "correlation": 0.8,
          "reason": "Resource exhaustion cascade"
        }
      ],
      "systemState": {
        "metrics": {
          "cpu_usage": 0.85,
          "memory_usage": 0.75,
          "disk_usage": 0.65,
          ...
        },
        "activeUsers": 120,
        "recentDeployment": {
          "id": "123e4567-e89b-12d3-a456-426614174002",
          "timestamp": "2023-06-01T11:00:00Z",
          ...
        },
        "maintenanceMode": false
      }
    }
  }
}
```

### `POST /api/process-alerts`

Process unprocessed alerts.

**Response:**

```json
{
  "success": true
}
```

## Integration with Alert Manager

The ML Alert Analyzer integrates with the Alert Manager to provide context-aware alerts and reduce alert fatigue.

### Alert Manager API Endpoints

#### `GET /api/alerts?include_ml=true`

Get alerts with ML context.

**Response:**

```json
{
  "alerts": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "alertId": "highCpuUsage",
      "created_at": "2023-06-01T12:00:00Z",
      "ml_processed": true,
      "ml_importance": 0.85,
      "ml_confidence": 0.92,
      "ml_reasons": [
        "High actionability (85%)",
        "Low false positive rate (12%)",
        "Moderate frequency (5.2 per day)"
      ],
      "ml_correlated_alerts": [
        "123e4567-e89b-12d3-a456-426614174001"
      ],
      "ml_system_state": {
        "metrics": {
          "cpu_usage": 0.85,
          "memory_usage": 0.75,
          "disk_usage": 0.65,
          ...
        },
        "activeUsers": 120,
        "recentDeployment": {
          "id": "123e4567-e89b-12d3-a456-426614174002",
          "timestamp": "2023-06-01T11:00:00Z",
          ...
        },
        "maintenanceMode": false
      }
    },
    ...
  ],
  "count": 10
}
```

#### `GET /api/alerts/:id/ml-context`

Get ML context for a specific alert.

**Response:**

```json
{
  "alert_id": "123e4567-e89b-12d3-a456-426614174000",
  "importance": 0.85,
  "confidence": 0.92,
  "reasons": [
    "High actionability (85%)",
    "Low false positive rate (12%)",
    "Moderate frequency (5.2 per day)"
  ],
  "correlated_alerts": [
    {
      "alert_id": "123e4567-e89b-12d3-a456-426614174001",
      "correlation": 0.8,
      "reason": "Resource exhaustion cascade"
    }
  ],
  "system_state": {
    "metrics": {
      "cpu_usage": 0.85,
      "memory_usage": 0.75,
      "disk_usage": 0.65,
      ...
    },
    "activeUsers": 120,
    "recentDeployment": {
      "id": "123e4567-e89b-12d3-a456-426614174002",
      "timestamp": "2023-06-01T11:00:00Z",
      ...
    },
    "maintenanceMode": false
  }
}
```

## Database Schema

### `alerts` Table

New columns added to the `alerts` table:

- `ml_processed` (boolean): Whether the alert has been processed by the ML Alert Analyzer
- `ml_importance` (float): The predicted importance of the alert (0-1)
- `ml_confidence` (float): The confidence of the prediction (0-1)
- `ml_reasons` (jsonb): The reasons for the prediction
- `ml_correlated_alerts` (jsonb): The IDs of correlated alerts
- `ml_system_state` (jsonb): The system state at the time of the alert

### `alert_actions` Table

New table to track actions taken on alerts:

- `id` (uuid): Primary key
- `alert_id` (uuid): Foreign key to the `alerts` table
- `action_type` (text): The type of action (e.g., acknowledge, resolve, escalate)
- `action_data` (jsonb): Additional data about the action
- `performed_by` (text): The user who performed the action
- `created_at` (timestamp): When the action was performed

### `system_metrics` Table

New table to track system metrics:

- `id` (uuid): Primary key
- `cpu_usage` (float): CPU usage (0-1)
- `memory_usage` (float): Memory usage (0-1)
- `disk_usage` (float): Disk usage (0-1)
- `network_in` (float): Network ingress (bytes/s)
- `network_out` (float): Network egress (bytes/s)
- `api_requests` (integer): Number of API requests
- `error_count` (integer): Number of errors
- `timestamp` (timestamp): When the metrics were collected

## Configuration

The ML Alert Analyzer can be configured with the following environment variables:

- `ML_ALERT_ANALYZER_PORT`: The port to listen on (default: 9102)
- `SUPABASE_URL`: The URL of the Supabase instance
- `SUPABASE_KEY`: The service key for the Supabase instance

## Deployment

The ML Alert Analyzer is deployed as a Docker container and integrated with the existing monitoring system.

### Docker Compose

```yaml
# ML Alert Analyzer
ml-alert-analyzer:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.ml-alert-analyzer
  container_name: mvs-vr-ml-alert-analyzer
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - ML_ALERT_ANALYZER_PORT=9102
  ports:
    - "9102:9102"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus
    - alert-manager-service
```

### Prometheus Configuration

```yaml
# ML Alert Analyzer
- job_name: "ml-alert-analyzer"
  static_configs:
    - targets: ["ml-alert-analyzer:9102"]
```
