import apiClient from './api-client';

/**
 * Interface for analytics data
 */
export interface AnalyticsData {
  totalViews: number;
  uniqueVisitors: number;
  averageSessionDuration: number;
  topAssets: Array<{
    id: string;
    name: string;
    views: number;
    averageTimeSpent: number;
  }>;
  viewsByDay: Array<{
    date: string;
    views: number;
  }>;
  viewsByDevice: Array<{
    device: string;
    count: number;
    percentage: number;
  }>;
}

/**
 * Interface for custom report
 */
export interface CustomReport {
  id: string;
  name: string;
  description?: string;
  dataSource: string;
  metrics: string[];
  filters: any[];
  groupBy?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface for report data
 */
export interface ReportData {
  columns: string[];
  rows: any[];
  totalRows: number;
}

/**
 * Interface for export options
 */
export interface ExportOptions {
  format: 'csv' | 'pdf' | 'excel';
  fileName?: string;
  includeHeaders?: boolean;
  orientation?: 'portrait' | 'landscape';
}

/**
 * Analytics service for vendor portal
 */
class AnalyticsService {
  /**
   * Get analytics data for a specific time range
   */
  public async getAnalyticsData(
    startDate: string,
    endDate: string
  ): Promise<AnalyticsData> {
    try {
      const response = await apiClient.get<AnalyticsData>('/vendor/analytics', {
        params: { start: startDate, end: endDate }
      });
      return response;
    } catch (error) {
      console.error('Failed to get analytics data:', error);
      throw error;
    }
  }

  /**
   * Get real-time analytics data
   */
  public async getRealTimeData(): Promise<any> {
    try {
      const response = await apiClient.get('/vendor/analytics/realtime');
      return response;
    } catch (error) {
      console.error('Failed to get real-time analytics data:', error);
      throw error;
    }
  }

  /**
   * Get list of saved custom reports
   */
  public async getCustomReports(): Promise<CustomReport[]> {
    try {
      const response = await apiClient.get<CustomReport[]>('/vendor/analytics/reports');
      return response;
    } catch (error) {
      console.error('Failed to get custom reports:', error);
      throw error;
    }
  }

  /**
   * Get a specific custom report
   */
  public async getCustomReport(id: string): Promise<CustomReport> {
    try {
      const response = await apiClient.get<CustomReport>(`/vendor/analytics/reports/${id}`);
      return response;
    } catch (error) {
      console.error(`Failed to get custom report ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new custom report
   */
  public async createCustomReport(report: Omit<CustomReport, 'id' | 'createdAt' | 'updatedAt'>): Promise<CustomReport> {
    try {
      const response = await apiClient.post<CustomReport>('/vendor/analytics/reports', report);
      return response;
    } catch (error) {
      console.error('Failed to create custom report:', error);
      throw error;
    }
  }

  /**
   * Update a custom report
   */
  public async updateCustomReport(id: string, report: Partial<CustomReport>): Promise<CustomReport> {
    try {
      const response = await apiClient.put<CustomReport>(`/vendor/analytics/reports/${id}`, report);
      return response;
    } catch (error) {
      console.error(`Failed to update custom report ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a custom report
   */
  public async deleteCustomReport(id: string): Promise<void> {
    try {
      await apiClient.delete(`/vendor/analytics/reports/${id}`);
    } catch (error) {
      console.error(`Failed to delete custom report ${id}:`, error);
      throw error;
    }
  }

  /**
   * Run a custom report
   */
  public async runCustomReport(
    reportId: string,
    page: number = 0,
    pageSize: number = 100
  ): Promise<ReportData> {
    try {
      const response = await apiClient.get<ReportData>(`/vendor/analytics/reports/${reportId}/run`, {
        params: { page, pageSize }
      });
      return response;
    } catch (error) {
      console.error(`Failed to run custom report ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Export analytics data
   */
  public async exportData(
    startDate: string,
    endDate: string,
    options: ExportOptions
  ): Promise<Blob> {
    try {
      const response = await apiClient.get('/vendor/analytics/export', {
        params: {
          start: startDate,
          end: endDate,
          format: options.format,
          fileName: options.fileName,
          includeHeaders: options.includeHeaders,
          orientation: options.orientation
        },
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('Failed to export analytics data:', error);
      throw error;
    }
  }

  /**
   * Export a custom report
   */
  public async exportCustomReport(
    reportId: string,
    options: ExportOptions
  ): Promise<Blob> {
    try {
      const response = await apiClient.get(`/vendor/analytics/reports/${reportId}/export`, {
        params: {
          format: options.format,
          fileName: options.fileName,
          includeHeaders: options.includeHeaders,
          orientation: options.orientation
        },
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error(`Failed to export custom report ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Get heatmap data
   */
  public async getHeatmapData(
    startDate: string,
    endDate: string,
    assetId?: string
  ): Promise<any> {
    try {
      const response = await apiClient.get('/vendor/analytics/heatmap', {
        params: {
          start: startDate,
          end: endDate,
          assetId
        }
      });
      return response;
    } catch (error) {
      console.error('Failed to get heatmap data:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const analyticsService = new AnalyticsService();
export default analyticsService;
