/**
 * Migration: Add A/B Testing Tables
 * 
 * This migration adds tables for A/B testing, including test management,
 * variant assignment, and conversion tracking.
 */

const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

async function run() {
  try {
    logger.info('Starting migration: Add A/B Testing Tables');
    
    // Create ab_tests table if it doesn't exist
    const { error: tableExistsError } = await supabase.rpc('table_exists', {
      table_name: 'ab_tests'
    });
    
    if (tableExistsError || tableExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'ab_tests',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'name', type: 'text', nullable: false },
          { name: 'description', type: 'text', nullable: true },
          { name: 'element_type', type: 'text', nullable: false },
          { name: 'element_path', type: 'text', nullable: false },
          { name: 'goal', type: 'text', nullable: false },
          { name: 'metrics', type: 'jsonb', nullable: true },
          { name: 'variants', type: 'jsonb', nullable: false },
          { name: 'traffic_allocation', type: 'float', nullable: false, default: 1.0 },
          { name: 'status', type: 'text', nullable: false },
          { name: 'start_date', type: 'timestamp with time zone', nullable: true },
          { name: 'end_date', type: 'timestamp with time zone', nullable: true },
          { name: 'winning_variant_id', type: 'text', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' },
          { name: 'updated_at', type: 'timestamp with time zone', nullable: true }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating ab_tests table', { error: createTableError.message });
      } else {
        logger.info('Created ab_tests table');
        
        // Create index on status
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'ab_tests',
          column_name: 'status'
        });
        
        if (indexError) {
          logger.error('Error creating index on status', { error: indexError.message });
        } else {
          logger.info('Created index on status');
        }
      }
    }
    
    // Create ab_test_assignments table if it doesn't exist
    const { error: assignmentsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'ab_test_assignments'
    });
    
    if (assignmentsExistsError || assignmentsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'ab_test_assignments',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'test_id', type: 'uuid', nullable: false },
          { name: 'user_id', type: 'uuid', nullable: false },
          { name: 'variant_id', type: 'text', nullable: false },
          { name: 'variant_name', type: 'text', nullable: false },
          { name: 'is_control', type: 'boolean', nullable: false, default: false },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating ab_test_assignments table', { error: createTableError.message });
      } else {
        logger.info('Created ab_test_assignments table');
        
        // Create index on test_id
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_assignments',
          column_name: 'test_id'
        });
        
        if (indexError1) {
          logger.error('Error creating index on test_id', { error: indexError1.message });
        } else {
          logger.info('Created index on test_id');
        }
        
        // Create index on user_id
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_assignments',
          column_name: 'user_id'
        });
        
        if (indexError2) {
          logger.error('Error creating index on user_id', { error: indexError2.message });
        } else {
          logger.info('Created index on user_id');
        }
        
        // Create unique index on test_id and user_id
        const { error: uniqueIndexError } = await supabase.rpc('create_unique_index', {
          table_name: 'ab_test_assignments',
          column_names: ['test_id', 'user_id']
        });
        
        if (uniqueIndexError) {
          logger.error('Error creating unique index on test_id and user_id', { error: uniqueIndexError.message });
        } else {
          logger.info('Created unique index on test_id and user_id');
        }
      }
    }
    
    // Create ab_test_conversions table if it doesn't exist
    const { error: conversionsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'ab_test_conversions'
    });
    
    if (conversionsExistsError || conversionsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'ab_test_conversions',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'test_id', type: 'uuid', nullable: false },
          { name: 'user_id', type: 'uuid', nullable: false },
          { name: 'variant_id', type: 'text', nullable: false },
          { name: 'variant_name', type: 'text', nullable: false },
          { name: 'goal_name', type: 'text', nullable: false },
          { name: 'conversion_data', type: 'jsonb', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating ab_test_conversions table', { error: createTableError.message });
      } else {
        logger.info('Created ab_test_conversions table');
        
        // Create index on test_id
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_conversions',
          column_name: 'test_id'
        });
        
        if (indexError1) {
          logger.error('Error creating index on test_id', { error: indexError1.message });
        } else {
          logger.info('Created index on test_id');
        }
        
        // Create index on user_id
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_conversions',
          column_name: 'user_id'
        });
        
        if (indexError2) {
          logger.error('Error creating index on user_id', { error: indexError2.message });
        } else {
          logger.info('Created index on user_id');
        }
        
        // Create index on goal_name
        const { error: indexError3 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_conversions',
          column_name: 'goal_name'
        });
        
        if (indexError3) {
          logger.error('Error creating index on goal_name', { error: indexError3.message });
        } else {
          logger.info('Created index on goal_name');
        }
      }
    }
    
    // Create ab_test_results table if it doesn't exist
    const { error: resultsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'ab_test_results'
    });
    
    if (resultsExistsError || resultsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'ab_test_results',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'test_id', type: 'uuid', nullable: false },
          { name: 'date', type: 'date', nullable: false },
          { name: 'variant_id', type: 'text', nullable: false },
          { name: 'variant_name', type: 'text', nullable: false },
          { name: 'user_count', type: 'integer', nullable: false },
          { name: 'conversion_count', type: 'integer', nullable: false },
          { name: 'conversion_rate', type: 'float', nullable: false },
          { name: 'improvement', type: 'float', nullable: true },
          { name: 'confidence', type: 'float', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating ab_test_results table', { error: createTableError.message });
      } else {
        logger.info('Created ab_test_results table');
        
        // Create index on test_id
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_results',
          column_name: 'test_id'
        });
        
        if (indexError1) {
          logger.error('Error creating index on test_id', { error: indexError1.message });
        } else {
          logger.info('Created index on test_id');
        }
        
        // Create index on date
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_results',
          column_name: 'date'
        });
        
        if (indexError2) {
          logger.error('Error creating index on date', { error: indexError2.message });
        } else {
          logger.info('Created index on date');
        }
        
        // Create unique index on test_id, date, and variant_id
        const { error: uniqueIndexError } = await supabase.rpc('create_unique_index', {
          table_name: 'ab_test_results',
          column_names: ['test_id', 'date', 'variant_id']
        });
        
        if (uniqueIndexError) {
          logger.error('Error creating unique index on test_id, date, and variant_id', { error: uniqueIndexError.message });
        } else {
          logger.info('Created unique index on test_id, date, and variant_id');
        }
      }
    }
    
    // Create ab_test_long_term_impact table if it doesn't exist
    const { error: impactExistsError } = await supabase.rpc('table_exists', {
      table_name: 'ab_test_long_term_impact'
    });
    
    if (impactExistsError || impactExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'ab_test_long_term_impact',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'test_id', type: 'uuid', nullable: false },
          { name: 'period', type: 'text', nullable: false },
          { name: 'start_date', type: 'date', nullable: false },
          { name: 'end_date', type: 'date', nullable: false },
          { name: 'winning_variant_id', type: 'text', nullable: false },
          { name: 'control_variant_id', type: 'text', nullable: false },
          { name: 'engagement_diff', type: 'jsonb', nullable: false },
          { name: 'business_metrics', type: 'jsonb', nullable: false },
          { name: 'impact_score', type: 'float', nullable: false },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating ab_test_long_term_impact table', { error: createTableError.message });
      } else {
        logger.info('Created ab_test_long_term_impact table');
        
        // Create index on test_id
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'ab_test_long_term_impact',
          column_name: 'test_id'
        });
        
        if (indexError1) {
          logger.error('Error creating index on test_id', { error: indexError1.message });
        } else {
          logger.info('Created index on test_id');
        }
        
        // Create unique index on test_id and period
        const { error: uniqueIndexError } = await supabase.rpc('create_unique_index', {
          table_name: 'ab_test_long_term_impact',
          column_names: ['test_id', 'period']
        });
        
        if (uniqueIndexError) {
          logger.error('Error creating unique index on test_id and period', { error: uniqueIndexError.message });
        } else {
          logger.info('Created unique index on test_id and period');
        }
      }
    }
    
    logger.info('Migration completed: Add A/B Testing Tables');
  } catch (error) {
    logger.error('Error in migration', { error: error.message });
  }
}

// Run migration if called directly
if (require.main === module) {
  run().then(() => {
    process.exit(0);
  }).catch(error => {
    logger.error('Migration failed', { error: error.message });
    process.exit(1);
  });
}

module.exports = { run };
