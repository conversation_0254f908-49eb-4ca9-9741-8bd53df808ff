/**
 * Simple script to convert a Jest test file to Vitest
 */

const fs = require('fs');
const path = require('path');

// Get the file path from command line arguments
const filePath = process.argv[2];

if (!filePath) {
  console.error('Please provide a file path');
  process.exit(1);
}

// Read the file
try {
  const fullPath = path.resolve(process.cwd(), filePath);
  console.log(`Converting ${fullPath}...`);

  if (!fs.existsSync(fullPath)) {
    console.error(`File not found: ${fullPath}`);
    process.exit(1);
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  const originalContent = content;

  // Replace Jest imports with Vitest
  content = content.replace(
    /import\s+\{([^}]*?)jest([^}]*?)\}\s+from\s+['"]@jest\/globals['"];?/g,
    (_, before, after) => `import {${before}vi${after}} from 'vitest';`,
  );

  content = content.replace(
    /import\s+\{([^}]*?)\}\s+from\s+['"]@jest\/globals['"];?/g,
    (_, imports) => `import {${imports}} from 'vitest';`,
  );

  // Replace Jest with Vitest
  content = content.replace(/jest\./g, 'vi.');
  content = content.replace(/import\s+.*\s+from\s+['"]jest['"];?/g, "import { vi } from 'vitest';");

  // Replace standalone jest references (like in type assertions)
  content = content.replace(/\bas\s+jest\b/g, 'as ReturnType<typeof vi.fn>');
  content = content.replace(
    /\(createClient\s+as\s+jest\.Mock\)/g,
    '(createClient as ReturnType<typeof vi.fn>)',
  );

  // Replace type casting
  content = content.replace(/as\s+jest\.Mock/g, 'as ReturnType<typeof vi.fn>');

  // Fix ioredis mocks
  content = content.replace(
    /vi\.mock\(['"]ioredis['"],\s*\(\)\s*=>\s*\{\s*return\s+vi\.fn\(\)\.mockImplementation\(\(\)\s*=>\s*\(\{([^}]*?)\}\)\);\s*\}\);/gs,
    (_, mockMethods) => `vi.mock('ioredis', () => {
  const RedisMock = vi.fn().mockImplementation(() => ({${mockMethods}}));
  return { default: RedisMock };
});`,
  );

  // Convert require to import
  content = content.replace(
    /const\s+\{([^}]*?)\}\s+=\s+require\(['"]([^'"]*?)['"]\);/g,
    (_, imports, modulePath) => {
      // Add .js extension to relative imports if needed
      if (modulePath.startsWith('.') && !path.extname(modulePath)) {
        modulePath = `${modulePath}.js`;
      }
      return `import { ${imports} } from '${modulePath}';`;
    },
  );

  // Check if content changed
  if (content !== originalContent) {
    // Create a backup
    fs.writeFileSync(`${fullPath}.bak`, originalContent, 'utf8');
    console.log(`Created backup: ${fullPath}.bak`);

    // Write the converted file
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ Successfully converted ${fullPath}`);
  } else {
    console.log(`⏭️ No changes needed for ${fullPath}`);
  }
} catch (error) {
  console.error(`Error: ${error.message}`);
  process.exit(1);
}
