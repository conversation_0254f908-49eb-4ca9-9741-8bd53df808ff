import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const ROOT_DIR = resolve(__dirname, '../..');

interface ModuleConfig {
  prefix: string;
  path: string;
  isExternal?: boolean;
}

const moduleConfigs: ModuleConfig[] = [
  // Node packages
  { prefix: 'vitest', path: 'node_modules/vitest', isExternal: true },
  { prefix: '@vitejs', path: 'node_modules/@vitejs', isExternal: true },
  { prefix: '@vue', path: 'node_modules/@vue', isExternal: true },
  { prefix: '@testing-library', path: 'node_modules/@testing-library', isExternal: true },

  // Project paths
  { prefix: '@', path: 'src' },
  { prefix: '@directus', path: 'directus/extensions' },
  { prefix: '@shared', path: 'shared' },
  { prefix: '@services', path: 'services' },
  { prefix: '@tests', path: 'tests' },
  { prefix: '@setup', path: 'tests/setup' },
];

export function resolveModulePath(importPath: string): string {
  // Handle node built-in modules
  if (importPath.startsWith('node:')) {
    return importPath;
  }

  // Find matching module config
  for (const config of moduleConfigs) {
    if (importPath.startsWith(config.prefix)) {
      const relativePath = importPath.replace(config.prefix, '');
      const basePath = resolve(ROOT_DIR, config.path);
      return config.isExternal
        ? resolve(basePath, relativePath)
        : resolve(basePath, `${relativePath}.ts`);
    }
  }

  // Handle local relative imports
  if (importPath.startsWith('.')) {
    const fullPath = resolve(dirname(fileURLToPath(import.meta.url)), importPath);
    return `${fullPath}.ts`;
  }

  // Handle node_modules imports
  return resolve(ROOT_DIR, 'node_modules', importPath);
}

export function createModuleResolver() {
  return {
    resolveId(importPath: string) {
      return resolveModulePath(importPath);
    },
  };
}

export default createModuleResolver;
