import { resolve, join } from 'path';
import { fileURLToPath } from 'url';

const __dirname = fileURLToPath(new URL('.', import.meta.url));
const ROOT_DIR = resolve(__dirname, '../..');

interface ModuleConfig {
  nodeModules: string[];
  localModules: {
    [key: string]: string;
  };
}

const config: ModuleConfig = {
  nodeModules: ['vitest', '@vitejs/plugin-vue', '@vue/test-utils', '@testing-library/jest-dom'],
  localModules: {
    '@': 'src',
    '@directus': 'directus/extensions',
    '@shared': 'shared',
    '@services': 'services',
    '@tests': 'tests',
    '@setup': 'tests/setup',
  },
};

export function resolveModule(moduleName: string): string {
  // Handle node modules
  if (config.nodeModules.includes(moduleName) || moduleName.startsWith('@types/')) {
    return join(ROOT_DIR, 'node_modules', moduleName);
  }

  // Handle local module aliases
  for (const [alias, path] of Object.entries(config.localModules)) {
    if (moduleName.startsWith(alias)) {
      const relativePath = moduleName.replace(alias, path);
      return join(ROOT_DIR, relativePath);
    }
  }

  // Handle relative imports
  if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
    return join(__dirname, moduleName);
  }

  throw new Error(`Unable to resolve module: ${moduleName}`);
}

export function importModule(moduleName: string) {
  const resolvedPath = resolveModule(moduleName);
  return import(resolvedPath);
}

export default {
  resolveModule,
  importModule,
};
