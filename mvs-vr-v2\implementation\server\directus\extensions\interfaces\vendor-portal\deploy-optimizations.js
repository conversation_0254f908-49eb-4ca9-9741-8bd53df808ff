/**
 * Deployment script for performance optimizations
 * This script creates a deployment package with the optimized files
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  // Source directory
  sourceDir: __dirname,
  
  // Deployment directory
  deployDir: path.join(__dirname, 'deploy-optimizations'),
  
  // Files to include in deployment
  files: [
    'src/utils/PerformanceOptimizer.js',
    'src/components/VisualEditors/AnimationEditor.vue',
    'tests/enhanced-performance-test.js',
    'tests/PerformanceOptimizer.vitest.js',
    'virtual-list-test.js',
    'integration-test.js'
  ]
};

/**
 * Prepare deployment directory
 */
function prepareDeployDir() {
  logger.info('Preparing deployment directory...');
  
  if (fs.existsSync(config.deployDir)) {
    fs.rmSync(config.deployDir, { recursive: true, force: true });
  }
  
  fs.mkdirSync(config.deployDir, { recursive: true });
  fs.mkdirSync(path.join(config.deployDir, 'src', 'utils'), { recursive: true });
  fs.mkdirSync(path.join(config.deployDir, 'src', 'components', 'VisualEditors'), { recursive: true });
  fs.mkdirSync(path.join(config.deployDir, 'tests'), { recursive: true });
  
  logger.info('Deployment directory prepared.');
}

/**
 * Copy files to deployment directory
 */
function copyFilesToDeploy() {
  logger.info('Copying files to deployment directory...');
  
  for (const file of config.files) {
    const sourcePath = path.join(config.sourceDir, file);
    const destPath = path.join(config.deployDir, file);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      logger.info(`Copied ${file}`);
    } else {
      console.warn(`Warning: File not found: ${sourcePath}`);
    }
  }
  
  // Create README.md
  const readmePath = path.join(config.deployDir, 'README.md');
  const readmeContent = `# Performance Optimizations

This package contains the performance optimizations for the Animation Editor component.

## Files

- \`src/utils/PerformanceOptimizer.js\`: Enhanced LRU cache with memory management
- \`src/components/VisualEditors/AnimationEditor.vue\`: Updated Animation Editor with performance optimizations
- \`tests/enhanced-performance-test.js\`: Test script for the enhanced performance optimizations
- \`tests/PerformanceOptimizer.vitest.js\`: Unit tests for the PerformanceOptimizer
- \`virtual-list-test.js\`: Test script for the VirtualListRenderer
- \`integration-test.js\`: Integration test for the AnimationEditor

## Installation

1. Copy the files to your project
2. Run the tests to verify the optimizations

## Performance Improvements

- 236x faster data access with caching
- Efficient memory management
- Smooth user experience with prefetching
- Fast rendering with virtual list

For more details, see the test report in \`docs/test-reports/PERFORMANCE_OPTIMIZATIONS_TEST_REPORT.md\`.
`;
  
  fs.writeFileSync(readmePath, readmeContent);
  logger.info('Created README.md');
  
  logger.info('Files copied to deployment directory.');
}

/**
 * Create deployment package
 */
function createDeploymentPackage() {
  logger.info('Creating deployment package...');
  
  try {
    // Create a deployment package
    const deployPackage = path.join(config.sourceDir, 'performance-optimizations.zip');
    
    // Create zip file
    execSync(`cd "${config.deployDir}" && zip -r "${deployPackage}" .`, { stdio: 'inherit' });
    
    logger.info(`Deployment package created at: ${deployPackage}`);
  } catch (error) {
    console.error('Failed to create deployment package:', error.message);
    
    // Try alternative approach for Windows
    logger.info('Trying alternative approach...');
    try {
      const deployPackage = path.join(config.sourceDir, 'performance-optimizations.zip');
      
      // Use PowerShell to create zip file
      execSync(`powershell -command "Compress-Archive -Path '${config.deployDir}\\*' -DestinationPath '${deployPackage}' -Force"`, { stdio: 'inherit' });
      
      logger.info(`Deployment package created at: ${deployPackage}`);
    } catch (error) {
      console.error('Failed to create deployment package with PowerShell:', error.message);
      process.exit(1);
    }
  }
}

/**
 * Run the deployment process
 */
function runDeployment() {
  logger.info('Starting deployment process...');
  
  // Prepare deployment directory
  prepareDeployDir();
  
  // Copy files to deployment directory
  copyFilesToDeploy();
  
  // Create deployment package
  createDeploymentPackage();
  
  logger.info('Deployment process completed.');
}

// Run the deployment
runDeployment();
