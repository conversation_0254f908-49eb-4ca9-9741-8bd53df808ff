/**
 * Alert Correlation Service
 * 
 * This service correlates alerts from different monitoring systems to reduce
 * alert fatigue and identify root causes.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { EventEmitter } = require('events');
const { Logger } = require('../integration/logger');
const { Counter, Gauge } = require('prom-client');

// Promisify functions
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);

// Create logger
const logger = new Logger();

// Create metrics
const alertsReceived = new Counter({
  name: 'alerts_received_total',
  help: 'Total number of alerts received',
  labelNames: ['source', 'severity', 'type']
});

const alertsCorrelated = new Counter({
  name: 'alerts_correlated_total',
  help: 'Total number of alerts correlated',
  labelNames: ['source', 'severity', 'type']
});

const alertsForwarded = new Counter({
  name: 'alerts_forwarded_total',
  help: 'Total number of alerts forwarded',
  labelNames: ['source', 'severity', 'type']
});

const alertsSuppressed = new Counter({
  name: 'alerts_suppressed_total',
  help: 'Total number of alerts suppressed',
  labelNames: ['source', 'severity', 'type']
});

const activeIncidents = new Gauge({
  name: 'active_incidents',
  help: 'Number of active incidents',
  labelNames: ['severity']
});

// Configuration
const config = {
  correlationRules: process.env.CORRELATION_RULES_PATH || path.join(__dirname, '../../config/correlation-rules.json'),
  alertHistoryPath: process.env.ALERT_HISTORY_PATH || path.join(__dirname, '../../logs/alerts'),
  incidentPath: process.env.INCIDENT_PATH || path.join(__dirname, '../../logs/incidents'),
  correlationWindowMs: parseInt(process.env.CORRELATION_WINDOW_MS || '300000', 10), // 5 minutes
  suppressionWindowMs: parseInt(process.env.SUPPRESSION_WINDOW_MS || '600000', 10), // 10 minutes
  maxAlertsPerIncident: parseInt(process.env.MAX_ALERTS_PER_INCIDENT || '100', 10)
};

/**
 * Alert correlation service
 */
class AlertCorrelationService extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      ...config,
      ...options
    };
    
    this.rules = [];
    this.alertHistory = [];
    this.activeIncidents = new Map();
    
    // Create directories
    mkdirAsync(this.options.alertHistoryPath, { recursive: true }).catch(err => {
      logger.error(`Error creating alert history directory: ${err.message}`, { error: err });
    });
    
    mkdirAsync(this.options.incidentPath, { recursive: true }).catch(err => {
      logger.error(`Error creating incident directory: ${err.message}`, { error: err });
    });
    
    // Load correlation rules
    this.loadRules().catch(err => {
      logger.error(`Error loading correlation rules: ${err.message}`, { error: err });
    });
    
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupAlertHistory();
    }, 60000); // Run every minute
    
    logger.info('Alert correlation service initialized');
  }
  
  /**
   * Load correlation rules
   * @returns {Promise<void>}
   */
  async loadRules() {
    try {
      const data = await readFileAsync(this.options.correlationRules, 'utf8');
      this.rules = JSON.parse(data);
      
      logger.info(`Loaded ${this.rules.length} correlation rules`);
    } catch (error) {
      logger.error(`Error loading correlation rules: ${error.message}`, { error });
      
      // Use default rules
      this.rules = [
        {
          id: 'default-cpu-memory',
          name: 'CPU and Memory Correlation',
          description: 'Correlate CPU and memory alerts for the same host',
          conditions: [
            {
              type: 'CPU_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            },
            {
              type: 'MEMORY_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            }
          ],
          timeWindowMs: 300000, // 5 minutes
          groupBy: ['host'],
          rootCause: 'CPU_HIGH',
          action: 'CORRELATE',
          incidentType: 'PERFORMANCE',
          incidentSeverity: 'HIGH'
        },
        {
          id: 'default-disk-io',
          name: 'Disk I/O Correlation',
          description: 'Correlate disk I/O alerts for the same host',
          conditions: [
            {
              type: 'DISK_IO_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            },
            {
              type: 'DISK_LATENCY_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            }
          ],
          timeWindowMs: 300000, // 5 minutes
          groupBy: ['host'],
          rootCause: 'DISK_IO_HIGH',
          action: 'CORRELATE',
          incidentType: 'PERFORMANCE',
          incidentSeverity: 'MEDIUM'
        },
        {
          id: 'default-network',
          name: 'Network Correlation',
          description: 'Correlate network alerts for the same host',
          conditions: [
            {
              type: 'NETWORK_TRAFFIC_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            },
            {
              type: 'NETWORK_ERRORS_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            }
          ],
          timeWindowMs: 300000, // 5 minutes
          groupBy: ['host'],
          rootCause: 'NETWORK_TRAFFIC_HIGH',
          action: 'CORRELATE',
          incidentType: 'NETWORK',
          incidentSeverity: 'MEDIUM'
        },
        {
          id: 'default-database',
          name: 'Database Correlation',
          description: 'Correlate database alerts',
          conditions: [
            {
              type: 'DATABASE_CONNECTIONS_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            },
            {
              type: 'DATABASE_QUERY_SLOW',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            }
          ],
          timeWindowMs: 300000, // 5 minutes
          groupBy: ['database'],
          rootCause: 'DATABASE_CONNECTIONS_HIGH',
          action: 'CORRELATE',
          incidentType: 'DATABASE',
          incidentSeverity: 'HIGH'
        },
        {
          id: 'default-api',
          name: 'API Correlation',
          description: 'Correlate API alerts',
          conditions: [
            {
              type: 'API_LATENCY_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            },
            {
              type: 'API_ERROR_RATE_HIGH',
              source: 'any',
              severity: ['WARNING', 'CRITICAL']
            }
          ],
          timeWindowMs: 300000, // 5 minutes
          groupBy: ['service'],
          rootCause: 'API_LATENCY_HIGH',
          action: 'CORRELATE',
          incidentType: 'SERVICE',
          incidentSeverity: 'HIGH'
        }
      ];
      
      logger.info(`Using ${this.rules.length} default correlation rules`);
    }
  }
  
  /**
   * Save correlation rules
   * @returns {Promise<void>}
   */
  async saveRules() {
    try {
      await writeFileAsync(this.options.correlationRules, JSON.stringify(this.rules, null, 2));
      logger.info(`Saved ${this.rules.length} correlation rules`);
    } catch (error) {
      logger.error(`Error saving correlation rules: ${error.message}`, { error });
      throw error;
    }
  }
  
  /**
   * Process alert
   * @param {Object} alert - Alert object
   * @returns {Promise<Object>} Processing result
   */
  async processAlert(alert) {
    // Validate alert
    if (!this.validateAlert(alert)) {
      logger.warn('Invalid alert received', { alert });
      return { status: 'INVALID', alert };
    }
    
    // Track alert
    alertsReceived.inc({
      source: alert.source,
      severity: alert.severity,
      type: alert.type
    });
    
    // Add timestamp if not present
    if (!alert.timestamp) {
      alert.timestamp = new Date().toISOString();
    }
    
    // Add to alert history
    this.alertHistory.push(alert);
    
    // Check if alert should be suppressed
    if (this.shouldSuppressAlert(alert)) {
      alertsSuppressed.inc({
        source: alert.source,
        severity: alert.severity,
        type: alert.type
      });
      
      logger.info('Alert suppressed', { alert });
      
      return { status: 'SUPPRESSED', alert };
    }
    
    // Check if alert matches any correlation rule
    const matchingRules = this.findMatchingRules(alert);
    
    if (matchingRules.length === 0) {
      // No matching rules, forward alert
      alertsForwarded.inc({
        source: alert.source,
        severity: alert.severity,
        type: alert.type
      });
      
      logger.info('Alert forwarded', { alert });
      
      // Emit alert event
      this.emit('alert', alert);
      
      return { status: 'FORWARDED', alert };
    }
    
    // Process matching rules
    const results = [];
    
    for (const rule of matchingRules) {
      const result = await this.processRule(rule, alert);
      results.push(result);
      
      if (result.status === 'CORRELATED') {
        alertsCorrelated.inc({
          source: alert.source,
          severity: alert.severity,
          type: alert.type
        });
      }
    }
    
    // If any rule resulted in correlation, return that result
    const correlatedResult = results.find(result => result.status === 'CORRELATED');
    
    if (correlatedResult) {
      logger.info('Alert correlated', { alert, incidentId: correlatedResult.incidentId });
      return correlatedResult;
    }
    
    // No correlation, forward alert
    alertsForwarded.inc({
      source: alert.source,
      severity: alert.severity,
      type: alert.type
    });
    
    logger.info('Alert forwarded', { alert });
    
    // Emit alert event
    this.emit('alert', alert);
    
    return { status: 'FORWARDED', alert };
  }
  
  /**
   * Validate alert
   * @param {Object} alert - Alert object
   * @returns {boolean} Whether alert is valid
   */
  validateAlert(alert) {
    // Check required fields
    if (!alert.id || !alert.type || !alert.source || !alert.severity) {
      return false;
    }
    
    // Check severity
    const validSeverities = ['INFO', 'WARNING', 'CRITICAL', 'ERROR'];
    if (!validSeverities.includes(alert.severity)) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Check if alert should be suppressed
   * @param {Object} alert - Alert object
   * @returns {boolean} Whether alert should be suppressed
   */
  shouldSuppressAlert(alert) {
    // Check if similar alert was received recently
    const now = new Date();
    const suppressionWindow = this.options.suppressionWindowMs;
    
    const similarAlerts = this.alertHistory.filter(a => {
      // Check if alert is similar
      const isSimilar = a.type === alert.type &&
                        a.source === alert.source &&
                        a.severity === alert.severity &&
                        a.host === alert.host;
      
      // Check if alert is within suppression window
      const timestamp = new Date(a.timestamp);
      const isRecent = (now - timestamp) < suppressionWindow;
      
      return isSimilar && isRecent && a.id !== alert.id;
    });
    
    return similarAlerts.length > 0;
  }
  
  /**
   * Find matching correlation rules
   * @param {Object} alert - Alert object
   * @returns {Array<Object>} Matching rules
   */
  findMatchingRules(alert) {
    return this.rules.filter(rule => {
      // Check if any condition matches the alert
      return rule.conditions.some(condition => {
        // Check type
        if (condition.type !== alert.type && condition.type !== 'any') {
          return false;
        }
        
        // Check source
        if (condition.source !== alert.source && condition.source !== 'any') {
          return false;
        }
        
        // Check severity
        if (condition.severity && !condition.severity.includes(alert.severity) && !condition.severity.includes('any')) {
          return false;
        }
        
        return true;
      });
    });
  }
  
  /**
   * Process correlation rule
   * @param {Object} rule - Correlation rule
   * @param {Object} alert - Alert object
   * @returns {Promise<Object>} Processing result
   */
  async processRule(rule, alert) {
    // Get group key
    const groupKey = this.getGroupKey(rule, alert);
    
    // Check if incident already exists for this group
    const incidentId = `${rule.id}-${groupKey}`;
    
    if (this.activeIncidents.has(incidentId)) {
      // Incident exists, add alert to incident
      const incident = this.activeIncidents.get(incidentId);
      
      // Check if max alerts reached
      if (incident.alerts.length >= this.options.maxAlertsPerIncident) {
        logger.warn(`Max alerts reached for incident ${incidentId}`, { incidentId });
        return { status: 'MAX_ALERTS_REACHED', incidentId };
      }
      
      // Add alert to incident
      incident.alerts.push(alert);
      incident.lastUpdated = new Date().toISOString();
      
      // Update incident
      await this.updateIncident(incident);
      
      return { status: 'CORRELATED', incidentId, incident };
    }
    
    // Check if all conditions are met
    const conditionsMet = this.checkConditions(rule, alert);
    
    if (conditionsMet) {
      // Create new incident
      const incident = {
        id: incidentId,
        ruleId: rule.id,
        ruleName: rule.name,
        groupKey,
        type: rule.incidentType || 'UNKNOWN',
        severity: rule.incidentSeverity || 'MEDIUM',
        status: 'OPEN',
        created: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        rootCause: rule.rootCause,
        alerts: [alert]
      };
      
      // Add incident to active incidents
      this.activeIncidents.set(incidentId, incident);
      
      // Update active incidents metric
      activeIncidents.inc({ severity: incident.severity });
      
      // Save incident
      await this.saveIncident(incident);
      
      // Emit incident event
      this.emit('incident', incident);
      
      return { status: 'CORRELATED', incidentId, incident };
    }
    
    // Conditions not met, forward alert
    return { status: 'FORWARDED', alert };
  }
  
  /**
   * Get group key for alert
   * @param {Object} rule - Correlation rule
   * @param {Object} alert - Alert object
   * @returns {string} Group key
   */
  getGroupKey(rule, alert) {
    if (!rule.groupBy || rule.groupBy.length === 0) {
      return 'default';
    }
    
    const groupValues = rule.groupBy.map(field => {
      return alert[field] || 'unknown';
    });
    
    return groupValues.join('-');
  }
  
  /**
   * Check if all conditions are met
   * @param {Object} rule - Correlation rule
   * @param {Object} alert - Alert object
   * @returns {boolean} Whether all conditions are met
   */
  checkConditions(rule, alert) {
    // Get time window
    const timeWindowMs = rule.timeWindowMs || this.options.correlationWindowMs;
    
    // Get group key
    const groupKey = this.getGroupKey(rule, alert);
    
    // Get alerts in time window
    const now = new Date();
    const alertsInWindow = this.alertHistory.filter(a => {
      // Check if alert is within time window
      const timestamp = new Date(a.timestamp);
      const isRecent = (now - timestamp) < timeWindowMs;
      
      // Check if alert is in the same group
      const alertGroupKey = this.getGroupKey(rule, a);
      const isSameGroup = alertGroupKey === groupKey;
      
      return isRecent && isSameGroup;
    });
    
    // Check if all conditions are met
    const conditionsMet = rule.conditions.every(condition => {
      // Find alerts matching this condition
      const matchingAlerts = alertsInWindow.filter(a => {
        // Check type
        if (condition.type !== a.type && condition.type !== 'any') {
          return false;
        }
        
        // Check source
        if (condition.source !== a.source && condition.source !== 'any') {
          return false;
        }
        
        // Check severity
        if (condition.severity && !condition.severity.includes(a.severity) && !condition.severity.includes('any')) {
          return false;
        }
        
        return true;
      });
      
      return matchingAlerts.length > 0;
    });
    
    return conditionsMet;
  }
  
  /**
   * Save incident
   * @param {Object} incident - Incident object
   * @returns {Promise<void>}
   */
  async saveIncident(incident) {
    try {
      const incidentPath = path.join(this.options.incidentPath, `${incident.id}.json`);
      await writeFileAsync(incidentPath, JSON.stringify(incident, null, 2));
      logger.info(`Saved incident ${incident.id}`);
    } catch (error) {
      logger.error(`Error saving incident ${incident.id}: ${error.message}`, { error });
      throw error;
    }
  }
  
  /**
   * Update incident
   * @param {Object} incident - Incident object
   * @returns {Promise<void>}
   */
  async updateIncident(incident) {
    try {
      const incidentPath = path.join(this.options.incidentPath, `${incident.id}.json`);
      await writeFileAsync(incidentPath, JSON.stringify(incident, null, 2));
      logger.info(`Updated incident ${incident.id}`);
    } catch (error) {
      logger.error(`Error updating incident ${incident.id}: ${error.message}`, { error });
      throw error;
    }
  }
  
  /**
   * Close incident
   * @param {string} incidentId - Incident ID
   * @param {string} resolution - Resolution
   * @returns {Promise<Object>} Closed incident
   */
  async closeIncident(incidentId, resolution) {
    try {
      // Check if incident exists
      if (!this.activeIncidents.has(incidentId)) {
        throw new Error(`Incident ${incidentId} not found`);
      }
      
      // Get incident
      const incident = this.activeIncidents.get(incidentId);
      
      // Update incident
      incident.status = 'CLOSED';
      incident.resolution = resolution;
      incident.closedAt = new Date().toISOString();
      
      // Remove from active incidents
      this.activeIncidents.delete(incidentId);
      
      // Update active incidents metric
      activeIncidents.dec({ severity: incident.severity });
      
      // Save incident
      await this.updateIncident(incident);
      
      // Emit incident closed event
      this.emit('incidentClosed', incident);
      
      return incident;
    } catch (error) {
      logger.error(`Error closing incident ${incidentId}: ${error.message}`, { error });
      throw error;
    }
  }
  
  /**
   * Clean up alert history
   */
  cleanupAlertHistory() {
    const now = new Date();
    const maxAge = Math.max(this.options.correlationWindowMs, this.options.suppressionWindowMs);
    
    // Remove old alerts
    this.alertHistory = this.alertHistory.filter(alert => {
      const timestamp = new Date(alert.timestamp);
      return (now - timestamp) < maxAge;
    });
    
    logger.debug(`Cleaned up alert history, ${this.alertHistory.length} alerts remaining`);
  }
  
  /**
   * Stop service
   */
  stop() {
    clearInterval(this.cleanupInterval);
    logger.info('Alert correlation service stopped');
  }
}

// Export singleton instance
let instance = null;

/**
 * Get alert correlation service instance
 * @param {Object} options - Service options
 * @returns {AlertCorrelationService} Service instance
 */
function getAlertCorrelationService(options = {}) {
  if (!instance) {
    instance = new AlertCorrelationService(options);
  }
  
  return instance;
}

module.exports = {
  AlertCorrelationService,
  getAlertCorrelationService
};
