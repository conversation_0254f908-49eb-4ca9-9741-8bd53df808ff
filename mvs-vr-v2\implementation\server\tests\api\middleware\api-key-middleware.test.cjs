/**
 * API Key Middleware Tests (CommonJS)
 */

const { describe, it, expect } = require('vitest');
const apiKeyMiddleware = require('../../../api/middleware/api-key-middleware.js');

// Extract the functions we want to test
const { hasRequiredPermissions, hasRequiredScopes } = apiKeyMiddleware;

describe('API Key Middleware', () => {
  // Tests for hasRequiredPermissions
  describe('hasRequiredPermissions', () => {
    it('should return true if no permissions are required', () => {
      expect(hasRequiredPermissions(['test:read'], [])).toBe(true);
    });

    it('should return true if key has wildcard permission', () => {
      expect(hasRequiredPermissions(['*'], ['test:read'])).toBe(true);
    });

    it('should return true if key has all required permissions', () => {
      expect(hasRequiredPermissions(['test:read', 'test:write'], ['test:read'])).toBe(true);
    });

    it('should return false if key does not have required permissions', () => {
      expect(hasRequiredPermissions(['test:read'], ['test:write'])).toBe(false);
    });
  });

  // Tests for hasRequiredScopes
  describe('hasRequiredScopes', () => {
    it('should return true if no scopes are required', () => {
      expect(hasRequiredScopes(['api'], [])).toBe(true);
    });

    it('should return true if key has wildcard scope', () => {
      expect(hasRequiredScopes(['*'], ['api'])).toBe(true);
    });

    it('should return true if key has all required scopes', () => {
      expect(hasRequiredScopes(['api', 'admin'], ['api'])).toBe(true);
    });

    it('should return false if key does not have required scopes', () => {
      expect(hasRequiredScopes(['api'], ['admin'])).toBe(false);
    });
  });
});
