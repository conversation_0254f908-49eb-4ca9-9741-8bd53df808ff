/**
 * Test Recovery Procedures Against RTOs
 * 
 * This script tests recovery procedures for system components and measures
 * recovery time against defined RTOs.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const rtoMeasurement = require('./rto-measurement');
const rtoDefinitions = require('./rto-definitions');
const { logger } = require('../shared/utils/logger');

// Recovery scripts directory
const RECOVERY_SCRIPTS_DIR = path.join(__dirname, 'procedures');

/**
 * Test recovery for a specific component
 * @param {string} componentPath - Dot notation path to component in rtoDefinitions
 * @param {boolean} simulate - Whether to simulate recovery instead of executing actual scripts
 * @returns {Object} Recovery measurement
 */
async function testRecovery(componentPath, simulate = false) {
  const component = rtoMeasurement.getComponentByPath(componentPath);
  
  if (!component) {
    throw new Error(`Component not found: ${componentPath}`);
  }

  logger.info(`Testing recovery for ${componentPath} (RTO: ${component.rto} minutes);`);
  logger.info(`Description: ${component.description}`);
  logger.info(`Priority: ${component.priority}`);
  
  if (component.dependencies && component.dependencies.length > 0) {
    logger.info(`Dependencies: ${component.dependencies.join(', ');}`);
  }

  const incidentId = `test-${Date.now()}`;
  const recoveryId = rtoMeasurement.startRecovery(
    componentPath,
    incidentId,
    `Test recovery for ${componentPath}`
  );

  try {
    if (simulate) {
      // Simulate recovery with random duration
      const simulatedDuration = Math.random() * component.rto * 1.5; // Up to 150% of RTO
      logger.info(`Simulating recovery for ${simulatedDuration.toFixed(2);} minutes...`);
      await new Promise(resolve => setTimeout(resolve, simulatedDuration * 60 * 1000));
    } else {
      // Execute actual recovery script
      const scriptPath = path.join(RECOVERY_SCRIPTS_DIR, `${componentPath.replace(/\./g, '-')}.sh`);
      
      if (fs.existsSync(scriptPath)) {
        logger.info(`Executing recovery script: ${scriptPath}`);
        execSync(scriptPath, { stdio: 'inherit' });
      } else {
        logger.info(`Recovery script not found: ${scriptPath}`);
        logger.info('Creating directory for recovery scripts...');
        
        // Create directory if it doesn't exist
        if (!fs.existsSync(RECOVERY_SCRIPTS_DIR)) {
          fs.mkdirSync(RECOVERY_SCRIPTS_DIR, { recursive: true });
        }
        
        // Create placeholder script
        const placeholderScript = `#!/bin/bash
# Recovery script for ${componentPath}
# Description: ${component.description}
# RTO: ${component.rto} minutes
# Priority: ${component.priority}
# Dependencies: ${component.dependencies ? component.dependencies.join(', ') : 'None'}

echo "Starting recovery for ${componentPath}..."
# Add recovery steps here
sleep ${Math.floor(component.rto * 0.8)} # Simulate recovery taking 80% of RTO
echo "Recovery completed for ${componentPath}"
`;
        
        fs.writeFileSync(scriptPath, placeholderScript);
        fs.chmodSync(scriptPath, '755');
        
        logger.info(`Created placeholder recovery script: ${scriptPath}`);
        logger.info('Simulating recovery instead...');
        
        // Simulate recovery
        await new Promise(resolve => setTimeout(resolve, component.rto * 0.8 * 60 * 1000));
      }
    }

    // End recovery measurement
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'success',
      simulate ? 'Simulated recovery' : 'Actual recovery test'
    );

    return recovery;
  } catch (error) {
    console.error(`Error during recovery test:`, error);
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );

    return recovery;
  }
}

/**
 * Test recovery for all components
 * @param {boolean} simulate - Whether to simulate recovery instead of executing actual scripts
 * @returns {Object} RTO compliance report
 */
async function testAllRecoveries(simulate = true) {
  const incidentId = `test-all-${Date.now()}`;
  const componentPaths = [];

  // Extract all component paths from RTO definitions
  function extractPaths(obj, prefix = '') {
    for (const key in obj) {
      const newPrefix = prefix ? `${prefix}.${key}` : key;
      if (obj[key] && typeof obj[key] === 'object' && obj[key].rto !== undefined) {
        componentPaths.push(newPrefix);
      }
      if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key]) && obj[key].rto === undefined) {
        extractPaths(obj[key], newPrefix);
      }
    }
  }

  extractPaths(rtoDefinitions);

  logger.info(`Testing recovery for ${componentPaths.length} components`);
  
  for (const componentPath of componentPaths) {
    await testRecovery(componentPath, simulate);
  }

  return rtoMeasurement.generateRtoReport(incidentId);
}

// If script is run directly, test recovery for all components
if (require.main === module) {
  const args = process.argv.slice(2);
  const componentPath = args[0];
  const simulate = args[1] !== 'false';

  if (componentPath) {
    testRecovery(componentPath, simulate)
      .then(recovery => {
        logger.info('Recovery test completed:');
        logger.info(JSON.stringify(recovery, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  } else {
    testAllRecoveries(simulate)
      .then(report => {
        logger.info('Recovery tests completed:');
        logger.info(JSON.stringify(report, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  }
}

module.exports = {
  testRecovery,
  testAllRecoveries
};
