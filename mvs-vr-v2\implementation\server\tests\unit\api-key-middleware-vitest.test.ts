/**
 * API Key Middleware Tests
 *
 * This file contains tests for the enhanced API key authentication middleware.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';

// Mock dependencies
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(),
}));

vi.mock('ioredis', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
      incr: vi.fn(),
      expire: vi.fn(),
      hincrby: vi.fn(),
      hset: vi.fn(),
      lpush: vi.fn(),
      ltrim: vi.fn(),
      call: vi.fn(),
    })),
  };
});

// Mock logger
const mockLogger = {
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock auth-middleware
vi.mock('../../api/middleware/auth-middleware.js', () => ({
  logger: mockLogger,
  redis: new Redis(),
}));

// Import the module under test - using dynamic import
let hashApiKey: (apiKey: string) => string;
let getApiKeyData: (hashedKey: string) => Promise<any>;
let isRateLimited: (apiKeyId: string, rateLimit?: number) => Promise<boolean>;

describe('API Key Middleware', () => {
  // Using underscore prefix for unused variables
  let _mockRequest: Partial<Request>;
  let _mockResponse: Partial<Response>;
  let _nextFunction: NextFunction;
  let mockSupabase: ReturnType<typeof vi.fn>;
  let mockRedis: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();

    // Import the module under test
    const apiKeyMiddleware = await import('../../api/middleware/api-key-middleware.js');
    hashApiKey = apiKeyMiddleware.hashApiKey;
    getApiKeyData = apiKeyMiddleware.getApiKeyData;
    isRateLimited = apiKeyMiddleware.isRateLimited;

    // Setup mock request and response
    _mockRequest = {
      headers: {},
      query: {},
      ip: '127.0.0.1',
      originalUrl: '/api/test',
      method: 'GET',
      connection: {
        remoteAddress: '127.0.0.1',
      },
    };

    _mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };

    _nextFunction = vi.fn();

    // Setup mock Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      update: vi.fn().mockReturnThis(),
    };

    (createClient as ReturnType<typeof vi.fn>).mockReturnValue(mockSupabase);

    // Get Redis mock from auth-middleware
    const authMiddleware = await import('../../api/middleware/auth-middleware.js');
    mockRedis = authMiddleware.redis;
  });

  describe('hashApiKey', () => {
    it('should hash an API key using SHA-256', () => {
      const apiKey = 'test-api-key';
      const hashedKey = hashApiKey(apiKey);

      // SHA-256 hash of 'test-api-key'
      const expectedHash = '9097f547c9d4e5e6b2ebafdee091f3952c41f1a9b8a5c5d32a0487f30a3a2b6f';

      expect(hashedKey).toBe(expectedHash);
    });
  });

  describe('getApiKeyData', () => {
    it('should return cached API key data if available', async () => {
      const hashedKey = 'hashed-key';
      const cachedData = JSON.stringify({ id: 'key-id', permissions: ['read'] });

      mockRedis.get.mockResolvedValue(cachedData);

      const result = await getApiKeyData(hashedKey);

      expect(mockRedis.get).toHaveBeenCalledWith('api_key:hashed-key');
      expect(result).toEqual({ id: 'key-id', permissions: ['read'] });
      expect(mockSupabase.from).not.toHaveBeenCalled();
    });

    it('should fetch API key data from database if not cached', async () => {
      const hashedKey = 'hashed-key';
      const apiKeyData = { id: 'key-id', permissions: ['read'] };

      mockRedis.get.mockResolvedValue(null);
      mockSupabase.single.mockResolvedValue({ data: apiKeyData, error: null });

      const result = await getApiKeyData(hashedKey);

      expect(mockRedis.get).toHaveBeenCalledWith('api_key:hashed-key');
      expect(mockSupabase.from).toHaveBeenCalledWith('api_keys');
      expect(mockSupabase.select).toHaveBeenCalled();
      expect(mockSupabase.eq).toHaveBeenCalledWith('key_hash', hashedKey);
      expect(mockRedis.set).toHaveBeenCalled();
      expect(result).toEqual(apiKeyData);
    });

    it('should return null if API key not found', async () => {
      const hashedKey = 'hashed-key';

      mockRedis.get.mockResolvedValue(null);
      mockSupabase.single.mockResolvedValue({ data: null, error: new Error('Not found') });

      const result = await getApiKeyData(hashedKey);

      expect(result).toBeNull();
    });
  });

  describe('isRateLimited', () => {
    it('should return false if under rate limit', async () => {
      const apiKeyId = 'key-id';
      const rateLimit = 10;

      mockRedis.incr.mockResolvedValue(5);

      const result = await isRateLimited(apiKeyId, rateLimit);

      expect(mockRedis.incr).toHaveBeenCalledWith('api_key:key-id:rate_limit');
      expect(mockRedis.expire).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should set expiry on first request', async () => {
      const apiKeyId = 'key-id';
      const rateLimit = 10;

      mockRedis.incr.mockResolvedValue(1);

      const result = await isRateLimited(apiKeyId, rateLimit);

      expect(mockRedis.incr).toHaveBeenCalledWith('api_key:key-id:rate_limit');
      expect(mockRedis.expire).toHaveBeenCalledWith('api_key:key-id:rate_limit', 60);
      expect(result).toBe(false);
    });

    it('should return true if over rate limit', async () => {
      const apiKeyId = 'key-id';
      const rateLimit = 10;

      mockRedis.incr.mockResolvedValue(11);

      const result = await isRateLimited(apiKeyId, rateLimit);

      expect(result).toBe(true);
    });
  });
});
