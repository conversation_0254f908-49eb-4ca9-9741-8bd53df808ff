/**
 * Simple test script for VirtualListRenderer
 * This script tests the core functionality without requiring a full build
 */

// Import the VirtualListRenderer class
const { VirtualListRenderer } = require('./src/utils/PerformanceOptimizer');

// Mock data
const mockItems = Array.from({ length: 100 }, (_, i) => ({
  id: `item-${i}`,
  name: `Item ${i}`,
  value: i
}));

// Mock load more function
const mockLoadMoreItems = async (page, pageSize) => {
  console.log(`Loading page ${page} with pageSize ${pageSize}`);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return Array.from({ length: pageSize }, (_, i) => ({
    id: `item-${(page - 1) * pageSize + i + 100}`,
    name: `Item ${(page - 1) * pageSize + i + 100}`,
    value: (page - 1) * pageSize + i + 100
  }));
};

// Create a new VirtualListRenderer
const renderer = new VirtualListRenderer(
  mockItems,
  40, // Item height
  400, // Container height
  5, // Buffer
  {
    lazyLoad: true,
    loadMoreItems: mockLoadMoreItems,
    loadThreshold: 0.8,
    pageSize: 20,
    totalItems: 200,
    prefetch: true,
    prefetchThreshold: 0.5
  }
);

console.log('=== Testing VirtualListRenderer ===');

// Test 1: Initial state
console.log('\nTest 1: Initial state');
console.log(`Items count: ${renderer.items.length}`);
console.log(`Current page: ${renderer.currentPage}`);
console.log(`Has more items: ${renderer.hasMoreItems}`);
console.log(`Is prefetching: ${renderer.isPrefetching}`);

// Test 2: Get visible items
console.log('\nTest 2: Get visible items');
const result1 = renderer.getVisibleItems();
console.log(`Visible items count: ${result1.visibleItems.length}`);
console.log(`Render time: ${result1.metrics.renderTime}ms`);

// Test 3: Scroll and trigger lazy loading
console.log('\nTest 3: Scroll and trigger lazy loading');
// Scroll to 80% of the current items
const scrollPosition = renderer.items.length * renderer.itemHeight * 0.8;
console.log(`Scrolling to position: ${scrollPosition}`);
renderer.updateScroll(scrollPosition);

// Wait for lazy loading to complete
setTimeout(async () => {
  console.log(`Items after scrolling: ${renderer.items.length}`);
  console.log(`Current page after scrolling: ${renderer.currentPage}`);
  
  // Test 4: Prefetching
  console.log('\nTest 4: Prefetching');
  console.log(`Is prefetching: ${renderer.isPrefetching}`);
  console.log(`Prefetched data available: ${!!renderer.prefetchedData}`);
  
  // Manually trigger prefetching
  console.log('Manually triggering prefetch...');
  renderer.prefetchNextPage();
  
  // Wait for prefetching to complete
  await new Promise(resolve => setTimeout(resolve, 200));
  
  console.log(`Is prefetching after manual trigger: ${renderer.isPrefetching}`);
  console.log(`Prefetched data available after manual trigger: ${!!renderer.prefetchedData}`);
  
  // Test 5: Get metrics
  console.log('\nTest 5: Get metrics');
  console.log('Metrics:', renderer.getMetrics());
  
  // Test 6: Cleanup
  console.log('\nTest 6: Cleanup');
  renderer.dispose();
  console.log('Renderer disposed');
  
  console.log('\n=== All tests completed ===');
}, 300);
