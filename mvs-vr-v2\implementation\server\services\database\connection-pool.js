/**
 * Database Connection Pool
 * 
 * This module provides an optimized database connection pool for high concurrency.
 */

const { Pool } = require('pg');
const { Logger } = require('../integration/logger');
const { Gauge, Counter } = require('prom-client');

// Create logger
const logger = new Logger();

// Create metrics
const poolConnectionsTotal = new Gauge({
  name: 'db_pool_connections_total',
  help: 'Total number of connections in the pool',
  labelNames: ['pool_name']
});

const poolConnectionsActive = new Gauge({
  name: 'db_pool_connections_active',
  help: 'Number of active connections in the pool',
  labelNames: ['pool_name']
});

const poolConnectionsIdle = new Gauge({
  name: 'db_pool_connections_idle',
  help: 'Number of idle connections in the pool',
  labelNames: ['pool_name']
});

const poolConnectionsWaiting = new Gauge({
  name: 'db_pool_connections_waiting',
  help: 'Number of clients waiting for a connection',
  labelNames: ['pool_name']
});

const poolConnectionsAcquired = new Counter({
  name: 'db_pool_connections_acquired_total',
  help: 'Total number of connections acquired from the pool',
  labelNames: ['pool_name']
});

const poolConnectionsReleased = new Counter({
  name: 'db_pool_connections_released_total',
  help: 'Total number of connections released back to the pool',
  labelNames: ['pool_name']
});

const poolConnectionsError = new Counter({
  name: 'db_pool_connections_error_total',
  help: 'Total number of connection errors',
  labelNames: ['pool_name', 'error_type']
});

const poolQueryDuration = new Counter({
  name: 'db_pool_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['pool_name', 'query_type']
});

/**
 * Create an optimized database connection pool
 * @param {Object} options - Pool options
 * @returns {Pool} Database connection pool
 */
function createConnectionPool(options = {}) {
  const {
    name = 'default',
    host = process.env.POSTGRES_HOST || 'localhost',
    port = process.env.POSTGRES_PORT || 5432,
    database = process.env.POSTGRES_DB || 'postgres',
    user = process.env.POSTGRES_USER || 'postgres',
    password = process.env.POSTGRES_PASSWORD || 'postgres',
    // Connection pool settings
    min = parseInt(process.env.DB_POOL_MIN || '2', 10),
    max = parseInt(process.env.DB_POOL_MAX || '10', 10),
    idleTimeoutMillis = parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000', 10),
    connectionTimeoutMillis = parseInt(process.env.DB_POOL_CONNECTION_TIMEOUT || '5000', 10),
    // Query settings
    statement_timeout = parseInt(process.env.DB_STATEMENT_TIMEOUT || '30000', 10),
    query_timeout = parseInt(process.env.DB_QUERY_TIMEOUT || '30000', 10),
    // SSL settings
    ssl = process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
  } = options;

  // Create pool
  const pool = new Pool({
    host,
    port,
    database,
    user,
    password,
    ssl,
    min,
    max,
    idleTimeoutMillis,
    connectionTimeoutMillis,
    statement_timeout,
    query_timeout
  });

  // Log pool creation
  logger.info(`Created database connection pool "${name}"`, {
    host,
    port,
    database,
    user,
    min,
    max,
    idleTimeoutMillis,
    connectionTimeoutMillis
  });

  // Set up event listeners
  pool.on('connect', client => {
    poolConnectionsAcquired.inc({ pool_name: name });
    logger.debug(`Client connected to pool "${name}"`, { pid: client.processID });
  });

  pool.on('acquire', () => {
    const { totalCount, idleCount, waitingCount } = pool;
    
    poolConnectionsTotal.set({ pool_name: name }, totalCount);
    poolConnectionsActive.set({ pool_name: name }, totalCount - idleCount);
    poolConnectionsIdle.set({ pool_name: name }, idleCount);
    poolConnectionsWaiting.set({ pool_name: name }, waitingCount);
    
    logger.debug(`Client acquired from pool "${name}"`, {
      total: totalCount,
      idle: idleCount,
      active: totalCount - idleCount,
      waiting: waitingCount
    });
  });

  pool.on('release', () => {
    poolConnectionsReleased.inc({ pool_name: name });
    
    const { totalCount, idleCount, waitingCount } = pool;
    
    poolConnectionsTotal.set({ pool_name: name }, totalCount);
    poolConnectionsActive.set({ pool_name: name }, totalCount - idleCount);
    poolConnectionsIdle.set({ pool_name: name }, idleCount);
    poolConnectionsWaiting.set({ pool_name: name }, waitingCount);
    
    logger.debug(`Client released to pool "${name}"`, {
      total: totalCount,
      idle: idleCount,
      active: totalCount - idleCount,
      waiting: waitingCount
    });
  });

  pool.on('error', (err, client) => {
    poolConnectionsError.inc({ pool_name: name, error_type: err.name });
    logger.error(`Error in pool "${name}"`, { error: err, pid: client?.processID });
  });

  // Wrap query method to add metrics
  const originalQuery = pool.query.bind(pool);
  
  pool.query = async function wrappedQuery(...args) {
    const startTime = process.hrtime();
    
    try {
      const result = await originalQuery(...args);
      
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const duration = seconds + nanoseconds / 1e9;
      
      // Determine query type
      let queryType = 'unknown';
      
      if (typeof args[0] === 'string') {
        const sql = args[0].trim().toLowerCase();
        
        if (sql.startsWith('select')) {
          queryType = 'select';
        } else if (sql.startsWith('insert')) {
          queryType = 'insert';
        } else if (sql.startsWith('update')) {
          queryType = 'update';
        } else if (sql.startsWith('delete')) {
          queryType = 'delete';
        } else if (sql.startsWith('create')) {
          queryType = 'create';
        } else if (sql.startsWith('alter')) {
          queryType = 'alter';
        } else if (sql.startsWith('drop')) {
          queryType = 'drop';
        }
      }
      
      poolQueryDuration.inc({ pool_name: name, query_type: queryType }, duration);
      
      return result;
    } catch (error) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const duration = seconds + nanoseconds / 1e9;
      
      poolQueryDuration.inc({ pool_name: name, query_type: 'error' }, duration);
      poolConnectionsError.inc({ pool_name: name, error_type: error.name });
      
      throw error;
    }
  };

  return pool;
}

/**
 * Default connection pool
 */
const defaultPool = createConnectionPool();

module.exports = {
  createConnectionPool,
  defaultPool
};
