# MVS-VR Mobile Monitoring

This directory contains the mobile monitoring implementation for the MVS-VR platform, providing comprehensive monitoring of mobile applications that interact with the MVS-VR ecosystem.

## Components

### 1. Mobile Metrics SDK

The Mobile Metrics SDK is a JavaScript library for React Native applications that collects and sends metrics to the monitoring service. It provides:

- Performance monitoring (frame rate, memory usage, battery level)
- Network monitoring (latency, bandwidth)
- API request tracking
- Asset loading metrics
- User interaction tracking
- Session tracking
- AR/VR session monitoring
- Error tracking

### 2. Mobile App Monitor

The Mobile App Monitor is a server-side service that receives and processes metrics from mobile applications. It integrates with the existing monitoring system to provide comprehensive mobile performance and usage monitoring.

## Mobile Metrics SDK

### Installation

```bash
npm install @mvs-vr/mobile-metrics-sdk
# or
yarn add @mvs-vr/mobile-metrics-sdk
```

### Dependencies

The SDK depends on the following packages:

```json
{
  "dependencies": {
    "react-native-device-info": "^8.0.0",
    "@react-native-community/netinfo": "^6.0.0",
    "react-native-performance": "^2.0.0"
  }
}
```

### Usage

```javascript
import MVSMetricsSDK from '@mvs-vr/mobile-metrics-sdk';

// Initialize the SDK
MVSMetricsSDK.initialize({
  apiUrl: 'https://api.mvs-vr.com',
  appId: 'your-app-id',
  enableDetailedMetrics: true,
  enableARTracking: true,
});

// Record API request
MVSMetricsSDK.recordApiRequest({
  endpoint: 'api/showrooms',
  method: 'GET',
  responseTime: 150,
  status: 200,
});

// Record screen load time
MVSMetricsSDK.recordScreenLoadTime('Home', 250);

// Record interaction latency
MVSMetricsSDK.recordInteractionLatency('button_press', 50);

// Record asset load time
MVSMetricsSDK.recordAssetLoadTime('texture', 1.5);

// Record asset cache hit rate
MVSMetricsSDK.recordAssetCacheHitRate('texture', 75);

// Record error
MVSMetricsSDK.recordError('api', 'home', 'error');

// Cleanup when app is closed
MVSMetricsSDK.cleanup();
```

### Configuration Options

The SDK can be configured with the following options:

```javascript
{
  apiUrl: 'https://api.mvs-vr.com',           // API URL
  metricsEndpoint: '/api/metrics',            // Metrics endpoint
  appId: 'mvs-vr-mobile',                     // App ID
  collectionInterval: 60000,                  // Collection interval in ms (1 minute)
  enableDetailedMetrics: false,               // Enable detailed metrics
  enableAutomaticCollection: true,            // Enable automatic collection
  enableAutomaticSubmission: true,            // Enable automatic submission
  submissionInterval: 300000,                 // Submission interval in ms (5 minutes)
  maxBatchSize: 100,                          // Maximum batch size
  maxStorageSize: 5 * 1024 * 1024,            // Maximum storage size (5 MB)
  persistMetrics: true,                       // Persist metrics
  enableNetworkMonitoring: true,              // Enable network monitoring
  enablePerformanceMonitoring: true,          // Enable performance monitoring
  enableErrorTracking: true,                  // Enable error tracking
  enableSessionTracking: true,                // Enable session tracking
  enableARTracking: false,                    // Enable AR tracking
  samplingRate: 1.0,                          // Sampling rate (100%)
}
```

## Mobile App Monitor

The Mobile App Monitor is a server-side service that receives and processes metrics from mobile applications. It exposes the following endpoints:

- `POST /api/metrics` - Receive metrics from mobile applications
- `GET /metrics` - Prometheus metrics endpoint

### Metrics

The Mobile App Monitor collects the following metrics:

#### Performance Metrics

- `mvs_vr_mobile_app_start_time_ms` - Application start time in milliseconds
- `mvs_vr_mobile_frame_rate` - Frame rate in frames per second
- `mvs_vr_mobile_memory_usage_mb` - Memory usage in megabytes
- `mvs_vr_mobile_battery_level_percent` - Battery level in percent

#### Network Metrics

- `mvs_vr_mobile_network_latency_ms` - Network latency in milliseconds
- `mvs_vr_mobile_network_bandwidth_kbps` - Network bandwidth in kilobits per second

#### API Metrics

- `mvs_vr_mobile_api_request_count` - Number of API requests
- `mvs_vr_mobile_api_response_time_ms` - API response time in milliseconds

#### Asset Loading Metrics

- `mvs_vr_mobile_asset_load_time_seconds` - Asset loading time in seconds
- `mvs_vr_mobile_asset_cache_hit_rate_percent` - Asset cache hit rate in percent

#### User Interaction Metrics

- `mvs_vr_mobile_screen_load_time_ms` - Screen load time in milliseconds
- `mvs_vr_mobile_interaction_latency_ms` - Interaction latency in milliseconds
- `mvs_vr_mobile_screen_view_count` - Number of screen views

#### Session Metrics

- `mvs_vr_mobile_session_duration_seconds` - Session duration in seconds

#### AR/VR Metrics

- `mvs_vr_mobile_ar_session_duration_seconds` - AR session duration in seconds
- `mvs_vr_mobile_tracking_quality` - AR tracking quality score (0-100)

#### Error Metrics

- `mvs_vr_mobile_error_count` - Number of errors
- `mvs_vr_mobile_crash_count` - Number of crashes
- `mvs_vr_mobile_anr_count` - Number of Application Not Responding events

### Integration with Monitoring System

The Mobile App Monitor integrates with the existing monitoring system through:

1. **Prometheus Integration**: Metrics are exposed in Prometheus format
2. **Grafana Dashboards**: Custom dashboards for mobile metrics
3. **Alerts**: Mobile-specific alerts for performance issues
4. **User Segmentation**: Mobile users are segmented for analysis

## Example Application

An example React Native application is provided in the `example` directory. It demonstrates how to use the Mobile Metrics SDK to collect and send metrics to the monitoring service.

### Running the Example

```bash
cd example
npm install
npm start
```

## Native Modules

The SDK uses native modules for certain metrics that cannot be collected from JavaScript:

### Android

- `PerformanceMonitor` - Collects performance metrics (memory usage, frame rate)
- `ARTracker` - Tracks AR session metrics

### iOS

- `PerformanceMonitor` - Collects performance metrics (memory usage, frame rate)
- `ARTracker` - Tracks AR session metrics

## Future Enhancements

1. **Offline Support**: Store metrics locally when offline and send when online
2. **Crash Reporting**: Detailed crash reports with stack traces
3. **User Journey Tracking**: Track complete user journeys through the app
4. **Heatmaps**: Visual heatmaps of user interactions
5. **A/B Testing Integration**: Track metrics for different A/B test variants
6. **Custom Events**: Allow developers to track custom events
7. **Automatic Instrumentation**: Automatically instrument React Native components
8. **Native SDK**: Native SDKs for Android and iOS for better performance
