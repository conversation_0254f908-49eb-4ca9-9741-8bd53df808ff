/**
 * Integration test for PerformanceOptimizer and VirtualListRenderer
 * This script simulates the AnimationEditor component's usage of these classes
 */

// Import the classes
const { PerformanceOptimizer, VirtualListRenderer } = require('./src/utils/PerformanceOptimizer');
const { logger } = require('../shared/utils/logger');

// Mock AnimationEditor component
class MockAnimationEditor {
  constructor() {
    this.animations = [];
    this.isLoading = false;
    this.error = null;
    this.performanceMetrics = {
      loadTime: 0,
      renderTime: 0,
      cacheHits: 0,
      cacheTimes: [],
      apiCalls: 0,
      apiTimes: []
    };
    
    // Initialize cache with memory management
    this.cache = new PerformanceOptimizer(50, 10 * 60 * 1000, {
      maxMemorySize: 10 * 1024 * 1024, // 10MB limit
      evictionThreshold: 0.8,
      trackHitRate: true
    });
    
    // Initialize with empty animations array
    this.initVirtualListRenderer();
  }
  
  initVirtualListRenderer() {
    logger.info('Initializing VirtualListRenderer...');
    this.virtualListRenderer = new VirtualListRenderer(
      this.animations,
      60, // Item height in pixels
      400, // Container height
      5, // Buffer size
      {
        lazyLoad: true,
        loadMoreItems: this.loadAnimationsWithPagination.bind(this),
        loadThreshold: 0.7,
        pageSize: 20,
        prefetch: true,
        prefetchThreshold: 0.5
      }
    );
  }
  
  async loadAnimationsWithPagination(page = 1, limit = 20) {
    const startTime = performance.now();
    logger.info(`Loading animations page ${page} with limit ${limit}...`);
    
    if (page === 1) {
      this.isLoading = true;
    }
    
    try {
      // Check cache first
      const cacheKey = `animations_page${page}_limit${limit}`;
      const cachedData = this.cache.get(cacheKey);
      
      if (cachedData) {
        logger.info(`Using cached data for page ${page}`);
        
        // Track cache performance
        const loadTime = performance.now() - startTime;
        this.performanceMetrics.cacheHits++;
        this.performanceMetrics.cacheTimes.push(loadTime);
        
        return cachedData;
      }
      
      // Simulate API call
      logger.info(`Fetching data for page ${page} from API`);
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
      
      // Generate mock data
      const data = Array.from({ length: limit }, (_, i) => ({
        id: `animation-${(page - 1) * limit + i}`,
        name: `Animation ${(page - 1) * limit + i}`,
        duration: 5,
        tracks: Array.from({ length: 3 }, (_, j) => ({
          id: `track-${(page - 1) * limit + i}-${j}`,
          name: `Track ${j}`,
          keyframes: Array.from({ length: 5 }, (_, k) => ({
            id: `keyframe-${(page - 1) * limit + i}-${j}-${k}`,
            time: k,
            value: { x: k, y: k, z: k }
          }))
        }))
      }));
      
      // Calculate approximate size
      const jsonString = JSON.stringify(data);
      const dataSize = jsonString.length * 2;
      
      // Cache the result
      this.cache.set(cacheKey, data, {
        size: dataSize,
        ttl: 10 * 60 * 1000 // 10 minutes TTL
      });
      
      // Track API performance
      const loadTime = performance.now() - startTime;
      this.performanceMetrics.apiCalls++;
      this.performanceMetrics.apiTimes.push(loadTime);
      
      logger.info(`Loaded page ${page} with ${data.length} animations (${Math.round(dataSize / 1024);}KB) in ${loadTime}ms`);
      
      return data;
    } catch (error) {
      console.error('Error loading animations:', error);
      return [];
    } finally {
      if (page === 1) {
        this.isLoading = false;
      }
      
      this.performanceMetrics.loadTime = performance.now() - startTime;
    }
  }
  
  async loadData() {
    logger.info('Loading initial data...');
    
    try {
      // Get first page of animations
      const result = await this.loadAnimationsWithPagination(1, 20);
      this.animations = result;
      
      // Update virtual list renderer
      this.virtualListRenderer.updateItems(this.animations, {
        totalItems: 100, // Simulate knowing the total count
        hasMoreItems: true
      });
      
      logger.info(`Loaded ${this.animations.length} animations`);
    } catch (error) {
      console.error('Error loading data:', error);
      this.error = 'Failed to load animations';
    }
  }
  
  simulateScroll(scrollPosition) {
    logger.info(`Scrolling to position: ${scrollPosition}`);
    this.virtualListRenderer.updateScroll(scrollPosition);
    
    // Get visible items
    const result = this.virtualListRenderer.getVisibleItems();
    logger.info(`Visible items: ${result.visibleItems.length}`);
    logger.info('Performance metrics:', result.metrics);
  }
  
  cleanup() {
    logger.info('Cleaning up resources...');
    
    if (this.virtualListRenderer) {
      this.virtualListRenderer.dispose();
    }
    
    if (this.cache) {
      logger.info('Cache statistics:', this.cache.getStats(););
      this.cache.dispose();
    }
    
    logger.info('Final performance metrics:', this.performanceMetrics);
  }
}

// Run the test
async function runTest() {
  logger.info('=== Testing AnimationEditor Integration ===');
  
  const editor = new MockAnimationEditor();
  
  // Load initial data
  await editor.loadData();
  
  // Simulate scrolling to trigger lazy loading
  logger.info('\n=== Testing Lazy Loading ===');
  const scrollPosition1 = 400; // Scroll down a bit
  editor.simulateScroll(scrollPosition1);
  
  // Wait a bit and scroll more to trigger prefetching
  await new Promise(resolve => setTimeout(resolve, 500));
  logger.info('\n=== Testing Prefetching ===');
  const scrollPosition2 = 800; // Scroll down more
  editor.simulateScroll(scrollPosition2);
  
  // Wait a bit and scroll to the bottom to trigger loading more items
  await new Promise(resolve => setTimeout(resolve, 500));
  logger.info('\n=== Testing Loading More Items ===');
  const scrollPosition3 = 1200; // Scroll to bottom
  editor.simulateScroll(scrollPosition3);
  
  // Wait for loading to complete
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Load the same data again to test caching
  logger.info('\n=== Testing Caching ===');
  await editor.loadAnimationsWithPagination(1, 20);
  await editor.loadAnimationsWithPagination(2, 20);
  
  // Clean up
  editor.cleanup();
  
  logger.info('\n=== Test Completed ===');
}

// Run the test
runTest().catch(console.error);
