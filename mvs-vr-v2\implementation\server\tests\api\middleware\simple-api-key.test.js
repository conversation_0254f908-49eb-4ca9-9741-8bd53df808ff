/**
 * Simple API Key Middleware Tests
 */

import { describe, it, expect } from 'vitest';

// Import the middleware functions
const apiKeyMiddleware = require('../../../api/middleware/api-key-middleware.js');

describe('API Key Middleware Simple Tests', () => {
  const { hasRequiredPermissions, hasRequiredScopes } = apiKeyMiddleware;

  // Tests for hasRequiredPermissions
  describe('hasRequiredPermissions', () => {
    it('should return true if no permissions are required', () => {
      expect(hasRequiredPermissions(['test:read'], [])).toBe(true);
    });

    it('should return true if key has wildcard permission', () => {
      expect(hasRequiredPermissions(['*'], ['test:read'])).toBe(true);
    });

    it('should return true if key has all required permissions', () => {
      expect(hasRequiredPermissions(['test:read', 'test:write'], ['test:read'])).toBe(true);
    });

    it('should return false if key does not have required permissions', () => {
      expect(hasRequiredPermissions(['test:read'], ['test:write'])).toBe(false);
    });
  });

  // Tests for hasRequiredScopes
  describe('hasRequiredScopes', () => {
    it('should return true if no scopes are required', () => {
      expect(hasRequiredScopes(['api'], [])).toBe(true);
    });

    it('should return true if key has wildcard scope', () => {
      expect(hasRequiredScopes(['*'], ['api'])).toBe(true);
    });

    it('should return true if key has all required scopes', () => {
      expect(hasRequiredScopes(['api', 'admin'], ['api'])).toBe(true);
    });

    it('should return false if key does not have required scopes', () => {
      expect(hasRequiredScopes(['api'], ['admin'])).toBe(false);
    });
  });
});
