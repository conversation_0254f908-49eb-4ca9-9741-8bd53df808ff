FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/user-behavior-predictor.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV USER_BEHAVIOR_PREDICTOR_PORT=9103

# Expose port
EXPOSE 9103

# Start the service
CMD ["node", "monitoring/user-behavior-predictor.js"]
