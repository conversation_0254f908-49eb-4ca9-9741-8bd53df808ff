/**
 * Adaptive Compression Service
 * 
 * This service provides adaptive compression for assets based on client capabilities.
 */

const zlib = require('zlib');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const stream = require('stream');
const { createReadStream, createWriteStream } = fs;
const { createGzip, createBrotliCompress, createDeflate } = zlib;
const pipeline = promisify(stream.pipeline);
const statAsync = promisify(fs.stat);
const logger = require('../../utils/logger').getLogger('adaptive-compression');
const { 
  assetCompressionDuration, 
  assetCompressionRatio, 
  assetCompressionTotal 
} = require('../../monitoring/metrics');

// Configuration
const config = {
  defaultCompressionLevel: 6,
  defaultCompressionAlgorithm: 'gzip',
  enableContentAwareCompression: true,
  enableClientCapabilityDetection: true,
  compressionAlgorithms: {
    gzip: {
      minLevel: 1,
      maxLevel: 9,
      defaultLevel: 6
    },
    brotli: {
      minLevel: 1,
      maxLevel: 11,
      defaultLevel: 4
    },
    deflate: {
      minLevel: 1,
      maxLevel: 9,
      defaultLevel: 6
    }
  },
  mimeTypeMap: {
    // Images (already compressed)
    'image/jpeg': { algorithm: 'gzip', level: 1 },
    'image/png': { algorithm: 'gzip', level: 1 },
    'image/gif': { algorithm: 'gzip', level: 1 },
    'image/webp': { algorithm: 'gzip', level: 1 },
    
    // 3D models
    'model/gltf+json': { algorithm: 'brotli', level: 4 },
    'model/gltf-binary': { algorithm: 'gzip', level: 6 },
    'model/obj': { algorithm: 'brotli', level: 6 },
    'model/fbx': { algorithm: 'gzip', level: 6 },
    
    // Text-based formats
    'text/plain': { algorithm: 'brotli', level: 6 },
    'text/html': { algorithm: 'brotli', level: 6 },
    'text/css': { algorithm: 'brotli', level: 6 },
    'text/javascript': { algorithm: 'brotli', level: 6 },
    'application/json': { algorithm: 'brotli', level: 6 },
    'application/xml': { algorithm: 'brotli', level: 6 },
    
    // Binary formats
    'application/octet-stream': { algorithm: 'gzip', level: 6 },
    'application/x-binary': { algorithm: 'gzip', level: 6 },
    
    // Audio/Video (already compressed)
    'audio/mpeg': { algorithm: 'gzip', level: 1 },
    'audio/mp4': { algorithm: 'gzip', level: 1 },
    'video/mp4': { algorithm: 'gzip', level: 1 },
    'video/webm': { algorithm: 'gzip', level: 1 }
  },
  clientCapabilityLevels: {
    low: {
      cpuPower: 'low',
      memoryLimit: 'low',
      networkQuality: 'low',
      compressionPreference: 'speed',
      algorithms: {
        gzip: { level: 3 },
        brotli: { level: 2 },
        deflate: { level: 3 }
      }
    },
    medium: {
      cpuPower: 'medium',
      memoryLimit: 'medium',
      networkQuality: 'medium',
      compressionPreference: 'balanced',
      algorithms: {
        gzip: { level: 6 },
        brotli: { level: 4 },
        deflate: { level: 6 }
      }
    },
    high: {
      cpuPower: 'high',
      memoryLimit: 'high',
      networkQuality: 'high',
      compressionPreference: 'size',
      algorithms: {
        gzip: { level: 9 },
        brotli: { level: 9 },
        deflate: { level: 9 }
      }
    }
  }
};

/**
 * Detect client capabilities from request headers
 * @param {Object} headers - Request headers
 * @returns {Object} Client capabilities
 */
function detectClientCapabilities(headers) {
  // Default to medium capabilities
  const capabilities = {
    level: 'medium',
    cpuPower: 'medium',
    memoryLimit: 'medium',
    networkQuality: 'medium',
    supportedAlgorithms: ['gzip', 'deflate'],
    preferredAlgorithm: 'gzip'
  };
  
  if (!headers) {
    return capabilities;
  }
  
  // Check for Accept-Encoding header
  if (headers['accept-encoding']) {
    const acceptEncoding = headers['accept-encoding'].toLowerCase();
    
    // Check for supported compression algorithms
    capabilities.supportedAlgorithms = [];
    
    if (acceptEncoding.includes('gzip')) {
      capabilities.supportedAlgorithms.push('gzip');
    }
    
    if (acceptEncoding.includes('br')) {
      capabilities.supportedAlgorithms.push('brotli');
      // If client supports Brotli, it's likely a modern browser
      capabilities.preferredAlgorithm = 'brotli';
    }
    
    if (acceptEncoding.includes('deflate')) {
      capabilities.supportedAlgorithms.push('deflate');
    }
  }
  
  // Check for client hints
  if (headers['device-memory']) {
    const deviceMemory = parseFloat(headers['device-memory']);
    
    if (deviceMemory <= 2) {
      capabilities.memoryLimit = 'low';
    } else if (deviceMemory <= 4) {
      capabilities.memoryLimit = 'medium';
    } else {
      capabilities.memoryLimit = 'high';
    }
  }
  
  if (headers['ect']) {
    const ect = headers['ect'].toLowerCase();
    
    if (ect === '4g') {
      capabilities.networkQuality = 'high';
    } else if (ect === '3g') {
      capabilities.networkQuality = 'medium';
    } else {
      capabilities.networkQuality = 'low';
    }
  }
  
  if (headers['cpu-class']) {
    const cpuClass = headers['cpu-class'].toLowerCase();
    
    if (cpuClass === 'high') {
      capabilities.cpuPower = 'high';
    } else if (cpuClass === 'medium') {
      capabilities.cpuPower = 'medium';
    } else {
      capabilities.cpuPower = 'low';
    }
  }
  
  // Determine overall capability level
  if (capabilities.cpuPower === 'low' || capabilities.memoryLimit === 'low' || capabilities.networkQuality === 'low') {
    capabilities.level = 'low';
  } else if (capabilities.cpuPower === 'high' && capabilities.memoryLimit === 'high' && capabilities.networkQuality === 'high') {
    capabilities.level = 'high';
  } else {
    capabilities.level = 'medium';
  }
  
  return capabilities;
}

/**
 * Determine the best compression algorithm and level based on content type and client capabilities
 * @param {string} mimeType - Content MIME type
 * @param {Object} clientCapabilities - Client capabilities
 * @param {number} fileSize - File size in bytes
 * @returns {Object} Compression settings
 */
function determineCompressionSettings(mimeType, clientCapabilities, fileSize) {
  // Default settings
  const settings = {
    algorithm: config.defaultCompressionAlgorithm,
    level: config.defaultCompressionLevel
  };
  
  // Check if content-aware compression is enabled
  if (config.enableContentAwareCompression && mimeType) {
    // Get settings for MIME type
    const mimeTypeSettings = config.mimeTypeMap[mimeType];
    
    if (mimeTypeSettings) {
      settings.algorithm = mimeTypeSettings.algorithm;
      settings.level = mimeTypeSettings.level;
    }
  }
  
  // Check if client capability detection is enabled
  if (config.enableClientCapabilityDetection && clientCapabilities) {
    // Get capability level
    const capabilityLevel = config.clientCapabilityLevels[clientCapabilities.level];
    
    if (capabilityLevel) {
      // Check if client supports the algorithm
      if (clientCapabilities.supportedAlgorithms.includes(settings.algorithm)) {
        // Adjust level based on capability level
        settings.level = capabilityLevel.algorithms[settings.algorithm]?.level || settings.level;
      } else {
        // Fall back to a supported algorithm
        if (clientCapabilities.supportedAlgorithms.includes('gzip')) {
          settings.algorithm = 'gzip';
          settings.level = capabilityLevel.algorithms.gzip?.level || config.compressionAlgorithms.gzip.defaultLevel;
        } else if (clientCapabilities.supportedAlgorithms.includes('deflate')) {
          settings.algorithm = 'deflate';
          settings.level = capabilityLevel.algorithms.deflate?.level || config.compressionAlgorithms.deflate.defaultLevel;
        }
      }
    }
  }
  
  // Adjust settings based on file size
  if (fileSize) {
    if (fileSize < 1024) { // Less than 1KB
      // Small files don't benefit much from compression
      settings.level = Math.min(settings.level, 3);
    } else if (fileSize > 100 * 1024 * 1024) { // More than 100MB
      // Very large files should use faster compression
      settings.level = Math.min(settings.level, 4);
    }
  }
  
  return settings;
}

/**
 * Compress a file using adaptive compression
 * @param {string} inputPath - Input file path
 * @param {string} outputPath - Output file path
 * @param {Object} options - Compression options
 * @returns {Promise<Object>} Compression result
 */
async function compressFile(inputPath, outputPath, options = {}) {
  const {
    mimeType = 'application/octet-stream',
    clientCapabilities = { level: 'medium', supportedAlgorithms: ['gzip', 'deflate'] },
    fileSize = null
  } = options;
  
  const startTime = process.hrtime();
  
  // Get file size if not provided
  let actualFileSize = fileSize;
  if (!actualFileSize) {
    const stats = await statAsync(inputPath);
    actualFileSize = stats.size;
  }
  
  // Determine compression settings
  const settings = determineCompressionSettings(mimeType, clientCapabilities, actualFileSize);
  
  logger.info(`Compressing file with adaptive compression`, {
    inputPath,
    outputPath,
    fileSize: actualFileSize,
    mimeType,
    algorithm: settings.algorithm,
    level: settings.level,
    clientCapabilityLevel: clientCapabilities.level
  });
  
  try {
    // Create read stream
    const readStream = createReadStream(inputPath);
    
    // Create compression stream based on algorithm
    let compressionStream;
    
    switch (settings.algorithm) {
      case 'brotli':
        compressionStream = createBrotliCompress({
          params: {
            [zlib.constants.BROTLI_PARAM_QUALITY]: settings.level
          }
        });
        break;
      case 'deflate':
        compressionStream = createDeflate({ level: settings.level });
        break;
      case 'gzip':
      default:
        compressionStream = createGzip({ level: settings.level });
        break;
    }
    
    // Create write stream
    const writeStream = createWriteStream(outputPath);
    
    // Compress file
    await pipeline(readStream, compressionStream, writeStream);
    
    // Calculate compression ratio
    const compressedStats = await statAsync(outputPath);
    const compressedSize = compressedStats.size;
    const compressionRatio = actualFileSize / compressedSize;
    
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;
    
    // Track metrics
    assetCompressionDuration.observe(
      { algorithm: settings.algorithm, level: settings.level, mime_type: mimeType },
      duration
    );
    assetCompressionRatio.observe(
      { algorithm: settings.algorithm, level: settings.level, mime_type: mimeType },
      compressionRatio
    );
    assetCompressionTotal.inc(
      { algorithm: settings.algorithm, level: settings.level, mime_type: mimeType, status: 'success' }
    );
    
    logger.info(`File compressed with adaptive compression`, {
      inputPath,
      outputPath,
      fileSize: actualFileSize,
      compressedSize,
      compressionRatio,
      duration,
      algorithm: settings.algorithm,
      level: settings.level
    });
    
    return {
      success: true,
      inputPath,
      outputPath,
      fileSize: actualFileSize,
      compressedSize,
      compressionRatio,
      duration,
      algorithm: settings.algorithm,
      level: settings.level
    };
  } catch (error) {
    logger.error(`Error compressing file with adaptive compression`, {
      inputPath,
      outputPath,
      error
    });
    
    // Track metrics
    assetCompressionTotal.inc(
      { algorithm: settings.algorithm, level: settings.level, mime_type: mimeType, status: 'error' }
    );
    
    throw error;
  }
}

module.exports = {
  compressFile,
  detectClientCapabilities,
  determineCompressionSettings
};
