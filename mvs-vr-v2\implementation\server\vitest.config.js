import { defineConfig } from 'npm:vitest/config';
import { resolve } from 'node:path';
import vue from 'npm:@vitejs/plugin-vue';
import { createModuleResolver } from './tests/setup/module-resolver.ts';

const customResolver = createModuleResolver();

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@directus': resolve(__dirname, './directus/extensions'),
      '@shared': resolve(__dirname, './shared'),
      '@services': resolve(__dirname, './services'),
      '@tests': resolve(__dirname, './tests'),
      '@setup': resolve(__dirname, './tests/setup'),
    },
    extensions: ['.ts', '.js', '.vue', '.json'],
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/run.ts'],
    include: ['**/*.{test,spec,vitest}.{js,ts}', '**/tests/**/*.{js,ts}'],
    exclude: ['**/node_modules/**', '**/dist/**', '**/.{idea,git,cache,output,temp}/**'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: ['**/node_modules/**', '**/tests/setup/**'],
    },
  },
  customResolver: {
    resolveId: customResolver.resolveId,
  },
  esbuild: {
    target: 'node18',
    format: 'esm',
    platform: 'node',
  },
  optimizeDeps: {
    entries: ['tests/**/*.{test,spec,vitest}.{js,ts}'],
    include: ['@vue/test-utils', '@testing-library/jest-dom'],
  },
  // Add Deno-specific configuration
  deno: {
    importMap: './import_map.json',
    typeCheck: false, // Disable Deno's type checking in favor of TypeScript
    // Map npm packages to Deno-compatible versions
    deps: {
      vitest: 'npm:vitest',
      '@vitejs/plugin-vue': 'npm:@vitejs/plugin-vue',
      '@vue/test-utils': 'npm:@vue/test-utils',
      '@testing-library/jest-dom': 'npm:@testing-library/jest-dom',
      dotenv: 'npm:dotenv',
    },
  },
});
