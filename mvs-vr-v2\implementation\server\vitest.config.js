import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'node',
    globals: true,
    setupFiles: ['./tests/api/middleware/vitest.setup.js'],
    include: ['**/*.{test,spec}.{js,ts,cjs,mjs}'],
    server: {
      deps: {
        inline: [
          '../services/integration/logger.ts',
          '../services/bootstrap/bootstrapService.ts',
          '../services/asset/assetService.ts',
          '../services/scene/sceneService.ts',
          '../services/blueprint/blueprintService.ts',
          '../../shared/utils/supabase.ts',
        ],
      },
    },
  },
});
