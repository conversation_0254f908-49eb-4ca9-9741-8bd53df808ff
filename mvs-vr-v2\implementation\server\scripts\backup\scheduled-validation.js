/**
 * Scheduled Backup Validation
 * 
 * This script schedules and runs validation for database, file storage,
 * and configuration backups.
 */

const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const { validateBackup, testRestoration } = require('./backup-validation');
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  region: process.env.AWS_REGION || 'us-east-1',
  buckets: {
    database: process.env.DB_BACKUP_BUCKET || 'mvs-vr-db-backups',
    files: process.env.FILE_BACKUP_BUCKET || 'mvs-vr-file-backups',
    config: process.env.CONFIG_BACKUP_BUCKET || 'mvs-vr-config-backups'
  },
  validationSchedulePath: path.join(__dirname, '../../config/backup-validation-schedule.json'),
  validationLogPath: path.join(__dirname, '../../logs/scheduled-validation.json'),
  maxBackupsToValidate: 5, // Maximum number of backups to validate per type
  validationInterval: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  restorationTestInterval: 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
};

/**
 * Create S3 client
 * @returns {S3Client} S3 client
 */
function createS3Client() {
  return new S3Client({
    region: config.region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
}

/**
 * Load validation schedule
 * @returns {Object} Validation schedule
 */
async function loadValidationSchedule() {
  try {
    if (fs.existsSync(config.validationSchedulePath)) {
      const data = await readFileAsync(config.validationSchedulePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading validation schedule:', error);
  }
  
  // Default schedule
  return {
    database: {
      validationInterval: config.validationInterval,
      restorationTestInterval: config.restorationTestInterval,
      lastValidation: null,
      lastRestorationTest: null,
      validatedBackups: []
    },
    files: {
      validationInterval: config.validationInterval,
      restorationTestInterval: config.restorationTestInterval,
      lastValidation: null,
      lastRestorationTest: null,
      validatedBackups: []
    },
    config: {
      validationInterval: config.validationInterval,
      restorationTestInterval: config.restorationTestInterval,
      lastValidation: null,
      lastRestorationTest: null,
      validatedBackups: []
    }
  };
}

/**
 * Save validation schedule
 * @param {Object} schedule - Validation schedule
 */
async function saveValidationSchedule(schedule) {
  try {
    await writeFileAsync(
      config.validationSchedulePath,
      JSON.stringify(schedule, null, 2),
      'utf8'
    );
  } catch (error) {
    console.error('Error saving validation schedule:', error);
  }
}

/**
 * Load validation log
 * @returns {Object} Validation log
 */
async function loadValidationLog() {
  try {
    if (fs.existsSync(config.validationLogPath)) {
      const data = await readFileAsync(config.validationLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading validation log:', error);
  }
  
  return {
    lastRun: null,
    validations: []
  };
}

/**
 * Save validation log
 * @param {Object} log - Validation log
 */
async function saveValidationLog(log) {
  try {
    await writeFileAsync(
      config.validationLogPath,
      JSON.stringify(log, null, 2),
      'utf8'
    );
  } catch (error) {
    console.error('Error saving validation log:', error);
  }
}

/**
 * List objects in a bucket
 * @param {S3Client} client - S3 client
 * @param {string} bucket - Bucket name
 * @returns {Array} List of objects
 */
async function listObjects(client, bucket) {
  const command = new ListObjectsV2Command({
    Bucket: bucket
  });

  const response = await client.send(command);
  return response.Contents || [];
}

/**
 * Run scheduled validation for a specific backup type
 * @param {string} backupType - Type of backup (database, files, config)
 * @returns {Object} Validation results
 */
async function runScheduledValidation(backupType) {
  logger.info(`Running scheduled validation for ${backupType} backups...`);
  
  const schedule = await loadValidationSchedule();
  const log = await loadValidationLog();
  const client = createS3Client();
  
  // Check if validation is due
  const now = new Date();
  const lastValidation = schedule[backupType].lastValidation ? new Date(schedule[backupType].lastValidation) : null;
  
  if (lastValidation && (now - lastValidation) < schedule[backupType].validationInterval) {
    logger.info(`Validation for ${backupType} backups is not due yet. Last validation: ${lastValidation.toISOString();}`);
    return {
      backupType,
      status: 'skipped',
      reason: 'Not due yet',
      timestamp: now.toISOString()
    };
  }
  
  // List objects in bucket
  const objects = await listObjects(client, config.buckets[backupType]);
  logger.info(`Found ${objects.length} objects in ${backupType} backup bucket`);
  
  // Sort objects by last modified date (newest first)
  objects.sort((a, b) => b.LastModified - a.LastModified);
  
  // Get objects to validate (newest ones that haven't been validated recently)
  const objectsToValidate = objects
    .filter(obj => {
      // Skip objects that have been validated recently
      return !schedule[backupType].validatedBackups.some(
        validatedBackup => validatedBackup.key === obj.Key && 
                          (now - new Date(validatedBackup.timestamp)) < schedule[backupType].validationInterval
      );
    })
    .slice(0, config.maxBackupsToValidate);
  
  logger.info(`Validating ${objectsToValidate.length} ${backupType} backups...`);
  
  // Validate objects
  const results = {
    backupType,
    startTime: now.toISOString(),
    endTime: null,
    duration: null,
    totalObjects: objectsToValidate.length,
    successfulValidations: 0,
    failedValidations: 0,
    details: []
  };
  
  for (const obj of objectsToValidate) {
    try {
      const validationResult = await validateBackup(backupType, obj.Key);
      
      results.details.push(validationResult);
      
      if (validationResult.status === 'success') {
        results.successfulValidations++;
        
        // Add to validated backups
        schedule[backupType].validatedBackups.push({
          key: obj.Key,
          timestamp: now.toISOString()
        });
        
        // Keep only the last 100 validated backups
        if (schedule[backupType].validatedBackups.length > 100) {
          schedule[backupType].validatedBackups = schedule[backupType].validatedBackups.slice(-100);
        }
      } else {
        results.failedValidations++;
      }
    } catch (error) {
      console.error(`Error validating ${obj.Key}:`, error);
      
      results.failedValidations++;
      results.details.push({
        key: obj.Key,
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Check if restoration test is due
  const lastRestorationTest = schedule[backupType].lastRestorationTest ? new Date(schedule[backupType].lastRestorationTest) : null;
  
  if (!lastRestorationTest || (now - lastRestorationTest) >= schedule[backupType].restorationTestInterval) {
    logger.info(`Running restoration test for ${backupType} backups...`);
    
    // Get newest backup for restoration test
    if (objects.length > 0) {
      try {
        const newestBackup = objects[0];
        const restorationResult = await testRestoration(backupType, newestBackup.Key);
        
        results.restorationTest = restorationResult;
        schedule[backupType].lastRestorationTest = now.toISOString();
      } catch (error) {
        console.error(`Error testing restoration:`, error);
        
        results.restorationTest = {
          status: 'failed',
          error: error.message,
          timestamp: now.toISOString()
        };
      }
    }
  }
  
  const endTime = new Date();
  const durationMs = endTime - now;
  
  results.endTime = endTime.toISOString();
  results.duration = durationMs / 1000; // Convert to seconds
  
  // Update schedule
  schedule[backupType].lastValidation = now.toISOString();
  await saveValidationSchedule(schedule);
  
  // Update log
  log.lastRun = endTime.toISOString();
  log.validations.push(results);
  
  // Keep only the last 100 validations
  if (log.validations.length > 100) {
    log.validations = log.validations.slice(-100);
  }
  
  await saveValidationLog(log);
  
  logger.info(`Completed ${backupType} backup validation in ${results.duration} seconds`);
  
  return results;
}

/**
 * Run scheduled validation for all backup types
 * @returns {Object} Validation results
 */
async function runAllScheduledValidations() {
  const startTime = new Date();
  
  logger.info(`Starting scheduled backup validation at ${startTime.toISOString();}`);
  
  const results = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    backupTypes: {}
  };
  
  // Run validation for each backup type
  for (const backupType of Object.keys(config.buckets)) {
    try {
      results.backupTypes[backupType] = await runScheduledValidation(backupType);
    } catch (error) {
      console.error(`Error validating ${backupType} backups:`, error);
      results.backupTypes[backupType] = {
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  const endTime = new Date();
  const durationMs = endTime - startTime;
  
  results.endTime = endTime.toISOString();
  results.duration = durationMs / 1000; // Convert to seconds
  
  logger.info(`Completed scheduled backup validation in ${results.duration} seconds`);
  
  return results;
}

// If script is run directly, run scheduled validation
if (require.main === module) {
  const args = process.argv.slice(2);
  const backupType = args[0];
  
  if (backupType && config.buckets[backupType]) {
    runScheduledValidation(backupType)
      .then(results => {
        logger.info('Validation completed:');
        logger.info(JSON.stringify(results, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  } else {
    runAllScheduledValidations()
      .then(results => {
        logger.info('Validation completed:');
        logger.info(JSON.stringify(results, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  }
}

module.exports = {
  runScheduledValidation,
  runAllScheduledValidations
};
