/**
 * Simple test script for PerformanceOptimizer
 * This script tests the core functionality without requiring a full build
 */

// Import the PerformanceOptimizer class
const { PerformanceOptimizer } = require('./src/utils/PerformanceOptimizer');
const { logger } = require('../shared/utils/logger');

// Create a new instance with memory management
const cache = new PerformanceOptimizer(100, 60 * 1000, {
  maxMemorySize: 1024 * 1024, // 1MB
  evictionThreshold: 0.8
});

logger.info('=== Testing Basic Cache Operations ===');

// Test 1: Set and get
logger.info('\nTest 1: Set and get');
cache.set('key1', 'value1');
logger.info(`Get key1: ${cache.get('key1');}`);
logger.info(`Get nonexistent: ${cache.get('nonexistent');}`);

// Test 2: Expiration
logger.info('\nTest 2: Expiration');
cache.set('expiring', 'will-expire', { ttl: 100 }); // 100ms TTL
logger.info(`Before expiration: ${cache.get('expiring');}`);
setTimeout(() => {
  logger.info(`After expiration: ${cache.get('expiring');}`);
  
  // Test 3: Memory management
  logger.info('\nTest 3: Memory management');
  // Create a large string (approximately 500KB)
  const largeString = 'x'.repeat(500 * 1024);
  
  // Add it to the cache
  cache.set('large1', largeString);
  logger.info(`Added large1, memory used: ${cache.totalMemoryUsed / 1024}KB`);
  
  // Add another large string to trigger eviction
  cache.set('large2', largeString);
  logger.info(`Added large2, memory used: ${cache.totalMemoryUsed / 1024}KB`);
  
  // Check if eviction occurred
  logger.info(`large1 still in cache: ${cache.get('large1'); !== null}`);
  logger.info(`large2 in cache: ${cache.get('large2'); !== null}`);
  logger.info(`Evictions: ${cache.evictions}`);
  
  // Test 4: Cache statistics
  logger.info('\nTest 4: Cache statistics');
  logger.info('Cache stats:', cache.getStats(););
  
  // Test 5: Cleanup
  logger.info('\nTest 5: Cleanup');
  cache.dispose();
  logger.info('Cache disposed');
  
  logger.info('\n=== All tests completed ===');
}, 200);
