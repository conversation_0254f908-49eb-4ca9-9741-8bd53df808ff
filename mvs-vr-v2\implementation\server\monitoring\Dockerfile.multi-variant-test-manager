FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/multi-variant-test-manager.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV MULTI_VARIANT_TEST_MANAGER_PORT=9107

# Expose port
EXPOSE 9107

# Start the service
CMD ["node", "monitoring/multi-variant-test-manager.js"]
