/**
 * StepDependencyManager
 * 
 * Utility class to manage dependencies between wizard steps.
 * Handles validation, data propagation, and conditional logic based on cross-step dependencies.
 */

export default class StepDependencyManager {
  /**
   * Constructor
   * @param {Object} dependencies - Configuration of dependencies between steps
   * @param {Function} getStepData - Function to get data for a specific step
   * @param {Function} updateStepData - Function to update data for a specific step
   */
  constructor(dependencies, getStepData, updateStepData) {
    this.dependencies = dependencies || {};
    this.getStepData = getStepData;
    this.updateStepData = updateStepData;
  }

  /**
   * Validate dependencies for a specific step
   * @param {Number} stepIndex - Index of the step to validate
   * @returns {Object} Validation result with isValid flag and any error messages
   */
  validateDependencies(stepIndex) {
    const stepDependencies = this.dependencies[stepIndex];
    
    if (!stepDependencies) {
      // No dependencies defined for this step
      return { isValid: true };
    }
    
    const errors = [];
    
    // Check required fields from other steps
    if (stepDependencies.requiredFields) {
      for (const dependency of stepDependencies.requiredFields) {
        const { stepIndex: dependentStepIndex, field, message } = dependency;
        const dependentStepData = this.getStepData(dependentStepIndex);
        
        if (!dependentStepData || dependentStepData[field] === undefined || dependentStepData[field] === null || dependentStepData[field] === '') {
          errors.push(message || `Required field from step ${dependentStepIndex + 1} is missing`);
        }
      }
    }
    
    // Check conditional dependencies
    if (stepDependencies.conditions) {
      for (const condition of stepDependencies.conditions) {
        const { stepIndex: dependentStepIndex, field, value, operator, message } = condition;
        const dependentStepData = this.getStepData(dependentStepIndex);
        
        if (!dependentStepData) {
          errors.push(message || `Data from step ${dependentStepIndex + 1} is required`);
          continue;
        }
        
        const fieldValue = dependentStepData[field];
        let conditionMet = false;
        
        switch (operator) {
          case '==':
            conditionMet = fieldValue == value;
            break;
          case '===':
            conditionMet = fieldValue === value;
            break;
          case '!=':
            conditionMet = fieldValue != value;
            break;
          case '!==':
            conditionMet = fieldValue !== value;
            break;
          case '>':
            conditionMet = fieldValue > value;
            break;
          case '>=':
            conditionMet = fieldValue >= value;
            break;
          case '<':
            conditionMet = fieldValue < value;
            break;
          case '<=':
            conditionMet = fieldValue <= value;
            break;
          case 'includes':
            conditionMet = Array.isArray(fieldValue) && fieldValue.includes(value);
            break;
          case 'startsWith':
            conditionMet = typeof fieldValue === 'string' && fieldValue.startsWith(value);
            break;
          case 'endsWith':
            conditionMet = typeof fieldValue === 'string' && fieldValue.endsWith(value);
            break;
          default:
            conditionMet = !!fieldValue;
        }
        
        if (!conditionMet) {
          errors.push(message || `Condition for step ${dependentStepIndex + 1} is not met`);
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Propagate data between steps based on dependencies
   * @param {Number} fromStepIndex - Index of the source step
   * @param {Object} fromStepData - Data from the source step
   */
  propagateData(fromStepIndex, fromStepData) {
    // Check if any other steps depend on this step's data
    for (const [targetStepIndex, dependencies] of Object.entries(this.dependencies)) {
      if (!dependencies.propagation) continue;
      
      const stepIndex = parseInt(targetStepIndex);
      
      // Skip if the target step is the same as the source step
      if (stepIndex === fromStepIndex) continue;
      
      const propagationRules = dependencies.propagation.filter(rule => 
        rule.fromStep === fromStepIndex
      );
      
      if (propagationRules.length === 0) continue;
      
      // Get current data for the target step
      const targetStepData = this.getStepData(stepIndex) || {};
      let hasChanges = false;
      
      // Apply propagation rules
      for (const rule of propagationRules) {
        const { fromField, toField, transform } = rule;
        
        if (fromStepData[fromField] !== undefined) {
          let newValue = fromStepData[fromField];
          
          // Apply transformation if specified
          if (transform && typeof transform === 'function') {
            newValue = transform(newValue, fromStepData, targetStepData);
          }
          
          // Update target field if value has changed
          if (targetStepData[toField] !== newValue) {
            targetStepData[toField] = newValue;
            hasChanges = true;
          }
        }
      }
      
      // Update target step data if changes were made
      if (hasChanges) {
        this.updateStepData(stepIndex, targetStepData);
      }
    }
  }

  /**
   * Get fields that should be disabled based on dependencies
   * @param {Number} stepIndex - Index of the step
   * @returns {Array} List of field names that should be disabled
   */
  getDisabledFields(stepIndex) {
    const stepDependencies = this.dependencies[stepIndex];
    
    if (!stepDependencies || !stepDependencies.disableFields) {
      return [];
    }
    
    const disabledFields = [];
    
    for (const rule of stepDependencies.disableFields) {
      const { field, condition } = rule;
      const { stepIndex: dependentStepIndex, field: dependentField, value, operator } = condition;
      const dependentStepData = this.getStepData(dependentStepIndex);
      
      if (!dependentStepData) continue;
      
      const fieldValue = dependentStepData[dependentField];
      let conditionMet = false;
      
      switch (operator) {
        case '==':
          conditionMet = fieldValue == value;
          break;
        case '===':
          conditionMet = fieldValue === value;
          break;
        case '!=':
          conditionMet = fieldValue != value;
          break;
        case '!==':
          conditionMet = fieldValue !== value;
          break;
        default:
          conditionMet = !!fieldValue;
      }
      
      if (conditionMet) {
        disabledFields.push(field);
      }
    }
    
    return disabledFields;
  }
}
