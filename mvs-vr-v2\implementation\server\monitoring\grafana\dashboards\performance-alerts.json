{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "enable": true, "expr": "ALERTS{alertname=~\".*Degradation.*\", alertstate=\"firing\"}", "iconColor": "rgba(255, 96, 96, 1)", "name": "Performance Degradation Alerts", "showIn": 0, "step": "1m", "tagKeys": "alertname,severity", "textFormat": "{{alertname}} - {{severity}}", "titleFormat": "Performance Alert"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 12, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(ALERTS{alertname=~\".*Degradation.*\", alertstate=\"firing\", severity=\"warning\"})", "legendFormat": "Warning", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(ALERTS{alertname=~\".*Degradation.*\", alertstate=\"firing\", severity=\"error\"})", "legendFormat": "Error", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(ALERTS{alertname=~\".*Degradation.*\", alertstate=\"firing\", severity=\"critical\"})", "legendFormat": "Critical", "range": true, "refId": "C"}], "title": "Active Performance Degradation Alerts", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0}, {"color": "red", "value": 0}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 180}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "severity"}, "properties": [{"id": "custom.width", "value": 100}, {"id": "mappings", "value": [{"options": {"critical": {"color": "red", "index": 2}, "error": {"color": "orange", "index": 1}, "warning": {"color": "yellow", "index": 0}}, "type": "value"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "alertname"}, "properties": [{"id": "custom.width", "value": 300}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 2, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "ALERTS{alertname=~\".*Degradation.*\", alertstate=\"firing\"}", "format": "table", "range": true, "refId": "A"}], "title": "Current Performance Degradation Alerts", "transformations": [{"id": "organize", "options": {"excludeByName": {"Value": true, "alertstate": true, "instance": true, "job": true}, "indexByName": {"Time": 0, "Value": 6, "alertname": 1, "alertstate": 7, "category": 4, "dashboard_id": 5, "instance": 8, "job": 9, "severity": 2, "summary": 3}, "renameByName": {}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 4, "panels": [], "title": "API Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 5, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "avg(mvs_vr_http_request_duration_seconds{quantile=\"0.95\"}) by (endpoint) * 1000", "legendFormat": "{{endpoint}}", "range": true, "refId": "A"}], "title": "API Response Time (p95)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 6, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(rate(mvs_vr_http_requests_total{status=~\"5..\"}[5m])) by (endpoint) / sum(rate(mvs_vr_http_requests_total[5m])) by (endpoint)", "legendFormat": "{{endpoint}}", "range": true, "refId": "A"}], "title": "API Error Rate", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 8, "panels": [], "title": "Database Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 300}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "id": 9, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "avg(mvs_vr_db_query_duration_seconds{quantile=\"0.95\"}) by (query_type) * 1000", "legendFormat": "{{query_type}}", "range": true, "refId": "A"}], "title": "Database Query Duration (p95)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}, "id": 10, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(increase(mvs_vr_db_connection_errors_total[5m])) by (database)", "legendFormat": "{{database}}", "range": true, "refId": "A"}], "title": "Database Connection Errors (5m)", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 38, "style": "dark", "tags": ["performance", "alerts", "monitoring"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Performance Alerts", "uid": "performance-alerts", "version": 1, "weekStart": ""}