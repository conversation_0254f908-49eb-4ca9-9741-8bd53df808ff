/**
 * Proactive Scaler
 * 
 * This service proactively scales services based on predicted load and usage patterns.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const axios = require('axios');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Scaling directions
const SCALING_DIRECTION = {
  UP: 'up',
  DOWN: 'down',
  NONE: 'none'
};

// Service types
const SERVICE_TYPE = {
  API: 'api',
  FRONTEND: 'frontend',
  BACKEND: 'backend',
  DATABASE: 'database',
  CACHE: 'cache',
  STORAGE: 'storage'
};

/**
 * Get service metrics data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Service metrics data
 */
async function getServiceMetricsData(days = 7) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('service_metrics')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: true });
      
    if (error) {
      logger.error('Error fetching service metrics data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getServiceMetricsData', { error: error.message });
    return [];
  }
}

/**
 * Get user activity data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User activity data
 */
async function getUserActivityData(days = 7) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Get current service scaling
 * 
 * @returns {Object} - Current service scaling
 */
async function getCurrentServiceScaling() {
  try {
    const { data, error } = await supabase
      .from('service_scaling')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(1);
      
    if (error) {
      logger.error('Error fetching current service scaling', { error: error.message });
      return null;
    }
    
    return data && data.length > 0 ? data[0] : null;
  } catch (error) {
    logger.error('Error in getCurrentServiceScaling', { error: error.message });
    return null;
  }
}

/**
 * Predict service load
 * 
 * @param {Array} serviceData - Service metrics data
 * @param {Array} activityData - User activity data
 * @returns {Object} - Predicted service load
 */
function predictServiceLoad(serviceData, activityData) {
  try {
    if (serviceData.length === 0) {
      return {
        api: 0.5,
        frontend: 0.5,
        backend: 0.5,
        database: 0.5,
        cache: 0.5,
        storage: 0.5,
        confidence: 0.3
      };
    }
    
    // Group service data by hour
    const serviceByHour = {};
    
    serviceData.forEach(metric => {
      const date = new Date(metric.timestamp);
      const hourKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
      
      if (!serviceByHour[hourKey]) {
        serviceByHour[hourKey] = {
          api_requests: [],
          frontend_requests: [],
          backend_requests: [],
          database_queries: [],
          cache_hits: [],
          cache_misses: [],
          storage_operations: []
        };
      }
      
      serviceByHour[hourKey].api_requests.push(metric.api_requests || 0);
      serviceByHour[hourKey].frontend_requests.push(metric.frontend_requests || 0);
      serviceByHour[hourKey].backend_requests.push(metric.backend_requests || 0);
      serviceByHour[hourKey].database_queries.push(metric.database_queries || 0);
      serviceByHour[hourKey].cache_hits.push(metric.cache_hits || 0);
      serviceByHour[hourKey].cache_misses.push(metric.cache_misses || 0);
      serviceByHour[hourKey].storage_operations.push(metric.storage_operations || 0);
    });
    
    // Calculate average service metrics by hour
    const avgServiceByHour = {};
    
    Object.entries(serviceByHour).forEach(([hourKey, metrics]) => {
      avgServiceByHour[hourKey] = {
        api_requests: metrics.api_requests.reduce((sum, val) => sum + val, 0) / metrics.api_requests.length,
        frontend_requests: metrics.frontend_requests.reduce((sum, val) => sum + val, 0) / metrics.frontend_requests.length,
        backend_requests: metrics.backend_requests.reduce((sum, val) => sum + val, 0) / metrics.backend_requests.length,
        database_queries: metrics.database_queries.reduce((sum, val) => sum + val, 0) / metrics.database_queries.length,
        cache_hits: metrics.cache_hits.reduce((sum, val) => sum + val, 0) / metrics.cache_hits.length,
        cache_misses: metrics.cache_misses.reduce((sum, val) => sum + val, 0) / metrics.cache_misses.length,
        storage_operations: metrics.storage_operations.reduce((sum, val) => sum + val, 0) / metrics.storage_operations.length
      };
    });
    
    // Group activity data by hour
    const activityByHour = {};
    
    activityData.forEach(activity => {
      const date = new Date(activity.created_at);
      const hourKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
      
      if (!activityByHour[hourKey]) {
        activityByHour[hourKey] = 0;
      }
      
      activityByHour[hourKey]++;
    });
    
    // Get current hour
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Calculate average service metrics for current hour of day and day of week
    const hourlyData = [];
    const dayOfWeekData = [];
    
    Object.entries(avgServiceByHour).forEach(([hourKey, metrics]) => {
      const [year, month, day, hour] = hourKey.split('-').map(Number);
      const date = new Date(year, month, day, hour);
      
      if (date.getHours() === currentHour) {
        hourlyData.push(metrics);
      }
      
      if (date.getDay() === currentDay) {
        dayOfWeekData.push(metrics);
      }
    });
    
    // Calculate predictions based on historical patterns
    const hourlyPrediction = {
      api: hourlyData.reduce((sum, metrics) => sum + metrics.api_requests, 0) / hourlyData.length,
      frontend: hourlyData.reduce((sum, metrics) => sum + metrics.frontend_requests, 0) / hourlyData.length,
      backend: hourlyData.reduce((sum, metrics) => sum + metrics.backend_requests, 0) / hourlyData.length,
      database: hourlyData.reduce((sum, metrics) => sum + metrics.database_queries, 0) / hourlyData.length,
      cache: hourlyData.reduce((sum, metrics) => sum + (metrics.cache_hits + metrics.cache_misses), 0) / hourlyData.length,
      storage: hourlyData.reduce((sum, metrics) => sum + metrics.storage_operations, 0) / hourlyData.length
    };
    
    const dayOfWeekPrediction = {
      api: dayOfWeekData.reduce((sum, metrics) => sum + metrics.api_requests, 0) / dayOfWeekData.length,
      frontend: dayOfWeekData.reduce((sum, metrics) => sum + metrics.frontend_requests, 0) / dayOfWeekData.length,
      backend: dayOfWeekData.reduce((sum, metrics) => sum + metrics.backend_requests, 0) / dayOfWeekData.length,
      database: dayOfWeekData.reduce((sum, metrics) => sum + metrics.database_queries, 0) / dayOfWeekData.length,
      cache: dayOfWeekData.reduce((sum, metrics) => sum + (metrics.cache_hits + metrics.cache_misses), 0) / dayOfWeekData.length,
      storage: dayOfWeekData.reduce((sum, metrics) => sum + metrics.storage_operations, 0) / dayOfWeekData.length
    };
    
    // Calculate recent trend
    const recentData = serviceData.slice(-24); // Last 24 data points
    
    if (recentData.length < 2) {
      return {
        api: hourlyPrediction.api,
        frontend: hourlyPrediction.frontend,
        backend: hourlyPrediction.backend,
        database: hourlyPrediction.database,
        cache: hourlyPrediction.cache,
        storage: hourlyPrediction.storage,
        confidence: 0.5
      };
    }
    
    const firstHalf = recentData.slice(0, Math.floor(recentData.length / 2));
    const secondHalf = recentData.slice(Math.floor(recentData.length / 2));
    
    const firstHalfAvg = {
      api: firstHalf.reduce((sum, metric) => sum + (metric.api_requests || 0), 0) / firstHalf.length,
      frontend: firstHalf.reduce((sum, metric) => sum + (metric.frontend_requests || 0), 0) / firstHalf.length,
      backend: firstHalf.reduce((sum, metric) => sum + (metric.backend_requests || 0), 0) / firstHalf.length,
      database: firstHalf.reduce((sum, metric) => sum + (metric.database_queries || 0), 0) / firstHalf.length,
      cache: firstHalf.reduce((sum, metric) => sum + ((metric.cache_hits || 0) + (metric.cache_misses || 0)), 0) / firstHalf.length,
      storage: firstHalf.reduce((sum, metric) => sum + (metric.storage_operations || 0), 0) / firstHalf.length
    };
    
    const secondHalfAvg = {
      api: secondHalf.reduce((sum, metric) => sum + (metric.api_requests || 0), 0) / secondHalf.length,
      frontend: secondHalf.reduce((sum, metric) => sum + (metric.frontend_requests || 0), 0) / secondHalf.length,
      backend: secondHalf.reduce((sum, metric) => sum + (metric.backend_requests || 0), 0) / secondHalf.length,
      database: secondHalf.reduce((sum, metric) => sum + (metric.database_queries || 0), 0) / secondHalf.length,
      cache: secondHalf.reduce((sum, metric) => sum + ((metric.cache_hits || 0) + (metric.cache_misses || 0)), 0) / secondHalf.length,
      storage: secondHalf.reduce((sum, metric) => sum + (metric.storage_operations || 0), 0) / secondHalf.length
    };
    
    const trend = {
      api: secondHalfAvg.api - firstHalfAvg.api,
      frontend: secondHalfAvg.frontend - firstHalfAvg.frontend,
      backend: secondHalfAvg.backend - firstHalfAvg.backend,
      database: secondHalfAvg.database - firstHalfAvg.database,
      cache: secondHalfAvg.cache - firstHalfAvg.cache,
      storage: secondHalfAvg.storage - firstHalfAvg.storage
    };
    
    // Get max values for normalization
    const maxValues = {
      api: Math.max(...serviceData.map(metric => metric.api_requests || 0)),
      frontend: Math.max(...serviceData.map(metric => metric.frontend_requests || 0)),
      backend: Math.max(...serviceData.map(metric => metric.backend_requests || 0)),
      database: Math.max(...serviceData.map(metric => metric.database_queries || 0)),
      cache: Math.max(...serviceData.map(metric => (metric.cache_hits || 0) + (metric.cache_misses || 0))),
      storage: Math.max(...serviceData.map(metric => metric.storage_operations || 0))
    };
    
    // Combine predictions
    const prediction = {
      api: (hourlyPrediction.api + trend.api) / (maxValues.api || 1),
      frontend: (hourlyPrediction.frontend + trend.frontend) / (maxValues.frontend || 1),
      backend: (hourlyPrediction.backend + trend.backend) / (maxValues.backend || 1),
      database: (hourlyPrediction.database + trend.database) / (maxValues.database || 1),
      cache: (hourlyPrediction.cache + trend.cache) / (maxValues.cache || 1),
      storage: (hourlyPrediction.storage + trend.storage) / (maxValues.storage || 1),
      confidence: 0.7
    };
    
    // Clamp values between 0 and 1
    prediction.api = Math.max(0, Math.min(1, prediction.api));
    prediction.frontend = Math.max(0, Math.min(1, prediction.frontend));
    prediction.backend = Math.max(0, Math.min(1, prediction.backend));
    prediction.database = Math.max(0, Math.min(1, prediction.database));
    prediction.cache = Math.max(0, Math.min(1, prediction.cache));
    prediction.storage = Math.max(0, Math.min(1, prediction.storage));
    
    return prediction;
  } catch (error) {
    logger.error('Error in predictServiceLoad', { error: error.message });
    
    return {
      api: 0.5,
      frontend: 0.5,
      backend: 0.5,
      database: 0.5,
      cache: 0.5,
      storage: 0.5,
      confidence: 0.3
    };
  }
}

/**
 * Generate scaling recommendations
 * 
 * @param {Object} prediction - Predicted service load
 * @param {Object} currentScaling - Current service scaling
 * @returns {Array} - Scaling recommendations
 */
function generateScalingRecommendations(prediction, currentScaling) {
  try {
    if (!prediction || !currentScaling) {
      return [];
    }
    
    const recommendations = [];
    
    // API scaling
    if (prediction.api > 0.8 && currentScaling.api_instances < currentScaling.api_max_instances) {
      recommendations.push({
        service: SERVICE_TYPE.API,
        current_load: prediction.api,
        current_instances: currentScaling.api_instances,
        direction: SCALING_DIRECTION.UP,
        target_instances: Math.min(
          currentScaling.api_max_instances,
          Math.ceil(currentScaling.api_instances * 1.5)
        ),
        priority: 'high',
        reason: `High API load predicted (${Math.round(prediction.api * 100)}%)`
      });
    } else if (prediction.api < 0.3 && currentScaling.api_instances > currentScaling.api_min_instances) {
      recommendations.push({
        service: SERVICE_TYPE.API,
        current_load: prediction.api,
        current_instances: currentScaling.api_instances,
        direction: SCALING_DIRECTION.DOWN,
        target_instances: Math.max(
          currentScaling.api_min_instances,
          Math.floor(currentScaling.api_instances * 0.7)
        ),
        priority: 'medium',
        reason: `Low API load predicted (${Math.round(prediction.api * 100)}%)`
      });
    }
    
    // Frontend scaling
    if (prediction.frontend > 0.8 && currentScaling.frontend_instances < currentScaling.frontend_max_instances) {
      recommendations.push({
        service: SERVICE_TYPE.FRONTEND,
        current_load: prediction.frontend,
        current_instances: currentScaling.frontend_instances,
        direction: SCALING_DIRECTION.UP,
        target_instances: Math.min(
          currentScaling.frontend_max_instances,
          Math.ceil(currentScaling.frontend_instances * 1.5)
        ),
        priority: 'high',
        reason: `High frontend load predicted (${Math.round(prediction.frontend * 100)}%)`
      });
    } else if (prediction.frontend < 0.3 && currentScaling.frontend_instances > currentScaling.frontend_min_instances) {
      recommendations.push({
        service: SERVICE_TYPE.FRONTEND,
        current_load: prediction.frontend,
        current_instances: currentScaling.frontend_instances,
        direction: SCALING_DIRECTION.DOWN,
        target_instances: Math.max(
          currentScaling.frontend_min_instances,
          Math.floor(currentScaling.frontend_instances * 0.7)
        ),
        priority: 'medium',
        reason: `Low frontend load predicted (${Math.round(prediction.frontend * 100)}%)`
      });
    }
    
    // Backend scaling
    if (prediction.backend > 0.8 && currentScaling.backend_instances < currentScaling.backend_max_instances) {
      recommendations.push({
        service: SERVICE_TYPE.BACKEND,
        current_load: prediction.backend,
        current_instances: currentScaling.backend_instances,
        direction: SCALING_DIRECTION.UP,
        target_instances: Math.min(
          currentScaling.backend_max_instances,
          Math.ceil(currentScaling.backend_instances * 1.5)
        ),
        priority: 'high',
        reason: `High backend load predicted (${Math.round(prediction.backend * 100)}%)`
      });
    } else if (prediction.backend < 0.3 && currentScaling.backend_instances > currentScaling.backend_min_instances) {
      recommendations.push({
        service: SERVICE_TYPE.BACKEND,
        current_load: prediction.backend,
        current_instances: currentScaling.backend_instances,
        direction: SCALING_DIRECTION.DOWN,
        target_instances: Math.max(
          currentScaling.backend_min_instances,
          Math.floor(currentScaling.backend_instances * 0.7)
        ),
        priority: 'medium',
        reason: `Low backend load predicted (${Math.round(prediction.backend * 100)}%)`
      });
    }
    
    // Database scaling
    if (prediction.database > 0.8 && currentScaling.database_instances < currentScaling.database_max_instances) {
      recommendations.push({
        service: SERVICE_TYPE.DATABASE,
        current_load: prediction.database,
        current_instances: currentScaling.database_instances,
        direction: SCALING_DIRECTION.UP,
        target_instances: Math.min(
          currentScaling.database_max_instances,
          Math.ceil(currentScaling.database_instances * 1.3) // More conservative scaling for database
        ),
        priority: 'high',
        reason: `High database load predicted (${Math.round(prediction.database * 100)}%)`
      });
    }
    
    // Sort recommendations by priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
    
    return recommendations;
  } catch (error) {
    logger.error('Error in generateScalingRecommendations', { error: error.message });
    return [];
  }
}

/**
 * Apply service scaling
 * 
 * @param {Array} recommendations - Scaling recommendations
 * @returns {Object} - Scaling result
 */
async function applyServiceScaling(recommendations) {
  try {
    if (recommendations.length === 0) {
      return {
        success: true,
        message: 'No scaling needed',
        changes: []
      };
    }
    
    // Get current scaling
    const currentScaling = await getCurrentServiceScaling();
    
    if (!currentScaling) {
      throw new Error('Failed to get current service scaling');
    }
    
    // Apply recommendations
    const newScaling = {
      ...currentScaling,
      updated_at: new Date().toISOString()
    };
    
    const changes = [];
    
    recommendations.forEach(recommendation => {
      switch (recommendation.service) {
        case SERVICE_TYPE.API:
          newScaling.api_instances = recommendation.target_instances;
          changes.push({
            service: SERVICE_TYPE.API,
            from: currentScaling.api_instances,
            to: recommendation.target_instances,
            direction: recommendation.direction
          });
          break;
        case SERVICE_TYPE.FRONTEND:
          newScaling.frontend_instances = recommendation.target_instances;
          changes.push({
            service: SERVICE_TYPE.FRONTEND,
            from: currentScaling.frontend_instances,
            to: recommendation.target_instances,
            direction: recommendation.direction
          });
          break;
        case SERVICE_TYPE.BACKEND:
          newScaling.backend_instances = recommendation.target_instances;
          changes.push({
            service: SERVICE_TYPE.BACKEND,
            from: currentScaling.backend_instances,
            to: recommendation.target_instances,
            direction: recommendation.direction
          });
          break;
        case SERVICE_TYPE.DATABASE:
          newScaling.database_instances = recommendation.target_instances;
          changes.push({
            service: SERVICE_TYPE.DATABASE,
            from: currentScaling.database_instances,
            to: recommendation.target_instances,
            direction: recommendation.direction
          });
          break;
      }
    });
    
    // Save new scaling
    const { error } = await supabase
      .from('service_scaling')
      .insert(newScaling);
      
    if (error) {
      throw error;
    }
    
    // Apply changes to infrastructure
    await applyInfrastructureScaling(changes);
    
    return {
      success: true,
      message: `Applied ${changes.length} scaling changes`,
      changes
    };
  } catch (error) {
    logger.error('Error in applyServiceScaling', { error: error.message });
    
    return {
      success: false,
      message: `Error applying scaling: ${error.message}`,
      changes: []
    };
  }
}

/**
 * Apply infrastructure scaling
 * 
 * @param {Array} changes - Infrastructure scaling changes
 */
async function applyInfrastructureScaling(changes) {
  try {
    // This would integrate with your infrastructure management system
    // For now, we'll just log the changes
    logger.info('Applying infrastructure scaling', { changes });
    
    // Example: Call Kubernetes API to scale services

    // 

    //   

    //       replicas: change.to
    //     }
    //   });
    // }
  } catch (error) {
    logger.error('Error in applyInfrastructureScaling', { error: error.message });
    throw error;
  }
}

/**
 * Scale services
 * 
 * @param {boolean} apply - Whether to apply the scaling
 * @returns {Object} - Scaling result
 */
async function scaleServices(apply = false) {
  try {
    // Get data
    const serviceData = await getServiceMetricsData();
    const activityData = await getUserActivityData();
    const currentScaling = await getCurrentServiceScaling();
    
    if (!currentScaling) {
      throw new Error('Failed to get current service scaling');
    }
    
    // Predict service load
    const prediction = predictServiceLoad(serviceData, activityData);
    
    // Generate scaling recommendations
    const recommendations = generateScalingRecommendations(prediction, currentScaling);
    
    // Apply scaling if requested
    if (apply && recommendations.length > 0) {
      const result = await applyServiceScaling(recommendations);
      
      return {
        prediction,
        recommendations,
        applied: result
      };
    }
    
    return {
      prediction,
      recommendations,
      applied: null
    };
  } catch (error) {
    logger.error('Error in scaleServices', { error: error.message });
    
    return {
      error: error.message,
      prediction: null,
      recommendations: [],
      applied: null
    };
  }
}

// API endpoints
app.get('/api/service-load-prediction', async (req, res) => {
  try {
    const serviceData = await getServiceMetricsData();
    const activityData = await getUserActivityData();
    
    const prediction = predictServiceLoad(serviceData, activityData);
    res.json({ prediction });
  } catch (error) {
    logger.error('Error in GET /api/service-load-prediction', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/scaling-recommendations', async (req, res) => {
  try {
    const serviceData = await getServiceMetricsData();
    const activityData = await getUserActivityData();
    const currentScaling = await getCurrentServiceScaling();
    
    if (!currentScaling) {
      return res.status(404).json({ error: 'Current service scaling not found' });
    }
    
    const prediction = predictServiceLoad(serviceData, activityData);
    const recommendations = generateScalingRecommendations(prediction, currentScaling);
    
    res.json({ prediction, recommendations });
  } catch (error) {
    logger.error('Error in GET /api/scaling-recommendations', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/scale-services', async (req, res) => {
  try {
    const { apply = false } = req.body;
    
    const result = await scaleServices(apply);
    res.json(result);
  } catch (error) {
    logger.error('Error in POST /api/scale-services', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.PROACTIVE_SCALER_PORT || 9110;
app.listen(PORT, () => {
  logger.info(`Proactive Scaler listening on port ${PORT}`);
});

module.exports = {
  predictServiceLoad,
  generateScalingRecommendations,
  scaleServices
};
