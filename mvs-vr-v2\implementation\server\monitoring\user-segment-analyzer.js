/**
 * User Segment Analyzer
 *
 * This service analyzes user behavior and segments users based on their activity patterns.
 * It integrates with the existing monitoring system to provide segment-specific metrics.
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Create Express app
const app = express();

// Create a Registry for user segment metrics
const register = new promClient.Registry();

// Define user segments
const USER_SEGMENTS = {
  // User activity-based segments
  POWER_USER: 'power_user',
  NEW_USER: 'new_user',
  INACTIVE_USER: 'inactive_user',
  RETURNING_USER: 'returning_user',

  // Role-based segments
  VENDOR_ADMIN: 'vendor_admin',
  CLIENT_USER: 'client_user',

  // Device-based segments
  MOBILE_USER: 'mobile_user',
  DESKTOP_USER: 'desktop_user',
  VR_USER: 'vr_user',

  // Time-based segments
  MORNING_USER: 'morning_user',
  AFTERNOON_USER: 'afternoon_user',
  EVENING_USER: 'evening_user',
  WEEKEND_USER: 'weekend_user',

  // Feature usage segments
  ASSET_UPLOADER: 'asset_uploader',
  SHOWROOM_VIEWER: 'showroom_viewer',
  CONFIGURATION_USER: 'configuration_user',

  // Performance experience segments
  HIGH_LATENCY_USER: 'high_latency_user',
  ERROR_PRONE_USER: 'error_prone_user',
};

// Create custom metrics for user segments
const metrics = {
  // User segment counts
  segmentUserCount: new promClient.Gauge({
    name: 'mvs_vr_segment_user_count',
    help: 'Number of users in each segment',
    labelNames: ['segment'],
  }),

  // Segment performance metrics
  segmentApiLatency: new promClient.Gauge({
    name: 'mvs_vr_segment_api_latency_ms',
    help: 'API latency by user segment in milliseconds',
    labelNames: ['segment', 'endpoint'],
  }),

  segmentPageLoadTime: new promClient.Gauge({
    name: 'mvs_vr_segment_page_load_time_ms',
    help: 'Page load time by user segment in milliseconds',
    labelNames: ['segment', 'page'],
  }),

  segmentErrorRate: new promClient.Gauge({
    name: 'mvs_vr_segment_error_rate_percent',
    help: 'Error rate by user segment in percent',
    labelNames: ['segment', 'error_type'],
  }),

  // Segment engagement metrics
  segmentSessionDuration: new promClient.Gauge({
    name: 'mvs_vr_segment_session_duration_seconds',
    help: 'Average session duration by user segment in seconds',
    labelNames: ['segment'],
  }),

  segmentFeatureUsage: new promClient.Gauge({
    name: 'mvs_vr_segment_feature_usage_count',
    help: 'Feature usage count by user segment',
    labelNames: ['segment', 'feature'],
  }),

  segmentConversionRate: new promClient.Gauge({
    name: 'mvs_vr_segment_conversion_rate_percent',
    help: 'Conversion rate by user segment in percent',
    labelNames: ['segment', 'conversion_type'],
  }),

  // Segment business metrics
  segmentShowroomVisits: new promClient.Gauge({
    name: 'mvs_vr_segment_showroom_visits',
    help: 'Showroom visits by user segment',
    labelNames: ['segment', 'showroom_id'],
  }),

  segmentAssetUploads: new promClient.Gauge({
    name: 'mvs_vr_segment_asset_uploads',
    help: 'Asset uploads by user segment',
    labelNames: ['segment', 'asset_type'],
  }),
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Identify user segments based on activity patterns
 */
async function identifyUserSegments() {
  try {
    // Get all users with their activity data
    const { data: users, error } = await supabase.from('users').select(`
        id,
        role,
        created_at,
        last_login,
        session_count,
        user_agent,
        user_metadata
      `);

    if (error) {
      logger.error('Error fetching users for segmentation', { error: error.message });
      return;
    }

    // Get session data for activity analysis
    const { data: sessions, error: sessionsError } = await supabase
      .from('user_sessions')
      .select(
        `
        user_id,
        duration,
        device_type,
        actions_count,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    if (sessionsError) {
      logger.error('Error fetching session data for segmentation', {
        error: sessionsError.message,
      });
      return;
    }

    // Group sessions by user
    const sessionsByUser = sessions.reduce((acc, session) => {
      if (!acc[session.user_id]) {
        acc[session.user_id] = [];
      }
      acc[session.user_id].push(session);
      return acc;
    }, {});

    // Initialize segment counters
    const segmentCounts = Object.values(USER_SEGMENTS).reduce((acc, segment) => {
      acc[segment] = 0;
      return acc;
    }, {});

    // Segment users
    const userSegments = {};

    // Get feature usage data
    const { data: featureUsage, error: featureError } = await supabase
      .from('user_feature_usage')
      .select(
        `
        user_id,
        feature,
        count,
        last_used
      `,
      )
      .gte('last_used', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    if (featureError) {
      logger.error('Error fetching feature usage data for segmentation', {
        error: featureError.message,
      });
    }

    // Group feature usage by user
    const featureUsageByUser = (featureUsage || []).reduce((acc, usage) => {
      if (!acc[usage.user_id]) {
        acc[usage.user_id] = [];
      }
      acc[usage.user_id].push(usage);
      return acc;
    }, {});

    // Get API performance data for user experience segmentation
    const { data: apiPerformance, error: apiError } = await supabase
      .from('api_metrics')
      .select(
        `
        user_id,
        endpoint,
        response_time,
        status_code,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    if (apiError) {
      logger.error('Error fetching API performance data for segmentation', {
        error: apiError.message,
      });
    }

    // Group API performance by user
    const apiPerformanceByUser = (apiPerformance || []).reduce((acc, perf) => {
      if (!acc[perf.user_id]) {
        acc[perf.user_id] = [];
      }
      acc[perf.user_id].push(perf);
      return acc;
    }, {});

    users.forEach(user => {
      const userSessions = sessionsByUser[user.id] || [];
      const userFeatures = featureUsageByUser[user.id] || [];
      const userApiPerformance = apiPerformanceByUser[user.id] || [];
      const segments = [];

      // Activity-based segments

      // Check for power users
      if (userSessions.length >= 20 || userSessions.some(s => s.actions_count >= 100)) {
        segments.push(USER_SEGMENTS.POWER_USER);
        segmentCounts[USER_SEGMENTS.POWER_USER]++;
      }

      // Check for new users
      const createdAt = new Date(user.created_at);
      const isNewUser = Date.now() - createdAt.getTime() < 7 * 24 * 60 * 60 * 1000; // 7 days
      if (isNewUser || userSessions.length <= 5) {
        segments.push(USER_SEGMENTS.NEW_USER);
        segmentCounts[USER_SEGMENTS.NEW_USER]++;
      }

      // Check for inactive users
      const lastLogin = user.last_login ? new Date(user.last_login) : null;
      const isInactive = lastLogin && Date.now() - lastLogin.getTime() > 30 * 24 * 60 * 60 * 1000; // 30 days
      if (isInactive) {
        segments.push(USER_SEGMENTS.INACTIVE_USER);
        segmentCounts[USER_SEGMENTS.INACTIVE_USER]++;
      }

      // Check for returning users (not new, but active)
      if (!isNewUser && !isInactive && userSessions.length > 0) {
        segments.push(USER_SEGMENTS.RETURNING_USER);
        segmentCounts[USER_SEGMENTS.RETURNING_USER]++;
      }

      // Role-based segments
      if (user.role === 'vendor_admin') {
        segments.push(USER_SEGMENTS.VENDOR_ADMIN);
        segmentCounts[USER_SEGMENTS.VENDOR_ADMIN]++;
      } else if (user.role === 'client') {
        segments.push(USER_SEGMENTS.CLIENT_USER);
        segmentCounts[USER_SEGMENTS.CLIENT_USER]++;
      }

      // Device-based segments
      const deviceTypes = new Set(userSessions.map(s => s.device_type));

      if (deviceTypes.has('mobile')) {
        segments.push(USER_SEGMENTS.MOBILE_USER);
        segmentCounts[USER_SEGMENTS.MOBILE_USER]++;
      }

      if (deviceTypes.has('desktop')) {
        segments.push(USER_SEGMENTS.DESKTOP_USER);
        segmentCounts[USER_SEGMENTS.DESKTOP_USER]++;
      }

      if (deviceTypes.has('vr')) {
        segments.push(USER_SEGMENTS.VR_USER);
        segmentCounts[USER_SEGMENTS.VR_USER]++;
      }

      // Time-based segments
      if (userSessions.length > 0) {
        // Analyze session times
        const sessionTimes = userSessions.map(s => new Date(s.created_at));

        // Count sessions by time of day
        let morningCount = 0;
        let afternoonCount = 0;
        let eveningCount = 0;
        let weekendCount = 0;

        sessionTimes.forEach(time => {
          const hour = time.getHours();
          const day = time.getDay();

          // Time of day
          if (hour >= 5 && hour < 12) {
            morningCount++;
          } else if (hour >= 12 && hour < 18) {
            afternoonCount++;
          } else {
            eveningCount++;
          }

          // Weekend
          if (day === 0 || day === 6) {
            weekendCount++;
          }
        });

        // Assign time-based segments based on predominant usage
        const totalSessions = sessionTimes.length;

        if (morningCount / totalSessions > 0.5) {
          segments.push(USER_SEGMENTS.MORNING_USER);
          segmentCounts[USER_SEGMENTS.MORNING_USER]++;
        }

        if (afternoonCount / totalSessions > 0.5) {
          segments.push(USER_SEGMENTS.AFTERNOON_USER);
          segmentCounts[USER_SEGMENTS.AFTERNOON_USER]++;
        }

        if (eveningCount / totalSessions > 0.5) {
          segments.push(USER_SEGMENTS.EVENING_USER);
          segmentCounts[USER_SEGMENTS.EVENING_USER]++;
        }

        if (weekendCount / totalSessions > 0.5) {
          segments.push(USER_SEGMENTS.WEEKEND_USER);
          segmentCounts[USER_SEGMENTS.WEEKEND_USER]++;
        }
      }

      // Feature usage segments
      if (userFeatures.length > 0) {
        const featureMap = userFeatures.reduce((acc, f) => {
          acc[f.feature] = f.count;
          return acc;
        }, {});

        if (featureMap['asset_upload'] && featureMap['asset_upload'] >= 5) {
          segments.push(USER_SEGMENTS.ASSET_UPLOADER);
          segmentCounts[USER_SEGMENTS.ASSET_UPLOADER]++;
        }

        if (featureMap['showroom_view'] && featureMap['showroom_view'] >= 10) {
          segments.push(USER_SEGMENTS.SHOWROOM_VIEWER);
          segmentCounts[USER_SEGMENTS.SHOWROOM_VIEWER]++;
        }

        if (featureMap['product_configure'] && featureMap['product_configure'] >= 5) {
          segments.push(USER_SEGMENTS.CONFIGURATION_USER);
          segmentCounts[USER_SEGMENTS.CONFIGURATION_USER]++;
        }
      }

      // Performance experience segments
      if (userApiPerformance.length > 0) {
        // Calculate average latency
        const totalLatency = userApiPerformance.reduce((sum, perf) => sum + perf.response_time, 0);
        const avgLatency = totalLatency / userApiPerformance.length;

        // Count errors
        const errorCount = userApiPerformance.filter(perf => perf.status_code >= 400).length;
        const errorRate = errorCount / userApiPerformance.length;

        // High latency users (avg > 500ms)
        if (avgLatency > 500) {
          segments.push(USER_SEGMENTS.HIGH_LATENCY_USER);
          segmentCounts[USER_SEGMENTS.HIGH_LATENCY_USER]++;
        }

        // Error prone users (error rate > 10%)
        if (errorRate > 0.1) {
          segments.push(USER_SEGMENTS.ERROR_PRONE_USER);
          segmentCounts[USER_SEGMENTS.ERROR_PRONE_USER]++;
        }
      }

      userSegments[user.id] = segments;
    });

    // Update segment count metrics
    Object.entries(segmentCounts).forEach(([segment, count]) => {
      metrics.segmentUserCount.set({ segment }, count);
    });

    // Store user segments for later use
    await analyzeSegmentPerformance(userSegments, sessionsByUser);

    logger.info('User segmentation completed', { segmentCounts });
  } catch (error) {
    logger.error('Error in user segmentation', { error: error.message });
  }
}

/**
 * Analyze performance metrics by user segment
 */
async function analyzeSegmentPerformance(userSegments, sessionsByUser) {
  try {
    // Get API performance data
    const { data: apiMetrics, error: apiError } = await supabase
      .from('api_metrics')
      .select(
        `
        user_id,
        endpoint,
        response_time,
        status_code,
        created_at
      `,
      )
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (apiError) {
      logger.error('Error fetching API metrics for segment analysis', { error: apiError.message });
      return;
    }

    // Calculate segment-specific metrics
    const segmentApiLatencies = {};
    const segmentErrorRates = {};

    // Process API metrics by segment
    apiMetrics.forEach(metric => {
      const userId = metric.user_id;
      const segments = userSegments[userId] || [];

      segments.forEach(segment => {
        // Track API latency by segment and endpoint
        if (!segmentApiLatencies[`${segment}-${metric.endpoint}`]) {
          segmentApiLatencies[`${segment}-${metric.endpoint}`] = {
            total: 0,
            count: 0,
          };
        }

        segmentApiLatencies[`${segment}-${metric.endpoint}`].total += metric.response_time;
        segmentApiLatencies[`${segment}-${metric.endpoint}`].count += 1;

        // Track error rates by segment
        const isError = metric.status_code >= 400;
        if (isError) {
          if (!segmentErrorRates[segment]) {
            segmentErrorRates[segment] = {
              errors: 0,
              total: 0,
            };
          }

          segmentErrorRates[segment].errors += 1;
        }

        if (!segmentErrorRates[segment]) {
          segmentErrorRates[segment] = {
            errors: 0,
            total: 0,
          };
        }

        segmentErrorRates[segment].total += 1;
      });
    });

    // Update API latency metrics
    Object.entries(segmentApiLatencies).forEach(([key, data]) => {
      const [segment, endpoint] = key.split('-');
      const avgLatency = data.total / data.count;

      metrics.segmentApiLatency.set({ segment, endpoint }, avgLatency);
    });

    // Update error rate metrics
    Object.entries(segmentErrorRates).forEach(([segment, data]) => {
      if (data.total > 0) {
        const errorRate = (data.errors / data.total) * 100;
        metrics.segmentErrorRate.set({ segment, error_type: 'api' }, errorRate);
      }
    });

    // Calculate average session duration by segment
    const segmentSessionDurations = {};

    Object.entries(sessionsByUser).forEach(([userId, sessions]) => {
      const segments = userSegments[userId] || [];

      segments.forEach(segment => {
        if (!segmentSessionDurations[segment]) {
          segmentSessionDurations[segment] = {
            total: 0,
            count: 0,
          };
        }

        sessions.forEach(session => {
          segmentSessionDurations[segment].total += session.duration;
          segmentSessionDurations[segment].count += 1;
        });
      });
    });

    // Update session duration metrics
    Object.entries(segmentSessionDurations).forEach(([segment, data]) => {
      if (data.count > 0) {
        const avgDuration = data.total / data.count;
        metrics.segmentSessionDuration.set({ segment }, avgDuration);
      }
    });

    logger.info('Segment performance analysis completed');
  } catch (error) {
    logger.error('Error in segment performance analysis', { error: error.message });
  }
}

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    // Collect latest metrics before responding
    await identifyUserSegments();

    // Return metrics in Prometheus format
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving user segment metrics', { error: error.message });
    res.status(500).send('Error collecting user segment metrics');
  }
});

// Start server
const PORT = process.env.USER_SEGMENT_ANALYZER_PORT || 9095;
app.listen(PORT, () => {
  logger.info(`User Segment Analyzer listening on port ${PORT}`);
});

// Schedule regular updates
const UPDATE_INTERVAL_MS = 15 * 60 * 1000; // 15 minutes
setInterval(identifyUserSegments, UPDATE_INTERVAL_MS);

// Initial data collection
identifyUserSegments().catch(error => {
  logger.error('Error in initial user segmentation', { error: error.message });
});

module.exports = {
  identifyUserSegments,
  analyzeSegmentPerformance,
  USER_SEGMENTS,
};
