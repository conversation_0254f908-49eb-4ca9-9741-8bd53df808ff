/**
 * Vitest setup file for the vendor portal extension
 *
 * This file sets up the testing environment for Vue 2.x components
 */

import Vue from 'vue';
import { config } from '@vue/test-utils';
import { vi } from 'vitest';

// Make Vue production tip quiet
Vue.config.productionTip = false;

// Set up global Vue compiler options
Vue.config.compilerOptions = {
  whitespace: 'condense',
};

// Mock localStorage
const localStorageMock = (() => {
  let store = {};

  return {
    getItem: key => store[key] || null,
    setItem: (key, value) => {
      store[key] = String(value);
    },
    removeItem: key => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

// Mock window properties
Object.defineProperty(globalThis, 'localStorage', { value: localStorageMock });

// Mock Directus global
globalThis.directus = {
  url: 'http://localhost:8055',
  auth: {
    token: 'mock-token',
    user: {
      id: 'user-id',
      vendor_id: 'vendor-id',
      role: 'admin',
    },
  },
  permissions: [
    { collection: 'vendor_onboarding', action: 'create' },
    { collection: 'vendor_onboarding', action: 'read' },
    { collection: 'vendor_onboarding', action: 'update' },
    { collection: 'vendor_onboarding', action: 'delete' },
  ],
};

// Mock Material Icons
class MaterialIconsMock extends HTMLElement {
  constructor() {
    super();
  }
}

// Register custom element with valid name
if (globalThis.customElements) {
  globalThis.customElements.define('material-icon', MaterialIconsMock);
}

// Configure Vue Test Utils
config.mocks = {
  $directus: globalThis.directus,
};

// Global mocks for all components
config.stubs = {
  'material-icon': true,
  'v-icon': true,
};

// Make vi available globally for mocking
globalThis.vi = vi;

// Create a global mock for axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    })),
  },
}));
