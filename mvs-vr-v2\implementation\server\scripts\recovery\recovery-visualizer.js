/**
 * Recovery Visualizer
 * 
 * This script generates visualizations of the recovery dependency graph and recovery process.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);
const { buildDependencyGraph, sortComponentsByRecoveryOrder } = require('./recovery-orchestrator');
const logger = require('../../utils/logger').getLogger('recovery-visualizer');
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  outputDir: path.join(__dirname, '../../public/recovery'),
  recoveryLogPath: path.join(__dirname, '../../logs/recovery-orchestration.json'),
  colors: {
    success: '#4CAF50',
    failure: '#F44336',
    pending: '#FFC107',
    running: '#2196F3',
    node: '#9C27B0',
    edge: '#607D8B',
    background: '#FFFFFF',
    text: '#212121'
  }
};

/**
 * Ensure output directory exists
 */
async function ensureOutputDirectory() {
  if (!await existsAsync(config.outputDir)) {
    await mkdirAsync(config.outputDir, { recursive: true });
  }
}

/**
 * Generate DOT graph for dependency visualization
 * @param {Object} graph - Dependency graph
 * @returns {string} DOT graph
 */
function generateDotGraph(graph) {
  const lines = [
    'digraph RecoveryDependencies {',
    '  rankdir=LR;',
    '  node [shape=box, style=filled, fillcolor="#E1BEE7", fontname="Arial"];',
    '  edge [color="#607D8B"];',
    ''
  ];
  
  // Add nodes
  for (const [id, component] of Object.entries(graph)) {
    lines.push(`  "${id}" [label="${component.name}\\n(Priority: ${component.priority})"];`);
  }
  
  lines.push('');
  
  // Add edges
  for (const [id, component] of Object.entries(graph)) {
    for (const dependency of component.dependencies) {
      lines.push(`  "${dependency}" -> "${id}";`);
    }
  }
  
  lines.push('}');
  
  return lines.join('\n');
}

/**
 * Generate HTML visualization for dependency graph
 * @param {Object} graph - Dependency graph
 * @returns {string} HTML content
 */
function generateHtmlVisualization(graph) {
  const sortedComponents = sortComponentsByRecoveryOrder(graph);
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recovery Dependency Visualization</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .graph {
      margin-top: 20px;
      overflow-x: auto;
    }
    .recovery-order {
      margin-top: 30px;
    }
    .component-list {
      list-style-type: none;
      padding: 0;
    }
    .component-item {
      padding: 10px;
      margin: 5px 0;
      background-color: #e8eaf6;
      border-radius: 3px;
      display: flex;
      align-items: center;
    }
    .component-number {
      background-color: #3f51b5;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      font-weight: bold;
    }
    .component-details {
      flex: 1;
    }
    .component-name {
      font-weight: bold;
    }
    .component-deps {
      font-size: 0.9em;
      color: #666;
      margin-top: 5px;
    }
    .priority-badge {
      background-color: #ff9800;
      color: white;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 0.8em;
      margin-left: 10px;
    }
    .dependency-graph {
      width: 100%;
      height: 500px;
      border: 1px solid #ddd;
      margin-top: 20px;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <div class="container">
    <h1>Recovery Dependency Visualization</h1>
    
    <h2>Dependency Graph</h2>
    <div class="graph">
      <div class="mermaid">
        graph LR;
        ${Object.entries(graph).map(([id, component]) => {
          return `${id}["${component.name} (Priority: ${component.priority})"];`;
        }).join('\n        ')}
        
        ${Object.entries(graph).flatMap(([id, component]) => {
          return component.dependencies.map(dep => `${dep} --> ${id};`);
        }).join('\n        ')}
      </div>
    </div>
    
    <div class="recovery-order">
      <h2>Recovery Order</h2>
      <ol class="component-list">
        ${sortedComponents.map((component, index) => `
        <li class="component-item">
          <div class="component-number">${index + 1}</div>
          <div class="component-details">
            <div class="component-name">${component.name} <span class="priority-badge">Priority: ${component.priority}</span></div>
            <div class="component-deps">
              Dependencies: ${component.dependencies.length > 0 ? component.dependencies.map(dep => graph[dep].name).join(', ') : 'None'}
            </div>
          </div>
        </li>
        `).join('')}
      </ol>
    </div>
  </div>

  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: 'default',
      securityLevel: 'loose',
    });
  </script>
</body>
</html>
  `;
  
  return html;
}

/**
 * Generate recovery process visualization
 * @param {Object} recoveryResults - Recovery results
 * @returns {string} HTML content
 */
function generateRecoveryVisualization(recoveryResults) {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recovery Process Visualization</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .summary {
      margin: 20px 0;
      padding: 15px;
      background-color: ${recoveryResults.success ? '#e8f5e9' : '#ffebee'};
      border-radius: 5px;
    }
    .summary-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .summary-status {
      font-size: 1.2em;
      color: ${recoveryResults.success ? '#4caf50' : '#f44336'};
      font-weight: bold;
    }
    .summary-details {
      margin-top: 10px;
    }
    .component-list {
      list-style-type: none;
      padding: 0;
    }
    .component-item {
      padding: 15px;
      margin: 10px 0;
      border-radius: 5px;
      border-left: 5px solid;
    }
    .component-success {
      background-color: #e8f5e9;
      border-left-color: #4caf50;
    }
    .component-failure {
      background-color: #ffebee;
      border-left-color: #f44336;
    }
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .component-name {
      font-weight: bold;
      font-size: 1.1em;
    }
    .component-status {
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 0.8em;
      font-weight: bold;
    }
    .status-success {
      background-color: #4caf50;
      color: white;
    }
    .status-failure {
      background-color: #f44336;
      color: white;
    }
    .component-details {
      font-size: 0.9em;
      color: #666;
    }
    .component-time {
      margin-top: 10px;
      font-size: 0.9em;
      color: #666;
    }
    .timeline {
      margin-top: 30px;
    }
    .timeline-container {
      position: relative;
      margin-top: 20px;
      padding-left: 20px;
    }
    .timeline-line {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: #ddd;
    }
    .timeline-item {
      position: relative;
      padding-bottom: 20px;
    }
    .timeline-dot {
      position: absolute;
      left: -25px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      top: 5px;
    }
    .timeline-dot-success {
      background-color: #4caf50;
    }
    .timeline-dot-failure {
      background-color: #f44336;
    }
    .timeline-content {
      padding-left: 15px;
    }
    .timeline-time {
      font-size: 0.8em;
      color: #666;
      margin-bottom: 5px;
    }
    .timeline-text {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 3px;
      overflow-x: auto;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Recovery Process Visualization</h1>
    
    <div class="summary">
      <div class="summary-title">Recovery Summary</div>
      <div class="summary-status">
        ${recoveryResults.success ? 'SUCCESS' : 'FAILURE'}
      </div>
      <div class="summary-details">
        <div>Start Time: ${new Date(recoveryResults.startTime).toLocaleString()}</div>
        <div>End Time: ${new Date(recoveryResults.endTime).toLocaleString()}</div>
        <div>Duration: ${recoveryResults.duration} seconds</div>
      </div>
    </div>
    
    <h2>Component Recovery Details</h2>
    <ul class="component-list">
      ${Object.entries(recoveryResults.components).map(([id, component]) => `
      <li class="component-item ${component.success ? 'component-success' : 'component-failure'}">
        <div class="component-header">
          <div class="component-name">${component.name} (${id})</div>
          <div class="component-status ${component.success ? 'status-success' : 'status-failure'}">
            ${component.success ? 'SUCCESS' : 'FAILURE'}
          </div>
        </div>
        <div class="component-details">
          <div>Retries: ${component.retries}</div>
          <div>Duration: ${component.duration} seconds</div>
          ${component.details.error ? `<div>Error: ${component.details.error}</div>` : ''}
        </div>
        <div class="component-time">
          <div>Start: ${new Date(component.startTime).toLocaleString()}</div>
          <div>End: ${new Date(component.endTime).toLocaleString()}</div>
        </div>
      </li>
      `).join('')}
    </ul>
    
    <div class="timeline">
      <h2>Recovery Timeline</h2>
      <div class="timeline-container">
        <div class="timeline-line"></div>
        
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <div class="timeline-time">${new Date(recoveryResults.startTime).toLocaleString()}</div>
            <div class="timeline-text">Recovery process started</div>
          </div>
        </div>
        
        ${Object.entries(recoveryResults.components).map(([id, component]) => `
        <div class="timeline-item">
          <div class="timeline-dot ${component.success ? 'timeline-dot-success' : 'timeline-dot-failure'}"></div>
          <div class="timeline-content">
            <div class="timeline-time">${new Date(component.startTime).toLocaleString()}</div>
            <div class="timeline-text">
              Started recovery of ${component.name} (${id})
            </div>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-dot ${component.success ? 'timeline-dot-success' : 'timeline-dot-failure'}"></div>
          <div class="timeline-content">
            <div class="timeline-time">${new Date(component.endTime).toLocaleString()}</div>
            <div class="timeline-text">
              Completed recovery of ${component.name} (${id}) - ${component.success ? 'SUCCESS' : 'FAILURE'}
              ${component.retries > 0 ? `<br>Required ${component.retries} retries` : ''}
              ${component.details.error ? `<br>Error: ${component.details.error}` : ''}
            </div>
          </div>
        </div>
        `).join('')}
        
        <div class="timeline-item">
          <div class="timeline-dot ${recoveryResults.success ? 'timeline-dot-success' : 'timeline-dot-failure'}"></div>
          <div class="timeline-content">
            <div class="timeline-time">${new Date(recoveryResults.endTime).toLocaleString()}</div>
            <div class="timeline-text">Recovery process completed - ${recoveryResults.success ? 'SUCCESS' : 'FAILURE'}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
  `;
  
  return html;
}

/**
 * Generate dependency graph visualization
 * @returns {Promise<string>} Path to visualization file
 */
async function generateDependencyVisualization() {
  await ensureOutputDirectory();
  
  // Build dependency graph
  const graph = buildDependencyGraph();
  
  // Generate DOT graph
  const dotGraph = generateDotGraph(graph);
  const dotPath = path.join(config.outputDir, 'dependency-graph.dot');
  await writeFileAsync(dotPath, dotGraph, 'utf8');
  
  // Generate HTML visualization
  const html = generateHtmlVisualization(graph);
  const htmlPath = path.join(config.outputDir, 'dependency-graph.html');
  await writeFileAsync(htmlPath, html, 'utf8');
  
  logger.info(`Generated dependency graph visualization at ${htmlPath}`);
  
  return htmlPath;
}

/**
 * Generate recovery process visualization from log
 * @param {string} recoveryId - Recovery ID (timestamp)
 * @returns {Promise<string>} Path to visualization file
 */
async function generateRecoveryVisualizationFromLog(recoveryId = null) {
  await ensureOutputDirectory();
  
  // Read recovery log
  const logData = await fs.promises.readFile(config.recoveryLogPath, 'utf8');
  const log = JSON.parse(logData);
  
  // Get recovery results
  let recoveryResults;
  
  if (recoveryId) {
    recoveryResults = log.recoveries.find(r => r.startTime === recoveryId);
    
    if (!recoveryResults) {
      throw new Error(`Recovery with ID ${recoveryId} not found`);
    }
  } else {
    // Use latest recovery
    recoveryResults = log.recoveries[log.recoveries.length - 1];
    
    if (!recoveryResults) {
      throw new Error('No recovery results found in log');
    }
  }
  
  // Generate HTML visualization
  const html = generateRecoveryVisualization(recoveryResults);
  const timestamp = new Date(recoveryResults.startTime).toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const htmlPath = path.join(config.outputDir, `recovery-process-${timestamp}.html`);
  await writeFileAsync(htmlPath, html, 'utf8');
  
  logger.info(`Generated recovery process visualization at ${htmlPath}`);
  
  return htmlPath;
}

// If script is run directly, generate visualizations
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  const recoveryId = args[1];
  
  (async () => {
    try {
      if (command === 'dependency' || command === 'all') {
        const dependencyPath = await generateDependencyVisualization();
        logger.info(`Dependency graph visualization generated at ${dependencyPath}`);
      }
      
      if (command === 'recovery' || command === 'all') {
        const recoveryPath = await generateRecoveryVisualizationFromLog(recoveryId);
        logger.info(`Recovery process visualization generated at ${recoveryPath}`);
      }
    } catch (error) {
      console.error('Error generating visualizations:', error);
      process.exit(1);
    }
  })();
}

module.exports = {
  generateDependencyVisualization,
  generateRecoveryVisualizationFromLog
};
