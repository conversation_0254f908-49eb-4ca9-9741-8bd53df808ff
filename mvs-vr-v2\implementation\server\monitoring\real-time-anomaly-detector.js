/**
 * Real-time Anomaly Detector
 * 
 * This service detects anomalies in real-time metrics using various statistical methods
 * and machine learning techniques. It integrates with the existing monitoring system
 * to provide immediate detection of unusual patterns in system behavior.
 */

const express = require('express');
const promClient = require('prom-client');
const { createLogger, format, transports } = require('winston');
const { Kafka } = require('kafkajs');
const axios = require('axios');
const { EventEmitter } = require('events');

// Configuration
const config = {
  port: parseInt(process.env.REAL_TIME_ANOMALY_DETECTOR_PORT || '9112', 10),
  logLevel: process.env.LOG_LEVEL || 'info',
  metricsEndpoint: process.env.METRICS_ENDPOINT || 'http://prometheus:9090/api/v1/query',
  alertManagerEndpoint: process.env.ALERT_MANAGER_ENDPOINT || 'http://alert-manager-service:9096/api/alerts',
  kafkaBrokers: (process.env.KAFKA_BROKERS || 'kafka:9092').split(','),
  kafkaClientId: process.env.KAFKA_CLIENT_ID || 'real-time-anomaly-detector',
  kafkaGroupId: process.env.KAFKA_GROUP_ID || 'anomaly-detector-group',
  kafkaMetricsTopic: process.env.KAFKA_METRICS_TOPIC || 'metrics',
  anomalyThresholds: {
    zScore: parseFloat(process.env.ANOMALY_THRESHOLD_ZSCORE || '3.0'),
    mad: parseFloat(process.env.ANOMALY_THRESHOLD_MAD || '3.5'),
    iqr: parseFloat(process.env.ANOMALY_THRESHOLD_IQR || '1.5'),
    percentChange: parseFloat(process.env.ANOMALY_THRESHOLD_PERCENT_CHANGE || '50.0'),
    consecutiveIncreases: parseInt(process.env.ANOMALY_THRESHOLD_CONSECUTIVE_INCREASES || '5', 10),
    suddenSpike: parseFloat(process.env.ANOMALY_THRESHOLD_SUDDEN_SPIKE || '100.0')
  },
  windowSizes: {
    short: parseInt(process.env.WINDOW_SIZE_SHORT || '5', 10), // 5 minutes
    medium: parseInt(process.env.WINDOW_SIZE_MEDIUM || '30', 10), // 30 minutes
    long: parseInt(process.env.WINDOW_SIZE_LONG || '120', 10) // 2 hours
  },
  updateInterval: parseInt(process.env.UPDATE_INTERVAL || '10000', 10), // 10 seconds
  metricPatterns: (process.env.METRIC_PATTERNS || 'mvs_vr_http_,mvs_vr_db_,mvs_vr_auth_,mvs_vr_asset_').split(',')
};

// Setup logger
const logger = createLogger({
  level: config.logLevel,
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  defaultMeta: { service: 'real-time-anomaly-detector' },
  transports: [
    new transports.Console()
  ]
});

// Initialize Prometheus metrics
const register = new promClient.Registry();
promClient.collectDefaultMetrics({ register });

// Custom metrics
const metrics = {
  anomaliesDetected: new promClient.Counter({
    name: 'mvs_vr_anomalies_detected_total',
    help: 'Total number of anomalies detected',
    labelNames: ['metric_name', 'detection_method', 'severity']
  }),
  anomalyScore: new promClient.Gauge({
    name: 'mvs_vr_anomaly_score',
    help: 'Anomaly score for a metric (0-100)',
    labelNames: ['metric_name', 'detection_method']
  }),
  metricPrediction: new promClient.Gauge({
    name: 'mvs_vr_metric_prediction',
    help: 'Predicted value for a metric',
    labelNames: ['metric_name', 'window_size']
  }),
  anomalyDetectionLatency: new promClient.Histogram({
    name: 'mvs_vr_anomaly_detection_latency_seconds',
    help: 'Latency of anomaly detection process',
    labelNames: ['detection_method'],
    buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5]
  })
};

// Register custom metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Real-time Anomaly Detector Service
 */
class RealTimeAnomalyDetector extends EventEmitter {
  constructor() {
    super();
    this.app = express();
    this.metricHistory = new Map();
    this.anomalyHistory = new Map();
    this.setupExpress();
    this.setupKafka();
    this.setupIntervals();
  }

  /**
   * Setup Express server
   */
  setupExpress() {
    this.app.get('/metrics', async (req, res) => {
      res.set('Content-Type', register.contentType);
      res.end(await register.metrics());
    });

    this.app.get('/api/anomalies', (req, res) => {
      const anomalies = Array.from(this.anomalyHistory.entries()).map(([key, value]) => ({
        metric: key,
        ...value
      }));
      res.json(anomalies);
    });

    this.app.get('/api/health', (req, res) => {
      res.json({ status: 'ok' });
    });
  }

  /**
   * Setup Kafka consumer
   */
  async setupKafka() {
    try {
      this.kafka = new Kafka({
        clientId: config.kafkaClientId,
        brokers: config.kafkaBrokers
      });

      this.consumer = this.kafka.consumer({ groupId: config.kafkaGroupId });
      await this.consumer.connect();
      await this.consumer.subscribe({ topic: config.kafkaMetricsTopic, fromBeginning: false });

      await this.consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          try {
            const metricData = JSON.parse(message.value.toString());
            this.processMetricData(metricData);
          } catch (error) {
            logger.error('Error processing Kafka message', { error: error.message });
          }
        }
      });

      logger.info('Kafka consumer setup complete');
    } catch (error) {
      logger.error('Error setting up Kafka consumer', { error: error.message });
    }
  }

  /**
   * Setup intervals for periodic tasks
   */
  setupIntervals() {
    // Fetch metrics from Prometheus periodically
    setInterval(() => this.fetchMetricsFromPrometheus(), config.updateInterval);

    // Detect anomalies periodically
    setInterval(() => this.detectAnomalies(), config.updateInterval);

    // Clean up old data periodically
    setInterval(() => this.cleanupOldData(), config.updateInterval * 10);
  }

  /**
   * Fetch metrics from Prometheus
   */
  async fetchMetricsFromPrometheus() {
    try {
      for (const pattern of config.metricPatterns) {
        const query = `{__name__=~"${pattern}.*"}`;
        const response = await axios.get(config.metricsEndpoint, {
          params: { query }
        });

        if (response.data && response.data.data && response.data.data.result) {
          response.data.data.result.forEach(result => {
            const metricName = result.metric.__name__;
            const value = parseFloat(result.value[1]);
            const timestamp = result.value[0] * 1000;

            this.processMetricData({
              name: metricName,
              value,
              timestamp,
              labels: result.metric
            });
          });
        }
      }
    } catch (error) {
      logger.error('Error fetching metrics from Prometheus', { error: error.message });
    }
  }

  /**
   * Process incoming metric data
   * @param {Object} metricData - Metric data object
   */
  processMetricData(metricData) {
    const { name, value, timestamp, labels } = metricData;
    
    if (!this.metricHistory.has(name)) {
      this.metricHistory.set(name, []);
    }

    const history = this.metricHistory.get(name);
    history.push({ value, timestamp, labels });

    // Keep only the last N data points
    const maxHistorySize = Math.max(
      config.windowSizes.short,
      config.windowSizes.medium,
      config.windowSizes.long
    ) * 6; // 6 data points per minute (10-second intervals)

    if (history.length > maxHistorySize) {
      history.shift();
    }
  }

  /**
   * Detect anomalies in all metrics
   */
  async detectAnomalies() {
    for (const [metricName, history] of this.metricHistory.entries()) {
      if (history.length < config.windowSizes.short) {
        continue; // Not enough data
      }

      const startTime = Date.now();
      
      // Get recent values for different window sizes
      const recentValues = {
        short: history.slice(-config.windowSizes.short).map(h => h.value),
        medium: history.slice(-config.windowSizes.medium).map(h => h.value),
        long: history.slice(-config.windowSizes.long).map(h => h.value)
      };

      // Detect anomalies using different methods
      const anomalies = {
        zScore: this.detectZScoreAnomalies(metricName, recentValues),
        mad: this.detectMADAnomalies(metricName, recentValues),
        iqr: this.detectIQRAnomalies(metricName, recentValues),
        percentChange: this.detectPercentChangeAnomalies(metricName, recentValues),
        consecutiveIncreases: this.detectConsecutiveIncreasesAnomalies(metricName, recentValues),
        suddenSpike: this.detectSuddenSpikeAnomalies(metricName, recentValues)
      };

      // Record detection latency
      const endTime = Date.now();
      const latencySeconds = (endTime - startTime) / 1000;
      Object.keys(anomalies).forEach(method => {
        metrics.anomalyDetectionLatency.observe({ detection_method: method }, latencySeconds);
      });

      // Process detected anomalies
      this.processAnomalyResults(metricName, anomalies);
    }
  }

  /**
   * Process anomaly detection results
   * @param {string} metricName - Metric name
   * @param {Object} anomalies - Anomaly detection results
   */
  processAnomalyResults(metricName, anomalies) {
    // Calculate overall anomaly score
    const methods = Object.keys(anomalies);
    const anomalyCount = methods.filter(method => anomalies[method].isAnomaly).length;
    const anomalyScore = (anomalyCount / methods.length) * 100;

    // Update anomaly history
    this.anomalyHistory.set(metricName, {
      score: anomalyScore,
      methods: methods.filter(method => anomalies[method].isAnomaly),
      details: anomalies,
      timestamp: Date.now()
    });

    // Update metrics
    metrics.anomalyScore.set({ metric_name: metricName, detection_method: 'combined' }, anomalyScore);

    // Determine severity
    let severity = 'info';
    if (anomalyScore >= 75) {
      severity = 'critical';
    } else if (anomalyScore >= 50) {
      severity = 'error';
    } else if (anomalyScore >= 25) {
      severity = 'warning';
    }

    // Increment counter if anomaly detected
    if (anomalyScore > 0) {
      metrics.anomaliesDetected.inc({ 
        metric_name: metricName, 
        detection_method: 'combined',
        severity
      });
    }

    // Send alert if significant anomaly
    if (anomalyScore >= 50) {
      this.sendAlert(metricName, anomalies, anomalyScore, severity);
    }
  }

  /**
   * Send alert to Alert Manager
   * @param {string} metricName - Metric name
   * @param {Object} anomalies - Anomaly detection results
   * @param {number} anomalyScore - Overall anomaly score
   * @param {string} severity - Alert severity
   */
  async sendAlert(metricName, anomalies, anomalyScore, severity) {
    try {
      const alert = {
        name: 'AnomalyDetected',
        metric: metricName,
        anomaly_score: anomalyScore,
        severity,
        methods: Object.keys(anomalies).filter(method => anomalies[method].isAnomaly),
        details: anomalies,
        timestamp: Date.now()
      };

      await axios.post(config.alertManagerEndpoint, alert);
      logger.info('Alert sent to Alert Manager', { metric: metricName, score: anomalyScore });
    } catch (error) {
      logger.error('Error sending alert to Alert Manager', { error: error.message });
    }
  }

  /**
   * Clean up old data
   */
  cleanupOldData() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    // Clean up anomaly history
    for (const [key, value] of this.anomalyHistory.entries()) {
      if (now - value.timestamp > maxAge) {
        this.anomalyHistory.delete(key);
      }
    }
  }

  /**
   * Start the service
   */
  start() {
    this.app.listen(config.port, () => {
      logger.info(`Real-time Anomaly Detector listening on port ${config.port}`);
    });
  }

  // Anomaly detection methods
  detectZScoreAnomalies(metricName, recentValues) {
    // Implementation details omitted for brevity
    return { isAnomaly: false, score: 0 };
  }

  detectMADAnomalies(metricName, recentValues) {
    // Implementation details omitted for brevity
    return { isAnomaly: false, score: 0 };
  }

  detectIQRAnomalies(metricName, recentValues) {
    // Implementation details omitted for brevity
    return { isAnomaly: false, score: 0 };
  }

  detectPercentChangeAnomalies(metricName, recentValues) {
    // Implementation details omitted for brevity
    return { isAnomaly: false, score: 0 };
  }

  detectConsecutiveIncreasesAnomalies(metricName, recentValues) {
    // Implementation details omitted for brevity
    return { isAnomaly: false, score: 0 };
  }

  detectSuddenSpikeAnomalies(metricName, recentValues) {
    // Implementation details omitted for brevity
    return { isAnomaly: false, score: 0 };
  }
}

// Create and start the service
const service = new RealTimeAnomalyDetector();
service.start();

module.exports = RealTimeAnomalyDetector;
