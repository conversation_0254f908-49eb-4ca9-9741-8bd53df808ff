import { SpyInstance } from 'npm:vitest';
import { JSD<PERSON> } from 'npm:jsdom';

// Mock document for component tests
function setupDom() {
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    url: 'http://localhost:3000',
    runScripts: 'dangerously'
  });
  globalThis.document = dom.window.document;
  globalThis.window = dom.window;
}

// Mock console for cleaner test output
function createMockConsole() {
  return {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
  };
}

// Test utility factory
export function createTestUtils() {
  setupDom();
  
  return {
    dom: globalThis.document,
    console: createMockConsole(),
    cleanup: () => {
      vi.clearAllMocks();
      document.body.innerHTML = '';
    }
  };
}
