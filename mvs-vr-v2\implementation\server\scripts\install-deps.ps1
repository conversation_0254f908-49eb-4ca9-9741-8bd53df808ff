# Script to install all required dependencies for both Node.js and Deno environments using PowerShell

# Colors for output
$RED = "\033[0;31m"
$GREEN = "\033[0;32m"
$YELLOW = "\033[1;33m"
$NC = "\033[0m"

Write-Host "${YELLOW}Setting up test environment...${NC}"

# Function to check if a command exists by attempting execution
function Test-CommandExists {
  param([string]$Command)
  try {
    # Attempt to execute the command and redirect output to null
    & $Command -v | Out-Null
    $LASTEXITCODE -eq 0
  }
  catch {
    $false
  }
}

# Check for required tools
$requiredTools = @("node", "deno", "npm")
$missingTools = @()

foreach ($tool in $requiredTools) {
  if (-not (Test-CommandExists $tool)) {
    $missingTools += $tool
  }
}

if ($missingTools.Count -ne 0) {
  Write-Host "${RED}Error: Missing required tools: $($missingTools -join ', ')${NC}"
  exit 1
}

# Create necessary directories
Write-Host "${YELLOW}Creating test directories...${NC}"
New-Item -ItemType Directory -Force -Path tests/integration, tests/services, tests/utils, tests/setup | Out-Null
New-Item -ItemType Directory -Force -Path .deno/types | Out-Null
New-Item -ItemType Directory -Force -Path coverage | Out-Null
New-Item -ItemType Directory -Force -Path dist/tests | Out-Null

# Install Node.js dependencies if package.json exists and node_modules is missing
if (Test-Path "package.json" -PathType Leaf) {
  if (-not (Test-Path "node_modules" -PathType Container)) {
    Write-Host "${YELLOW}Installing Node.js dependencies...${NC}"
    npm install
  }
}

# Add Deno standard library modules
Write-Host "${YELLOW}Adding Deno standard library modules...${NC}"
# Removed deno add npm:... commands as they conflict with importMap

# Update import map
Write-Host "${YELLOW}Updating import map...${NC}"
@"
{
  "imports": {
    "vitest": "npm:vitest",
    "vitest/": "npm:vitest/",
    "@testing-library/": "npm:@testing-library/",
    "node:": "./node_modules/",
    "@/": "./src/",
    "@directus/": "./directus/extensions/",
    "@shared/": "./shared/",
    "@services/": "./services/",
    "@tests/": "./tests/",
    "@setup/": "./tests/setup/",
    "path": "https://deno.land/std@0.181.0/path/mod.ts",
    "fs": "https://deno.land/std@0.181.0/fs/mod.ts",
    "util": "node:util",
    "express": "npm:express",
    "papaparse": "npm:papaparse",
    "pdfkit": "npm:pdfkit",
    "exceljs": "npm:exceljs",
    "zod": "npm:zod" # Added mapping for zod module
  }
}
"@ | Set-Content -Path import_map.json

# Create Deno configuration
Write-Host "${YELLOW}Creating Deno configuration...${NC}"
@"
{
  "importMap": "./import_map.json",
  "compilerOptions": {
    "allowJs": true,
    "lib": ["dom", "dom.iterable", "dom.asynciterable", "deno.ns", "deno.window"],
    "types": ["vitest/globals"],
  },
  "nodeModulesDir": "auto"
}
"@ | Set-Content -Path deno.json

# Generate type definitions for Deno compatibility
Write-Host "${YELLOW}Generating type definitions...${NC}"
deno types | Set-Content -Path .deno/types/lib.deno.d.ts

# Set up environment variables
Write-Host "${YELLOW}Setting up test environment variables...${NC}"
if (-not (Test-Path ".env.test" -PathType Leaf)) {
  Write-Host "${YELLOW}Creating .env.test file...${NC}"
  @"
NODE_ENV=test
VITEST_SEGFAULT_RETRY=3
VITEST_MAX_THREADS=4
VITEST_MIN_THREADS=1
VITEST_POOL_OPTIONS={"threads":{"singleThread":true}}

# Coverage settings
COVERAGE_PROVIDER=v8
COVERAGE_DIRECTORY=coverage

# Test timeouts
TEST_TIMEOUT=5000
TEST_SETUP_TIMEOUT=10000

# Debug settings
DEBUG_PORT=9229
DEBUG_BREAK_ON_FAILURE=false
DEBUG_PRINT_LIMIT=10000
"@ | Set-Content -Path .env.test
}

# Update tsconfig for tests
Write-Host "${YELLOW}Updating test TypeScript configuration...${NC}"
@"
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "types": ["vitest/globals", "@testing-library/jest-dom", "node"],
    "paths": {
      "@/*": ["../src/*"],
      "@tests/*": ["./*"],
      "@shared/*": ["../shared/*"],
      "@services/*": ["../services/*"]
    }
  },
  "include": [
    "./**/*.ts",
    "../src/types/*.d.ts"
  ]
}
"@ | Set-Content -Path tests/tsconfig.json

# Create test utility module
Write-Host "${YELLOW}Creating test utility module...${NC}"
@"
import { SpyInstance } from 'npm:vitest';
import { JSDOM } from 'npm:jsdom';

// Mock document for component tests
function setupDom() {
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    url: 'http://localhost:3000',
    runScripts: 'dangerously'
  });
  globalThis.document = dom.window.document;
  globalThis.window = dom.window;
}

// Mock console for cleaner test output
function createMockConsole() {
  return {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
  };
}

// Test utility factory
export function createTestUtils() {
  setupDom();
  
  return {
    dom: globalThis.document,
    console: createMockConsole(),
    cleanup: () => {
      vi.clearAllMocks();
      document.body.innerHTML = '';
    }
  };
}
"@ | Set-Content -Path tests/utils/test-utils.ts

# Clear test cache
Write-Host "${YELLOW}Clearing test cache...${NC}"
Remove-Item -Recurse -Force -Path ./node_modules/.vitest -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force -Path ./.deno/.vitest -ErrorAction SilentlyContinue

# Run type checking
Write-Host "${YELLOW}Running type checking...${NC}"
deno check **/*.ts

# Success message
Write-Host "${GREEN}Test environment setup complete!${NC}"
Write-Host "${YELLOW}You can now run tests using:${NC}"
Write-Host "  ${GREEN}npm test${NC} - Run tests with Node.js"
Write-Host "  ${GREEN}deno test${NC} - Run tests with Deno"