/**
 * Vitest tests for TeamMemberManagement component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createWrapper } from '../src/test-utils/component-test-utils.js';

// Mock API service
const mockApiService = {
  getTeamMembers: vi.fn(),
  addTeamMember: vi.fn(),
  updateTeamMember: vi.fn(),
  removeTeamMember: vi.fn(),
};

// Create a mock TeamMemberManagement component
const TeamMemberManagementMock = {
  name: 'TeamMemberManagement',
  data() {
    return {
      teamMembers: [
        { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'admin' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'editor' },
        { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'viewer' },
      ],
      newMember: {
        name: '',
        email: '',
        role: 'viewer',
      },
      editingMember: null,
      isLoading: false,
      error: null,
    };
  },
  methods: {
    async loadTeamMembers() {
      this.isLoading = true;
      this.error = null;
      
      try {
        const response = await mockApiService.getTeamMembers();
        this.teamMembers = response || this.teamMembers;
      } catch (err) {
        this.error = 'Failed to load team members';
        console.error(err);
      } finally {
        this.isLoading = false;
      }
    },
    
    async addMember() {
      if (!this.validateMember(this.newMember)) {
        return;
      }
      
      this.isLoading = true;
      this.error = null;
      
      try {
        const response = await mockApiService.addTeamMember(this.newMember);
        if (response) {
          this.teamMembers.push(response);
          this.resetNewMember();
        }
      } catch (err) {
        this.error = 'Failed to add team member';
        console.error(err);
      } finally {
        this.isLoading = false;
      }
    },
    
    async updateMember() {
      if (!this.editingMember || !this.validateMember(this.editingMember)) {
        return;
      }
      
      this.isLoading = true;
      this.error = null;
      
      try {
        const response = await mockApiService.updateTeamMember(this.editingMember);
        if (response) {
          const index = this.teamMembers.findIndex(m => m.id === response.id);
          if (index !== -1) {
            this.teamMembers.splice(index, 1, response);
          }
          this.cancelEdit();
        }
      } catch (err) {
        this.error = 'Failed to update team member';
        console.error(err);
      } finally {
        this.isLoading = false;
      }
    },
    
    async removeMember(member) {
      this.isLoading = true;
      this.error = null;
      
      try {
        await mockApiService.removeTeamMember(member.id);
        const index = this.teamMembers.findIndex(m => m.id === member.id);
        if (index !== -1) {
          this.teamMembers.splice(index, 1);
        }
      } catch (err) {
        this.error = 'Failed to remove team member';
        console.error(err);
      } finally {
        this.isLoading = false;
      }
    },
    
    editMember(member) {
      this.editingMember = { ...member };
    },
    
    cancelEdit() {
      this.editingMember = null;
    },
    
    resetNewMember() {
      this.newMember = {
        name: '',
        email: '',
        role: 'viewer',
      };
    },
    
    validateMember(member) {
      if (!member.name || !member.email || !member.role) {
        this.error = 'All fields are required';
        return false;
      }
      
      if (!this.validateEmail(member.email)) {
        this.error = 'Invalid email format';
        return false;
      }
      
      return true;
    },
    
    validateEmail(email) {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return re.test(email);
    },
  },
  template: `
    <div class="team-member-management">
      <h2>Team Members</h2>
      
      <div v-if="error" class="error-message">{{ error }}</div>
      
      <div class="team-members-list">
        <div v-for="member in teamMembers" :key="member.id" class="team-member">
          <div v-if="editingMember && editingMember.id === member.id" class="edit-form">
            <input v-model="editingMember.name" placeholder="Name" />
            <input v-model="editingMember.email" placeholder="Email" />
            <select v-model="editingMember.role">
              <option value="admin">Admin</option>
              <option value="editor">Editor</option>
              <option value="viewer">Viewer</option>
            </select>
            <button @click="updateMember" :disabled="isLoading">Save</button>
            <button @click="cancelEdit" :disabled="isLoading">Cancel</button>
          </div>
          <div v-else class="member-info">
            <div class="member-name">{{ member.name }}</div>
            <div class="member-email">{{ member.email }}</div>
            <div class="member-role">{{ member.role }}</div>
            <button @click="editMember(member)" :disabled="isLoading" class="edit-button">Edit</button>
            <button @click="removeMember(member)" :disabled="isLoading" class="remove-button">Remove</button>
          </div>
        </div>
      </div>
      
      <div class="add-member-form">
        <h3>Add New Member</h3>
        <input v-model="newMember.name" placeholder="Name" />
        <input v-model="newMember.email" placeholder="Email" />
        <select v-model="newMember.role">
          <option value="admin">Admin</option>
          <option value="editor">Editor</option>
          <option value="viewer">Viewer</option>
        </select>
        <button @click="addMember" :disabled="isLoading">Add Member</button>
      </div>
    </div>
  `,
};

describe('TeamMemberManagement', () => {
  let wrapper;

  beforeEach(() => {
    vi.clearAllMocks();
    wrapper = createWrapper(TeamMemberManagementMock);
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.team-member-management').exists()).toBe(true);
  });

  it('displays the list of team members', () => {
    const memberElements = wrapper.findAll('.team-member');
    expect(memberElements.length).toBe(3);
  });

  it('allows editing a team member', async () => {
    // Click the edit button for the first member
    const editButton = wrapper.findAll('.edit-button').at(0);
    await editButton.trigger('click');
    
    // Check that the edit form is displayed
    expect(wrapper.find('.edit-form').exists()).toBe(true);
    
    // Update the member's name
    const nameInput = wrapper.find('.edit-form input');
    await nameInput.setValue('Updated Name');
    
    // Save the changes
    const saveButton = wrapper.find('.edit-form button');
    await saveButton.trigger('click');
    
    // Check that the API was called with the updated member
    expect(mockApiService.updateTeamMember).toHaveBeenCalledWith({
      id: 1,
      name: 'Updated Name',
      email: '<EMAIL>',
      role: 'admin',
    });
  });

  it('validates email format when adding a new member', async () => {
    // Set invalid email
    await wrapper.setData({
      newMember: {
        name: 'New User',
        email: 'invalid-email',
        role: 'viewer',
      },
    });
    
    // Try to add the member
    const addButton = wrapper.find('.add-member-form button');
    await addButton.trigger('click');
    
    // Check that validation error is displayed
    expect(wrapper.vm.error).toBe('Invalid email format');
    expect(mockApiService.addTeamMember).not.toHaveBeenCalled();
  });

  it('removes a team member when the remove button is clicked', async () => {
    // Click the remove button for the first member
    const removeButton = wrapper.findAll('.remove-button').at(0);
    await removeButton.trigger('click');
    
    // Check that the API was called with the correct member ID
    expect(mockApiService.removeTeamMember).toHaveBeenCalledWith(1);
    
    // Check that the member was removed from the list
    expect(wrapper.vm.teamMembers.length).toBe(2);
    expect(wrapper.vm.teamMembers.find(m => m.id === 1)).toBeUndefined();
  });
});
