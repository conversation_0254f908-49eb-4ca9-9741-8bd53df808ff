/**
 * User Behavior Predictor
 * 
 * This service analyzes user behavior patterns and predicts future actions.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

/**
 * Get user activity data
 * 
 * @param {string} userId - User ID
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User activity data
 */
async function getUserActivityData(userId, days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Predict next user actions
 * 
 * @param {string} userId - User ID
 * @returns {Object} - Predicted actions
 */
async function predictNextActions(userId) {
  try {
    // Get user activity data
    const activityData = await getUserActivityData(userId);
    
    if (activityData.length === 0) {
      return {
        userId,
        predictions: [],
        confidence: 0,
        nextLikelyActions: []
      };
    }
    
    // Simple prediction based on most frequent actions
    const actionCounts = {};
    
    activityData.forEach(activity => {
      if (!actionCounts[activity.action_type]) {
        actionCounts[activity.action_type] = 0;
      }
      
      actionCounts[activity.action_type]++;
    });
    
    // Sort actions by frequency
    const sortedActions = Object.entries(actionCounts)
      .sort((a, b) => b[1] - a[1])
      .map(([action, count]) => ({
        action,
        count,
        probability: count / activityData.length
      }));
    
    // Get next likely actions
    const nextLikelyActions = sortedActions.slice(0, 5);
    
    return {
      userId,
      predictions: sortedActions,
      confidence: nextLikelyActions.length > 0 ? nextLikelyActions[0].probability : 0,
      nextLikelyActions
    };
  } catch (error) {
    logger.error('Error in predictNextActions', { error: error.message });
    
    return {
      userId,
      predictions: [],
      confidence: 0,
      nextLikelyActions: []
    };
  }
}

/**
 * Get user session patterns
 * 
 * @param {string} userId - User ID
 * @returns {Object} - Session patterns
 */
async function getUserSessionPatterns(userId) {
  try {
    // Get user activity data
    const activityData = await getUserActivityData(userId);
    
    if (activityData.length === 0) {
      return {
        userId,
        sessionCount: 0,
        avgSessionDuration: 0,
        timeOfDayDistribution: Array(24).fill(0),
        dayOfWeekDistribution: Array(7).fill(0)
      };
    }
    
    // Group activities by session
    const sessions = [];
    let currentSession = [];
    
    activityData.forEach((activity, index) => {
      if (index === 0) {
        currentSession.push(activity);
      } else {
        const prevActivity = activityData[index - 1];
        const timeDiff = new Date(activity.created_at) - new Date(prevActivity.created_at);
        
        // If more than 30 minutes between activities, start a new session
        if (timeDiff > 30 * 60 * 1000) {
          sessions.push(currentSession);
          currentSession = [activity];
        } else {
          currentSession.push(activity);
        }
      }
    });
    
    // Add the last session
    if (currentSession.length > 0) {
      sessions.push(currentSession);
    }
    
    // Calculate session durations
    const sessionDurations = sessions.map(session => {
      const startTime = new Date(session[0].created_at);
      const endTime = new Date(session[session.length - 1].created_at);
      
      return (endTime - startTime) / 1000; // in seconds
    });
    
    // Calculate average session duration
    const avgSessionDuration = sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessions.length;
    
    // Calculate time of day distribution
    const timeOfDayDistribution = Array(24).fill(0);
    
    sessions.forEach(session => {
      const hour = new Date(session[0].created_at).getHours();
      timeOfDayDistribution[hour]++;
    });
    
    // Normalize time of day distribution
    const totalSessions = sessions.length;
    const normalizedTimeOfDayDistribution = timeOfDayDistribution.map(count => count / totalSessions);
    
    // Calculate day of week distribution
    const dayOfWeekDistribution = Array(7).fill(0);
    
    sessions.forEach(session => {
      const day = new Date(session[0].created_at).getDay();
      dayOfWeekDistribution[day]++;
    });
    
    // Normalize day of week distribution
    const normalizedDayOfWeekDistribution = dayOfWeekDistribution.map(count => count / totalSessions);
    
    return {
      userId,
      sessionCount: sessions.length,
      avgSessionDuration,
      timeOfDayDistribution: normalizedTimeOfDayDistribution,
      dayOfWeekDistribution: normalizedDayOfWeekDistribution
    };
  } catch (error) {
    logger.error('Error in getUserSessionPatterns', { error: error.message });
    
    return {
      userId,
      sessionCount: 0,
      avgSessionDuration: 0,
      timeOfDayDistribution: Array(24).fill(0),
      dayOfWeekDistribution: Array(7).fill(0)
    };
  }
}

// API endpoints
app.get('/api/user/:userId/next-actions', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const predictions = await predictNextActions(userId);
    res.json(predictions);
  } catch (error) {
    logger.error('Error in GET /api/user/:userId/next-actions', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/user/:userId/session-patterns', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const patterns = await getUserSessionPatterns(userId);
    res.json(patterns);
  } catch (error) {
    logger.error('Error in GET /api/user/:userId/session-patterns', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.USER_BEHAVIOR_PREDICTOR_PORT || 9103;
app.listen(PORT, () => {
  logger.info(`User Behavior Predictor listening on port ${PORT}`);
});

module.exports = {
  predictNextActions,
  getUserSessionPatterns,
  getUserActivityData
};
