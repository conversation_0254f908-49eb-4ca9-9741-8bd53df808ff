/**
 * Unit tests for GuidedSetupService
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import axios from 'axios';
import GuidedSetupService from '../GuidedSetupService';
import * as directusUtils from '../../utils/directus';

// Mock axios
vi.mock('axios');

// Mock directus utils
vi.mock('../../utils/directus', () => ({
  getDirectusUrl: vi.fn().mockReturnValue('http://localhost:8055'),
  getDirectusToken: vi.fn().mockReturnValue('mock-token'),
}));

describe('GuidedSetupService', () => {
  let mockAxios;

  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks();

    // Setup axios mock
    mockAxios = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
    };

    vi.mocked(axios.create).mockReturnValue(mockAxios);
  });

  describe('constructor', () => {
    it('should create an axios instance with the correct configuration', () => {
      // Act
      new GuidedSetupService();

      // Assert
      expect(axios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:8055',
        headers: {
          Authorization: 'Bearer mock-token',
          'Content-Type': 'application/json',
        },
      });

      expect(directusUtils.getDirectusUrl).toHaveBeenCalled();
      expect(directusUtils.getDirectusToken).toHaveBeenCalled();
    });
  });

  describe('getOnboardingStatus', () => {
    it('should return onboarding status when it exists', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockResponse = {
        data: {
          data: [
            {
              id: 'status-id',
              vendor_id: vendorId,
              is_completed: false,
              progress_data: '{}',
            },
          ],
        },
      };

      mockAxios.get.mockResolvedValue(mockResponse);

      // Act
      const result = await GuidedSetupService.getOnboardingStatus(vendorId);

      // Assert
      expect(mockAxios.get).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      expect(result).toEqual(mockResponse.data.data[0]);
    });

    it('should return null when onboarding status does not exist', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockResponse = {
        data: {
          data: [],
        },
      };

      mockAxios.get.mockResolvedValue(mockResponse);

      // Act
      const result = await GuidedSetupService.getOnboardingStatus(vendorId);

      // Assert
      expect(mockAxios.get).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      expect(result).toBeNull();
    });

    it('should throw an error when the API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const mockError = new Error('API error');

      mockAxios.get.mockRejectedValue(mockError);

      // Act & Assert
      await expect(GuidedSetupService.getOnboardingStatus(vendorId)).rejects.toThrow(mockError);
      expect(mockAxios.get).toHaveBeenCalledWith(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
    });
  });

  describe('saveOnboardingStatus', () => {
    it('should update existing onboarding status', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = true;
      const progressData = { step1: true, step2: false };

      const mockExistingStatus = {
        id: 'status-id',
        vendor_id: vendorId,
        is_completed: false,
        progress_data: '{}',
      };

      const mockUpdateResponse = {
        data: {
          data: {
            id: 'status-id',
            vendor_id: vendorId,
            is_completed: true,
            progress_data: JSON.stringify(progressData),
          },
        },
      };

      // Mock getOnboardingStatus to return existing status
      vi.spyOn(GuidedSetupService, 'getOnboardingStatus').mockResolvedValue(mockExistingStatus);

      mockAxios.patch.mockResolvedValue(mockUpdateResponse);

      // Act
      const result = await GuidedSetupService.saveOnboardingStatus(
        vendorId,
        isCompleted,
        progressData,
      );

      // Assert
      expect(GuidedSetupService.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(mockAxios.patch).toHaveBeenCalledWith(
        `/items/vendor_onboarding/${mockExistingStatus.id}`,
        {
          is_completed: isCompleted,
          progress_data: JSON.stringify(progressData),
          updated_at: expect.any(String),
        },
      );
      expect(result).toEqual(mockUpdateResponse.data.data);
    });

    it('should create new onboarding status when it does not exist', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = false;
      const progressData = { step1: true, step2: false };

      const mockCreateResponse = {
        data: {
          data: {
            id: 'new-status-id',
            vendor_id: vendorId,
            is_completed: false,
            progress_data: JSON.stringify(progressData),
          },
        },
      };

      // Mock getOnboardingStatus to return null (no existing status)
      vi.spyOn(GuidedSetupService, 'getOnboardingStatus').mockResolvedValue(null);

      mockAxios.post.mockResolvedValue(mockCreateResponse);

      // Act
      const result = await GuidedSetupService.saveOnboardingStatus(
        vendorId,
        isCompleted,
        progressData,
      );

      // Assert
      expect(GuidedSetupService.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
      expect(mockAxios.post).toHaveBeenCalledWith('/items/vendor_onboarding', {
        vendor_id: vendorId,
        is_completed: isCompleted,
        progress_data: JSON.stringify(progressData),
        created_at: expect.any(String),
        updated_at: expect.any(String),
      });
      expect(result).toEqual(mockCreateResponse.data.data);
    });

    it('should throw an error when the API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const isCompleted = true;
      const progressData = { step1: true, step2: false };

      const mockError = new Error('API error');

      // Mock getOnboardingStatus to throw an error
      vi.spyOn(GuidedSetupService, 'getOnboardingStatus').mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        GuidedSetupService.saveOnboardingStatus(vendorId, isCompleted, progressData),
      ).rejects.toThrow(mockError);
      expect(GuidedSetupService.getOnboardingStatus).toHaveBeenCalledWith(vendorId);
    });
  });

  describe('trackWizardAnalytics', () => {
    it('should track wizard analytics successfully', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const analyticsData = {
        eventType: 'step_view',
        eventData: { stepIndex: 1, stepTitle: 'Company Profile' },
      };

      const mockResponse = {
        data: {
          data: {
            id: 'analytics-id',
            vendor_id: vendorId,
            event_type: analyticsData.eventType,
            event_data: JSON.stringify(analyticsData.eventData),
            timestamp: '2025-05-22T10:00:00Z',
          },
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      // Act
      const result = await GuidedSetupService.trackWizardAnalytics(vendorId, analyticsData);

      // Assert
      expect(mockAxios.post).toHaveBeenCalledWith('/items/wizard_analytics', {
        vendor_id: vendorId,
        event_type: analyticsData.eventType,
        event_data: JSON.stringify(analyticsData.eventData),
        timestamp: expect.any(String),
      });
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should return null when the API call fails', async () => {
      // Arrange
      const vendorId = 'test-vendor-id';
      const analyticsData = {
        eventType: 'step_view',
        eventData: { stepIndex: 1, stepTitle: 'Company Profile' },
      };

      const mockError = new Error('API error');

      mockAxios.post.mockRejectedValue(mockError);

      // Act
      const result = await GuidedSetupService.trackWizardAnalytics(vendorId, analyticsData);

      // Assert
      expect(mockAxios.post).toHaveBeenCalledWith('/items/wizard_analytics', {
        vendor_id: vendorId,
        event_type: analyticsData.eventType,
        event_data: JSON.stringify(analyticsData.eventData),
        timestamp: expect.any(String),
      });
      expect(result).toBeNull();
    });
  });
});
