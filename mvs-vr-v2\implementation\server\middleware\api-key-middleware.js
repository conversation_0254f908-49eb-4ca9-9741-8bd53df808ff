/**
 * API Key Middleware
 * 
 * This middleware validates API keys, including rotated keys in the grace period.
 */

const { Pool } = require('pg');

// Create database pool
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: process.env.POSTGRES_PORT || 5432,
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DB || 'postgres'
});

/**
 * Get API key from database
 * @param {string} key - API key
 * @returns {Object|null} API key object or null if not found
 */
async function getApiKey(key) {
  const client = await pool.connect();
  
  try {
    // Check current API keys
    const currentResult = await client.query(`
      SELECT 
        k.id, 
        k.name, 
        k.key, 
        k.owner_id,
        k.scopes,
        k.created_at, 
        k.last_rotated_at,
        u.email as owner_email,
        u.role as owner_role
      FROM api_keys k
      JOIN users u ON k.owner_id = u.id
      WHERE k.key = $1
    `, [key]);
    
    if (currentResult.rows.length > 0) {
      return {
        ...currentResult.rows[0],
        isRotated: false
      };
    }
    
    // Check historical API keys in grace period
    const historyResult = await client.query(`
      SELECT 
        h.id as history_id,
        h.api_key_id,
        h.key,
        h.created_at as key_created_at,
        h.expires_at,
        k.id,
        k.name,
        k.owner_id,
        k.scopes,
        k.created_at,
        k.last_rotated_at,
        k.key as current_key,
        u.email as owner_email,
        u.role as owner_role
      FROM api_key_history h
      JOIN api_keys k ON h.api_key_id = k.id
      JOIN users u ON k.owner_id = u.id
      WHERE h.key = $1 AND h.expires_at > NOW()
    `, [key]);
    
    if (historyResult.rows.length > 0) {
      return {
        ...historyResult.rows[0],
        isRotated: true,
        gracePeriodEnds: historyResult.rows[0].expires_at
      };
    }
    
    return null;
  } finally {
    client.release();
  }
}

/**
 * Log API key usage
 * @param {string} apiKeyId - API key ID
 * @param {string} endpoint - API endpoint
 * @param {string} method - HTTP method
 * @param {string} ip - Client IP address
 * @param {number} statusCode - HTTP status code
 * @param {boolean} isRotated - Whether the key is rotated
 */
async function logApiKeyUsage(apiKeyId, endpoint, method, ip, statusCode, isRotated) {
  const client = await pool.connect();
  
  try {
    await client.query(`
      INSERT INTO api_key_usage (
        api_key_id,
        endpoint,
        method,
        ip_address,
        status_code,
        is_rotated_key
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [apiKeyId, endpoint, method, ip, statusCode, isRotated]);
  } catch (error) {
    console.error('Error logging API key usage:', error);
  } finally {
    client.release();
  }
}

/**
 * API key middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function apiKeyMiddleware(options = {}) {
  const {
    headerName = 'X-API-Key',
    queryParam = 'api_key',
    requireScopes = [],
    optional = false
  } = options;
  
  return async (req, res, next) => {
    // Get API key from header or query parameter
    const apiKey = req.headers[headerName.toLowerCase()] || req.query[queryParam];
    
    // If API key is not provided
    if (!apiKey) {
      if (optional) {
        return next();
      }
      
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'API key is required'
      });
    }
    
    try {
      // Get API key from database
      const keyData = await getApiKey(apiKey);
      
      // If API key is not found
      if (!keyData) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid API key'
        });
      }
      
      // Check if key has required scopes
      if (requireScopes.length > 0) {
        const keyScopes = keyData.scopes || [];
        const hasRequiredScopes = requireScopes.every(scope => keyScopes.includes(scope));
        
        if (!hasRequiredScopes) {
          return res.status(403).json({
            error: 'Forbidden',
            message: 'API key does not have required scopes'
          });
        }
      }
      
      // Add API key data to request
      req.apiKey = keyData;
      
      // Add warning header if key is rotated
      if (keyData.isRotated) {
        res.setHeader('X-API-Key-Rotated', 'true');
        res.setHeader('X-API-Key-Grace-Period-Ends', keyData.gracePeriodEnds.toISOString());
        res.setHeader('X-API-Key-Current', keyData.current_key);
      }
      
      // Log API key usage after response
      res.on('finish', () => {
        logApiKeyUsage(
          keyData.id,
          req.originalUrl,
          req.method,
          req.ip,
          res.statusCode,
          keyData.isRotated
        );
      });
      
      next();
    } catch (error) {
      console.error('Error validating API key:', error);
      
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Error validating API key'
      });
    }
  };
}

module.exports = apiKeyMiddleware;
