#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Tickable.h"
#include "Interfaces/IHttpRequest.h"
#include "MVSMetricsCollector.generated.h"

class UMVSClient;

DECLARE_LOG_CATEGORY_EXTERN(LogMVSMetrics, Log, All);

/**
 * Asset load count structure
 */
USTRUCT()
struct FMVSAssetLoadCount
{
  GENERATED_BODY()
  
  UPROPERTY()
  int32 Success;
  
  UPROPERTY()
  int32 Failure;
  
  FMVSAssetLoadCount()
    : Success(0)
    , Failure(0)
  {
  }
};

/**
 * Network bandwidth structure
 */
USTRUCT()
struct FMVSNetworkBandwidth
{
  GENERATED_BODY()
  
  UPROPERTY()
  float Download;
  
  UPROPERTY()
  float Upload;
  
  FMVSNetworkBandwidth()
    : Download(0.0f)
    , Upload(0.0f)
  {
  }
};

/**
 * LLM request count structure
 */
USTRUCT()
struct FMVSLLMRequestCount
{
  GENERATED_BODY()
  
  UPROPERTY()
  int32 Success;
  
  UPROPERTY()
  int32 Failure;
  
  FMVSLLMRequestCount()
    : Success(0)
    , Failure(0)
  {
  }
};

/**
 * LLM token usage structure
 */
USTRUCT()
struct FMVSLLMTokenUsage
{
  GENERATED_BODY()
  
  UPROPERTY()
  int32 Prompt;
  
  UPROPERTY()
  int32 Completion;
  
  FMVSLLMTokenUsage()
    : Prompt(0)
    , Completion(0)
  {
  }
};

/**
 * Performance metrics structure
 */
USTRUCT()
struct FMVSPerformanceMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  float FrameRate;
  
  UPROPERTY()
  int32 DrawCalls;
  
  UPROPERTY()
  int32 TriangleCount;
  
  UPROPERTY()
  float GPUMemoryUsage;
  
  UPROPERTY()
  float CPUUsage;
  
  UPROPERTY()
  float MemoryUsage;
  
  FMVSPerformanceMetrics()
    : FrameRate(0.0f)
    , DrawCalls(0)
    , TriangleCount(0)
    , GPUMemoryUsage(0.0f)
    , CPUUsage(0.0f)
    , MemoryUsage(0.0f)
  {
  }
};

/**
 * Asset loading metrics structure
 */
USTRUCT()
struct FMVSAssetLoadingMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  TMap<FString, float> LoadTimes;
  
  UPROPERTY()
  TMap<FString, float> CacheHitRates;
  
  UPROPERTY()
  TMap<FString, FMVSAssetLoadCount> LoadCounts;
  
  FMVSAssetLoadingMetrics()
  {
  }
};

/**
 * Network metrics structure
 */
USTRUCT()
struct FMVSNetworkMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  TMap<FString, float> Latencies;
  
  UPROPERTY()
  FMVSNetworkBandwidth Bandwidth;
  
  FMVSNetworkMetrics()
  {
  }
};

/**
 * Offline mode metrics structure
 */
USTRUCT()
struct FMVSOfflineModeMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  bool Active;
  
  UPROPERTY()
  float CacheSize;
  
  UPROPERTY()
  float NetworkQuality;
  
  FMVSOfflineModeMetrics()
    : Active(false)
    , CacheSize(0.0f)
    , NetworkQuality(0.0f)
  {
  }
};

/**
 * Interaction metrics structure
 */
USTRUCT()
struct FMVSInteractionMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  TMap<FString, int32> Counts;
  
  UPROPERTY()
  TMap<FString, float> Latencies;
  
  FMVSInteractionMetrics()
  {
  }
};

/**
 * LLM metrics structure
 */
USTRUCT()
struct FMVSLLMMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  TMap<FString, FMVSLLMRequestCount> Requests;
  
  UPROPERTY()
  TMap<FString, float> ResponseTimes;
  
  UPROPERTY()
  TMap<FString, FMVSLLMTokenUsage> TokenUsage;
  
  FMVSLLMMetrics()
  {
  }
};

/**
 * Error metrics structure
 */
USTRUCT()
struct FMVSErrorMetrics
{
  GENERATED_BODY()
  
  UPROPERTY()
  TMap<FString, TMap<FString, int32>> Errors;
  
  FMVSErrorMetrics()
  {
  }
};

/**
 * MVS Metrics Collector
 * 
 * Collects and submits performance metrics from the UE plugin to the monitoring service.
 */
UCLASS()
class MVSVR_API UMVSMetricsCollector : public UObject
{
  GENERATED_BODY()
  
public:
  UMVSMetricsCollector();
  
  /**
   * Initialize the metrics collector
   * 
   * @param InClient - The MVS client
   */
  void Initialize(UMVSClient* InClient);
  
  /**
   * Start metrics collection
   */
  void StartMetricsCollection();
  
  /**
   * Stop metrics collection
   */
  void StopMetricsCollection();
  
  /**
   * Tick function for collecting metrics
   * 
   * @param DeltaTime - Time since last tick
   * @return bool - Whether to continue ticking
   */
  bool Tick(float DeltaTime);
  
  /**
   * Collect metrics
   */
  void CollectMetrics();
  
  /**
   * Submit metrics to the monitoring service
   */
  void SubmitMetrics();
  
  /**
   * Set whether metrics collection is enabled
   * 
   * @param bEnable - Whether to enable metrics collection
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void SetEnableMetricsCollection(bool bEnable);
  
  /**
   * Set whether detailed metrics collection is enabled
   * 
   * @param bEnable - Whether to enable detailed metrics collection
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void SetEnableDetailedMetrics(bool bEnable);
  
  /**
   * Set whether automatic metrics submission is enabled
   * 
   * @param bEnable - Whether to enable automatic metrics submission
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void SetEnableAutomaticSubmission(bool bEnable);
  
  /**
   * Set the metrics collection interval
   * 
   * @param Interval - Collection interval in seconds
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void SetCollectionInterval(float Interval);
  
  /**
   * Set the metrics submission interval
   * 
   * @param Interval - Submission interval in seconds
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void SetMetricsSubmissionInterval(float Interval);
  
  /**
   * Record asset load time
   * 
   * @param AssetType - Type of asset
   * @param LoadTimeSeconds - Load time in seconds
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void RecordAssetLoadTime(const FString& AssetType, float LoadTimeSeconds);
  
  /**
   * Record asset load result
   * 
   * @param AssetType - Type of asset
   * @param bSuccess - Whether the load was successful
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void RecordAssetLoadResult(const FString& AssetType, bool bSuccess);
  
  /**
   * Record network latency
   * 
   * @param Endpoint - API endpoint
   * @param LatencyMs - Latency in milliseconds
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void RecordNetworkLatency(const FString& Endpoint, float LatencyMs);
  
  /**
   * Record user interaction
   * 
   * @param InteractionType - Type of interaction
   * @param LatencyMs - Latency in milliseconds
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void RecordInteraction(const FString& InteractionType, float LatencyMs);
  
  /**
   * Record LLM request
   * 
   * @param Model - LLM model name
   * @param bSuccess - Whether the request was successful
   * @param ResponseTimeSeconds - Response time in seconds
   * @param PromptTokens - Number of prompt tokens
   * @param CompletionTokens - Number of completion tokens
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void RecordLLMRequest(const FString& Model, bool bSuccess, float ResponseTimeSeconds, int32 PromptTokens, int32 CompletionTokens);
  
  /**
   * Record error
   * 
   * @param ErrorType - Type of error
   * @param Component - Component where the error occurred
   */
  UFUNCTION(BlueprintCallable, Category = "MVS|Metrics")
  void RecordError(const FString& ErrorType, const FString& Component);
  
  // UObject interface
  virtual void BeginDestroy() override;
  
private:
  /**
   * Collect performance metrics
   */
  void CollectPerformanceMetrics();
  
  /**
   * Collect asset loading metrics
   */
  void CollectAssetLoadingMetrics();
  
  /**
   * Collect network metrics
   */
  void CollectNetworkMetrics();
  
  /**
   * Collect offline mode metrics
   */
  void CollectOfflineModeMetrics();
  
  /**
   * Collect detailed metrics
   */
  void CollectDetailedMetrics();
  
  /**
   * Collect interaction metrics
   */
  void CollectInteractionMetrics();
  
  /**
   * Collect LLM metrics
   */
  void CollectLLMMetrics();
  
  /**
   * Collect error metrics
   */
  void CollectErrorMetrics();
  
  /**
   * Create metrics payload
   * 
   * @return TSharedPtr<FJsonObject> - Metrics payload
   */
  TSharedPtr<FJsonObject> CreateMetricsPayload();
  
  /**
   * Callback for metrics submission
   * 
   * @param Request - HTTP request
   * @param Response - HTTP response
   * @param bSuccess - Whether the request was successful
   */
  void OnMetricsSubmitted(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess);
  
  /**
   * Reset metrics after submission
   */
  void ResetMetrics();
  
  /** Collection interval in seconds */
  float CollectionInterval;
  
  /** Whether metrics collection is enabled */
  bool bEnableMetricsCollection;
  
  /** Whether detailed metrics collection is enabled */
  bool bEnableDetailedMetrics;
  
  /** Whether automatic metrics submission is enabled */
  bool bEnableAutomaticSubmission;
  
  /** Metrics submission interval in seconds */
  float MetricsSubmissionInterval;
  
  /** Time since last collection */
  float LastCollectionTime;
  
  /** Time since last submission */
  float LastSubmissionTime;
  
  /** Client ID */
  FString ClientId;
  
  /** Device type */
  FString DeviceType;
  
  /** UE version */
  FString UEVersion;
  
  /** MVS client */
  UPROPERTY()
  UMVSClient* Client;
  
  /** Tick delegate handle */
  FTSTicker::FDelegateHandle TickDelegateHandle;
  
  /** Metrics lock */
  FCriticalSection MetricsLock;
  
  /** Performance metrics */
  FMVSPerformanceMetrics PerformanceMetrics;
  
  /** Asset loading metrics */
  FMVSAssetLoadingMetrics AssetLoadingMetrics;
  
  /** Network metrics */
  FMVSNetworkMetrics NetworkMetrics;
  
  /** Offline mode metrics */
  FMVSOfflineModeMetrics OfflineModeMetrics;
  
  /** Interaction metrics */
  FMVSInteractionMetrics InteractionMetrics;
  
  /** LLM metrics */
  FMVSLLMMetrics LLMMetrics;
  
  /** Error metrics */
  FMVSErrorMetrics ErrorMetrics;
};
