/**
 * Incident Management Integration Service
 * 
 * This service integrates the monitoring system with incident management platforms
 * like PagerDuty, OpsGenie, and ServiceNow to streamline incident response.
 */

const express = require('express');
const promClient = require('prom-client');
const { createLogger, format, transports } = require('winston');
const axios = require('axios');
const { EventEmitter } = require('events');

// Configuration
const config = {
  port: parseInt(process.env.INCIDENT_MANAGEMENT_INTEGRATION_PORT || '9114', 10),
  logLevel: process.env.LOG_LEVEL || 'info',
  alertManagerEndpoint: process.env.ALERT_MANAGER_ENDPOINT || 'http://alert-manager-service:9096/api/alerts',
  updateInterval: parseInt(process.env.UPDATE_INTERVAL || '30000', 10), // 30 seconds
  integrations: {
    pagerduty: {
      enabled: process.env.PAGERDUTY_ENABLED === 'true',
      apiKey: process.env.PAGERDUTY_API_KEY || '',
      serviceId: process.env.PAGERDUTY_SERVICE_ID || '',
      endpoint: process.env.PAGERDUTY_ENDPOINT || 'https://events.pagerduty.com/v2/enqueue'
    },
    opsgenie: {
      enabled: process.env.OPSGENIE_ENABLED === 'true',
      apiKey: process.env.OPSGENIE_API_KEY || '',
      endpoint: process.env.OPSGENIE_ENDPOINT || 'https://api.opsgenie.com/v2/alerts'
    },
    servicenow: {
      enabled: process.env.SERVICENOW_ENABLED === 'true',
      instance: process.env.SERVICENOW_INSTANCE || '',
      username: process.env.SERVICENOW_USERNAME || '',
      password: process.env.SERVICENOW_PASSWORD || '',
      endpoint: process.env.SERVICENOW_ENDPOINT || ''
    },
    jira: {
      enabled: process.env.JIRA_ENABLED === 'true',
      host: process.env.JIRA_HOST || '',
      username: process.env.JIRA_USERNAME || '',
      apiToken: process.env.JIRA_API_TOKEN || '',
      projectKey: process.env.JIRA_PROJECT_KEY || 'OPS'
    }
  },
  severityMapping: {
    info: {
      pagerduty: 'info',
      opsgenie: 'P5',
      servicenow: '4',
      jira: 'Low'
    },
    warning: {
      pagerduty: 'warning',
      opsgenie: 'P3',
      servicenow: '3',
      jira: 'Medium'
    },
    error: {
      pagerduty: 'error',
      opsgenie: 'P2',
      servicenow: '2',
      jira: 'High'
    },
    critical: {
      pagerduty: 'critical',
      opsgenie: 'P1',
      servicenow: '1',
      jira: 'Highest'
    }
  },
  deduplicationWindow: parseInt(process.env.DEDUPLICATION_WINDOW || '3600000', 10) // 1 hour
};

// Setup logger
const logger = createLogger({
  level: config.logLevel,
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  defaultMeta: { service: 'incident-management-integration' },
  transports: [
    new transports.Console()
  ]
});

// Initialize Prometheus metrics
const register = new promClient.Registry();
promClient.collectDefaultMetrics({ register });

// Custom metrics
const metrics = {
  incidentsCreated: new promClient.Counter({
    name: 'mvs_vr_incidents_created_total',
    help: 'Total number of incidents created',
    labelNames: ['platform', 'severity']
  }),
  incidentsUpdated: new promClient.Counter({
    name: 'mvs_vr_incidents_updated_total',
    help: 'Total number of incidents updated',
    labelNames: ['platform', 'severity']
  }),
  incidentsClosed: new promClient.Counter({
    name: 'mvs_vr_incidents_closed_total',
    help: 'Total number of incidents closed',
    labelNames: ['platform', 'severity']
  }),
  integrationErrors: new promClient.Counter({
    name: 'mvs_vr_integration_errors_total',
    help: 'Total number of integration errors',
    labelNames: ['platform', 'error_type']
  }),
  integrationLatency: new promClient.Histogram({
    name: 'mvs_vr_integration_latency_seconds',
    help: 'Latency of integration operations',
    labelNames: ['platform', 'operation'],
    buckets: [0.1, 0.5, 1, 2, 5, 10]
  })
};

// Register custom metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Incident Management Integration Service
 */
class IncidentManagementIntegration extends EventEmitter {
  constructor() {
    super();
    this.app = express();
    this.activeIncidents = new Map();
    this.incidentHistory = [];
    this.setupExpress();
    this.setupIntervals();
  }

  /**
   * Setup Express server
   */
  setupExpress() {
    this.app.get('/metrics', async (req, res) => {
      res.set('Content-Type', register.contentType);
      res.end(await register.metrics());
    });

    this.app.get('/api/incidents', (req, res) => {
      const incidents = Array.from(this.activeIncidents.values());
      res.json(incidents);
    });

    this.app.get('/api/incident-history', (req, res) => {
      res.json(this.incidentHistory);
    });

    this.app.post('/api/incidents', async (req, res) => {
      const { alert } = req.body;
      
      if (!alert) {
        return res.status(400).json({ error: 'Missing alert data' });
      }

      try {
        const result = await this.createIncident(alert);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.post('/api/incidents/:id/update', async (req, res) => {
      const { id } = req.params;
      const { update } = req.body;
      
      if (!update) {
        return res.status(400).json({ error: 'Missing update data' });
      }

      try {
        const result = await this.updateIncident(id, update);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.post('/api/incidents/:id/close', async (req, res) => {
      const { id } = req.params;
      const { resolution } = req.body;
      
      try {
        const result = await this.closeIncident(id, resolution);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/health', (req, res) => {
      res.json({ status: 'ok' });
    });
  }

  /**
   * Setup intervals for periodic tasks
   */
  setupIntervals() {
    // Fetch alerts from Alert Manager periodically
    setInterval(() => this.fetchAlertsFromAlertManager(), config.updateInterval);

    // Clean up old incidents periodically
    setInterval(() => this.cleanupOldIncidents(), config.updateInterval * 10);
  }

  /**
   * Fetch alerts from Alert Manager
   */
  async fetchAlertsFromAlertManager() {
    try {
      const response = await axios.get(config.alertManagerEndpoint);
      
      if (response.data && Array.isArray(response.data)) {
        for (const alert of response.data) {
          if (alert.status === 'firing' && !this.isIncidentActive(alert)) {
            await this.createIncident(alert);
          } else if (alert.status === 'resolved' && this.isIncidentActive(alert)) {
            const incident = this.getActiveIncident(alert);
            if (incident) {
              await this.closeIncident(incident.id, 'Alert resolved automatically');
            }
          }
        }
      }
    } catch (error) {
      logger.error('Error fetching alerts from Alert Manager', { error: error.message });
    }
  }

  /**
   * Check if an incident is already active for an alert
   * @param {Object} alert - Alert data
   * @returns {boolean} - Whether an incident is active
   */
  isIncidentActive(alert) {
    const alertKey = this.getAlertKey(alert);
    return this.activeIncidents.has(alertKey);
  }

  /**
   * Get active incident for an alert
   * @param {Object} alert - Alert data
   * @returns {Object|null} - Active incident or null
   */
  getActiveIncident(alert) {
    const alertKey = this.getAlertKey(alert);
    return this.activeIncidents.get(alertKey) || null;
  }

  /**
   * Get a unique key for an alert
   * @param {Object} alert - Alert data
   * @returns {string} - Alert key
   */
  getAlertKey(alert) {
    return `${alert.name}-${alert.labels ? alert.labels.instance || '' : ''}-${alert.labels ? alert.labels.job || '' : ''}`;
  }

  /**
   * Create an incident from an alert
   * @param {Object} alert - Alert data
   * @returns {Object} - Created incident
   */
  async createIncident(alert) {
    const alertKey = this.getAlertKey(alert);
    
    // Check for duplicate
    if (this.activeIncidents.has(alertKey)) {
      return this.activeIncidents.get(alertKey);
    }

    const severity = alert.labels ? alert.labels.severity || 'warning' : 'warning';
    const summary = alert.annotations ? alert.annotations.summary || 'Alert triggered' : 'Alert triggered';
    const description = alert.annotations ? alert.annotations.description || summary : summary;
    
    const incident = {
      id: `incident-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      alertKey,
      alert,
      severity,
      summary,
      description,
      status: 'open',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      platforms: {}
    };

    // Create incidents in each enabled platform
    const promises = [];
    
    if (config.integrations.pagerduty.enabled) {
      promises.push(this.createPagerDutyIncident(incident));
    }
    
    if (config.integrations.opsgenie.enabled) {
      promises.push(this.createOpsGenieIncident(incident));
    }
    
    if (config.integrations.servicenow.enabled) {
      promises.push(this.createServiceNowIncident(incident));
    }
    
    if (config.integrations.jira.enabled) {
      promises.push(this.createJiraIncident(incident));
    }

    try {
      await Promise.all(promises);
      
      // Store the incident
      this.activeIncidents.set(alertKey, incident);
      
      // Update metrics
      Object.keys(incident.platforms).forEach(platform => {
        metrics.incidentsCreated.inc({ platform, severity });
      });
      
      logger.info('Incident created', { id: incident.id, summary });
      
      return incident;
    } catch (error) {
      logger.error('Error creating incident', { error: error.message });
      throw error;
    }
  }

  /**
   * Update an incident
   * @param {string} id - Incident ID
   * @param {Object} update - Update data
   * @returns {Object} - Updated incident
   */
  async updateIncident(id, update) {
    // Find the incident
    let incident = null;
    let alertKey = null;
    
    for (const [key, value] of this.activeIncidents.entries()) {
      if (value.id === id) {
        incident = value;
        alertKey = key;
        break;
      }
    }
    
    if (!incident) {
      throw new Error(`Incident ${id} not found`);
    }

    // Update the incident
    incident.updatedAt = Date.now();
    
    if (update.summary) {
      incident.summary = update.summary;
    }
    
    if (update.description) {
      incident.description = update.description;
    }
    
    if (update.severity) {
      incident.severity = update.severity;
    }

    // Update incidents in each enabled platform
    const promises = [];
    
    if (config.integrations.pagerduty.enabled && incident.platforms.pagerduty) {
      promises.push(this.updatePagerDutyIncident(incident));
    }
    
    if (config.integrations.opsgenie.enabled && incident.platforms.opsgenie) {
      promises.push(this.updateOpsGenieIncident(incident));
    }
    
    if (config.integrations.servicenow.enabled && incident.platforms.servicenow) {
      promises.push(this.updateServiceNowIncident(incident));
    }
    
    if (config.integrations.jira.enabled && incident.platforms.jira) {
      promises.push(this.updateJiraIncident(incident));
    }

    try {
      await Promise.all(promises);
      
      // Store the updated incident
      this.activeIncidents.set(alertKey, incident);
      
      // Update metrics
      Object.keys(incident.platforms).forEach(platform => {
        metrics.incidentsUpdated.inc({ platform, severity: incident.severity });
      });
      
      logger.info('Incident updated', { id: incident.id });
      
      return incident;
    } catch (error) {
      logger.error('Error updating incident', { id, error: error.message });
      throw error;
    }
  }

  /**
   * Close an incident
   * @param {string} id - Incident ID
   * @param {string} resolution - Resolution note
   * @returns {Object} - Closed incident
   */
  async closeIncident(id, resolution) {
    // Find the incident
    let incident = null;
    let alertKey = null;
    
    for (const [key, value] of this.activeIncidents.entries()) {
      if (value.id === id) {
        incident = value;
        alertKey = key;
        break;
      }
    }
    
    if (!incident) {
      throw new Error(`Incident ${id} not found`);
    }

    // Update the incident
    incident.status = 'closed';
    incident.resolution = resolution;
    incident.closedAt = Date.now();
    incident.updatedAt = Date.now();

    // Close incidents in each enabled platform
    const promises = [];
    
    if (config.integrations.pagerduty.enabled && incident.platforms.pagerduty) {
      promises.push(this.closePagerDutyIncident(incident));
    }
    
    if (config.integrations.opsgenie.enabled && incident.platforms.opsgenie) {
      promises.push(this.closeOpsGenieIncident(incident));
    }
    
    if (config.integrations.servicenow.enabled && incident.platforms.servicenow) {
      promises.push(this.closeServiceNowIncident(incident));
    }
    
    if (config.integrations.jira.enabled && incident.platforms.jira) {
      promises.push(this.closeJiraIncident(incident));
    }

    try {
      await Promise.all(promises);
      
      // Move to history and remove from active
      this.incidentHistory.push(incident);
      this.activeIncidents.delete(alertKey);
      
      // Update metrics
      Object.keys(incident.platforms).forEach(platform => {
        metrics.incidentsClosed.inc({ platform, severity: incident.severity });
      });
      
      logger.info('Incident closed', { id: incident.id, resolution });
      
      return incident;
    } catch (error) {
      logger.error('Error closing incident', { id, error: error.message });
      throw error;
    }
  }

  /**
   * Clean up old incidents
   */
  cleanupOldIncidents() {
    const now = Date.now();
    const maxHistorySize = 1000;
    
    // Trim history if it gets too large
    if (this.incidentHistory.length > maxHistorySize) {
      this.incidentHistory = this.incidentHistory.slice(-maxHistorySize);
    }
  }

  // Platform-specific methods (implementation details omitted for brevity)
  async createPagerDutyIncident(incident) {
    // Implementation details omitted
    incident.platforms.pagerduty = { id: `pd-${Date.now()}` };
    return incident;
  }

  async updatePagerDutyIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async closePagerDutyIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async createOpsGenieIncident(incident) {
    // Implementation details omitted
    incident.platforms.opsgenie = { id: `og-${Date.now()}` };
    return incident;
  }

  async updateOpsGenieIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async closeOpsGenieIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async createServiceNowIncident(incident) {
    // Implementation details omitted
    incident.platforms.servicenow = { id: `sn-${Date.now()}` };
    return incident;
  }

  async updateServiceNowIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async closeServiceNowIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async createJiraIncident(incident) {
    // Implementation details omitted
    incident.platforms.jira = { id: `jira-${Date.now()}` };
    return incident;
  }

  async updateJiraIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  async closeJiraIncident(incident) {
    // Implementation details omitted
    return incident;
  }

  /**
   * Start the service
   */
  start() {
    this.app.listen(config.port, () => {
      logger.info(`Incident Management Integration Service listening on port ${config.port}`);
    });
  }
}

// Create and start the service
const service = new IncidentManagementIntegration();
service.start();

module.exports = IncidentManagementIntegration;
