/**
 * Business Continuity Integration
 * 
 * This script integrates disaster recovery with business continuity planning.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync, exec } = require('child_process');
const nodemailer = require('nodemailer');
const axios = require('axios');
const rtoMeasurement = require('./rto-measurement');
const automatedRecovery = require('./automated-recovery');
const { Logger } = require('../../services/integration/logger');
const { logger } = require('../shared/utils/logger');

// Promisify functions
const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);

// Create logger
const logger = new Logger();

// Configuration
const config = {
  bcpPlanPath: process.env.BCP_PLAN_PATH || path.join(__dirname, '../../config/bcp-plan.json'),
  notificationConfig: {
    email: {
      enabled: process.env.EMAIL_NOTIFICATIONS === 'true',
      host: process.env.SMTP_HOST || 'smtp.example.com',
      port: parseInt(process.env.SMTP_PORT || '587', 10),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASSWORD || 'password'
      },
      from: process.env.SMTP_FROM || '<EMAIL>'
    },
    slack: {
      enabled: process.env.SLACK_NOTIFICATIONS === 'true',
      webhookUrl: process.env.SLACK_WEBHOOK_URL || 'https://hooks.slack.com/services/xxx/yyy/zzz',
      channel: process.env.SLACK_CHANNEL || '#incidents'
    },
    sms: {
      enabled: process.env.SMS_NOTIFICATIONS === 'true',
      provider: process.env.SMS_PROVIDER || 'twilio',
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      from: process.env.TWILIO_PHONE_NUMBER
    }
  },
  incidentLogPath: path.join(__dirname, '../../logs/incidents'),
  statusPageConfig: {
    enabled: process.env.STATUS_PAGE_ENABLED === 'true',
    apiKey: process.env.STATUS_PAGE_API_KEY,
    pageId: process.env.STATUS_PAGE_ID,
    url: process.env.STATUS_PAGE_URL || 'https://status.mvs-vr.com'
  }
};

/**
 * Load BCP plan
 * @returns {Promise<Object>} BCP plan
 */
async function loadBcpPlan() {
  try {
    const data = await readFileAsync(config.bcpPlanPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    logger.error(`Error loading BCP plan: ${error.message}`, { error });
    
    // Return default plan
    return {
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      contacts: [],
      teams: [],
      procedures: [],
      recoveryPriorities: [],
      communicationPlan: {
        internal: [],
        external: []
      }
    };
  }
}

/**
 * Save BCP plan
 * @param {Object} plan - BCP plan
 * @returns {Promise<void>}
 */
async function saveBcpPlan(plan) {
  try {
    await writeFileAsync(config.bcpPlanPath, JSON.stringify(plan, null, 2));
  } catch (error) {
    logger.error(`Error saving BCP plan: ${error.message}`, { error });
    throw error;
  }
}

/**
 * Create email transporter
 * @returns {Object} Email transporter
 */
function createEmailTransporter() {
  return nodemailer.createTransport({
    host: config.notificationConfig.email.host,
    port: config.notificationConfig.email.port,
    secure: config.notificationConfig.email.secure,
    auth: config.notificationConfig.email.auth
  });
}

/**
 * Send email notification
 * @param {Object} options - Email options
 * @returns {Promise<Object>} Email result
 */
async function sendEmailNotification(options) {
  if (!config.notificationConfig.email.enabled) {
    logger.info('Email notifications are disabled');
    return { success: false, reason: 'Email notifications are disabled' };
  }
  
  try {
    const transporter = createEmailTransporter();
    
    const mailOptions = {
      from: config.notificationConfig.email.from,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html
    };
    
    const result = await transporter.sendMail(mailOptions);
    
    logger.info(`Email notification sent to ${options.to}`);
    
    return { success: true, result };
  } catch (error) {
    logger.error(`Error sending email notification: ${error.message}`, { error });
    return { success: false, error: error.message };
  }
}

/**
 * Send Slack notification
 * @param {Object} options - Slack options
 * @returns {Promise<Object>} Slack result
 */
async function sendSlackNotification(options) {
  if (!config.notificationConfig.slack.enabled) {
    logger.info('Slack notifications are disabled');
    return { success: false, reason: 'Slack notifications are disabled' };
  }
  
  try {
    const response = await axios.post(config.notificationConfig.slack.webhookUrl, {
      channel: options.channel || config.notificationConfig.slack.channel,
      text: options.text,
      blocks: options.blocks
    });
    
    logger.info(`Slack notification sent to ${options.channel || config.notificationConfig.slack.channel}`);
    
    return { success: true, result: response.data };
  } catch (error) {
    logger.error(`Error sending Slack notification: ${error.message}`, { error });
    return { success: false, error: error.message };
  }
}

/**
 * Send SMS notification
 * @param {Object} options - SMS options
 * @returns {Promise<Object>} SMS result
 */
async function sendSmsNotification(options) {
  if (!config.notificationConfig.sms.enabled) {
    logger.info('SMS notifications are disabled');
    return { success: false, reason: 'SMS notifications are disabled' };
  }
  
  try {
    // Implement SMS provider integration
    // This is a placeholder for actual SMS provider integration
    logger.info(`SMS notification would be sent to ${options.to}`);
    
    return { success: true, result: { message: 'SMS notification sent' } };
  } catch (error) {
    logger.error(`Error sending SMS notification: ${error.message}`, { error });
    return { success: false, error: error.message };
  }
}

/**
 * Update status page
 * @param {Object} options - Status page options
 * @returns {Promise<Object>} Status page result
 */
async function updateStatusPage(options) {
  if (!config.statusPageConfig.enabled) {
    logger.info('Status page updates are disabled');
    return { success: false, reason: 'Status page updates are disabled' };
  }
  
  try {
    // Implement status page provider integration
    // This is a placeholder for actual status page provider integration
    logger.info(`Status page would be updated with ${options.status}`);
    
    return { success: true, result: { message: 'Status page updated' } };
  } catch (error) {
    logger.error(`Error updating status page: ${error.message}`, { error });
    return { success: false, error: error.message };
  }
}

/**
 * Create incident record
 * @param {Object} incident - Incident details
 * @returns {Promise<Object>} Incident record
 */
async function createIncidentRecord(incident) {
  try {
    // Create incident log directory
    await mkdirAsync(config.incidentLogPath, { recursive: true });
    
    // Generate incident ID if not provided
    if (!incident.id) {
      incident.id = `INC-${Date.now()}`;
    }
    
    // Set timestamp if not provided
    if (!incident.timestamp) {
      incident.timestamp = new Date().toISOString();
    }
    
    // Save incident record
    const incidentPath = path.join(config.incidentLogPath, `${incident.id}.json`);
    await writeFileAsync(incidentPath, JSON.stringify(incident, null, 2));
    
    logger.info(`Incident record created: ${incident.id}`);
    
    return incident;
  } catch (error) {
    logger.error(`Error creating incident record: ${error.message}`, { error });
    throw error;
  }
}

/**
 * Update incident record
 * @param {string} incidentId - Incident ID
 * @param {Object} updates - Incident updates
 * @returns {Promise<Object>} Updated incident record
 */
async function updateIncidentRecord(incidentId, updates) {
  try {
    // Load incident record
    const incidentPath = path.join(config.incidentLogPath, `${incidentId}.json`);
    const data = await readFileAsync(incidentPath, 'utf8');
    const incident = JSON.parse(data);
    
    // Update incident record
    const updatedIncident = { ...incident, ...updates };
    
    // Add update to history
    if (!updatedIncident.history) {
      updatedIncident.history = [];
    }
    
    updatedIncident.history.push({
      timestamp: new Date().toISOString(),
      updates
    });
    
    // Save updated incident record
    await writeFileAsync(incidentPath, JSON.stringify(updatedIncident, null, 2));
    
    logger.info(`Incident record updated: ${incidentId}`);
    
    return updatedIncident;
  } catch (error) {
    logger.error(`Error updating incident record: ${error.message}`, { error });
    throw error;
  }
}

/**
 * Get incident record
 * @param {string} incidentId - Incident ID
 * @returns {Promise<Object>} Incident record
 */
async function getIncidentRecord(incidentId) {
  try {
    // Load incident record
    const incidentPath = path.join(config.incidentLogPath, `${incidentId}.json`);
    const data = await readFileAsync(incidentPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    logger.error(`Error getting incident record: ${error.message}`, { error });
    throw error;
  }
}

/**
 * Notify incident response team
 * @param {Object} incident - Incident details
 * @param {Object} bcpPlan - BCP plan
 * @returns {Promise<Object>} Notification results
 */
async function notifyIncidentResponseTeam(incident, bcpPlan) {
  logger.info(`Notifying incident response team for incident ${incident.id}`);
  
  const results = {
    email: [],
    slack: [],
    sms: []
  };
  
  try {
    // Get contacts to notify
    const contacts = bcpPlan.contacts.filter(contact => {
      // Check if contact should be notified for this incident type
      if (contact.notifyFor && contact.notifyFor.length > 0) {
        return contact.notifyFor.includes(incident.type);
      }
      
      // Check if contact is part of a team that should be notified
      if (contact.teams && contact.teams.length > 0) {
        const teams = bcpPlan.teams.filter(team => contact.teams.includes(team.id));
        return teams.some(team => team.notifyFor && team.notifyFor.includes(incident.type));
      }
      
      return false;
    });
    
    logger.info(`Found ${contacts.length} contacts to notify`);
    
    // Send email notifications
    for (const contact of contacts) {
      if (contact.email) {
        const emailResult = await sendEmailNotification({
          to: contact.email,
          subject: `Incident Alert: ${incident.title} (${incident.id})`,
          text: `
Incident Alert: ${incident.title}
ID: ${incident.id}
Type: ${incident.type}
Severity: ${incident.severity}
Status: ${incident.status}
Timestamp: ${incident.timestamp}

Description:
${incident.description}

Impact:
${incident.impact}

Response Actions:
${incident.responseActions ? incident.responseActions.join('\n') : 'None defined yet'}

Please follow the business continuity plan procedures for this type of incident.
          `,
          html: `
<h1>Incident Alert: ${incident.title}</h1>
<p><strong>ID:</strong> ${incident.id}</p>
<p><strong>Type:</strong> ${incident.type}</p>
<p><strong>Severity:</strong> ${incident.severity}</p>
<p><strong>Status:</strong> ${incident.status}</p>
<p><strong>Timestamp:</strong> ${incident.timestamp}</p>

<h2>Description:</h2>
<p>${incident.description}</p>

<h2>Impact:</h2>
<p>${incident.impact}</p>

<h2>Response Actions:</h2>
<ul>
${incident.responseActions ? incident.responseActions.map(action => `<li>${action}</li>`).join('') : '<li>None defined yet</li>'}
</ul>

<p>Please follow the business continuity plan procedures for this type of incident.</p>
          `
        });
        
        results.email.push({
          contact: contact.id,
          result: emailResult
        });
      }
      
      // Send SMS notifications
      if (contact.phone && contact.notificationPreferences && contact.notificationPreferences.includes('sms')) {
        const smsResult = await sendSmsNotification({
          to: contact.phone,
          text: `Incident Alert: ${incident.title} (${incident.id}). Type: ${incident.type}, Severity: ${incident.severity}, Status: ${incident.status}. Please check your email for details.`
        });
        
        results.sms.push({
          contact: contact.id,
          result: smsResult
        });
      }
    }
    
    // Send Slack notification
    const slackResult = await sendSlackNotification({
      text: `Incident Alert: ${incident.title} (${incident.id})`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `Incident Alert: ${incident.title}`
          }
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*ID:* ${incident.id}`
            },
            {
              type: 'mrkdwn',
              text: `*Type:* ${incident.type}`
            },
            {
              type: 'mrkdwn',
              text: `*Severity:* ${incident.severity}`
            },
            {
              type: 'mrkdwn',
              text: `*Status:* ${incident.status}`
            },
            {
              type: 'mrkdwn',
              text: `*Timestamp:* ${incident.timestamp}`
            }
          ]
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Description:*\n${incident.description}`
          }
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Impact:*\n${incident.impact}`
          }
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Response Actions:*\n${incident.responseActions ? incident.responseActions.join('\n') : 'None defined yet'}`
          }
        }
      ]
    });
    
    results.slack.push({
      channel: config.notificationConfig.slack.channel,
      result: slackResult
    });
    
    // Update status page
    if (incident.updateStatusPage) {
      const statusPageResult = await updateStatusPage({
        status: incident.status,
        title: incident.title,
        description: incident.description,
        impact: incident.severity
      });
      
      results.statusPage = statusPageResult;
    }
    
    logger.info(`Incident response team notified for incident ${incident.id}`);
    
    return results;
  } catch (error) {
    logger.error(`Error notifying incident response team: ${error.message}`, { error });
    throw error;
  }
}

/**
 * Execute recovery procedure
 * @param {Object} incident - Incident details
 * @param {Object} procedure - Recovery procedure
 * @returns {Promise<Object>} Recovery results
 */
async function executeRecoveryProcedure(incident, procedure) {
  logger.info(`Executing recovery procedure ${procedure.id} for incident ${incident.id}`);
  
  try {
    // Update incident status
    await updateIncidentRecord(incident.id, {
      status: 'RECOVERING',
      currentProcedure: procedure.id
    });
    
    // Execute recovery steps
    const results = {
      procedureId: procedure.id,
      steps: []
    };
    
    for (const step of procedure.steps) {
      logger.info(`Executing recovery step ${step.id}: ${step.description}`);
      
      let stepResult = {
        stepId: step.id,
        description: step.description,
        status: 'PENDING',
        startTime: new Date().toISOString()
      };
      
      try {
        // Execute step based on type
        switch (step.type) {
          case 'MANUAL':
            // Manual steps require human intervention
            stepResult.status = 'WAITING_FOR_HUMAN';
            stepResult.message = 'This step requires human intervention';
            break;
            
          case 'AUTOMATED':
            // Execute automated recovery
            if (step.action === 'RECOVER_DATABASE') {
              const recoveryResult = await automatedRecovery.runAutomatedRecovery({
                components: ['database'],
                incidentId: incident.id
              });
              
              stepResult.status = recoveryResult.components.database.success ? 'COMPLETED' : 'FAILED';
              stepResult.result = recoveryResult.components.database;
            } else if (step.action === 'RECOVER_FILES') {
              const recoveryResult = await automatedRecovery.runAutomatedRecovery({
                components: ['files'],
                incidentId: incident.id
              });
              
              stepResult.status = recoveryResult.components.files.success ? 'COMPLETED' : 'FAILED';
              stepResult.result = recoveryResult.components.files;
            } else if (step.action === 'RECOVER_SERVICES') {
              const recoveryResult = await automatedRecovery.runAutomatedRecovery({
                components: ['services'],
                incidentId: incident.id
              });
              
              stepResult.status = 'COMPLETED';
              stepResult.result = recoveryResult.components.services;
            } else if (step.action === 'RECOVER_ALL') {
              const recoveryResult = await automatedRecovery.runAutomatedRecovery({
                components: ['database', 'files', 'services'],
                incidentId: incident.id
              });
              
              stepResult.status = 'COMPLETED';
              stepResult.result = recoveryResult;
            } else {
              stepResult.status = 'SKIPPED';
              stepResult.message = `Unknown action: ${step.action}`;
            }
            break;
            
          case 'NOTIFICATION':
            // Send notification
            const notificationResult = await notifyIncidentResponseTeam(incident, await loadBcpPlan());
            
            stepResult.status = 'COMPLETED';
            stepResult.result = notificationResult;
            break;
            
          default:
            stepResult.status = 'SKIPPED';
            stepResult.message = `Unknown step type: ${step.type}`;
            break;
        }
      } catch (error) {
        logger.error(`Error executing recovery step ${step.id}: ${error.message}`, { error });
        
        stepResult.status = 'FAILED';
        stepResult.error = error.message;
      }
      
      stepResult.endTime = new Date().toISOString();
      results.steps.push(stepResult);
      
      // Update incident with step result
      await updateIncidentRecord(incident.id, {
        currentStep: step.id,
        stepResults: {
          ...incident.stepResults,
          [step.id]: stepResult
        }
      });
      
      // If step failed and it's critical, stop execution
      if (stepResult.status === 'FAILED' && step.critical) {
        logger.error(`Critical step ${step.id} failed, stopping procedure execution`);
        
        results.status = 'FAILED';
        results.error = `Critical step ${step.id} failed: ${stepResult.error || 'Unknown error'}`;
        
        // Update incident status
        await updateIncidentRecord(incident.id, {
          status: 'FAILED',
          error: results.error
        });
        
        return results;
      }
      
      // If step is waiting for human intervention, stop execution
      if (stepResult.status === 'WAITING_FOR_HUMAN') {
        logger.info(`Step ${step.id} is waiting for human intervention, pausing procedure execution`);
        
        results.status = 'WAITING_FOR_HUMAN';
        results.message = `Step ${step.id} is waiting for human intervention`;
        
        // Update incident status
        await updateIncidentRecord(incident.id, {
          status: 'WAITING_FOR_HUMAN',
          message: results.message
        });
        
        return results;
      }
    }
    
    // All steps completed successfully
    results.status = 'COMPLETED';
    
    // Update incident status
    await updateIncidentRecord(incident.id, {
      status: 'RECOVERED',
      completedProcedures: [
        ...(incident.completedProcedures || []),
        procedure.id
      ]
    });
    
    logger.info(`Recovery procedure ${procedure.id} completed successfully for incident ${incident.id}`);
    
    return results;
  } catch (error) {
    logger.error(`Error executing recovery procedure: ${error.message}`, { error });
    
    // Update incident status
    await updateIncidentRecord(incident.id, {
      status: 'FAILED',
      error: error.message
    });
    
    throw error;
  }
}

/**
 * Handle incident
 * @param {Object} incidentDetails - Incident details
 * @returns {Promise<Object>} Incident record
 */
async function handleIncident(incidentDetails) {
  logger.info('Handling incident', { incidentDetails });
  
  try {
    // Load BCP plan
    const bcpPlan = await loadBcpPlan();
    
    // Create incident record
    const incident = await createIncidentRecord({
      ...incidentDetails,
      status: 'OPEN',
      timestamp: new Date().toISOString()
    });
    
    // Notify incident response team
    const notificationResults = await notifyIncidentResponseTeam(incident, bcpPlan);
    
    // Update incident with notification results
    await updateIncidentRecord(incident.id, {
      notificationResults
    });
    
    // Find applicable procedures
    const procedures = bcpPlan.procedures.filter(procedure => {
      return procedure.applicableIncidentTypes.includes(incident.type);
    });
    
    logger.info(`Found ${procedures.length} applicable procedures for incident type ${incident.type}`);
    
    // Execute procedures
    if (procedures.length > 0) {
      // Sort procedures by priority
      procedures.sort((a, b) => a.priority - b.priority);
      
      // Execute first procedure
      const procedure = procedures[0];
      
      logger.info(`Executing procedure ${procedure.id} for incident ${incident.id}`);
      
      const procedureResults = await executeRecoveryProcedure(incident, procedure);
      
      // Update incident with procedure results
      await updateIncidentRecord(incident.id, {
        procedureResults: {
          ...incident.procedureResults,
          [procedure.id]: procedureResults
        }
      });
    }
    
    // Get updated incident record
    return await getIncidentRecord(incident.id);
  } catch (error) {
    logger.error(`Error handling incident: ${error.message}`, { error });
    throw error;
  }
}

// If script is run directly, handle test incident
if (require.main === module) {
  const args = process.argv.slice(2);
  const incidentType = args[0] || 'DATABASE_FAILURE';
  const severity = args[1] || 'HIGH';
  
  handleIncident({
    title: `Test ${incidentType} Incident`,
    type: incidentType,
    severity,
    description: `This is a test ${incidentType} incident with ${severity} severity.`,
    impact: 'This is a test incident with no real impact.',
    responseActions: [
      'Notify incident response team',
      'Execute recovery procedures',
      'Update status page'
    ],
    updateStatusPage: false
  })
    .then(incident => {
      logger.info('Incident handled:');
      logger.info(`ID: ${incident.id}`);
      logger.info(`Status: ${incident.status}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  handleIncident,
  executeRecoveryProcedure,
  notifyIncidentResponseTeam,
  createIncidentRecord,
  updateIncidentRecord,
  getIncidentRecord,
  loadBcpPlan,
  saveBcpPlan
};
