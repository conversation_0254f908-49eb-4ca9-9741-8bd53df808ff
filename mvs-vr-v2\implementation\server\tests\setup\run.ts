#!/usr/bin/env node

import { resolve } from 'path';
import { fileURLToPath } from 'url';
import * as dotenv from 'dotenv';

// Set up environment
const __dirname = fileURLToPath(new URL('.', import.meta.url));
const ROOT_DIR = resolve(__dirname, '../..');

// Load environment variables
dotenv.config({ path: resolve(ROOT_DIR, '.env.test') });

// Import order matters for proper setup
async function bootstrap() {
  try {
    // 1. Register module loader
    await import('./register.js');

    // 2. Set up test environment
    const { moduleLoader } = await import('./test-loader.js');

    // 3. Register test framework
    const testUtils = await moduleLoader.load('npm:@testing-library/jest-dom');
    const vitestGlobals = await moduleLoader.load('npm:vitest/globals');

    // 4. Set up global test environment
    Object.assign(global, {
      ...vitestGlobals,
      expect: {
        ...vitestGlobals.expect,
        ...testUtils.matchers,
      },
    });

    // 5. Configure Vue test utils
    const { config } = await moduleLoader.load('npm:@vue/test-utils');
    config.global.stubs = {
      transition: false,
      'router-link': true,
    };

    // 6. Configure test environment
    process.env.NODE_ENV = 'test';
    process.env.TEST_ENV = process.env.TEST_ENV || 'node';

    // 7. Log setup completion
    console.log('Test environment setup complete');
  } catch (error) {
    console.error('Failed to set up test environment:', error);
    process.exit(1);
  }
}

// Run bootstrap
bootstrap().catch(error => {
  console.error('Bootstrap failed:', error);
  process.exit(1);
});
