# Vitest Migration Step-by-Step Guide

This document provides a detailed, step-by-step approach to migrating tests from <PERSON><PERSON> to Vitest in the MVS-VR project.

## Prerequisites

1. Ensure you have the latest dependencies installed:

```bash
npm install
npm install helmet
npm install vue-template-compiler@3.2.47 --save-dev
```

## Migration Process

### Phase 1: Automated Migration

1. Run the migration script in dry-run mode to see what changes would be made:

```bash
node scripts/jest-to-vitest-migration.js --dry-run
```

2. Run the migration script on a specific test file or directory:

```bash
node scripts/jest-to-vitest-migration.js --path=tests/unit/example.test.ts
```

3. Run the migration script on all test files:

```bash
node scripts/jest-to-vitest-migration.js
```

### Phase 2: Manual Fixes

After running the automated migration, you'll need to manually fix some issues:

#### 1. Fix Module Mocking

For modules that export a default:

```javascript
// Before
vi.mock('module-name', () => {
  return vi.fn().mockImplementation(() => ({
    method1: vi.fn(),
    method2: vi.fn(),
  }));
});

// After
vi.mock('module-name', () => {
  const MockModule = vi.fn().mockImplementation(() => ({
    method1: vi.fn(),
    method2: vi.fn(),
  }));
  return { default: MockModule };
});
```

#### 2. Fix Import Paths

Add `.js` extension to local imports:

```javascript
// Before
import { something } from '../../path/to/module';

// After
import { something } from '../../path/to/module.js';
```

#### 3. Fix Type Assertions

Update type assertions for mocks:

```javascript
// Before
(myFunction as jest.Mock).mockReturnValue('test');

// After
(myFunction as ReturnType<typeof vi.fn>).mockReturnValue('test');
```

#### 4. Update Test Setup

If you have custom test setup files, update them:

```javascript
// Before (in vitest.setup.ts)
import { jest } from '@jest/globals';
global.jest = jest;

// After (in vitest.setup.ts)
import { vi } from 'vitest';
// No need to set global.jest
```

### Phase 3: Testing and Verification

1. Run a single test file to verify it works:

```bash
npx vitest run tests/unit/example.test.ts
```

2. Run all unit tests:

```bash
npm run test:unit
```

3. For tests that require more memory:

```bash
NODE_OPTIONS=--max-old-space-size=4096 npx vitest run tests/large-test.ts
```

4. Update the tracking document after each successful migration:

```
mvs-vr-v2/implementation/server/docs/VITEST_MIGRATION_TRACKING.md
```

## Troubleshooting Common Issues

### Issue: "vi.mock() is not returning an object"

This happens when mocking a module that exports a default. Fix by returning an object with a `default` property:

```javascript
vi.mock('module-name', () => {
  const MockClass = vi.fn().mockImplementation(() => ({}));
  return { default: MockClass };
});
```

### Issue: "Cannot find module" errors

This often happens when the module path doesn't include the file extension:

```javascript
// Add .js extension
import { something } from './module.js';
```

### Issue: CommonJS/ESM Compatibility Issues

If you're facing issues with CommonJS and ESM compatibility:

```javascript
// Before (CommonJS)
const { someFunction } = require('../path/to/module');

// After (ESM)
import { someFunction } from '../path/to/module.js';

// For dynamic imports
const module = await import('../path/to/module.js');
const someFunction = module.someFunction;
```

### Issue: Using the Mock Helpers

Use the provided mock helpers to standardize mock creation:

```javascript
import {
  createMockFn,
  createMockObject,
  createMockClass,
  createMockModule
} from '../helpers/mock-helpers';

// Create a mock function
const mockFn = createMockFn(() => 'mocked-value');

// Create a mock module
vi.mock('module-name', () => {
  const MockClass = createMockClass({
    method1: () => 'mocked-value-1',
    method2: () => 'mocked-value-2',
  });

  return createMockModule(MockClass, {
    namedExport: vi.fn(),
  });
});
```

### Issue: Vue version mismatch

If you see errors about Vue version mismatch:

```bash
npm install vue-template-compiler@3.2.47 --save-dev
```

### Issue: Memory errors

For tests that consume a lot of memory:

```bash
NODE_OPTIONS=--max-old-space-size=4096 npx vitest run tests/memory-intensive.test.ts
```

### Issue: Handling Timeouts

If tests are timing out, adjust the timeout settings:

```javascript
// Before (Jest)
jest.setTimeout(10000);

// After (Vitest)
vi.setConfig({ testTimeout: 10000 });
```

## Migration Order

For best results, migrate tests in this order:

1. Simple unit tests
2. Complex unit tests
3. Integration tests
4. API tests
5. E2E tests
6. Directus extension tests

## Verifying Migration Success

After migrating each test file:

1. Run the specific test file:

   ```bash
   npx vitest run path/to/test.ts
   ```

2. Check for any errors and fix them

3. Update the tracking document with the status

4. Commit your changes with a descriptive message:

   ```bash
   git commit -m "Migrate [test-name] from Jest to Vitest"
   ```

## Final Steps

Once all tests have been migrated:

1. Run the full test suite:

   ```bash
   npm test
   ```

2. Update any CI/CD configurations to use Vitest instead of Jest

3. Remove any Jest-specific dependencies that are no longer needed

4. Update documentation to reflect the new testing framework
