/**
 * Simple API Key Middleware Test
 *
 * This file contains a simple test for the API key middleware.
 */

import { describe, it, expect } from 'vitest';
import crypto from 'crypto';

// Mock the crypto module
function mockHashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

describe('API Key Middleware', () => {
  describe('hashApiKey', () => {
    it('should hash an API key using SHA-256', () => {
      const apiKey = 'test-api-key';
      const hashedKey = mockHashApiKey(apiKey);

      // SHA-256 hash of 'test-api-key'
      const expectedHash = '4c806362b613f7496abf284146efd31da90e4b16169fe001841ca17290f427c4';

      expect(hashedKey).toBe(expectedHash);
    });
  });
});
