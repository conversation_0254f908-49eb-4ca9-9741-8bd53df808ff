/**
 * Component test utilities for Vue 2 with Vitest
 *
 * This module provides helper functions for testing Vue components.
 */

import { mount, shallowMount } from '@vue/test-utils';
import Vue from 'vue';

/**
 * Create a wrapper for a Vue component with the given options
 *
 * @param {Object} component - The Vue component to mount
 * @param {Object} options - Mount options
 * @param {boolean} shallow - Whether to use shallowMount (default: false)
 * @returns {Object} The mounted component wrapper
 */
export function createWrapper(component, options = {}, shallow = false) {
  const mountFn = shallow ? shallowMount : mount;

  // Default options
  const defaultOptions = {
    // Add any global mocks or stubs here
    mocks: {
      $directus: {
        url: 'http://localhost:8055',
        auth: {
          token: 'mock-token',
          user: {
            id: 'user-id',
            vendor_id: 'vendor-id',
            role: 'admin',
          },
        },
      },
    },
    stubs: {
      'material-icon': true,
      'v-icon': true,
    },
  };

  // Merge options
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    mocks: {
      ...defaultOptions.mocks,
      ...(options.mocks || {}),
    },
    stubs: {
      ...defaultOptions.stubs,
      ...(options.stubs || {}),
    },
  };

  return mountFn(component, mergedOptions);
}

/**
 * Wait for the next tick in the Vue update cycle
 *
 * @returns {Promise} A promise that resolves after the next tick
 */
export function nextTick() {
  return Vue.nextTick();
}

/**
 * Wait for a specified amount of time
 *
 * @param {number} ms - The number of milliseconds to wait
 * @returns {Promise} A promise that resolves after the specified time
 */
export function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Find elements in the wrapper that match the given selector
 *
 * This is a more robust version of wrapper.findAll() that ensures
 * the returned array has the expected methods.
 *
 * @param {Object} wrapper - The Vue test wrapper
 * @param {string} selector - The CSS selector to find
 * @returns {Array} An array of element wrappers
 */
export function findAll(wrapper, selector) {
  const elements = wrapper.findAll(selector);

  // If elements is already an array with the expected methods, return it
  if (elements && typeof elements.at === 'function') {
    return elements;
  }

  // Otherwise, convert to an array with the expected methods
  const result = Array.from(elements || []);

  // Add helper methods
  result.at = index => result[index];

  // Add text method to each element
  result.forEach(el => {
    if (!el.text && el.element) {
      el.text = () => el.element.textContent.trim();
    }
  });

  return result;
}

/**
 * Trigger an event on an element
 *
 * This is a more robust version of element.trigger() that works
 * even if the element doesn't have a trigger method.
 *
 * @param {Object} element - The element to trigger the event on
 * @param {string} eventName - The name of the event to trigger
 * @param {Object} options - Event options
 * @returns {Promise} A promise that resolves after the event is triggered
 */
export function trigger(element, eventName, options = {}) {
  if (!element) {
    throw new Error(`Cannot trigger event: element is undefined`);
  }

  if (typeof element.trigger === 'function') {
    return element.trigger(eventName, options);
  }

  // Fallback: dispatch a DOM event
  if (element.element) {
    const event = new Event(eventName, {
      bubbles: true,
      cancelable: true,
      ...options,
    });

    element.element.dispatchEvent(event);
    return Vue.nextTick();
  }

  throw new Error(`Cannot trigger event: element is not a valid wrapper`);
}

/**
 * Set the value of an input element
 *
 * @param {Object} input - The input element wrapper
 * @param {string|number} value - The value to set
 * @returns {Promise} A promise that resolves after the value is set
 */
export function setValue(input, value) {
  if (!input) {
    throw new Error(`Cannot set value: input is undefined`);
  }

  if (typeof input.setValue === 'function') {
    return input.setValue(value);
  }

  throw new Error(`Cannot set value: input is not a valid wrapper`);
}

/**
 * Check if an element has a class
 *
 * @param {Object} element - The element wrapper
 * @param {string} className - The class name to check
 * @returns {boolean} True if the element has the class
 */
export function hasClass(element, className) {
  if (!element) {
    return false;
  }

  if (typeof element.classes === 'function') {
    return element.classes().includes(className);
  }

  if (element.element && element.element.classList) {
    return element.element.classList.contains(className);
  }

  // Try to get class from the class attribute
  if (element.attributes && element.attributes().class) {
    return element.attributes().class.split(' ').includes(className);
  }

  return false;
}

export default {
  createWrapper,
  nextTick,
  wait,
  findAll,
  trigger,
  setValue,
  hasClass,
};
