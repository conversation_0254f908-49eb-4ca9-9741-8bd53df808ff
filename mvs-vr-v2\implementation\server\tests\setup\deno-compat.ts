import { join, dirname } from 'npm:@deno/path';
import { existsSync } from 'npm:@deno/fs';

/**
 * Helper functions to ensure compatibility between Node.js and Deno environments
 */

/**
 * Normalizes import paths to work in both Node.js and Deno
 */
export function normalizeImport(path: string): string {
  // Handle npm packages
  if (!path.startsWith('.') && !path.startsWith('/')) {
    return `npm:${path}`;
  }

  // Handle local modules
  return path.endsWith('.ts') ? path : `${path}.ts`;
}

/**
 * Resolves file paths consistently across environments
 */
export function resolvePath(...paths: string[]): string {
  return join(...paths);
}

/**
 * Checks if a path exists in both environments
 */
export function pathExists(path: string): boolean {
  return existsSync(path);
}

/**
 * Gets environment-specific configuration
 */
export function getEnvConfig(): Record<string, string> {
  return {
    NODE_ENV: Deno.env.get('NODE_ENV') || 'test',
    DENO_ENV: Deno.env.get('DENO_ENV') || 'test',
    IS_DENO: 'true',
  };
}

/**
 * Error handler that works in both environments
 */
export function handleError(error: unknown): void {
  if (error instanceof Error) {
    console.error('Error:', error.message);
    if (error.stack) {
      console.error('Stack:', error.stack);
    }
  } else {
    console.error('Unknown error:', error);
  }
}

/**
 * Type guard for checking if code is running in Deno
 */
export function isDeno(): boolean {
  return typeof Deno !== 'undefined';
}

/**
 * Mock console methods that work in both environments
 */
export function createMockConsole() {
  return {
    log: () => {},
    error: () => {},
    warn: () => {},
    info: () => {},
    debug: () => {},
  };
}

/**
 * Normalize test environment between Node.js and Deno
 */
export function setupTestEnvironment(): void {
  // Set up global types
  if (isDeno()) {
    (globalThis as any).window = globalThis;
    (globalThis as any).global = globalThis;
  }

  // Set up console
  if (!globalThis.console) {
    globalThis.console = createMockConsole();
  }
}

// Initialize test environment when imported
setupTestEnvironment();
