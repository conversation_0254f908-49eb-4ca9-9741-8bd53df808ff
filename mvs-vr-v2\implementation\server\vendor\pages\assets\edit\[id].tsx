import React, { useEffect, useState, useRef } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Alert,
  Snackbar,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon, Save as SaveIcon, CloudUpload as UploadIcon } from '@mui/icons-material';
import { useRouter } from 'next/router';
import VendorLayout from '../../../components/VendorLayout';
import ProtectedRoute from '../../../components/ProtectedRoute';
import { assetService, Asset } from '../../../utils/api/asset-service';

/**
 * Asset edit page
 */
const AssetEdit: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const [asset, setAsset] = useState<Asset | null>(null);
  const [assetName, setAssetName] = useState('');
  const [assetType, setAssetType] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [status, setStatus] = useState('');
  const [newFile, setNewFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch asset details
  useEffect(() => {
    const fetchAssetDetails = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);
        
        // Fetch asset details
        const assetData = await assetService.getAssetById(id as string);
        setAsset(assetData);
        
        // Set form values
        setAssetName(assetData.name);
        setAssetType(assetData.type);
        setDescription(assetData.description || '');
        setTags(assetData.tags ? assetData.tags.join(', ') : '');
        setStatus(assetData.status);
      } catch (err: unknown) {
        console.error('Error fetching asset details:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch asset details');
        setShowError(true);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAssetDetails();
  }, [id]);

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setNewFile(event.target.files[0]);
    }
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!asset) return;
    
    // Validate form
    if (!assetName) {
      setError('Asset name is required');
      setShowError(true);
      return;
    }
    
    try {
      setSaving(true);
      setError(null);
      
      // Create tag array from comma-separated string
      const tagArray = tags.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);
      
      // Update asset
      await assetService.updateAsset({
        id: asset.id,
        name: assetName,
        type: assetType,
        description,
        tags: tagArray,
        status
      });
      
      // Upload new version if file is selected
      if (newFile) {
        await assetService.uploadNewVersion(asset.id, newFile);
      }
      
      // Show success message
      setSuccess('Asset updated successfully');
      setShowSuccess(true);
      
      // Redirect to asset details page after a delay
      setTimeout(() => {
        router.push(`/assets/${asset.id}`);
      }, 2000);
    } catch (err: unknown) {
      console.error('Error updating asset:', err);
      setError(err instanceof Error ? err.message : 'Failed to update asset');
      setShowError(true);
    } finally {
      setSaving(false);
    }
  };

  // Handle error snackbar close
  const handleCloseError = () => {
    setShowError(false);
  };

  // Handle success snackbar close
  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  if (loading) {
    return (
      <VendorLayout title="Edit Asset">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }

  return (
    <VendorLayout title={`Edit Asset: ${asset?.name}`}>
      {/* Error snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      {/* Success snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      {/* Back button */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => router.push(`/assets/${id}`)}
          sx={{ mb: 2 }}
        >
          Back to Asset Details
        </Button>
        
        <Typography variant="h4" component="h1" gutterBottom>
          Edit Asset
        </Typography>
      </Box>

      <Paper sx={{ p: 3, mb: 4 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Asset Name */}
            <Grid item xs={12} md={6}>
              <TextField
                label="Asset Name"
                fullWidth
                value={assetName}
                onChange={(e) => setAssetName(e.target.value)}
                required
                disabled={saving}
              />
            </Grid>

            {/* Asset Type */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Asset Type</InputLabel>
                <Select
                  value={assetType}
                  onChange={(e) => setAssetType(e.target.value)}
                  label="Asset Type"
                  disabled={saving}
                >
                  <MenuItem value="model">3D Model</MenuItem>
                  <MenuItem value="texture">Texture</MenuItem>
                  <MenuItem value="material">Material</MenuItem>
                  <MenuItem value="animation">Animation</MenuItem>
                  <MenuItem value="scene">Scene</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Status */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Status</InputLabel>
                <Select
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                  label="Status"
                  disabled={saving}
                >
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="review">In Review</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="archived">Archived</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Tags */}
            <Grid item xs={12} md={6}>
              <TextField
                label="Tags"
                fullWidth
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                disabled={saving}
                helperText="Separate tags with commas"
              />
            </Grid>

            {/* Description */}
            <Grid item xs={12}>
              <TextField
                label="Description"
                fullWidth
                multiline
                rows={4}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={saving}
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Upload New Version
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Upload a new file to create a new version of this asset
              </Typography>

              <Box sx={{ mt: 2 }}>
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                  accept=".glb,.gltf,.fbx,.obj,.usdz,.usd"
                />
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  onClick={() => fileInputRef.current?.click()}
                  disabled={saving}
                >
                  Select File
                </Button>
                {newFile && (
                  <Box mt={2}>
                    <Alert severity="info">
                      Selected file: {newFile.name} ({(newFile.size / (1024 * 1024)).toFixed(2)} MB)
                    </Alert>
                  </Box>
                )}
              </Box>
            </Grid>

            {/* Submit Button */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="flex-end">
                <Button
                  type="button"
                  variant="outlined"
                  sx={{ mr: 2 }}
                  disabled={saving}
                  onClick={() => router.push(`/assets/${id}`)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={saving}
                  startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </VendorLayout>
  );
};

// Wrap the AssetEdit component with ProtectedRoute
const ProtectedAssetEdit: React.FC = () => {
  return (
    <ProtectedRoute>
      <AssetEdit />
    </ProtectedRoute>
  );
};

export default ProtectedAssetEdit;
