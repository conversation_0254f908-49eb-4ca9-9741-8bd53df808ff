import { logger } from '../shared/utils/logger';

/**
 * Advanced performance optimizer with LRU caching capabilities
 */
class PerformanceOptimizer {
  /**
   * Create a new cache instance
   * @param {Number} maxSize - Maximum number of items to cache
   * @param {Number} ttl - Time to live in milliseconds
   * @param {Object} options - Additional options
   * @param {Number} options.maxMemorySize - Maximum memory size in bytes (approximate)
   * @param {Boolean} options.trackHitRate - Whether to track cache hit rate
   * @param {Number} options.evictionThreshold - Memory threshold for eviction (0-1)
   */
  constructor(maxSize = 100, ttl = 5 * 60 * 1000, options = {}) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.hits = 0;
    this.misses = 0;

    // Advanced options
    this.maxMemorySize = options.maxMemorySize || 50 * 1024 * 1024; // Default 50MB
    this.evictionThreshold = options.evictionThreshold || 0.9; // Start evicting at 90% capacity

    // Cache statistics
    this.evictions = 0;
    this.expirations = 0;
    this.totalMemoryUsed = 0;

    // Set up automatic cleanup
    this.setupAutoCleanup();
  }

  /**
   * Set up automatic cleanup of expired items
   * @private
   */
  setupAutoCleanup() {
    // Clean up expired items every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredItems();
    }, 60 * 1000);
  }

  /**
   * Clean up expired items
   * @private
   */
  cleanupExpiredItems() {
    const now = Date.now();
    let expiredCount = 0;
    let freedMemory = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        freedMemory += item.size || 0;
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.expirations += expiredCount;
      this.totalMemoryUsed -= freedMemory;
    }
  }

  /**
   * Approximate the size of a value in bytes
   * @param {*} value - Value to measure
   * @returns {Number} - Approximate size in bytes
   * @private
   */
  approximateSize(value) {
    if (value === null || value === undefined) return 8;

    const type = typeof value;

    if (type === 'boolean') return 4;
    if (type === 'number') return 8;
    if (type === 'string') return value.length * 2;

    if (type === 'object') {
      if (Array.isArray(value)) {
        return value.reduce((size, item) => size + this.approximateSize(item), 0);
      }

      // For objects, estimate based on keys and values
      return Object.entries(value).reduce((size, [key, val]) => {
        return size + key.length * 2 + this.approximateSize(val);
      }, 0);
    }

    return 8; // Default size for other types
  }

  /**
   * Get an item from the cache
   * @param {String} key - Cache key
   * @returns {*} - Cached value or null if not found
   */
  get(key) {
    if (!this.cache.has(key)) {
      this.misses++;
      return null;
    }

    const item = this.cache.get(key);

    // Check if item has expired
    if (item.expiry < Date.now()) {
      this.cache.delete(key);
      this.totalMemoryUsed -= item.size || 0;
      this.expirations++;
      this.misses++;
      return null;
    }

    // Update access time and frequency
    item.lastAccessed = Date.now();
    item.accessCount = (item.accessCount || 0) + 1;
    this.hits++;

    return item.value;
  }

  /**
   * Set an item in the cache
   * @param {String} key - Cache key
   * @param {*} value - Value to cache
   * @param {Object} options - Additional options
   * @param {Number} options.ttl - Custom TTL for this item
   * @param {Number} options.size - Approximate size of the item in bytes
   */
  set(key, value, options = {}) {
    const ttl = options.ttl || this.ttl;

    // Calculate approximate size of the value
    const valueSize = options.size || this.approximateSize(value);

    // Check if adding this item would exceed memory limit
    if (valueSize > this.maxMemorySize) {
      console.warn(
        `Item too large for cache: ${valueSize} bytes exceeds limit of ${this.maxMemorySize} bytes`,
      );
      return;
    }

    // If we already have this key, remove its size from the total
    if (this.cache.has(key)) {
      const oldItem = this.cache.get(key);
      this.totalMemoryUsed -= oldItem.size || 0;
    }

    // Check if we need to evict items due to memory pressure
    if (this.totalMemoryUsed + valueSize > this.maxMemorySize * this.evictionThreshold) {
      this.evictByMemory(this.totalMemoryUsed + valueSize - this.maxMemorySize * 0.7);
    }

    // If cache is full by count, remove least recently used item
    if (!this.cache.has(key) && this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    // Add new item
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl,
      lastAccessed: Date.now(),
      size: valueSize,
      accessCount: 0,
    });

    this.totalMemoryUsed += valueSize;
  }

  /**
   * Evict items based on memory pressure
   * @param {Number} bytesToFree - Number of bytes to free
   * @private
   */
  evictByMemory(bytesToFree) {
    if (bytesToFree <= 0) return;

    // Sort items by priority (least valuable first)
    const items = Array.from(this.cache.entries())
      .map(([key, item]) => ({
        key,
        lastAccessed: item.lastAccessed,
        accessCount: item.accessCount || 0,
        size: item.size || 0,
      }))
      .sort((a, b) => {
        // Sort by access count first, then by last accessed time
        if (a.accessCount !== b.accessCount) {
          return a.accessCount - b.accessCount;
        }
        return a.lastAccessed - b.lastAccessed;
      });

    let freedBytes = 0;
    let evictedCount = 0;

    // Evict items until we've freed enough memory
    for (const item of items) {
      if (freedBytes >= bytesToFree) break;

      this.cache.delete(item.key);
      freedBytes += item.size;
      evictedCount++;

      if (this.cache.size === 0) break;
    }

    this.totalMemoryUsed -= freedBytes;
    this.evictions += evictedCount;
  }

  /**
   * Evict the least recently used item
   * @private
   */
  evictLeastRecentlyUsed() {
    const oldestKey = this.findOldestKey();

    if (oldestKey) {
      const item = this.cache.get(oldestKey);
      this.totalMemoryUsed -= item.size || 0;
      this.cache.delete(oldestKey);
      this.evictions++;

      // Log eviction for debugging
      logger.info(`Evicted item with key: ${oldestKey}`);
    }
  }

  /**
   * Find the least recently used cache key
   * @returns {String} - The oldest key
   */
  findOldestKey() {
    let oldestKey = null;
    let oldestTime = Infinity;

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestKey = key;
        oldestTime = item.lastAccessed;
      }
    }

    return oldestKey;
  }

  /**
   * Clear the cache
   */
  clear() {
    this.cache.clear();
    this.totalMemoryUsed = 0;
  }

  /**
   * Dispose of resources
   */
  dispose() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hits: this.hits,
      misses: this.misses,
      evictions: this.evictions,
      expirations: this.expirations,
      hitRatio: this.hits / (this.hits + this.misses || 1),
      memoryUsed: this.totalMemoryUsed,
      memoryLimit: this.maxMemorySize,
      memoryUsageRatio: this.totalMemoryUsed / this.maxMemorySize,
    };
  }
}

/**
 * Virtual list renderer for optimizing large lists with lazy loading
 */
class VirtualListRenderer {
  /**
   * Create a new virtual list renderer
   * @param {Array} items - Full list of items
   * @param {Number} itemHeight - Height of each item in pixels
   * @param {Number} containerHeight - Height of the container in pixels
   * @param {Number} buffer - Number of items to render above and below the visible area
   * @param {Object} options - Additional options
   * @param {Boolean} options.lazyLoad - Whether to enable lazy loading
   * @param {Function} options.loadMoreItems - Function to load more items
   * @param {Number} options.loadThreshold - Threshold for triggering load more (0-1)
   * @param {Number} options.pageSize - Number of items to load per page
   * @param {Boolean} options.prefetch - Whether to prefetch the next page of data
   * @param {Number} options.prefetchThreshold - Threshold for triggering prefetch (0-1)
   * @param {Boolean} options.useWorker - Whether to use a web worker for data loading
   */
  constructor(items = [], itemHeight = 40, containerHeight = 400, buffer = 5, options = {}) {
    this.items = items;
    this.itemHeight = itemHeight;
    this.containerHeight = containerHeight;
    this.buffer = buffer;
    this.scrollTop = 0;

    // Lazy loading options
    this.lazyLoad = options.lazyLoad || false;
    this.loadMoreItems = options.loadMoreItems || null;
    this.loadThreshold = options.loadThreshold || 0.8;
    this.pageSize = options.pageSize || 20;
    this.isLoading = false;
    this.hasMoreItems = true;
    this.totalItems = options.totalItems || null;

    // Prefetching options
    this.prefetch = options.prefetch || false;
    this.prefetchThreshold = options.prefetchThreshold || 0.5; // Start prefetching at 50% of the current page
    this.isPrefetching = false;
    this.prefetchedData = null;
    this.currentPage = Math.ceil(this.items.length / this.pageSize) || 1;

    // Web Worker options
    this.useWorker = options.useWorker || false;
    this.worker = null;

    if (this.useWorker) {
      this.initWorker();
    }

    // Performance metrics
    this.renderCount = 0;
    this.lastRenderTime = 0;
    this.averageRenderTime = 0;
    this.loadTimes = [];
  }

  /**
   * Initialize web worker for background loading
   * @private
   */
  initWorker() {
    try {
      const workerCode = `
        self.onmessage = function(e) {
          const { action, page, pageSize, loadFn } = e.data;

          if (action === 'load') {
            // In a real implementation, we would call the load function
            // For now, we'll simulate a response
            setTimeout(() => {
              self.postMessage({
                action: 'loaded',
                page: page,
                data: Array.from({ length: pageSize }, (_, i) => ({
                  id: \`worker-item-\${(page - 1) * pageSize + i}\`,
                  name: \`Worker Item \${(page - 1) * pageSize + i}\`,
                  value: (page - 1) * pageSize + i
                }))
              });
            }, 200);
          }
        };
      `;

      const blob = new Blob([workerCode], { type: 'application/javascript' });
      this.worker = new Worker(URL.createObjectURL(blob));

      this.worker.onmessage = e => {
        const { action, page, data } = e.data;

        if (action === 'loaded') {
          this.handleWorkerData(page, data);
        }
      };
    } catch (error) {
      console.error('Failed to initialize web worker:', error);
      this.useWorker = false;
    }
  }

  /**
   * Handle data received from web worker
   * @param {Number} page - Page number
   * @param {Array} data - Data loaded by the worker
   * @private
   */
  handleWorkerData(page, data) {
    if (page === this.currentPage + 1) {
      if (this.isLoading) {
        // This is the data we're currently loading
        this.items = [...this.items, ...data];
        this.isLoading = false;
        this.currentPage = page;

        // If we have more items to load, start prefetching the next page
        if (this.hasMoreItems && this.prefetch) {
          this.prefetchNextPage();
        }
      } else {
        // This is prefetched data
        this.prefetchedData = data;
        this.isPrefetching = false;
      }
    }
  }

  /**
   * Prefetch the next page of data
   * @private
   */
  prefetchNextPage() {
    if (this.isPrefetching || !this.hasMoreItems || !this.loadMoreItems) return;

    this.isPrefetching = true;
    const nextPage = this.currentPage + 1;

    if (this.useWorker && this.worker) {
      // Use web worker for prefetching
      this.worker.postMessage({
        action: 'load',
        page: nextPage,
        pageSize: this.pageSize,
      });
    } else {
      // Use main thread for prefetching
      this.loadMoreItems(nextPage, this.pageSize)
        .then(data => {
          this.prefetchedData = data;
          this.isPrefetching = false;
        })
        .catch(error => {
          console.error('Error prefetching data:', error);
          this.isPrefetching = false;
        });
    }
  }

  /**
   * Update the scroll position and check if more items need to be loaded
   * @param {Number} scrollTop - New scroll position
   */
  updateScroll(scrollTop) {
    this.scrollTop = scrollTop;

    // Check if we need to load more items
    if (this.lazyLoad && this.loadMoreItems && this.hasMoreItems && !this.isLoading) {
      const scrollRatio = scrollTop / (this.items.length * this.itemHeight - this.containerHeight);

      if (scrollRatio > this.loadThreshold) {
        this.loadMore();
      } else if (this.prefetch && !this.isPrefetching && scrollRatio > this.prefetchThreshold) {
        // Start prefetching the next page when we're halfway through the current page
        this.prefetchNextPage();
      }
    }
  }

  /**
   * Load more items
   * @returns {Promise} - Promise that resolves when items are loaded
   */
  async loadMore() {
    if (this.isLoading || !this.hasMoreItems) return;

    this.isLoading = true;
    const startTime = performance.now();

    try {
      const nextPage = Math.ceil(this.items.length / this.pageSize) + 1;

      // Check if we already have prefetched data
      if (this.prefetch && this.prefetchedData) {
        // Use prefetched data
        const newItems = this.prefetchedData;
        this.prefetchedData = null;

        if (newItems && newItems.length > 0) {
          this.items = [...this.items, ...newItems];
          this.currentPage = nextPage;

          // Start prefetching the next page
          if (this.prefetch) {
            this.prefetchNextPage();
          }
        } else {
          this.hasMoreItems = false;
        }
      } else if (this.useWorker && this.worker) {
        // Use web worker for loading
        this.worker.postMessage({
          action: 'load',
          page: nextPage,
          pageSize: this.pageSize,
        });

        // Note: The worker will update the items when it's done
        // We'll keep isLoading = true until the worker responds
        return;
      } else {
        // Load data on the main thread
        const newItems = await this.loadMoreItems(nextPage, this.pageSize);

        if (newItems && newItems.length > 0) {
          this.items = [...this.items, ...newItems];
          this.currentPage = nextPage;

          // Start prefetching the next page
          if (this.prefetch && this.hasMoreItems) {
            this.prefetchNextPage();
          }
        } else {
          this.hasMoreItems = false;
        }
      }

      // If we know the total number of items, check if we've loaded all of them
      if (this.totalItems !== null && this.items.length >= this.totalItems) {
        this.hasMoreItems = false;
      }

      // Track load time
      const loadTime = performance.now() - startTime;
      this.loadTimes.push(loadTime);

      // Keep only the last 10 load times for average calculation
      if (this.loadTimes.length > 10) {
        this.loadTimes.shift();
      }
    } catch (error) {
      console.error('Error loading more items:', error);
    } finally {
      if (!this.useWorker || !this.worker) {
        // Only set isLoading to false if we're not using a worker
        // For workers, this will be set when the worker responds
        this.isLoading = false;
      }
    }
  }

  /**
   * Get the items that should be rendered
   * @returns {Object} - Visible items, container style, and loading state
   */
  getVisibleItems() {
    const startTime = performance.now();
    this.renderCount++;

    const totalHeight =
      this.totalItems !== null
        ? this.totalItems * this.itemHeight
        : this.items.length * this.itemHeight + (this.hasMoreItems ? 100 : 0);

    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.buffer * 2;

    // Calculate start index
    const startIndex = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.buffer);

    // Calculate end index
    const endIndex = Math.min(this.items.length, startIndex + visibleCount);

    // Get visible items
    const visibleItems = this.items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      style: {
        position: 'absolute',
        top: `${(startIndex + index) * this.itemHeight}px`,
        height: `${this.itemHeight}px`,
        width: '100%',
      },
    }));

    // Create container style
    const containerStyle = {
      position: 'relative',
      height: `${totalHeight}px`,
      overflow: 'hidden',
    };

    // Calculate render time
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Update average render time
    this.lastRenderTime = renderTime;
    this.averageRenderTime =
      (this.averageRenderTime * (this.renderCount - 1) + renderTime) / this.renderCount;

    // Calculate average load time
    const averageLoadTime =
      this.loadTimes.length > 0
        ? this.loadTimes.reduce((sum, time) => sum + time, 0) / this.loadTimes.length
        : 0;

    return {
      visibleItems,
      containerStyle,
      isLoading: this.isLoading,
      hasMoreItems: this.hasMoreItems,
      isPrefetching: this.isPrefetching,
      prefetchedDataAvailable: !!this.prefetchedData,
      usingWorker: this.useWorker && !!this.worker,
      metrics: {
        renderTime,
        averageRenderTime: this.averageRenderTime,
        renderCount: this.renderCount,
        visibleCount: visibleItems.length,
        totalItems: this.items.length,
        loadTimes: this.loadTimes,
        averageLoadTime,
        currentPage: this.currentPage,
        prefetchStatus: this.isPrefetching ? 'prefetching' : this.prefetchedData ? 'ready' : 'none',
      },
    };
  }

  /**
   * Update the list of items
   * @param {Array} items - New list of items
   * @param {Object} options - Additional options
   * @param {Number} options.totalItems - Total number of items (for pagination)
   * @param {Boolean} options.hasMoreItems - Whether there are more items to load
   */
  updateItems(items, options = {}) {
    this.items = items;

    if (options.totalItems !== undefined) {
      this.totalItems = options.totalItems;
    }

    if (options.hasMoreItems !== undefined) {
      this.hasMoreItems = options.hasMoreItems;
    }
  }

  /**
   * Get performance metrics
   * @returns {Object} - Performance metrics
   */
  getMetrics() {
    // Calculate average load time
    const averageLoadTime =
      this.loadTimes.length > 0
        ? this.loadTimes.reduce((sum, time) => sum + time, 0) / this.loadTimes.length
        : 0;

    return {
      renderCount: this.renderCount,
      lastRenderTime: this.lastRenderTime,
      averageRenderTime: this.averageRenderTime,
      itemCount: this.items.length,
      visibleItemCount: Math.ceil(this.containerHeight / this.itemHeight),
      loadTimes: [...this.loadTimes],
      averageLoadTime,
      prefetchingEnabled: this.prefetch,
      prefetchingStatus: this.isPrefetching
        ? 'prefetching'
        : this.prefetchedData
          ? 'ready'
          : 'none',
      workerEnabled: this.useWorker && !!this.worker,
      currentPage: this.currentPage,
      totalPages: this.totalItems ? Math.ceil(this.totalItems / this.pageSize) : null,
      hasMoreItems: this.hasMoreItems,
    };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics() {
    this.renderCount = 0;
    this.lastRenderTime = 0;
    this.averageRenderTime = 0;
    this.loadTimes = [];
  }

  /**
   * Clean up resources
   */
  dispose() {
    // Clean up web worker if it exists
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // Clear any cached data
    this.prefetchedData = null;

    // Reset metrics
    this.resetMetrics();
  }
}

/**
 * Debounce function to limit how often a function is called
 * @param {Function} func - Function to debounce
 * @param {Number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
function debounce(func, wait = 300) {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function to limit how often a function is called
 * @param {Function} func - Function to throttle
 * @param {Number} limit - Limit in milliseconds
 * @returns {Function} - Throttled function
 */
function throttle(func, limit = 100) {
  let inThrottle;

  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

/**
 * Measure the execution time of a function
 * @param {Function} func - Function to measure
 * @param {Array} args - Arguments to pass to the function
 * @returns {Object} - Result and execution time
 */
function measureExecutionTime(func, ...args) {
  const start = performance.now();
  const result = func(...args);
  const end = performance.now();

  return {
    result,
    executionTime: end - start,
  };
}

export { PerformanceOptimizer, VirtualListRenderer, debounce, throttle, measureExecutionTime };
