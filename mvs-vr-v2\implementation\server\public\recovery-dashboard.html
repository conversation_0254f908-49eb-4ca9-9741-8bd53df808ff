<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recovery Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding-top: 20px;
      padding-bottom: 40px;
      background-color: #f8f9fa;
    }
    .card {
      margin-bottom: 20px;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    .card-header {
      font-weight: 600;
    }
    .stat-card {
      text-align: center;
      padding: 15px;
    }
    .stat-value {
      font-size: 2rem;
      font-weight: 700;
    }
    .stat-label {
      font-size: 0.9rem;
      color: #6c757d;
    }
    .success-rate {
      font-size: 2.5rem;
      font-weight: 700;
    }
    .component-status {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }
    .status-success {
      background-color: #28a745;
    }
    .status-failure {
      background-color: #dc3545;
    }
    .status-badge {
      font-size: 0.8rem;
      padding: 0.25rem 0.5rem;
    }
    .recovery-item {
      border-left: 4px solid;
      margin-bottom: 10px;
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
    }
    .recovery-success {
      border-left-color: #28a745;
    }
    .recovery-failure {
      border-left-color: #dc3545;
    }
    .recovery-time {
      font-size: 0.8rem;
      color: #6c757d;
    }
    .recovery-details {
      margin-top: 5px;
      font-size: 0.9rem;
    }
    .progress {
      height: 5px;
      margin-top: 5px;
    }
    .visualization-frame {
      width: 100%;
      height: 600px;
      border: none;
    }
    #loadingOverlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .spinner-container {
      text-align: center;
    }
    .spinner-text {
      margin-top: 10px;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <div id="loadingOverlay">
    <div class="spinner-container">
      <div class="spinner-border text-primary" role="status"></div>
      <div class="spinner-text">Loading dashboard data...</div>
    </div>
  </div>

  <div class="container">
    <h1 class="mb-4">Recovery Dashboard</h1>
    
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Recovery Status</div>
          <div class="card-body" id="recoveryStatus">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">Current Status</h5>
                <p class="mb-0" id="currentStatus">Loading...</p>
              </div>
              <button class="btn btn-primary" id="startRecoveryBtn">Start Recovery</button>
            </div>
            <hr>
            <div>
              <h5 class="mb-1">Last Recovery</h5>
              <p class="mb-0" id="lastRecovery">Loading...</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Recovery Statistics</div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-4">
                <div class="stat-card">
                  <div class="stat-value" id="totalRecoveries">-</div>
                  <div class="stat-label">Total Recoveries</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stat-card">
                  <div class="stat-value text-success" id="successfulRecoveries">-</div>
                  <div class="stat-label">Successful</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stat-card">
                  <div class="stat-value text-danger" id="failedRecoveries">-</div>
                  <div class="stat-label">Failed</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="text-center">
              <div class="success-rate" id="successRate">-%</div>
              <div class="stat-label">Success Rate</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Component Performance</div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Component</th>
                    <th>Success Rate</th>
                    <th>Avg. Duration</th>
                  </tr>
                </thead>
                <tbody id="componentStats">
                  <tr>
                    <td colspan="3" class="text-center">Loading component statistics...</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Recent Recoveries</div>
          <div class="card-body">
            <div id="recentRecoveries">
              <p class="text-center">Loading recent recoveries...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="visualizationTabs">
              <li class="nav-item">
                <a class="nav-link active" id="dependencyTab" data-bs-toggle="tab" href="#dependencyViz">Dependency Graph</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" id="recoveryTab" data-bs-toggle="tab" href="#recoveryViz">Recovery Process</a>
              </li>
            </ul>
          </div>
          <div class="card-body">
            <div class="tab-content">
              <div class="tab-pane fade show active" id="dependencyViz">
                <div class="text-center mb-3">
                  <button class="btn btn-outline-primary" id="loadDependencyVizBtn">Load Dependency Visualization</button>
                </div>
                <div id="dependencyVizContainer" style="display: none;">
                  <iframe id="dependencyFrame" class="visualization-frame"></iframe>
                </div>
              </div>
              <div class="tab-pane fade" id="recoveryViz">
                <div class="text-center mb-3">
                  <button class="btn btn-outline-primary" id="loadRecoveryVizBtn">Load Latest Recovery Visualization</button>
                </div>
                <div id="recoveryVizContainer" style="display: none;">
                  <iframe id="recoveryFrame" class="visualization-frame"></iframe>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Start Recovery Modal -->
  <div class="modal fade" id="startRecoveryModal" tabindex="-1" aria-labelledby="startRecoveryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="startRecoveryModalLabel">Start Recovery Process</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="testModeCheck">
              <label class="form-check-label" for="testModeCheck">
                Test Mode (No actual recovery)
              </label>
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label">Select Components to Recover</label>
            <div id="componentCheckboxes">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="allComponentsCheck" checked>
                <label class="form-check-label" for="allComponentsCheck">
                  All Components
                </label>
              </div>
              <hr>
              <div id="individualComponentChecks">
                <!-- Component checkboxes will be added here -->
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="confirmStartRecoveryBtn">Start Recovery</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Dashboard functionality will be implemented here
    document.addEventListener('DOMContentLoaded', function() {
      // Load dashboard data
      loadDashboardData();
      
      // Set up event listeners
      document.getElementById('startRecoveryBtn').addEventListener('click', showStartRecoveryModal);
      document.getElementById('confirmStartRecoveryBtn').addEventListener('click', startRecovery);
      document.getElementById('loadDependencyVizBtn').addEventListener('click', loadDependencyVisualization);
      document.getElementById('loadRecoveryVizBtn').addEventListener('click', loadRecoveryVisualization);
      document.getElementById('allComponentsCheck').addEventListener('change', toggleAllComponents);
      
      // Set up polling for status updates
      setInterval(updateRecoveryStatus, 10000); // Update every 10 seconds
    });
    
    // Load dashboard data
    async function loadDashboardData() {
      try {
        const response = await fetch('/api/recovery-dashboard');
        const data = await response.json();
        
        // Update dashboard with data
        updateDashboard(data);
        
        // Hide loading overlay
        document.getElementById('loadingOverlay').style.display = 'none';
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        alert('Error loading dashboard data. Please try refreshing the page.');
      }
    }
    
    // Update dashboard with data
    function updateDashboard(data) {
      // Update statistics
      document.getElementById('totalRecoveries').textContent = data.statistics.totalRecoveries;
      document.getElementById('successfulRecoveries').textContent = data.statistics.successfulRecoveries;
      document.getElementById('failedRecoveries').textContent = data.statistics.failedRecoveries;
      document.getElementById('successRate').textContent = `${data.statistics.successRate.toFixed(1)}%`;
      
      // Update last recovery info
      if (data.lastRun) {
        const lastRunDate = new Date(data.lastRun);
        document.getElementById('lastRecovery').textContent = `${lastRunDate.toLocaleString()}`;
      } else {
        document.getElementById('lastRecovery').textContent = 'No recoveries recorded';
      }
      
      // Update component stats
      const componentStatsTable = document.getElementById('componentStats');
      componentStatsTable.innerHTML = '';
      
      if (data.componentStats.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="3" class="text-center">No component statistics available</td>';
        componentStatsTable.appendChild(row);
      } else {
        data.componentStats.forEach(component => {
          const row = document.createElement('tr');
          const successRate = component.totalRecoveries > 0 
            ? (component.successfulRecoveries / component.totalRecoveries) * 100 
            : 0;
          
          row.innerHTML = `
            <td>${component.name}</td>
            <td>
              <div class="progress">
                <div class="progress-bar bg-success" role="progressbar" style="width: ${successRate}%" aria-valuenow="${successRate}" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <small>${successRate.toFixed(1)}% (${component.successfulRecoveries}/${component.totalRecoveries})</small>
            </td>
            <td>${component.averageDuration.toFixed(2)}s</td>
          `;
          
          componentStatsTable.appendChild(row);
        });
      }
      
      // Update recent recoveries
      const recentRecoveriesContainer = document.getElementById('recentRecoveries');
      recentRecoveriesContainer.innerHTML = '';
      
      if (data.recentRecoveries.length === 0) {
        recentRecoveriesContainer.innerHTML = '<p class="text-center">No recent recoveries</p>';
      } else {
        data.recentRecoveries.forEach(recovery => {
          const div = document.createElement('div');
          div.className = `recovery-item ${recovery.success ? 'recovery-success' : 'recovery-failure'}`;
          
          const startDate = new Date(recovery.startTime);
          const endDate = new Date(recovery.endTime);
          
          div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
              <div class="recovery-time">${startDate.toLocaleString()}</div>
              <span class="badge ${recovery.success ? 'bg-success' : 'bg-danger'} status-badge">
                ${recovery.success ? 'SUCCESS' : 'FAILURE'}
              </span>
            </div>
            <div class="recovery-details">
              <div>Duration: ${recovery.duration.toFixed(2)}s</div>
              <div>Components: ${recovery.successfulComponents}/${recovery.componentCount} successful</div>
            </div>
          `;
          
          recentRecoveriesContainer.appendChild(div);
        });
      }
      
      // Update recovery status
      updateRecoveryStatusDisplay(data.latestRecovery);
      
      // Populate component checkboxes
      const componentChecks = document.getElementById('individualComponentChecks');
      componentChecks.innerHTML = '';
      
      data.componentStats.forEach(component => {
        const div = document.createElement('div');
        div.className = 'form-check';
        div.innerHTML = `
          <input class="form-check-input component-check" type="checkbox" id="component-${component.id}" value="${component.id}">
          <label class="form-check-label" for="component-${component.id}">
            ${component.name}
          </label>
        `;
        componentChecks.appendChild(div);
      });
    }
    
    // Update recovery status display
    function updateRecoveryStatusDisplay(latestRecovery) {
      const statusElement = document.getElementById('currentStatus');
      
      if (!latestRecovery) {
        statusElement.innerHTML = '<span class="badge bg-secondary">No recovery data</span>';
        return;
      }
      
      const inProgress = !latestRecovery.endTime;
      
      if (inProgress) {
        statusElement.innerHTML = '<span class="badge bg-warning">Recovery in progress</span>';
      } else if (latestRecovery.success) {
        statusElement.innerHTML = '<span class="badge bg-success">Last recovery successful</span>';
      } else {
        statusElement.innerHTML = '<span class="badge bg-danger">Last recovery failed</span>';
      }
    }
    
    // Update recovery status
    async function updateRecoveryStatus() {
      try {
        const response = await fetch('/api/recovery-dashboard/status');
        const data = await response.json();
        
        // Update status display
        updateRecoveryStatusDisplay(data.latestRecovery);
        
        // If recovery was in progress and is now complete, reload dashboard data
        if (data.latestRecovery && data.latestRecovery.endTime) {
          loadDashboardData();
        }
      } catch (error) {
        console.error('Error updating recovery status:', error);
      }
    }
    
    // Show start recovery modal
    function showStartRecoveryModal() {
      const modal = new bootstrap.Modal(document.getElementById('startRecoveryModal'));
      modal.show();
    }
    
    // Toggle all components
    function toggleAllComponents() {
      const allCheck = document.getElementById('allComponentsCheck');
      const componentChecks = document.querySelectorAll('.component-check');
      
      componentChecks.forEach(check => {
        check.disabled = allCheck.checked;
        if (allCheck.checked) {
          check.checked = false;
        }
      });
    }
    
    // Start recovery
    async function startRecovery() {
      const testMode = document.getElementById('testModeCheck').checked;
      const allComponents = document.getElementById('allComponentsCheck').checked;
      
      let components = null;
      
      if (!allComponents) {
        components = Array.from(document.querySelectorAll('.component-check:checked'))
          .map(check => check.value);
        
        if (components.length === 0) {
          alert('Please select at least one component to recover');
          return;
        }
      }
      
      try {
        const response = await fetch('/api/recovery-dashboard/start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            components,
            test: testMode
          })
        });
        
        const data = await response.json();
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('startRecoveryModal')).hide();
        
        // Show success message
        alert(`Recovery process started ${testMode ? 'in test mode' : ''}`);
        
        // Update status
        updateRecoveryStatus();
      } catch (error) {
        console.error('Error starting recovery:', error);
        alert('Error starting recovery process');
      }
    }
    
    // Load dependency visualization
    async function loadDependencyVisualization() {
      try {
        const response = await fetch('/api/recovery-dashboard/visualization/dependency');
        const data = await response.json();
        
        // Show visualization
        document.getElementById('dependencyVizContainer').style.display = 'block';
        document.getElementById('dependencyFrame').src = data.visualizationUrl;
        document.getElementById('loadDependencyVizBtn').style.display = 'none';
      } catch (error) {
        console.error('Error loading dependency visualization:', error);
        alert('Error loading dependency visualization');
      }
    }
    
    // Load recovery visualization
    async function loadRecoveryVisualization() {
      try {
        const response = await fetch('/api/recovery-dashboard/visualization/recovery');
        const data = await response.json();
        
        // Show visualization
        document.getElementById('recoveryVizContainer').style.display = 'block';
        document.getElementById('recoveryFrame').src = data.visualizationUrl;
        document.getElementById('loadRecoveryVizBtn').style.display = 'none';
      } catch (error) {
        console.error('Error loading recovery visualization:', error);
        alert('Error loading recovery visualization');
      }
    }
  </script>
</body>
</html>
