/**
 * Automated Test Creator
 * 
 * This service analyzes user behavior patterns and suggests A/B tests
 * that could improve key metrics.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Test opportunity types
const TEST_OPPORTUNITY_TYPE = {
  CONVERSION: 'conversion',
  ENGAGEMENT: 'engagement',
  RETENTION: 'retention',
  PERFORMANCE: 'performance'
};

// Test element types
const TEST_ELEMENT_TYPE = {
  UI: 'ui',
  CONTENT: 'content',
  FEATURE: 'feature',
  FLOW: 'flow',
  PRICING: 'pricing'
};

/**
 * Get user activity data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User activity data
 */
async function getUserActivityData(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Get business metrics data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Business metrics data
 */
async function getBusinessMetricsData(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('business_metrics')
      .select('*')
      .gte('date', startDate.toISOString())
      .order('date', { ascending: true });
      
    if (error) {
      logger.error('Error fetching business metrics data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getBusinessMetricsData', { error: error.message });
    return [];
  }
}

/**
 * Get existing A/B tests
 * 
 * @returns {Array} - Existing A/B tests
 */
async function getExistingTests() {
  try {
    const { data, error } = await supabase
      .from('ab_tests')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) {
      logger.error('Error fetching existing A/B tests', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getExistingTests', { error: error.message });
    return [];
  }
}

/**
 * Identify test opportunities
 * 
 * @returns {Array} - Test opportunities
 */
async function identifyTestOpportunities() {
  try {
    // Get data
    const activityData = await getUserActivityData(60);
    const businessData = await getBusinessMetricsData(60);
    const existingTests = await getExistingTests();
    
    if (activityData.length === 0 || businessData.length === 0) {
      return [];
    }
    
    // Group activity data by action type
    const actionCounts = {};
    
    activityData.forEach(activity => {
      if (!actionCounts[activity.action_type]) {
        actionCounts[activity.action_type] = 0;
      }
      
      actionCounts[activity.action_type]++;
    });
    
    // Sort actions by frequency
    const sortedActions = Object.entries(actionCounts)
      .sort((a, b) => b[1] - a[1])
      .map(([action, count]) => ({
        action,
        count,
        frequency: count / activityData.length
      }));
    
    // Identify drop-off points in user flows
    const userFlows = {};
    
    // Group activities by user and session
    const userSessions = {};
    
    activityData.forEach(activity => {
      if (!userSessions[activity.user_id]) {
        userSessions[activity.user_id] = [];
      }
      
      // Check if this is a new session (more than 30 minutes since last activity)
      const lastActivity = userSessions[activity.user_id][userSessions[activity.user_id].length - 1];
      
      if (!lastActivity || new Date(activity.created_at) - new Date(lastActivity.created_at) > 30 * 60 * 1000) {
        userSessions[activity.user_id].push([activity]);
      } else {
        userSessions[activity.user_id][userSessions[activity.user_id].length - 1].push(activity);
      }
    });
    
    // Analyze user flows
    Object.values(userSessions).forEach(sessions => {
      sessions.forEach(session => {
        // Create flow from session
        const flow = session.map(activity => activity.action_type).join(' > ');
        
        if (!userFlows[flow]) {
          userFlows[flow] = 0;
        }
        
        userFlows[flow]++;
      });
    });
    
    // Find common flows
    const commonFlows = Object.entries(userFlows)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([flow, count]) => ({
        flow,
        count,
        steps: flow.split(' > ')
      }));
    
    // Identify drop-off points
    const dropOffPoints = [];
    
    commonFlows.forEach(flowData => {
      const { flow, steps } = flowData;
      
      // Check for each step how many users continue to the next step
      for (let i = 0; i < steps.length - 1; i++) {
        const currentStep = steps[i];
        const nextStep = steps[i + 1];
        
        // Count sessions with current step
        const sessionsWithCurrentStep = Object.values(userSessions).flat().filter(session => 
          session.some(activity => activity.action_type === currentStep)
        ).length;
        
        // Count sessions with next step after current step
        const sessionsWithNextStep = Object.values(userSessions).flat().filter(session => {
          const currentStepIndex = session.findIndex(activity => activity.action_type === currentStep);
          
          if (currentStepIndex === -1) {
            return false;
          }
          
          return session.slice(currentStepIndex + 1).some(activity => activity.action_type === nextStep);
        }).length;
        
        // Calculate drop-off rate
        const dropOffRate = sessionsWithCurrentStep > 0 
          ? 1 - (sessionsWithNextStep / sessionsWithCurrentStep) 
          : 0;
        
        if (dropOffRate > 0.3) { // More than 30% drop-off
          dropOffPoints.push({
            flow,
            currentStep,
            nextStep,
            dropOffRate,
            sessionsWithCurrentStep,
            sessionsWithNextStep
          });
        }
      }
    });
    
    // Sort drop-off points by drop-off rate
    dropOffPoints.sort((a, b) => b.dropOffRate - a.dropOffRate);
    
    // Analyze business metrics trends
    const metricTrends = {};
    
    if (businessData.length >= 2) {
      const firstHalf = businessData.slice(0, Math.floor(businessData.length / 2));
      const secondHalf = businessData.slice(Math.floor(businessData.length / 2));
      
      const calculateAvg = (data, metric) => data.reduce((sum, item) => sum + item[metric], 0) / data.length;
      
      const metrics = ['conversion_rate', 'revenue', 'retention_rate', 'satisfaction_score'];
      
      metrics.forEach(metric => {
        const firstHalfAvg = calculateAvg(firstHalf, metric);
        const secondHalfAvg = calculateAvg(secondHalf, metric);
        
        const trend = firstHalfAvg > 0 
          ? (secondHalfAvg - firstHalfAvg) / firstHalfAvg 
          : 0;
        
        metricTrends[metric] = {
          firstHalfAvg,
          secondHalfAvg,
          trend
        };
      });
    }
    
    // Generate test opportunities
    const opportunities = [];
    
    // 1. Conversion opportunities based on drop-off points
    dropOffPoints.slice(0, 5).forEach(dropOff => {
      // Check if there's already a test for this drop-off point
      const existingTest = existingTests.find(test => 
        test.element_path === dropOff.currentStep && 
        test.goal === 'increase_conversion_to_' + dropOff.nextStep
      );
      
      if (!existingTest) {
        opportunities.push({
          type: TEST_OPPORTUNITY_TYPE.CONVERSION,
          element_type: TEST_ELEMENT_TYPE.FLOW,
          element_path: dropOff.currentStep,
          current_value: `${(1 - dropOff.dropOffRate) * 100}%`,
          goal: 'increase_conversion_to_' + dropOff.nextStep,
          potential_impact: 'high',
          description: `High drop-off rate (${(dropOff.dropOffRate * 100).toFixed(1)}%) between "${dropOff.currentStep}" and "${dropOff.nextStep}" steps`,
          suggested_variants: [
            {
              name: 'simplified_flow',
              description: 'Simplify the flow by reducing required steps or fields'
            },
            {
              name: 'improved_guidance',
              description: 'Add more guidance or help text to assist users'
            },
            {
              name: 'visual_emphasis',
              description: 'Increase visual emphasis on the next step or call to action'
            }
          ]
        });
      }
    });
    
    // 2. Engagement opportunities based on low-frequency actions
    const lowFrequencyActions = sortedActions
      .filter(action => action.frequency < 0.1) // Less than 10% of users
      .slice(0, 5);
      
    lowFrequencyActions.forEach(action => {
      // Check if there's already a test for this action
      const existingTest = existingTests.find(test => 
        test.element_path === action.action && 
        test.goal === 'increase_engagement'
      );
      
      if (!existingTest) {
        opportunities.push({
          type: TEST_OPPORTUNITY_TYPE.ENGAGEMENT,
          element_type: TEST_ELEMENT_TYPE.FEATURE,
          element_path: action.action,
          current_value: `${(action.frequency * 100).toFixed(1)}%`,
          goal: 'increase_engagement',
          potential_impact: 'medium',
          description: `Low engagement (${(action.frequency * 100).toFixed(1)}%) with "${action.action}" feature`,
          suggested_variants: [
            {
              name: 'improved_discoverability',
              description: 'Make the feature more discoverable in the UI'
            },
            {
              name: 'onboarding_highlight',
              description: 'Highlight the feature during onboarding'
            },
            {
              name: 'contextual_prompts',
              description: 'Add contextual prompts to encourage feature usage'
            }
          ]
        });
      }
    });
    
    // 3. Retention opportunities based on business metrics
    if (metricTrends.retention_rate && metricTrends.retention_rate.trend < 0) {
      // Check if there's already a test for retention
      const existingTest = existingTests.find(test => 
        test.goal === 'increase_retention'
      );
      
      if (!existingTest) {
        opportunities.push({
          type: TEST_OPPORTUNITY_TYPE.RETENTION,
          element_type: TEST_ELEMENT_TYPE.CONTENT,
          element_path: 'user_dashboard',
          current_value: `${(metricTrends.retention_rate.secondHalfAvg * 100).toFixed(1)}%`,
          goal: 'increase_retention',
          potential_impact: 'high',
          description: `Declining retention rate (${(metricTrends.retention_rate.trend * 100).toFixed(1)}% change)`,
          suggested_variants: [
            {
              name: 'personalized_content',
              description: 'Show more personalized content on the dashboard'
            },
            {
              name: 'progress_tracking',
              description: 'Add progress tracking and achievement features'
            },
            {
              name: 'engagement_emails',
              description: 'Test different re-engagement email strategies'
            }
          ]
        });
      }
    }
    
    // 4. Performance opportunities
    opportunities.push({
      type: TEST_OPPORTUNITY_TYPE.PERFORMANCE,
      element_type: TEST_ELEMENT_TYPE.UI,
      element_path: 'product_listing',
      current_value: 'current',
      goal: 'improve_load_time',
      potential_impact: 'medium',
      description: 'Test different loading strategies for product listings',
      suggested_variants: [
        {
          name: 'lazy_loading',
          description: 'Implement lazy loading for images'
        },
        {
          name: 'pagination',
          description: 'Use pagination instead of infinite scroll'
        },
        {
          name: 'reduced_initial_load',
          description: 'Reduce initial load size and progressively enhance'
        }
      ]
    });
    
    return opportunities;
  } catch (error) {
    logger.error('Error in identifyTestOpportunities', { error: error.message });
    return [];
  }
}

/**
 * Create A/B test
 * 
 * @param {Object} testData - Test data
 * @returns {Object} - Created test
 */
async function createTest(testData) {
  try {
    // Validate test data
    if (!testData.name || !testData.element_path || !testData.goal || !testData.variants || testData.variants.length < 2) {
      throw new Error('Invalid test data');
    }
    
    // Create test
    const { data, error } = await supabase
      .from('ab_tests')
      .insert({
        name: testData.name,
        description: testData.description,
        element_type: testData.element_type,
        element_path: testData.element_path,
        goal: testData.goal,
        metrics: testData.metrics,
        variants: testData.variants,
        traffic_allocation: testData.traffic_allocation || 1.0,
        status: 'draft',
        created_at: new Date().toISOString()
      })
      .select();
      
    if (error) {
      logger.error('Error creating A/B test', { error: error.message });
      throw error;
    }
    
    return data[0];
  } catch (error) {
    logger.error('Error in createTest', { error: error.message });
    throw error;
  }
}

// API endpoints
app.get('/api/test-opportunities', async (req, res) => {
  try {
    const opportunities = await identifyTestOpportunities();
    res.json({ opportunities });
  } catch (error) {
    logger.error('Error in GET /api/test-opportunities', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/tests', async (req, res) => {
  try {
    const testData = req.body;
    
    // Create test
    const test = await createTest(testData);
    
    res.status(201).json({ test });
  } catch (error) {
    logger.error('Error in POST /api/tests', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.AUTOMATED_TEST_CREATOR_PORT || 9106;
app.listen(PORT, () => {
  logger.info(`Automated Test Creator listening on port ${PORT}`);
});

module.exports = {
  identifyTestOpportunities,
  createTest
};
