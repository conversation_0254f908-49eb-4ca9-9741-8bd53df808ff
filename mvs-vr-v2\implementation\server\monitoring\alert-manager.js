/**
 * Alert Manager
 *
 * This service manages alerts for the monitoring system, including:
 * - Alert configuration
 * - Alert triggering
 * - Alert notification
 * - Alert history
 */

const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const { EventEmitter } = require('events');

// ML Alert Analyzer URL
const ML_ALERT_ANALYZER_URL = `http://ml-alert-analyzer:${process.env.ML_ALERT_ANALYZER_PORT || 9102}`;

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Create Express app
const app = express();
app.use(express.json());

// Create event emitter for alert events
const alertEmitter = new EventEmitter();

// Alert severity levels
const SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  CRITICAL: 'critical',
};

// Alert categories
const CATEGORY = {
  PERFORMANCE: 'performance',
  SECURITY: 'security',
  BUSINESS: 'business',
  SYSTEM: 'system',
};

// Alert status
const STATUS = {
  ACTIVE: 'active',
  ACKNOWLEDGED: 'acknowledged',
  RESOLVED: 'resolved',
};

// Default alert configuration
const defaultAlertConfig = {
  // Performance alerts
  highApiLatency: {
    name: 'High API Latency',
    description: 'API latency is above threshold',
    category: CATEGORY.PERFORMANCE,
    severity: SEVERITY.WARNING,
    threshold: 500, // ms
    duration: 300, // 5 minutes
    enabled: true,
    channels: ['slack', 'dashboard'],
  },
  criticalApiLatency: {
    name: 'Critical API Latency',
    description: 'API latency is critically high',
    category: CATEGORY.PERFORMANCE,
    severity: SEVERITY.CRITICAL,
    threshold: 1000, // ms
    duration: 60, // 1 minute
    enabled: true,
    channels: ['slack', 'email', 'dashboard'],
  },
  highErrorRate: {
    name: 'High Error Rate',
    description: 'Error rate is above threshold',
    category: CATEGORY.PERFORMANCE,
    severity: SEVERITY.WARNING,
    threshold: 5, // percent
    duration: 300, // 5 minutes
    enabled: true,
    channels: ['slack', 'dashboard'],
  },
  criticalErrorRate: {
    name: 'Critical Error Rate',
    description: 'Error rate is critically high',
    category: CATEGORY.PERFORMANCE,
    severity: SEVERITY.CRITICAL,
    threshold: 10, // percent
    duration: 120, // 2 minutes
    enabled: true,
    channels: ['slack', 'email', 'dashboard'],
  },
  highMemoryUsage: {
    name: 'High Memory Usage',
    description: 'Memory usage is above threshold',
    category: CATEGORY.SYSTEM,
    severity: SEVERITY.WARNING,
    threshold: 85, // percent
    duration: 600, // 10 minutes
    enabled: true,
    channels: ['slack', 'dashboard'],
  },

  // Business alerts
  lowActiveUsers: {
    name: 'Low Active Users',
    description: 'Number of active users is below threshold',
    category: CATEGORY.BUSINESS,
    severity: SEVERITY.WARNING,
    threshold: 10, // users
    duration: 1800, // 30 minutes
    enabled: true,
    channels: ['slack', 'dashboard'],
  },
  noShowroomVisits: {
    name: 'No Showroom Visits',
    description: 'No showroom visits recorded in the last hour',
    category: CATEGORY.BUSINESS,
    severity: SEVERITY.WARNING,
    threshold: 0, // visits
    duration: 3600, // 1 hour
    enabled: true,
    channels: ['slack', 'dashboard'],
  },
};

// Alert notification channels
const notificationChannels = {
  slack: {
    enabled: true,
    webhook: config.notifications?.slack?.webhook || '',
    channel: config.notifications?.slack?.channel || '#alerts',
    username: config.notifications?.slack?.username || 'Alert Manager',
    cooldown: 300, // 5 minutes
  },
  email: {
    enabled: true,
    recipients: config.notifications?.email?.recipients || [],
    cooldown: 1800, // 30 minutes
  },
  dashboard: {
    enabled: true,
    cooldown: 0, // No cooldown for dashboard
  },
};

// In-memory store for active alerts
const activeAlerts = new Map();

// In-memory store for alert state
const alertState = new Map();

/**
 * Initialize alert configuration
 */
async function initializeAlertConfig() {
  try {
    // Check if alert configuration exists in database
    const { data: existingConfig, error } = await supabase.from('alert_config').select('*');

    if (error) {
      logger.error('Error fetching alert configuration', { error: error.message });
      return defaultAlertConfig;
    }

    // If no configuration exists, create default configuration
    if (!existingConfig || existingConfig.length === 0) {
      const configEntries = Object.entries(defaultAlertConfig).map(([id, config]) => ({
        id,
        ...config,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: insertError } = await supabase.from('alert_config').insert(configEntries);

      if (insertError) {
        logger.error('Error creating default alert configuration', { error: insertError.message });
        return defaultAlertConfig;
      }

      logger.info('Created default alert configuration');
      return defaultAlertConfig;
    }

    // Convert existing configuration to map
    const config = existingConfig.reduce((acc, alert) => {
      const { id, ...alertConfig } = alert;
      acc[id] = alertConfig;
      return acc;
    }, {});

    logger.info('Loaded alert configuration from database');
    return config;
  } catch (error) {
    logger.error('Error initializing alert configuration', { error: error.message });
    return defaultAlertConfig;
  }
}

/**
 * Trigger an alert
 *
 * @param {string} alertId - Alert ID
 * @param {Object} data - Alert data
 * @returns {Object} - Alert object
 */
async function triggerAlert(alertId, data) {
  try {
    const alertConfig = await getAlertConfig();
    const config = alertConfig[alertId];

    if (!config || !config.enabled) {
      logger.debug(`Alert ${alertId} is disabled or not found`);
      return null;
    }

    // Check if alert is already active
    if (activeAlerts.has(alertId)) {
      logger.debug(`Alert ${alertId} is already active`);
      return activeAlerts.get(alertId);
    }

    // Create alert object
    const alert = {
      id: `${alertId}-${Date.now()}`,
      alertId,
      name: config.name,
      description: config.description,
      category: config.category,
      severity: config.severity,
      status: STATUS.ACTIVE,
      data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Store alert in database
    const { error } = await supabase.from('alerts').insert([alert]);

    if (error) {
      logger.error('Error storing alert', { error: error.message });
    }

    // Add to active alerts
    activeAlerts.set(alertId, alert);

    // Emit alert event
    alertEmitter.emit('alert', alert);

    // Send notifications
    await sendAlertNotifications(alert);

    // Send to ML Alert Analyzer for processing
    try {
      await axios.post(`${ML_ALERT_ANALYZER_URL}/api/process-alerts`);
      logger.info(`Sent alert to ML Alert Analyzer: ${alertId}`);
    } catch (error) {
      logger.error('Error sending alert to ML Alert Analyzer', { error: error.message });
    }

    logger.info(`Triggered alert: ${alertId}`, { alert });
    return alert;
  } catch (error) {
    logger.error('Error triggering alert', { error: error.message });
    return null;
  }
}

/**
 * Resolve an alert
 *
 * @param {string} alertId - Alert ID
 * @returns {boolean} - Success
 */
async function resolveAlert(alertId) {
  try {
    if (!activeAlerts.has(alertId)) {
      logger.debug(`Alert ${alertId} is not active`);
      return false;
    }

    const alert = activeAlerts.get(alertId);

    // Update alert status
    alert.status = STATUS.RESOLVED;
    alert.updated_at = new Date().toISOString();
    alert.resolved_at = new Date().toISOString();

    // Update alert in database
    const { error } = await supabase
      .from('alerts')
      .update({
        status: STATUS.RESOLVED,
        updated_at: alert.updated_at,
        resolved_at: alert.resolved_at,
      })
      .eq('id', alert.id);

    if (error) {
      logger.error('Error updating alert', { error: error.message });
    }

    // Remove from active alerts
    activeAlerts.delete(alertId);

    // Emit alert resolved event
    alertEmitter.emit('alert-resolved', alert);

    logger.info(`Resolved alert: ${alertId}`);
    return true;
  } catch (error) {
    logger.error('Error resolving alert', { error: error.message });
    return false;
  }
}

/**
 * Send alert notifications
 *
 * @param {Object} alert - Alert object
 */
async function sendAlertNotifications(alert) {
  try {
    const alertConfig = await getAlertConfig();
    const config = alertConfig[alert.alertId];

    if (!config || !config.channels || config.channels.length === 0) {
      logger.debug(`No notification channels configured for alert ${alert.alertId}`);
      return;
    }

    // Send notifications to each channel
    for (const channelId of config.channels) {
      const channel = notificationChannels[channelId];

      if (!channel || !channel.enabled) {
        continue;
      }

      // Check cooldown
      const lastNotification = alertState.get(`${alert.alertId}-${channelId}`);
      const now = Date.now();

      if (lastNotification && now - lastNotification < channel.cooldown * 1000) {
        logger.debug(`Notification cooldown for ${alert.alertId} on ${channelId}`);
        continue;
      }

      // Send notification
      switch (channelId) {
        case 'slack':
          await sendSlackNotification(alert);
          break;
        case 'email':
          await sendEmailNotification(alert);
          break;
        case 'dashboard':
          // Dashboard notifications are handled by the frontend
          break;
      }

      // Update last notification time
      alertState.set(`${alert.alertId}-${channelId}`, now);
    }
  } catch (error) {
    logger.error('Error sending alert notifications', { error: error.message });
  }
}

/**
 * Send Slack notification
 *
 * @param {Object} alert - Alert object
 */
async function sendSlackNotification(alert) {
  try {
    const channel = notificationChannels.slack;

    if (!channel.webhook) {
      logger.warn('Slack webhook not configured');
      return;
    }

    // Create Slack message
    const message = {
      channel: channel.channel,
      username: channel.username,
      icon_emoji: getAlertEmoji(alert.severity),
      attachments: [
        {
          color: getAlertColor(alert.severity),
          title: alert.name,
          text: alert.description,
          fields: [
            {
              title: 'Category',
              value: alert.category,
              short: true,
            },
            {
              title: 'Severity',
              value: alert.severity,
              short: true,
            },
            {
              title: 'Time',
              value: new Date(alert.created_at).toLocaleString(),
              short: true,
            },
          ],
          footer: 'MVS-VR Alert Manager',
        },
      ],
    };

    // Add data fields
    if (alert.data) {
      Object.entries(alert.data).forEach(([key, value]) => {
        message.attachments[0].fields.push({
          title: key,
          value: value.toString(),
          short: true,
        });
      });
    }

    // Send message
    await axios.post(channel.webhook, message);
    logger.info(`Sent Slack notification for alert ${alert.alertId}`);
  } catch (error) {
    logger.error('Error sending Slack notification', { error: error.message });
  }
}

/**
 * Send email notification
 *
 * @param {Object} alert - Alert object
 */
async function sendEmailNotification(alert) {
  try {
    const channel = notificationChannels.email;

    if (!channel.recipients || channel.recipients.length === 0) {
      logger.warn('Email recipients not configured');
      return;
    }

    // In a real implementation, this would send an email
    // For now, just log it
    logger.info(
      `Would send email notification for alert ${alert.alertId} to ${channel.recipients.join(', ')}`,
    );
  } catch (error) {
    logger.error('Error sending email notification', { error: error.message });
  }
}

/**
 * Get alert emoji based on severity
 *
 * @param {string} severity - Alert severity
 * @returns {string} - Emoji
 */
function getAlertEmoji(severity) {
  switch (severity) {
    case SEVERITY.CRITICAL:
      return ':rotating_light:';
    case SEVERITY.WARNING:
      return ':warning:';
    case SEVERITY.INFO:
      return ':information_source:';
    default:
      return ':bell:';
  }
}

/**
 * Get alert color based on severity
 *
 * @param {string} severity - Alert severity
 * @returns {string} - Color
 */
function getAlertColor(severity) {
  switch (severity) {
    case SEVERITY.CRITICAL:
      return '#FF0000';
    case SEVERITY.WARNING:
      return '#FFA500';
    case SEVERITY.INFO:
      return '#0000FF';
    default:
      return '#808080';
  }
}

// Cache for alert configuration
let alertConfigCache = null;
let alertConfigLastUpdated = 0;

/**
 * Get alert configuration
 *
 * @returns {Object} - Alert configuration
 */
async function getAlertConfig() {
  // Check cache
  const now = Date.now();
  if (alertConfigCache && now - alertConfigLastUpdated < 60000) {
    // 1 minute cache
    return alertConfigCache;
  }

  // Initialize configuration
  alertConfigCache = await initializeAlertConfig();
  alertConfigLastUpdated = now;

  return alertConfigCache;
}

// API endpoints
app.get('/api/alerts', async (req, res) => {
  try {
    const {
      status = 'active',
      severity,
      category,
      limit = 100,
      offset = 0,
      include_ml = 'false',
    } = req.query;

    // Build query
    let query = supabase.from('alerts').select('*');

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (severity && severity !== 'all') {
      query = query.eq('severity', severity);
    }

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    // Add pagination
    query = query
      .order('created_at', { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    // Execute query
    const { data, error, count } = await query;

    if (error) {
      logger.error('Error fetching alerts', { error: error.message });
      return res.status(500).json({ error: 'Error fetching alerts' });
    }

    // If ML context is requested, get it for each alert
    if (include_ml === 'true' && data && data.length > 0) {
      try {
        // Get ML context for each alert that has been processed
        const processedAlerts = data.filter(alert => alert.ml_processed);

        // For alerts that haven't been processed, try to process them now
        const unprocessedAlerts = data.filter(alert => !alert.ml_processed);

        if (unprocessedAlerts.length > 0) {
          try {
            await axios.post(`${ML_ALERT_ANALYZER_URL}/api/process-alerts`);
            logger.info(`Requested processing for ${unprocessedAlerts.length} unprocessed alerts`);
          } catch (mlError) {
            logger.error('Error requesting ML processing for unprocessed alerts', {
              error: mlError.message,
            });
          }
        }
      } catch (mlError) {
        logger.error('Error getting ML context for alerts', { error: mlError.message });
      }
    }

    res.json({
      alerts: data,
      count,
    });
  } catch (error) {
    logger.error('Error in GET /api/alerts', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/alert-config', async (req, res) => {
  try {
    const config = await getAlertConfig();
    res.json({ config });
  } catch (error) {
    logger.error('Error in GET /api/alert-config', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/api/alert-config/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const config = req.body;

    if (!config) {
      return res.status(400).json({ error: 'Invalid configuration' });
    }

    // Update configuration in database
    const { error } = await supabase
      .from('alert_config')
      .update({
        ...config,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) {
      logger.error('Error updating alert configuration', { error: error.message });
      return res.status(500).json({ error: 'Error updating configuration' });
    }

    // Invalidate cache
    alertConfigCache = null;

    res.json({ success: true });
  } catch (error) {
    logger.error('Error in PUT /api/alert-config/:id', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/alerts/:id/acknowledge', async (req, res) => {
  try {
    const { id } = req.params;
    const { user } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Alert ID is required' });
    }

    // Update alert in database
    const { error } = await supabase
      .from('alerts')
      .update({
        status: STATUS.ACKNOWLEDGED,
        acknowledged_by: user || 'system',
        acknowledged_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) {
      logger.error('Error acknowledging alert', { error: error.message });
      return res.status(500).json({ error: 'Error acknowledging alert' });
    }

    res.json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/alerts/:id/acknowledge', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/alerts/:id/resolve', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Alert ID is required' });
    }

    // Get alert
    const { data: alerts, error: fetchError } = await supabase
      .from('alerts')
      .select('*')
      .eq('id', id)
      .limit(1);

    if (fetchError || !alerts || alerts.length === 0) {
      logger.error('Error fetching alert', { error: fetchError?.message });
      return res.status(404).json({ error: 'Alert not found' });
    }

    const alert = alerts[0];

    // Resolve alert
    await resolveAlert(alert.alertId);

    res.json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/alerts/:id/resolve', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/alerts/:id/ml-context', async (req, res) => {
  try {
    const { id } = req.params;

    // Get alert
    const { data: alerts, error } = await supabase.from('alerts').select('*').eq('id', id).limit(1);

    if (error) {
      logger.error('Error fetching alert', { error: error.message });
      return res.status(500).json({ error: 'Error fetching alert' });
    }

    if (!alerts || alerts.length === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    const alert = alerts[0];

    // If alert has already been processed by ML, return the context
    if (alert.ml_processed) {
      return res.json({
        alert_id: alert.id,
        importance: alert.ml_importance,
        confidence: alert.ml_confidence,
        reasons: alert.ml_reasons,
        correlated_alerts: alert.ml_correlated_alerts,
        system_state: alert.ml_system_state,
      });
    }

    // If not processed, request processing from ML Alert Analyzer
    try {
      const mlResponse = await axios.get(`${ML_ALERT_ANALYZER_URL}/api/alert-context/${id}`);

      if (mlResponse.data && mlResponse.data.alert && mlResponse.data.alert.context) {
        const context = mlResponse.data.alert.context;

        return res.json({
          alert_id: alert.id,
          importance: context.importance,
          confidence: context.confidence,
          reasons: context.reasons,
          correlated_alerts: context.correlatedAlerts.map(ca => ({
            alert_id: ca.alert.id,
            correlation: ca.correlation,
            reason: ca.reason,
          })),
          system_state: context.systemState,
        });
      } else {
        return res.status(404).json({ error: 'ML context not available' });
      }
    } catch (mlError) {
      logger.error('Error getting ML context for alert', { error: mlError.message });
      return res.status(500).json({ error: 'Error getting ML context' });
    }
  } catch (error) {
    logger.error('Error in GET /api/alerts/:id/ml-context', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.ALERT_MANAGER_PORT || 9096;
app.listen(PORT, () => {
  logger.info(`Alert Manager listening on port ${PORT}`);
});

module.exports = {
  triggerAlert,
  resolveAlert,
  getAlertConfig,
  SEVERITY,
  CATEGORY,
  STATUS,
};
