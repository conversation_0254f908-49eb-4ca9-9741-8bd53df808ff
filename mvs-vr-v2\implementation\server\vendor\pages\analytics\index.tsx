import React, { useEffect, useState } from 'react';
import { Box, But<PERSON>, Card, CardContent, CardHeader, CircularProgress, FormControl, Grid, InputLabel, MenuItem, Paper, Select, SelectChangeEvent, Typography } from '@mui/material';
import { Download as DownloadIcon } from '@mui/icons-material';
import VendorLayout from '../../components/VendorLayout';
import { useSupabaseClient } from '@supabase/auth-helpers-react';

interface AnalyticsData {
  totalViews: number;
  uniqueVisitors: number;
  averageSessionDuration: number;
  topAssets: Array<{
    id: string;
    name: string;
    views: number;
    averageTimeSpent: number;
  }>;
  viewsByDay: Array<{
    date: string;
    views: number;
  }>;
  viewsByDevice: Array<{
    device: string;
    count: number;
    percentage: number;
  }>;
}

const Analytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const supabase = useSupabaseClient();

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Get vendor ID from session
      const { data: { session } } = await supabase.auth.getSession();
      const vendorId = session?.user?.id;

      if (!vendorId) {
        console.error('No vendor ID found in session');
        setLoading(false);
        return;
      }

      // Calculate date range based on selected time range
      const endDate = new Date();
      let startDate = new Date();
      
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Format dates for API
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Fetch analytics data from API
      const response = await fetch(`/api/analytics/vendor/${vendorId}?start=${startDateStr}&end=${endDateStr}`);
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const data = await response.json();
      setAnalyticsData(data);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      
      // Set mock data for development purposes
      setAnalyticsData({
        totalViews: 1250,
        uniqueVisitors: 450,
        averageSessionDuration: 325, // in seconds
        topAssets: [
          { id: '1', name: 'Living Room Set', views: 320, averageTimeSpent: 145 },
          { id: '2', name: 'Kitchen Cabinets', views: 280, averageTimeSpent: 120 },
          { id: '3', name: 'Bathroom Fixtures', views: 210, averageTimeSpent: 90 },
          { id: '4', name: 'Office Furniture', views: 180, averageTimeSpent: 75 },
          { id: '5', name: 'Outdoor Patio', views: 150, averageTimeSpent: 60 },
        ],
        viewsByDay: Array.from({ length: 30 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - (29 - i));
          return {
            date: date.toISOString().split('T')[0],
            views: Math.floor(Math.random() * 50) + 20,
          };
        }),
        viewsByDevice: [
          { device: 'Desktop', count: 650, percentage: 52 },
          { device: 'Mobile', count: 350, percentage: 28 },
          { device: 'VR Headset', count: 200, percentage: 16 },
          { device: 'Tablet', count: 50, percentage: 4 },
        ],
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, supabase]);

  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  const handleExportData = async (format: 'csv' | 'pdf' | 'excel') => {
    try {
      // Get vendor ID from session
      const { data: { session } } = await supabase.auth.getSession();
      const vendorId = session?.user?.id;

      if (!vendorId) {
        console.error('No vendor ID found in session');
        return;
      }

      // Calculate date range based on selected time range
      const endDate = new Date();
      let startDate = new Date();
      
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Format dates for API
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Request export from API
      const response = await fetch(`/api/analytics/export?vendor=${vendorId}&start=${startDateStr}&end=${endDateStr}&format=${format}`);
      
      if (!response.ok) {
        throw new Error(`Export request failed with status ${response.status}`);
      }
      
      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `analytics-export-${startDateStr}-to-${endDateStr}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      alert('Failed to export analytics data. Please try again later.');
    }
  };

  if (loading && !analyticsData) {
    return (
      <VendorLayout title="Analytics">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }

  return (
    <VendorLayout title="Analytics">
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1">
            Analytics Dashboard
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel id="time-range-label">Time Range</InputLabel>
              <Select
                labelId="time-range-label"
                id="time-range"
                value={timeRange}
                label="Time Range"
                onChange={handleTimeRangeChange}
              >
                <MenuItem value="7d">Last 7 days</MenuItem>
                <MenuItem value="30d">Last 30 days</MenuItem>
                <MenuItem value="90d">Last 90 days</MenuItem>
                <MenuItem value="1y">Last year</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => handleExportData('csv')}
            >
              Export CSV
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Summary Cards */}
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Total Views
              </Typography>
              <Typography component="p" variant="h3">
                {analyticsData?.totalViews.toLocaleString() || 0}
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Unique Visitors
              </Typography>
              <Typography component="p" variant="h3">
                {analyticsData?.uniqueVisitors.toLocaleString() || 0}
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Avg. Session Duration
              </Typography>
              <Typography component="p" variant="h3">
                {analyticsData ? `${Math.floor(analyticsData.averageSessionDuration / 60)}m ${analyticsData.averageSessionDuration % 60}s` : '0m 0s'}
              </Typography>
            </Paper>
          </Grid>

          {/* Top Assets */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Top Assets" />
              <CardContent>
                {analyticsData?.topAssets.map((asset, index) => (
                  <Box key={asset.id} sx={{ mb: 2, p: 1, borderBottom: index < analyticsData.topAssets.length - 1 ? '1px solid #eee' : 'none' }}>
                    <Typography variant="subtitle1">{asset.name}</Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">
                        {asset.views.toLocaleString()} views
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Avg. time: {Math.floor(asset.averageTimeSpent / 60)}m {asset.averageTimeSpent % 60}s
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Device Breakdown */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Device Breakdown" />
              <CardContent>
                {analyticsData?.viewsByDevice.map((device) => (
                  <Box key={device.device} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body1">{device.device}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {device.count.toLocaleString()} views ({device.percentage}%)
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        mt: 1,
                        height: 8,
                        width: '100%',
                        bgcolor: 'grey.300',
                        borderRadius: 1,
                        overflow: 'hidden',
                      }}
                    >
                      <Box
                        sx={{
                          height: '100%',
                          width: `${device.percentage}%`,
                          bgcolor: 'primary.main',
                        }}
                      />
                    </Box>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </VendorLayout>
  );
};

export default Analytics;
