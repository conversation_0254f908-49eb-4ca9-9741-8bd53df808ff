FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/resource-optimizer.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV RESOURCE_OPTIMIZER_PORT=9109

# Expose port
EXPOSE 9109

# Start the service
CMD ["node", "monitoring/resource-optimizer.js"]
