groups:
  - name: alerts
    rules:
      - alert: HighCpuUsage
        expr: avg(rate(node_cpu_seconds_total{mode="idle"}[5m])) by (instance) < 0.3
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is above 70% for 5 minutes"
          ml_context: "true"
          
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 80% for 5 minutes"
          ml_context: "true"
          
      - alert: HighDiskUsage
        expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} < 0.2
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High disk usage on {{ $labels.instance }}"
          description: "Disk usage is above 80% for 5 minutes"
          ml_context: "true"
          
      - alert: HighApiLatency
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, instance, path)) > 0.5
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High API latency on {{ $labels.instance }} for {{ $labels.path }}"
          description: "95th percentile API latency is above 500ms for 5 minutes"
          ml_context: "true"
          
      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (instance) / sum(rate(http_requests_total[5m])) by (instance) > 0.05
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is above 5% for 5 minutes"
          ml_context: "true"
          
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute"
          ml_context: "true"
          
      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, sum(rate(database_query_duration_seconds_bucket[5m])) by (le, instance, query_type)) > 1
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "Slow database queries on {{ $labels.instance }} for {{ $labels.query_type }}"
          description: "95th percentile database query duration is above 1s for 5 minutes"
          ml_context: "true"
          
      - alert: HighConnectionPoolUsage
        expr: sum(database_connections_used) by (instance) / sum(database_connections_max) by (instance) > 0.8
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High connection pool usage on {{ $labels.instance }}"
          description: "Connection pool usage is above 80% for 5 minutes"
          ml_context: "true"
          
      - alert: LowActiveUsers
        expr: sum(active_users) < 10
        for: 30m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "Low active users"
          description: "Less than 10 active users for 30 minutes"
          ml_context: "true"
          
      - alert: HighAssetUploadFailures
        expr: sum(rate(asset_upload_failures_total[1h])) / sum(rate(asset_uploads_total[1h])) > 0.1
        for: 1h
        labels:
          severity: warning
          category: business
        annotations:
          summary: "High asset upload failures"
          description: "Asset upload failure rate is above 10% for 1 hour"
          ml_context: "true"
          
      - alert: HighNetworkLatency
        expr: avg(network_latency_seconds) by (instance) > 0.2
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High network latency on {{ $labels.instance }}"
          description: "Network latency is above 200ms for 5 minutes"
          ml_context: "true"
          
      - alert: HighNetworkErrors
        expr: sum(rate(network_errors_total[5m])) by (instance) > 1
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "High network errors on {{ $labels.instance }}"
          description: "Network error rate is above 1 per second for 5 minutes"
          ml_context: "true"
          
      - alert: HighDatabaseLatency
        expr: avg(database_latency_seconds) by (instance) > 0.1
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High database latency on {{ $labels.instance }}"
          description: "Database latency is above 100ms for 5 minutes"
          ml_context: "true"
          
      - alert: HighDatabaseErrors
        expr: sum(rate(database_errors_total[5m])) by (instance) > 1
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "High database errors on {{ $labels.instance }}"
          description: "Database error rate is above 1 per second for 5 minutes"
          ml_context: "true"
