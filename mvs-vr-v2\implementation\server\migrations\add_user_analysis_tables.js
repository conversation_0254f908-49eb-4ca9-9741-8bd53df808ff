/**
 * Migration: Add User Analysis Tables
 * 
 * This migration adds tables for user behavior analysis, churn prediction,
 * and personalization impact measurement.
 */

const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

async function run() {
  try {
    logger.info('Starting migration: Add User Analysis Tables');
    
    // Create user_activities table if it doesn't exist
    const { error: tableExistsError } = await supabase.rpc('table_exists', {
      table_name: 'user_activities'
    });
    
    if (tableExistsError || tableExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'user_activities',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'user_id', type: 'uuid', nullable: false },
          { name: 'action_type', type: 'text', nullable: false },
          { name: 'action_data', type: 'jsonb', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating user_activities table', { error: createTableError.message });
      } else {
        logger.info('Created user_activities table');
        
        // Create index on user_id
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'user_activities',
          column_name: 'user_id'
        });
        
        if (indexError) {
          logger.error('Error creating index on user_id', { error: indexError.message });
        } else {
          logger.info('Created index on user_id');
        }
        
        // Create index on created_at
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'user_activities',
          column_name: 'created_at'
        });
        
        if (indexError2) {
          logger.error('Error creating index on created_at', { error: indexError2.message });
        } else {
          logger.info('Created index on created_at');
        }
      }
    }
    
    // Create user_sessions table if it doesn't exist
    const { error: sessionsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'user_sessions'
    });
    
    if (sessionsExistsError || sessionsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'user_sessions',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'user_id', type: 'uuid', nullable: false },
          { name: 'duration', type: 'integer', nullable: false },
          { name: 'page_views', type: 'integer', nullable: false },
          { name: 'interactions', type: 'integer', nullable: false },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating user_sessions table', { error: createTableError.message });
      } else {
        logger.info('Created user_sessions table');
        
        // Create index on user_id
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'user_sessions',
          column_name: 'user_id'
        });
        
        if (indexError) {
          logger.error('Error creating index on user_id', { error: indexError.message });
        } else {
          logger.info('Created index on user_id');
        }
        
        // Create index on created_at
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'user_sessions',
          column_name: 'created_at'
        });
        
        if (indexError2) {
          logger.error('Error creating index on created_at', { error: indexError2.message });
        } else {
          logger.info('Created index on created_at');
        }
      }
    }
    
    // Create personalization_events table if it doesn't exist
    const { error: personalizationExistsError } = await supabase.rpc('table_exists', {
      table_name: 'personalization_events'
    });
    
    if (personalizationExistsError || personalizationExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'personalization_events',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'user_id', type: 'uuid', nullable: false },
          { name: 'type', type: 'text', nullable: false },
          { name: 'content', type: 'jsonb', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating personalization_events table', { error: createTableError.message });
      } else {
        logger.info('Created personalization_events table');
        
        // Create index on user_id
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'personalization_events',
          column_name: 'user_id'
        });
        
        if (indexError) {
          logger.error('Error creating index on user_id', { error: indexError.message });
        } else {
          logger.info('Created index on user_id');
        }
        
        // Create index on created_at
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'personalization_events',
          column_name: 'created_at'
        });
        
        if (indexError2) {
          logger.error('Error creating index on created_at', { error: indexError2.message });
        } else {
          logger.info('Created index on created_at');
        }
      }
    }
    
    // Create business_metrics table if it doesn't exist
    const { error: businessExistsError } = await supabase.rpc('table_exists', {
      table_name: 'business_metrics'
    });
    
    if (businessExistsError || businessExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'business_metrics',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'date', type: 'date', nullable: false },
          { name: 'active_users', type: 'integer', nullable: false },
          { name: 'new_users', type: 'integer', nullable: false },
          { name: 'conversion_rate', type: 'float', nullable: false },
          { name: 'revenue', type: 'float', nullable: false },
          { name: 'retention_rate', type: 'float', nullable: false },
          { name: 'satisfaction_score', type: 'float', nullable: false },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating business_metrics table', { error: createTableError.message });
      } else {
        logger.info('Created business_metrics table');
        
        // Create index on date
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'business_metrics',
          column_name: 'date'
        });
        
        if (indexError) {
          logger.error('Error creating index on date', { error: indexError.message });
        } else {
          logger.info('Created index on date');
        }
      }
    }
    
    // Create user_predictions table if it doesn't exist
    const { error: predictionsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'user_predictions'
    });
    
    if (predictionsExistsError || predictionsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'user_predictions',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'user_id', type: 'uuid', nullable: false },
          { name: 'prediction_type', type: 'text', nullable: false },
          { name: 'prediction_data', type: 'jsonb', nullable: false },
          { name: 'confidence', type: 'float', nullable: false },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating user_predictions table', { error: createTableError.message });
      } else {
        logger.info('Created user_predictions table');
        
        // Create index on user_id
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'user_predictions',
          column_name: 'user_id'
        });
        
        if (indexError) {
          logger.error('Error creating index on user_id', { error: indexError.message });
        } else {
          logger.info('Created index on user_id');
        }
        
        // Create index on prediction_type
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'user_predictions',
          column_name: 'prediction_type'
        });
        
        if (indexError2) {
          logger.error('Error creating index on prediction_type', { error: indexError2.message });
        } else {
          logger.info('Created index on prediction_type');
        }
        
        // Create index on created_at
        const { error: indexError3 } = await supabase.rpc('create_index', {
          table_name: 'user_predictions',
          column_name: 'created_at'
        });
        
        if (indexError3) {
          logger.error('Error creating index on created_at', { error: indexError3.message });
        } else {
          logger.info('Created index on created_at');
        }
      }
    }
    
    logger.info('Migration completed: Add User Analysis Tables');
  } catch (error) {
    logger.error('Error in migration', { error: error.message });
  }
}

// Run migration if called directly
if (require.main === module) {
  run().then(() => {
    process.exit(0);
  }).catch(error => {
    logger.error('Migration failed', { error: error.message });
    process.exit(1);
  });
}

module.exports = { run };
