/**
 * Anomaly Detection Service
 * 
 * This service implements anomaly detection algorithms to identify unusual patterns
 * in time series data.
 */

const { Logger } = require('../integration/logger');
const { Counter, Gauge } = require('prom-client');

// Create logger
const logger = new Logger();

// Create metrics
const anomaliesDetected = new Counter({
  name: 'anomalies_detected_total',
  help: 'Total number of anomalies detected',
  labelNames: ['metric', 'algorithm', 'severity']
});

const anomalyScore = new Gauge({
  name: 'anomaly_score',
  help: 'Anomaly score for metrics',
  labelNames: ['metric', 'algorithm']
});

/**
 * Anomaly detection algorithms
 */
const ALGORITHMS = {
  Z_SCORE: 'z-score',
  MAD: 'median-absolute-deviation',
  IQR: 'interquartile-range',
  ISOLATION_FOREST: 'isolation-forest',
  DBSCAN: 'dbscan'
};

/**
 * Anomaly detection service
 */
class AnomalyDetectionService {
  /**
   * Constructor
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    this.options = {
      defaultAlgorithm: ALGORITHMS.Z_SCORE,
      zScoreThreshold: 3.0,
      madThreshold: 3.5,
      iqrMultiplier: 1.5,
      minSamples: 10,
      ...options
    };
    
    logger.info('Anomaly detection service initialized');
  }
  
  /**
   * Detect anomalies in time series data
   * @param {Array<Object>} timeSeries - Time series data with timestamp and value
   * @param {Object} options - Detection options
   * @returns {Array<Object>} Detected anomalies
   */
  detectAnomalies(timeSeries, options = {}) {
    const {
      algorithm = this.options.defaultAlgorithm,
      threshold = null,
      windowSize = timeSeries.length
    } = options;
    
    // Extract values
    const values = timeSeries.map(point => point.value);
    
    // Check if we have enough data points
    if (values.length < this.options.minSamples) {
      logger.debug('Not enough data points for anomaly detection');
      return [];
    }
    
    // Get recent values
    const recentValues = values.slice(-windowSize);
    
    // Detect anomalies based on algorithm
    let anomalies;
    
    switch (algorithm) {
      case ALGORITHMS.Z_SCORE:
        anomalies = this.detectZScoreAnomalies(timeSeries, recentValues, threshold || this.options.zScoreThreshold);
        break;
        
      case ALGORITHMS.MAD:
        anomalies = this.detectMADAnomalies(timeSeries, recentValues, threshold || this.options.madThreshold);
        break;
        
      case ALGORITHMS.IQR:
        anomalies = this.detectIQRAnomalies(timeSeries, recentValues, threshold || this.options.iqrMultiplier);
        break;
        
      case ALGORITHMS.ISOLATION_FOREST:
        anomalies = this.detectIsolationForestAnomalies(timeSeries, recentValues);
        break;
        
      case ALGORITHMS.DBSCAN:
        anomalies = this.detectDBSCANAnomalies(timeSeries, recentValues);
        break;
        
      default:
        throw new Error(`Unknown algorithm: ${algorithm}`);
    }
    
    // Track metrics
    if (anomalies.length > 0) {
      anomaliesDetected.inc({
        metric: options.metric || 'unknown',
        algorithm,
        severity: options.severity || 'warning'
      });
    }
    
    return anomalies;
  }
  
  /**
   * Detect anomalies using Z-Score
   * @param {Array<Object>} timeSeries - Original time series
   * @param {Array<number>} values - Values to analyze
   * @param {number} threshold - Z-Score threshold
   * @returns {Array<Object>} Detected anomalies
   */
  detectZScoreAnomalies(timeSeries, values, threshold) {
    // Calculate mean and standard deviation
    const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
    const stdDev = Math.sqrt(
      values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length
    );
    
    // Avoid division by zero
    if (stdDev === 0) {
      return [];
    }
    
    // Calculate Z-Scores and find anomalies
    const anomalies = [];
    
    for (let i = 0; i < timeSeries.length; i++) {
      const value = timeSeries[i].value;
      const zScore = Math.abs((value - mean) / stdDev);
      
      // Track anomaly score
      anomalyScore.set({
        metric: timeSeries[i].metric || 'unknown',
        algorithm: ALGORITHMS.Z_SCORE
      }, zScore);
      
      if (zScore > threshold) {
        anomalies.push({
          timestamp: timeSeries[i].timestamp,
          value,
          score: zScore,
          algorithm: ALGORITHMS.Z_SCORE,
          threshold,
          mean,
          stdDev
        });
      }
    }
    
    return anomalies;
  }
  
  /**
   * Detect anomalies using Median Absolute Deviation (MAD)
   * @param {Array<Object>} timeSeries - Original time series
   * @param {Array<number>} values - Values to analyze
   * @param {number} threshold - MAD threshold
   * @returns {Array<Object>} Detected anomalies
   */
  detectMADAnomalies(timeSeries, values, threshold) {
    // Calculate median
    const sortedValues = [...values].sort((a, b) => a - b);
    const median = sortedValues[Math.floor(sortedValues.length / 2)];
    
    // Calculate MAD
    const deviations = values.map(value => Math.abs(value - median));
    const sortedDeviations = [...deviations].sort((a, b) => a - b);
    const mad = sortedDeviations[Math.floor(sortedDeviations.length / 2)];
    
    // Avoid division by zero
    if (mad === 0) {
      return [];
    }
    
    // Find anomalies
    const anomalies = [];
    
    for (let i = 0; i < timeSeries.length; i++) {
      const value = timeSeries[i].value;
      const score = Math.abs(value - median) / mad;
      
      // Track anomaly score
      anomalyScore.set({
        metric: timeSeries[i].metric || 'unknown',
        algorithm: ALGORITHMS.MAD
      }, score);
      
      if (score > threshold) {
        anomalies.push({
          timestamp: timeSeries[i].timestamp,
          value,
          score,
          algorithm: ALGORITHMS.MAD,
          threshold,
          median,
          mad
        });
      }
    }
    
    return anomalies;
  }
  
  /**
   * Detect anomalies using Interquartile Range (IQR)
   * @param {Array<Object>} timeSeries - Original time series
   * @param {Array<number>} values - Values to analyze
   * @param {number} multiplier - IQR multiplier
   * @returns {Array<Object>} Detected anomalies
   */
  detectIQRAnomalies(timeSeries, values, multiplier) {
    // Calculate quartiles
    const sortedValues = [...values].sort((a, b) => a - b);
    const q1Index = Math.floor(sortedValues.length * 0.25);
    const q3Index = Math.floor(sortedValues.length * 0.75);
    const q1 = sortedValues[q1Index];
    const q3 = sortedValues[q3Index];
    const iqr = q3 - q1;
    
    // Calculate bounds
    const lowerBound = q1 - multiplier * iqr;
    const upperBound = q3 + multiplier * iqr;
    
    // Find anomalies
    const anomalies = [];
    
    for (let i = 0; i < timeSeries.length; i++) {
      const value = timeSeries[i].value;
      
      if (value < lowerBound || value > upperBound) {
        // Calculate score (distance from nearest bound)
        const distanceFromBound = Math.min(
          Math.abs(value - lowerBound),
          Math.abs(value - upperBound)
        );
        const score = distanceFromBound / iqr;
        
        // Track anomaly score
        anomalyScore.set({
          metric: timeSeries[i].metric || 'unknown',
          algorithm: ALGORITHMS.IQR
        }, score);
        
        anomalies.push({
          timestamp: timeSeries[i].timestamp,
          value,
          score,
          algorithm: ALGORITHMS.IQR,
          multiplier,
          q1,
          q3,
          iqr,
          lowerBound,
          upperBound
        });
      }
    }
    
    return anomalies;
  }
  
  /**
   * Detect anomalies using Isolation Forest
   * @param {Array<Object>} timeSeries - Original time series
   * @param {Array<number>} values - Values to analyze
   * @returns {Array<Object>} Detected anomalies
   */
  detectIsolationForestAnomalies(timeSeries, values) {
    // This is a simplified implementation
    // In a real implementation, we would use a proper machine learning library
    
    logger.warn('Isolation Forest algorithm not fully implemented');
    
    // For now, fall back to Z-Score
    return this.detectZScoreAnomalies(timeSeries, values, this.options.zScoreThreshold);
  }
  
  /**
   * Detect anomalies using DBSCAN
   * @param {Array<Object>} timeSeries - Original time series
   * @param {Array<number>} values - Values to analyze
   * @returns {Array<Object>} Detected anomalies
   */
  detectDBSCANAnomalies(timeSeries, values) {
    // This is a simplified implementation
    // In a real implementation, we would use a proper machine learning library
    
    logger.warn('DBSCAN algorithm not fully implemented');
    
    // For now, fall back to Z-Score
    return this.detectZScoreAnomalies(timeSeries, values, this.options.zScoreThreshold);
  }
}

module.exports = {
  AnomalyDetectionService,
  ALGORITHMS
};
