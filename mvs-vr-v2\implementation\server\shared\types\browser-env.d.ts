// DOM Types from lib.dom.d.ts
interface CSSStyleDeclaration {
  [key: string]: string | number | (() => void);
  getPropertyValue(property: string): string;
  setProperty(property: string, value: string | null, priority?: string): void;
  removeProperty(property: string): string;
}

interface Window {
  getComputedStyle(element: Element): CSSStyleDeclaration;
  matchMedia(query: string): MediaQueryList;
}

interface Document {
  createElement(tagName: string): Element;
  createElementNS(namespaceURI: string, qualifiedName: string): Element;
  createTextNode(data: string): Text;
}

interface Element {
  classList: DOMTokenList;
  innerHTML: string;
  outerHTML: string;
  textContent: string | null;
  getAttribute(name: string): string | null;
  hasAttribute(name: string): boolean;
  setAttribute(name: string, value: string): void;
  removeAttribute(name: string): void;
  matches(selector: string): boolean;
  closest(selector: string): Element | null;
}

interface DOMTokenList {
  readonly length: number;
  contains(token: string): boolean;
  add(...tokens: string[]): void;
  remove(...tokens: string[]): void;
  toggle(token: string, force?: boolean): boolean;
  replace(oldToken: string, newToken: string): boolean;
}

interface NodeList {
  forEach(callbackfn: (value: Node, key: number, parent: NodeList) => void): void;
  item(index: number): Node | null;
  readonly length: number;
  [index: number]: Node;
}

interface NodeListOf<T extends Node> extends NodeList {
  item(index: number): T | null;
  forEach(callbackfn: (value: T, key: number, parent: NodeListOf<T>) => void): void;
  [index: number]: T;
}

interface MediaQueryList {
  readonly matches: boolean;
  readonly media: string;
  addListener(listener: (this: MediaQueryList, ev: MediaQueryListEvent) => void): void;
  removeListener(listener: (this: MediaQueryList, ev: MediaQueryListEvent) => void): void;
}

interface MediaQueryListEvent extends Event {
  readonly matches: boolean;
  readonly media: string;
}

// Extend test environment globals
declare global {
  var document: Document;
  var window: Window;

  // Add Node.js specific types
  namespace NodeJS {
    interface Global {
      document: Document;
      window: Window;
    }
  }
}
