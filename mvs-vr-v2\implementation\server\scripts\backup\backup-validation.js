/**
 * Backup Validation
 * 
 * This script implements comprehensive validation for database, file storage,
 * and configuration backups.
 */

const { S3Client, GetObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);
const { Readable } = require('stream');
const { finished } = require('stream/promises');
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  region: process.env.AWS_REGION || 'us-east-1',
  buckets: {
    database: process.env.DB_BACKUP_BUCKET || 'mvs-vr-db-backups',
    files: process.env.FILE_BACKUP_BUCKET || 'mvs-vr-file-backups',
    config: process.env.CONFIG_BACKUP_BUCKET || 'mvs-vr-config-backups'
  },
  tempDir: path.join(__dirname, '../../temp/backup-validation'),
  validationLogPath: path.join(__dirname, '../../logs/backup-validation.json'),
  pgHost: process.env.POSTGRES_HOST || 'localhost',
  pgPort: process.env.POSTGRES_PORT || '5432',
  pgUser: process.env.POSTGRES_USER || 'postgres',
  pgPassword: process.env.POSTGRES_PASSWORD || 'postgres',
  pgDatabase: process.env.POSTGRES_DB || 'postgres',
  pgTestDatabase: process.env.POSTGRES_TEST_DB || 'postgres_test'
};

/**
 * Create S3 client
 * @returns {S3Client} S3 client
 */
function createS3Client() {
  return new S3Client({
    region: config.region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
}

/**
 * Load validation log
 * @returns {Object} Validation log
 */
async function loadValidationLog() {
  try {
    if (fs.existsSync(config.validationLogPath)) {
      const data = await readFileAsync(config.validationLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading validation log:', error);
  }
  
  return {
    lastRun: null,
    validations: []
  };
}

/**
 * Save validation log
 * @param {Object} log - Validation log
 */
async function saveValidationLog(log) {
  try {
    await writeFileAsync(
      config.validationLogPath,
      JSON.stringify(log, null, 2),
      'utf8'
    );
  } catch (error) {
    console.error('Error saving validation log:', error);
  }
}

/**
 * Download object from S3
 * @param {S3Client} client - S3 client
 * @param {string} bucket - Bucket name
 * @param {string} key - Object key
 * @param {string} outputPath - Output file path
 * @returns {Object} Object metadata
 */
async function downloadObject(client, bucket, key, outputPath) {
  // Create directory if it doesn't exist
  const dir = path.dirname(outputPath);
  await mkdirAsync(dir, { recursive: true });
  
  // Get object
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key
  });
  
  const response = await client.send(command);
  
  // Calculate checksum while downloading
  const hash = crypto.createHash('sha256');
  const writeStream = fs.createWriteStream(outputPath);
  
  response.Body.on('data', chunk => {
    hash.update(chunk);
  });
  
  await finished(Readable.fromWeb(response.Body).pipe(writeStream));
  
  const checksum = hash.digest('hex');
  
  return {
    key,
    size: response.ContentLength,
    lastModified: response.LastModified,
    contentType: response.ContentType,
    checksum
  };
}

/**
 * Validate database backup
 * @param {string} backupKey - Backup object key
 * @returns {Object} Validation results
 */
async function validateDatabaseBackup(backupKey) {
  logger.info(`Validating database backup: ${backupKey}`);
  
  const client = createS3Client();
  const outputPath = path.join(config.tempDir, 'database', backupKey);
  
  try {
    // Download backup
    const metadata = await downloadObject(client, config.buckets.database, backupKey, outputPath);
    
    // Verify file integrity
    logger.info(`Verifying file integrity...`);
    const fileStats = fs.statSync(outputPath);
    
    if (fileStats.size !== metadata.size) {
      throw new Error(`File size mismatch: expected ${metadata.size}, got ${fileStats.size}`);
    }
    
    // Test restoration in a temporary database
    logger.info(`Testing restoration...`);
    
    // Drop test database if it exists
    try {
      execSync(`PGPASSWORD=${config.pgPassword} dropdb -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} ${config.pgTestDatabase}`, { stdio: 'ignore' });
    } catch (error) {
      // Ignore error if database doesn't exist
    }
    
    // Create test database
    execSync(`PGPASSWORD=${config.pgPassword} createdb -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} ${config.pgTestDatabase}`, { stdio: 'inherit' });
    
    // Restore backup
    execSync(`PGPASSWORD=${config.pgPassword} pg_restore -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} -d ${config.pgTestDatabase} ${outputPath}`, { stdio: 'inherit' });
    
    // Verify database integrity
    logger.info(`Verifying database integrity...`);
    const integrityCheck = execSync(`PGPASSWORD=${config.pgPassword} psql -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} -d ${config.pgTestDatabase} -c "SELECT count(*) FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema'"`, { encoding: 'utf8' });
    
    const tableCount = parseInt(integrityCheck.trim().split('\n')[2].trim(), 10);
    
    if (isNaN(tableCount) || tableCount === 0) {
      throw new Error('Database integrity check failed: no tables found');
    }
    
    // Clean up
    logger.info(`Cleaning up...`);
    execSync(`PGPASSWORD=${config.pgPassword} dropdb -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} ${config.pgTestDatabase}`, { stdio: 'ignore' });
    fs.unlinkSync(outputPath);
    
    return {
      key: backupKey,
      status: 'success',
      size: metadata.size,
      checksum: metadata.checksum,
      tableCount,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error validating database backup:`, error);
    
    // Clean up
    try {
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
      
      execSync(`PGPASSWORD=${config.pgPassword} dropdb -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} ${config.pgTestDatabase}`, { stdio: 'ignore' });
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return {
      key: backupKey,
      status: 'failed',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Validate file backup
 * @param {string} backupKey - Backup object key
 * @returns {Object} Validation results
 */
async function validateFileBackup(backupKey) {
  logger.info(`Validating file backup: ${backupKey}`);
  
  const client = createS3Client();
  const outputPath = path.join(config.tempDir, 'files', backupKey);
  
  try {
    // Download backup
    const metadata = await downloadObject(client, config.buckets.files, backupKey, outputPath);
    
    // Verify file integrity
    logger.info(`Verifying file integrity...`);
    const fileStats = fs.statSync(outputPath);
    
    if (fileStats.size !== metadata.size) {
      throw new Error(`File size mismatch: expected ${metadata.size}, got ${fileStats.size}`);
    }
    
    // For zip/tar files, test extraction
    if (backupKey.endsWith('.zip') || backupKey.endsWith('.tar') || backupKey.endsWith('.tar.gz') || backupKey.endsWith('.tgz')) {
      logger.info(`Testing extraction...`);
      const extractDir = path.join(config.tempDir, 'extract', path.basename(backupKey, path.extname(backupKey)));
      
      // Create extract directory
      await mkdirAsync(extractDir, { recursive: true });
      
      // Extract archive
      if (backupKey.endsWith('.zip')) {
        execSync(`unzip -t ${outputPath}`, { stdio: 'inherit' });
      } else {
        execSync(`tar -tf ${outputPath}`, { stdio: 'inherit' });
      }
    }
    
    // Clean up
    logger.info(`Cleaning up...`);
    fs.unlinkSync(outputPath);
    
    return {
      key: backupKey,
      status: 'success',
      size: metadata.size,
      checksum: metadata.checksum,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error validating file backup:`, error);
    
    // Clean up
    try {
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return {
      key: backupKey,
      status: 'failed',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Validate configuration backup
 * @param {string} backupKey - Backup object key
 * @returns {Object} Validation results
 */
async function validateConfigBackup(backupKey) {
  logger.info(`Validating configuration backup: ${backupKey}`);
  
  const client = createS3Client();
  const outputPath = path.join(config.tempDir, 'config', backupKey);
  
  try {
    // Download backup
    const metadata = await downloadObject(client, config.buckets.config, backupKey, outputPath);
    
    // Verify file integrity
    logger.info(`Verifying file integrity...`);
    const fileStats = fs.statSync(outputPath);
    
    if (fileStats.size !== metadata.size) {
      throw new Error(`File size mismatch: expected ${metadata.size}, got ${fileStats.size}`);
    }
    
    // For JSON files, validate JSON structure
    if (backupKey.endsWith('.json')) {
      logger.info(`Validating JSON structure...`);
      const content = await readFileAsync(outputPath, 'utf8');
      JSON.parse(content); // Will throw if invalid JSON
    }
    
    // Clean up
    logger.info(`Cleaning up...`);
    fs.unlinkSync(outputPath);
    
    return {
      key: backupKey,
      status: 'success',
      size: metadata.size,
      checksum: metadata.checksum,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error validating configuration backup:`, error);
    
    // Clean up
    try {
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return {
      key: backupKey,
      status: 'failed',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Run backup validation for a specific backup type and key
 * @param {string} backupType - Type of backup (database, files, config)
 * @param {string} backupKey - Backup object key
 * @returns {Object} Validation results
 */
async function validateBackup(backupType, backupKey) {
  switch (backupType) {
    case 'database':
      return await validateDatabaseBackup(backupKey);
    case 'files':
      return await validateFileBackup(backupKey);
    case 'config':
      return await validateConfigBackup(backupKey);
    default:
      throw new Error(`Unknown backup type: ${backupType}`);
  }
}

/**
 * Run automated restoration testing
 * @param {string} backupType - Type of backup (database, files, config)
 * @param {string} backupKey - Backup object key
 * @returns {Object} Restoration test results
 */
async function testRestoration(backupType, backupKey) {
  logger.info(`Testing restoration for ${backupType} backup: ${backupKey}`);
  
  const client = createS3Client();
  const outputPath = path.join(config.tempDir, 'restoration', backupType, backupKey);
  
  try {
    // Download backup
    const metadata = await downloadObject(client, config.buckets[backupType], backupKey, outputPath);
    
    // Test restoration based on backup type
    switch (backupType) {
      case 'database':
        // Create test environment
        const testDbName = `restoration_test_${Date.now()}`;
        
        // Create test database
        execSync(`PGPASSWORD=${config.pgPassword} createdb -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} ${testDbName}`, { stdio: 'inherit' });
        
        // Restore backup
        execSync(`PGPASSWORD=${config.pgPassword} pg_restore -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} -d ${testDbName} ${outputPath}`, { stdio: 'inherit' });
        
        // Verify restoration
        const verifyResult = execSync(`PGPASSWORD=${config.pgPassword} psql -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} -d ${testDbName} -c "SELECT count(*) FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema'"`, { encoding: 'utf8' });
        
        // Clean up
        execSync(`PGPASSWORD=${config.pgPassword} dropdb -h ${config.pgHost} -p ${config.pgPort} -U ${config.pgUser} ${testDbName}`, { stdio: 'ignore' });
        
        return {
          key: backupKey,
          status: 'success',
          details: `Restored database with ${verifyResult.trim().split('\n')[2].trim()} tables`,
          timestamp: new Date().toISOString()
        };
        
      case 'files':
        // Create test environment
        const extractDir = path.join(config.tempDir, 'restoration', 'extract', path.basename(backupKey, path.extname(backupKey)));
        
        // Create extract directory
        await mkdirAsync(extractDir, { recursive: true });
        
        // Extract archive if applicable
        if (backupKey.endsWith('.zip')) {
          execSync(`unzip -q ${outputPath} -d ${extractDir}`, { stdio: 'inherit' });
        } else if (backupKey.endsWith('.tar') || backupKey.endsWith('.tar.gz') || backupKey.endsWith('.tgz')) {
          execSync(`tar -xf ${outputPath} -C ${extractDir}`, { stdio: 'inherit' });
        } else {
          // Just copy the file
          fs.copyFileSync(outputPath, path.join(extractDir, path.basename(backupKey)));
        }
        
        // Count files
        const fileCount = execSync(`find ${extractDir} -type f | wc -l`, { encoding: 'utf8' }).trim();
        
        return {
          key: backupKey,
          status: 'success',
          details: `Restored ${fileCount} files`,
          timestamp: new Date().toISOString()
        };
        
      case 'config':
        // For config files, just verify they can be parsed
        if (backupKey.endsWith('.json')) {
          const content = await readFileAsync(outputPath, 'utf8');
          const config = JSON.parse(content);
          
          return {
            key: backupKey,
            status: 'success',
            details: `Parsed JSON configuration with ${Object.keys(config).length} top-level keys`,
            timestamp: new Date().toISOString()
          };
        } else {
          return {
            key: backupKey,
            status: 'success',
            details: `Verified file integrity`,
            timestamp: new Date().toISOString()
          };
        }
        
      default:
        throw new Error(`Unknown backup type: ${backupType}`);
    }
  } catch (error) {
    console.error(`Error testing restoration:`, error);
    
    return {
      key: backupKey,
      status: 'failed',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  } finally {
    // Clean up
    try {
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
  }
}

// If script is run directly, run validation
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'validate';
  const backupType = args[1];
  const backupKey = args[2];
  
  if (!backupType || !backupKey) {
    console.error('Usage: node backup-validation.js [validate|test] <backup-type> <backup-key>');
    process.exit(1);
  }
  
  if (command === 'validate') {
    validateBackup(backupType, backupKey)
      .then(results => {
        logger.info('Validation completed:');
        logger.info(JSON.stringify(results, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  } else if (command === 'test') {
    testRestoration(backupType, backupKey)
      .then(results => {
        logger.info('Restoration test completed:');
        logger.info(JSON.stringify(results, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  } else {
    console.error(`Unknown command: ${command}`);
    console.error('Usage: node backup-validation.js [validate|test] <backup-type> <backup-key>');
    process.exit(1);
  }
}

module.exports = {
  validateBackup,
  testRestoration,
  validateDatabaseBackup,
  validateFileBackup,
  validateConfigBackup
};
