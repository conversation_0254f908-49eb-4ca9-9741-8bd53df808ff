{"name": "mvs-vr-monitoring", "version": "1.0.0", "description": "MVS-VR Monitoring System", "main": "index.js", "scripts": {"start": "docker-compose up -d", "stop": "docker-compose down", "logs": "docker-compose logs -f", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.6.2", "express": "^4.18.2", "kafkajs": "^2.2.4", "prom-client": "^14.2.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "author": "MVS-VR Team", "license": "UNLICENSED", "private": true}