import React, { useEffect, useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  TextField,
  Typography,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Save as SaveIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import VendorLayout from '../../components/VendorLayout';
import ProtectedRoute from '../../components/ProtectedRoute';
import { useAuth } from '../../utils/auth/AuthContext';
import { authService } from '../../utils/api/auth-service';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

/**
 * Profile page
 */
const Profile: React.FC = () => {
  const { user, updateUserProfile } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Profile form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [phone, setPhone] = useState('');
  
  // Password form state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [assetUpdates, setAssetUpdates] = useState(true);
  const [securityAlerts, setSecurityAlerts] = useState(true);
  
  // Initialize form with user data
  useEffect(() => {
    if (user) {
      setName(user.name || '');
      setEmail(user.email || '');
      setCompany(user.company || '');
      setPhone(user.phone || '');
    }
  }, [user]);
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Handle profile update
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Update profile
      await updateUserProfile({
        name,
        company,
        phone
      });
      
      // Show success message
      setSuccess('Profile updated successfully');
      setShowSuccess(true);
    } catch (err: unknown) {
      console.error('Error updating profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to update profile');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle password change
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!currentPassword) {
      setError('Current password is required');
      setShowError(true);
      return;
    }
    
    if (!newPassword) {
      setError('New password is required');
      setShowError(true);
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      setShowError(true);
      return;
    }
    
    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      setShowError(true);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Change password
      await authService.changePassword({
        currentPassword,
        newPassword
      });
      
      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // Show success message
      setSuccess('Password changed successfully');
      setShowSuccess(true);
    } catch (err: unknown) {
      console.error('Error changing password:', err);
      setError(err instanceof Error ? err.message : 'Failed to change password');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle notification settings update
  const handleUpdateNotifications = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      
      // Update notification settings
      await authService.updateNotificationSettings({
        emailNotifications,
        assetUpdates,
        securityAlerts
      });
      
      // Show success message
      setSuccess('Notification settings updated successfully');
      setShowSuccess(true);
    } catch (err: unknown) {
      console.error('Error updating notification settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to update notification settings');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle error snackbar close
  const handleCloseError = () => {
    setShowError(false);
  };
  
  // Handle success snackbar close
  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };
  
  if (!user) {
    return (
      <VendorLayout title="Profile">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }
  
  return (
    <VendorLayout title="Profile">
      {/* Error snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
      
      {/* Success snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Profile
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" gutterBottom>
          Manage your account settings
        </Typography>
      </Box>
      
      <Grid container spacing={4}>
        {/* Profile sidebar */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main'
                }}
              >
                {name.charAt(0)}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {name}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {email}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {company}
              </Typography>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Account Information
              </Typography>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="textSecondary">
                  Member Since
                </Typography>
                <Typography variant="body1">
                  {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                </Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="textSecondary">
                  Last Login
                </Typography>
                <Typography variant="body1">
                  {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'N/A'}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  Role
                </Typography>
                <Typography variant="body1">
                  {user.role || 'Vendor'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Profile tabs */}
        <Grid item xs={12} md={8}>
          <Paper>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab icon={<PersonIcon />} label="Personal Info" />
              <Tab icon={<LockIcon />} label="Password" />
              <Tab icon={<NotificationsIcon />} label="Notifications" />
            </Tabs>
            
            {/* Personal Info Tab */}
            <TabPanel value={tabValue} index={0}>
              <form onSubmit={handleUpdateProfile}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      label="Full Name"
                      fullWidth
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                      disabled={loading}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Email"
                      type="email"
                      fullWidth
                      value={email}
                      disabled
                      helperText="Email cannot be changed. Contact support for assistance."
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Company"
                      fullWidth
                      value={company}
                      onChange={(e) => setCompany(e.target.value)}
                      disabled={loading}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Phone"
                      fullWidth
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      disabled={loading}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={loading}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </TabPanel>
            
            {/* Password Tab */}
            <TabPanel value={tabValue} index={1}>
              <form onSubmit={handleChangePassword}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      label="Current Password"
                      type="password"
                      fullWidth
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      required
                      disabled={loading}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="New Password"
                      type="password"
                      fullWidth
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      required
                      disabled={loading}
                      helperText="Password must be at least 8 characters long"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Confirm New Password"
                      type="password"
                      fullWidth
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      disabled={loading}
                      error={newPassword !== confirmPassword && confirmPassword !== ''}
                      helperText={
                        newPassword !== confirmPassword && confirmPassword !== ''
                          ? 'Passwords do not match'
                          : ''
                      }
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={loading}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Changing...' : 'Change Password'}
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </TabPanel>
            
            {/* Notifications Tab */}
            <TabPanel value={tabValue} index={2}>
              <form onSubmit={handleUpdateNotifications}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Email Notifications
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body1">
                          Receive email notifications
                        </Typography>
                        <Button
                          variant={emailNotifications ? 'contained' : 'outlined'}
                          color="primary"
                          size="small"
                          onClick={() => setEmailNotifications(!emailNotifications)}
                        >
                          {emailNotifications ? 'Enabled' : 'Disabled'}
                        </Button>
                      </Box>
                      <Typography variant="body2" color="textSecondary">
                        Receive general email notifications about your account
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body1">
                          Asset updates
                        </Typography>
                        <Button
                          variant={assetUpdates ? 'contained' : 'outlined'}
                          color="primary"
                          size="small"
                          onClick={() => setAssetUpdates(!assetUpdates)}
                        >
                          {assetUpdates ? 'Enabled' : 'Disabled'}
                        </Button>
                      </Box>
                      <Typography variant="body2" color="textSecondary">
                        Receive notifications when your assets are updated or processed
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body1">
                          Security alerts
                        </Typography>
                        <Button
                          variant={securityAlerts ? 'contained' : 'outlined'}
                          color="primary"
                          size="small"
                          onClick={() => setSecurityAlerts(!securityAlerts)}
                        >
                          {securityAlerts ? 'Enabled' : 'Disabled'}
                        </Button>
                      </Box>
                      <Typography variant="body2" color="textSecondary">
                        Receive notifications about security events like password changes
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={loading}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Saving...' : 'Save Preferences'}
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
    </VendorLayout>
  );
};

// Wrap the Profile component with ProtectedRoute
const ProtectedProfile: React.FC = () => {
  return (
    <ProtectedRoute>
      <Profile />
    </ProtectedRoute>
  );
};

export default ProtectedProfile;
