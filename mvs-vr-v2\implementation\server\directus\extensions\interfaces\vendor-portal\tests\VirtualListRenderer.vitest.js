import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { VirtualListRenderer } from '../src/utils/PerformanceOptimizer';

describe('VirtualListRenderer', () => {
  let renderer;
  let mockItems;
  let mockLoadMoreItems;

  beforeEach(() => {
    // Setup fake timers
    vi.useFakeTimers();
  });

  afterEach(() => {
    // Clean up fake timers
    vi.restoreAllMocks();
  });

  beforeEach(() => {
    // Create mock items
    mockItems = Array.from({ length: 50 }, (_, i) => ({
      id: `item-${i}`,
      name: `Item ${i}`,
      value: i,
    }));

    // Create mock load more function
    mockLoadMoreItems = vi.fn().mockImplementation(async (page, pageSize) => {
      const startIndex = (page - 1) * pageSize;
      return Array.from({ length: pageSize }, (_, i) => ({
        id: `item-${startIndex + i + mockItems.length}`,
        name: `Item ${startIndex + i + mockItems.length}`,
        value: startIndex + i + mockItems.length,
      }));
    });

    // Create renderer with lazy loading
    renderer = new VirtualListRenderer(
      mockItems,
      40, // Item height
      400, // Container height
      5, // Buffer
      {
        lazyLoad: true,
        loadMoreItems: mockLoadMoreItems,
        loadThreshold: 0.7,
        pageSize: 20,
        totalItems: 100,
      },
    );
  });

  it('should initialize with correct properties', () => {
    expect(renderer.items).toEqual(mockItems);
    expect(renderer.itemHeight).toBe(40);
    expect(renderer.containerHeight).toBe(400);
    expect(renderer.buffer).toBe(5);
    expect(renderer.lazyLoad).toBe(true);
    expect(renderer.loadMoreItems).toBe(mockLoadMoreItems);
    expect(renderer.loadThreshold).toBe(0.7);
    expect(renderer.pageSize).toBe(20);
    expect(renderer.isLoading).toBe(false);
    expect(renderer.hasMoreItems).toBe(true);
    expect(renderer.totalItems).toBe(100);
  });

  it('should calculate visible items correctly', () => {
    // Set scroll position to show items starting from index 10
    renderer.scrollTop = 400; // 10 * 40 (itemHeight)

    const result = renderer.getVisibleItems();

    // Should include buffer items (5 before, 5 after)
    // Container height 400 / item height 40 = 10 visible items
    // Total: 5 (buffer) + 10 (visible) + 5 (buffer) = 20 items
    expect(result.visibleItems.length).toBe(20);

    // First visible item should be at index 5 (10 - 5 buffer)
    expect(result.visibleItems[0].id).toBe('item-5');

    // Items should have correct positioning
    expect(result.visibleItems[0].style.top).toBe('200px'); // 5 * 40
    expect(result.visibleItems[1].style.top).toBe('240px'); // 6 * 40
  });

  it('should trigger load more when scrolling near the threshold', async () => {
    // Create a custom implementation of loadMore that directly calls the mock function
    renderer.loadMore = async () => {
      await renderer.loadMoreItems(2, 20);
    };

    // Spy on the loadMore method
    const loadMoreSpy = vi.spyOn(renderer, 'loadMore');

    // Set scroll position near the bottom
    const totalHeight = renderer.items.length * renderer.itemHeight;
    const scrollPosition = (totalHeight - renderer.containerHeight) * renderer.loadThreshold + 1;

    // Trigger the scroll update
    renderer.updateScroll(scrollPosition);

    // Verify loadMore was called
    expect(loadMoreSpy).toHaveBeenCalled();

    // Advance timers and wait for promises
    vi.runAllTimers();
    await vi.runAllTimersAsync();

    // Should have called loadMoreItems
    expect(mockLoadMoreItems).toHaveBeenCalled();
    expect(mockLoadMoreItems).toHaveBeenCalledWith(2, 20); // Page 2, pageSize 20
  });

  it('should update items with new data', () => {
    const newItems = Array.from({ length: 10 }, (_, i) => ({
      id: `new-item-${i}`,
      name: `New Item ${i}`,
      value: i,
    }));

    renderer.updateItems(newItems, { totalItems: 50, hasMoreItems: false });

    expect(renderer.items).toEqual(newItems);
    expect(renderer.totalItems).toBe(50);
    expect(renderer.hasMoreItems).toBe(false);
  });

  it('should track performance metrics', () => {
    // Initial metrics should be zero
    expect(renderer.renderCount).toBe(0);
    expect(renderer.lastRenderTime).toBe(0);
    expect(renderer.averageRenderTime).toBe(0);

    // Get visible items to trigger metrics update
    renderer.getVisibleItems();

    // Metrics should be updated
    expect(renderer.renderCount).toBe(1);
    expect(renderer.lastRenderTime).toBeGreaterThan(0);
    expect(renderer.averageRenderTime).toBeGreaterThan(0);

    // Get metrics
    const metrics = renderer.getMetrics();
    expect(metrics.renderCount).toBe(1);
    expect(metrics.lastRenderTime).toBeGreaterThan(0);
    expect(metrics.itemCount).toBe(50);
  });

  it('should reset metrics correctly', () => {
    // Get visible items to trigger metrics update
    renderer.getVisibleItems();

    // Metrics should be updated
    expect(renderer.renderCount).toBe(1);

    // Reset metrics
    renderer.resetMetrics();

    // Metrics should be reset
    expect(renderer.renderCount).toBe(0);
    expect(renderer.lastRenderTime).toBe(0);
    expect(renderer.averageRenderTime).toBe(0);
  });
});
