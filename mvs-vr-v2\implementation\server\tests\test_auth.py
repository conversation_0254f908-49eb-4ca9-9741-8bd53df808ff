import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Assuming the main FastAPI app is in services.vendor-management.app.main
# and auth functions/models are in services.vendor-management.app.auth and .models
from ..services.vendor_management.app.main import app
from ..services.vendor_management.app.auth import create_tokens, hash_refresh_token, SECRET_KEY, ALGORITHM, REFRESH_TOKEN_EXPIRE_DAYS
from ..services.vendor_management.app.models import RefreshTokenRequest

client = TestClient(app)

# Mock the database client
@pytest.fixture
def mock_supabase_client():
    with patch('services.vendor_management.app.main.get_supabase_client') as mock_get:
        mock_supabase = MagicMock()
        mock_get.return_value = mock_supabase
        yield mock_supabase

# Test case for successful token refresh
def test_refresh_token_success(mock_supabase_client):
    user_id = "test_user_id"
    initial_access_token, initial_refresh_token = create_tokens({"sub": user_id})
    initial_refresh_token_hash = hash_refresh_token(initial_refresh_token)

    # Mock the database response for finding the refresh token
    mock_supabase_client.table("refresh_tokens").select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
        "user_id": user_id,
        "token_hash": initial_refresh_token_hash,
        "expires_at": (datetime.utcnow() + timedelta(days=1)).isoformat() # Not expired
    }

    # Mock the database response for deleting the old refresh token
    mock_supabase_client.table("refresh_tokens").delete.return_value.eq.return_value.execute.return_value.data = {}

    # Mock the database response for inserting the new refresh token
    mock_supabase_client.table("refresh_tokens").insert.return_value.execute.return_value.data = {}

    response = client.post("/token/refresh", json={"refresh_token": initial_refresh_token})

    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"

    # Verify the old token was deleted
    mock_supabase_client.table("refresh_tokens").delete.assert_called_once_with()
    mock_supabase_client.table("refresh_tokens").delete.return_value.eq.assert_called_once_with("token_hash", initial_refresh_token_hash)

    # Verify a new refresh token was inserted (check hash)
    mock_supabase_client.table("refresh_tokens").insert.assert_called_once()
    inserted_data = mock_supabase_client.table("refresh_tokens").insert.call_args[0][0]
    assert inserted_data["user_id"] == user_id
    assert "token_hash" in inserted_data
    assert inserted_data["token_hash"] != initial_refresh_token_hash # Ensure rotation
    assert "expires_at" in inserted_data

# Test case for invalid refresh token
def test_refresh_token_invalid(mock_supabase_client):
    # Mock the database response for not finding the refresh token
    mock_supabase_client.table("refresh_tokens").select.return_value.eq.return_value.single.return_value.execute.return_value.data = None

    response = client.post("/token/refresh", json={"refresh_token": "invalid_token"})

    assert response.status_code == 401
    assert response.json() == {"detail": "Invalid refresh token"}

    # Verify no delete or insert operations were called
    mock_supabase_client.table("refresh_tokens").delete.assert_not_called()
    mock_supabase_client.table("refresh_tokens").insert.assert_not_called()

# Test case for expired refresh token
def test_refresh_token_expired(mock_supabase_client):
    user_id = "test_user_id"
    initial_access_token, initial_refresh_token = create_tokens({"sub": user_id})
    initial_refresh_token_hash = hash_refresh_token(initial_refresh_token)

    # Mock the database response for finding the refresh token, but with an expired date
    mock_supabase_client.table("refresh_tokens").select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
        "user_id": user_id,
        "token_hash": initial_refresh_token_hash,
        "expires_at": (datetime.utcnow() - timedelta(days=1)).isoformat() # Expired
    }

    # Mock the database response for deleting the expired refresh token
    mock_supabase_client.table("refresh_tokens").delete.return_value.eq.return_value.execute.return_value.data = {}


    response = client.post("/token/refresh", json={"refresh_token": initial_refresh_token})

    assert response.status_code == 401
    assert response.json() == {"detail": "Refresh token expired"}

    # Verify the expired token was deleted
    mock_supabase_client.table("refresh_tokens").delete.assert_called_once_with()
    mock_supabase_client.table("refresh_tokens").delete.return_value.eq.assert_called_once_with("token_hash", initial_refresh_token_hash)

    # Verify no new token was inserted
    mock_supabase_client.table("refresh_tokens").insert.assert_not_called()