import { execSync } from 'child_process';
import { writeFileSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));
const rootDir = join(__dirname, '..');

// Install dependencies
console.log('Installing dependencies...');
execSync('npm install', { cwd: rootDir, stdio: 'inherit' });

// Create test directories if they don't exist
const directories = ['tests/unit', 'tests/integration', 'tests/setup', 'tests/api', 'tests/mocks'];

console.log('Creating test directories...');
directories.forEach(dir => {
  const fullPath = join(rootDir, dir);
  mkdirSync(fullPath, { recursive: true });
});

// Create initial test setup files if they don't exist
console.log('Setting up test configuration...');

// Add vitest types to tsconfig
const tsconfigPath = join(rootDir, 'tsconfig.json');
const tsconfig = {
  extends: './tsconfig.base.json',
  compilerOptions: {
    types: ['vitest/globals', '@testing-library/jest-dom', 'node'],
  },
  include: ['**/*.ts', '**/*.tsx', '**/*.vue', 'tests/**/*'],
};

writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2));

console.log('Test environment setup complete!');
console.log('You can now run tests with:');
console.log('  npm test           # Run tests once');
console.log('  npm run test:watch # Run tests in watch mode');
