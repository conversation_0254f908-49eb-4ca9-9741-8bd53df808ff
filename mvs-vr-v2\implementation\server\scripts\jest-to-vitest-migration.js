/**
 * Jest to Vitest Migration Script
 *
 * This script helps automate the migration from Jest to Vitest by:
 * 1. Finding all test files in the project
 * 2. Applying common replacements
 * 3. Handling special cases
 *
 * Usage:
 * node scripts/jest-to-vitest-migration.js [--dry-run] [--path=<specific-path>]
 */

const fs = require('fs');
const path = require('path');
const process = require('process');

// Configuration
const config = {
  dryRun: process.argv.includes('--dry-run'),
  specificPath: process.argv.find(arg => arg.startsWith('--path='))?.split('=')[1],
  rootDir: path.resolve(__dirname, '..'),
  testFilePatterns: ['**/*.test.js', '**/*.test.ts', '**/*.spec.js', '**/*.spec.ts'],
  ignorePatterns: ['**/node_modules/**', '**/dist/**', '**/build/**'],
};

// Replacements to apply
const replacements = [
  // Import replacements
  {
    pattern: /import\s+\{([^}]*?)jest([^}]*?)\}\s+from\s+['"]@jest\/globals['"];?/g,
    replacement: (_match, before, after) => `import {${before}vi${after}} from 'vitest';`,
  },
  {
    pattern: /import\s+\{([^}]*?)\}\s+from\s+['"]@jest\/globals['"];?/g,
    replacement: (_match, imports) => `import {${imports}} from 'vitest';`,
  },
  // Add explicit imports for tests without imports
  {
    pattern: /^(?!import)(?!\/\/)(?!\/\*)(?!\s*$)(?!\*)/m,
    replacement: (match, _offset, string) => {
      // Only add imports if they don't already exist
      if (!string.includes('import') && !string.includes('require(')) {
        return `import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';\n\n${match}`;
      }
      return match;
    },
  },
  // Jest function replacements
  {
    pattern: /jest\.mock\(/g,
    replacement: 'vi.mock(',
  },
  {
    pattern: /jest\.fn\(/g,
    replacement: 'vi.fn(',
  },
  {
    pattern: /jest\.spyOn\(/g,
    replacement: 'vi.spyOn(',
  },
  {
    pattern: /jest\.clearAllMocks\(/g,
    replacement: 'vi.clearAllMocks(',
  },
  {
    pattern: /jest\.resetAllMocks\(/g,
    replacement: 'vi.resetAllMocks(',
  },
  {
    pattern: /jest\.restoreAllMocks\(/g,
    replacement: 'vi.restoreAllMocks(',
  },
  {
    pattern: /jest\.useFakeTimers\(/g,
    replacement: 'vi.useFakeTimers(',
  },
  {
    pattern: /jest\.useRealTimers\(/g,
    replacement: 'vi.useRealTimers(',
  },
  {
    pattern: /jest\.advanceTimersByTime\(/g,
    replacement: 'vi.advanceTimersByTime(',
  },
  {
    pattern: /jest\.runAllTimers\(/g,
    replacement: 'vi.runAllTimers(',
  },
  {
    pattern: /jest\.setTimeout\(/g,
    replacement: 'vi.setConfig({ testTimeout: ',
  },
  // Type casting replacements
  {
    pattern: /as\s+jest\.Mock/g,
    replacement: 'as ReturnType<typeof vi.fn>',
  },
  // Fix module imports without extensions
  {
    pattern: /from\s+['"](\.\.[\/\\][^'"]*?)['"];/g,
    replacement: (match, importPath) => {
      // Only add .js extension if it doesn't already have an extension
      if (!path.extname(importPath)) {
        return `from '${importPath}.js';`;
      }
      return match;
    },
  },
  // Fix require statements
  {
    pattern: /const\s+\{([^}]*?)\}\s+=\s+require\(['"]([^'"]*?)['"]\);/g,
    replacement: (_match, imports, modulePath) => {
      // Convert to import statement
      if (modulePath.startsWith('.')) {
        // Add .js extension to relative imports if needed
        if (!path.extname(modulePath)) {
          modulePath = `${modulePath}.js`;
        }
      }
      return `import { ${imports} } from '${modulePath}';`;
    },
  },
  // Fix simple require statements
  {
    pattern: /const\s+([a-zA-Z0-9_]+)\s+=\s+require\(['"]([^'"]*?)['"]\);/g,
    replacement: (_match, varName, modulePath) => {
      if (modulePath.startsWith('.')) {
        // Add .js extension to relative imports if needed
        if (!path.extname(modulePath)) {
          modulePath = `${modulePath}.js`;
        }
      }
      return `import ${varName} from '${modulePath}';`;
    },
  },
];

// Special case handlers
const specialCaseHandlers = [
  // Fix ioredis mocks
  {
    pattern:
      /vi\.mock\(['"]ioredis['"],\s*\(\)\s*=>\s*\{\s*return\s+vi\.fn\(\)\.mockImplementation\(\(\)\s*=>\s*\(\{([^}]*?)\}\)\);\s*\}\);/gs,
    replacement: (_match, mockMethods) => {
      return `vi.mock('ioredis', () => {
  const RedisMock = vi.fn().mockImplementation(() => ({${mockMethods}}));
  return { default: RedisMock };
});`;
    },
  },
  // Fix @supabase/supabase-js mocks
  {
    pattern: /vi\.mock\(['"]@supabase\/supabase-js['"],\s*\(\)\s*=>\s*\(\{([^}]*?)\}\)\);/g,
    replacement: (_match, mockMethods) => {
      return `vi.mock('@supabase/supabase-js', () => {
  return {
    ${mockMethods}
  };
});`;
    },
  },
  // Fix @google-cloud/storage mocks
  {
    pattern: /vi\.mock\(['"]@google-cloud\/storage['"],\s*\(\)\s*=>\s*\(\{([^}]*?)\}\)\);/g,
    replacement: (_match, mockMethods) => {
      return `vi.mock('@google-cloud/storage', () => {
  return {
    ${mockMethods}
  };
});`;
    },
  },
  // Fix express mocks
  {
    pattern: /vi\.mock\(['"]express['"],\s*\(\)\s*=>\s*\(\{([^}]*?)\}\)\);/g,
    replacement: (_match, _mockMethods) => {
      return `vi.mock('express', () => {
  const mockExpress = vi.fn().mockReturnValue({
    use: vi.fn(),
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    listen: vi.fn(),
  });
  mockExpress.Router = vi.fn().mockReturnValue({
    use: vi.fn(),
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  });
  mockExpress.json = vi.fn();
  mockExpress.urlencoded = vi.fn();
  mockExpress.static = vi.fn();
  return { default: mockExpress };
});`;
    },
  },
  // Fix other common module mocks
  {
    pattern: /vi\.mock\(['"]([^'"]*?)['"]\)\s*;/g,
    replacement: (match, moduleName) => {
      // Skip if it's already a complex mock
      if (match.includes('=>')) {
        return match;
      }

      // Handle different types of modules
      if (moduleName.includes('/')) {
        // Likely a path or scoped package, use default export
        return `vi.mock('${moduleName}', () => {
  return { default: vi.fn() };
});`;
      } else {
        // Likely a simple module, return the module itself as a mock function
        return `vi.mock('${moduleName}', () => {
  return vi.fn();
});`;
      }
    },
  },
  // Fix mockImplementation with object returns
  {
    pattern: /mockImplementation\(\(\)\s*=>\s*\{([^}]*?)\}\)/g,
    replacement: (match, implementation) => {
      // If it's a simple implementation, keep it as is
      if (!implementation.includes('return')) {
        return match;
      }

      // Otherwise, make sure it returns properly
      return `mockImplementation(() => {${implementation}})`;
    },
  },
];

/**
 * Find all test files in the project
 */
function findTestFiles() {
  // Use Node.js fs methods instead of shell commands for better cross-platform compatibility
  const testFiles = [];

  let searchPath = config.rootDir;
  if (config.specificPath) {
    searchPath = path.resolve(config.rootDir, config.specificPath);
  }

  // If specificPath points directly to a file, just check that file
  if (config.specificPath && fs.existsSync(searchPath) && fs.statSync(searchPath).isFile()) {
    const relativePath = path.relative(config.rootDir, searchPath);
    if (
      config.testFilePatterns.some(pattern => {
        // Convert glob pattern to regex
        const regexPattern = pattern.replace(/\./g, '\\.').replace(/\*/g, '.*');
        return new RegExp(regexPattern).test(relativePath);
      })
    ) {
      return [searchPath];
    }
    return [];
  }

  // Helper function to recursively find files
  function findFilesRecursive(dir) {
    if (!fs.existsSync(dir)) {
      return;
    }

    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const relativePath = path.relative(config.rootDir, filePath);

      // Skip ignored paths
      if (
        config.ignorePatterns.some(pattern => {
          // Convert glob pattern to regex
          const regexPattern = pattern
            .replace(/\*\*/g, '.*')
            .replace(/\*/g, '[^/\\\\]*')
            .replace(/\./g, '\\.');
          return new RegExp(regexPattern).test(relativePath);
        })
      ) {
        continue;
      }

      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        findFilesRecursive(filePath);
      } else if (stat.isFile()) {
        // Check if file matches test patterns
        if (
          config.testFilePatterns.some(pattern => {
            // Convert glob pattern to regex
            const regexPattern = pattern
              .replace(/\*\*/g, '.*')
              .replace(/\*/g, '[^/\\\\]*')
              .replace(/\./g, '\\.');
            return new RegExp(regexPattern).test(file);
          })
        ) {
          testFiles.push(filePath);
        }
      }
    }
  }

  findFilesRecursive(searchPath);
  return testFiles;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  console.log(`Processing ${filePath}...`);

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Apply standard replacements
    for (const { pattern, replacement } of replacements) {
      content = content.replace(pattern, replacement);
    }

    // Apply special case handlers
    for (const { pattern, replacement } of specialCaseHandlers) {
      content = content.replace(pattern, replacement);
    }

    // Check if content changed
    if (content !== originalContent) {
      if (!config.dryRun) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Updated ${filePath}`);
      } else {
        console.log(`🔍 Would update ${filePath} (dry run)`);
      }
      return true;
    } else {
      console.log(`⏭️ No changes needed for ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main function
 */
function main() {
  console.log('Jest to Vitest Migration Script');
  console.log('==============================');
  console.log(`Mode: ${config.dryRun ? 'Dry Run' : 'Live'}`);
  console.log(`Root Directory: ${config.rootDir}`);
  if (config.specificPath) {
    console.log(`Specific Path: ${config.specificPath}`);
  }
  console.log('');

  const testFiles = findTestFiles();
  console.log(`Found ${testFiles.length} test files`);

  let updatedCount = 0;
  let errorCount = 0;

  for (const filePath of testFiles) {
    try {
      const updated = processFile(filePath);
      if (updated) updatedCount++;
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error);
      errorCount++;
    }
  }

  console.log('');
  console.log('Summary:');
  console.log(`- Total files: ${testFiles.length}`);
  console.log(`- Updated files: ${updatedCount}`);
  console.log(`- Errors: ${errorCount}`);

  if (config.dryRun) {
    console.log('\nThis was a dry run. No files were actually modified.');
    console.log('Run without --dry-run to apply changes.');
  }
}

// Run the script
main();
