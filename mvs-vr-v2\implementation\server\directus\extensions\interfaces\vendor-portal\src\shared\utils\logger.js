/**
 * Logger utility for the vendor portal extension
 * 
 * This module provides logging functionality with different log levels.
 */

// Default log level
let currentLogLevel = 'info';

// Log levels
const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
  none: 4
};

/**
 * Set the current log level
 * @param {string} level - The log level to set ('debug', 'info', 'warn', 'error', 'none')
 */
function setLogLevel(level) {
  if (LOG_LEVELS[level] !== undefined) {
    currentLogLevel = level;
  } else {
    console.error(`Invalid log level: ${level}`);
  }
}

/**
 * Get the current log level
 * @returns {string} The current log level
 */
function getLogLevel() {
  return currentLogLevel;
}

/**
 * Log a debug message
 * @param {string} message - The message to log
 * @param {any} data - Optional data to log
 */
function debug(message, data) {
  if (LOG_LEVELS[currentLogLevel] <= LOG_LEVELS.debug) {
    console.debug(`[DEBUG] ${message}`, data !== undefined ? data : '');
  }
}

/**
 * Log an info message
 * @param {string} message - The message to log
 * @param {any} data - Optional data to log
 */
function info(message, data) {
  if (LOG_LEVELS[currentLogLevel] <= LOG_LEVELS.info) {
    console.info(`[INFO] ${message}`, data !== undefined ? data : '');
  }
}

/**
 * Log a warning message
 * @param {string} message - The message to log
 * @param {any} data - Optional data to log
 */
function warn(message, data) {
  if (LOG_LEVELS[currentLogLevel] <= LOG_LEVELS.warn) {
    console.warn(`[WARN] ${message}`, data !== undefined ? data : '');
  }
}

/**
 * Log an error message
 * @param {string} message - The message to log
 * @param {any} data - Optional data to log
 */
function error(message, data) {
  if (LOG_LEVELS[currentLogLevel] <= LOG_LEVELS.error) {
    console.error(`[ERROR] ${message}`, data !== undefined ? data : '');
  }
}

export default {
  setLogLevel,
  getLogLevel,
  debug,
  info,
  warn,
  error
};
