/**
 * Asset Delivery System Monitor
 * 
 * This service monitors the asset delivery system, tracking delivery times,
 * CDN performance, cache hit/miss rates, and bandwidth usage.
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const cors = require('cors');
const logger = require('../utils/logger');
const config = require('../config');
const { triggerAlert } = require('./alert-manager');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({ register });

// Create custom metrics for asset delivery monitoring
const metrics = {
  // Delivery metrics
  assetDeliveryTime: new promClient.Histogram({
    name: 'mvs_vr_asset_delivery_time_ms',
    help: 'Asset delivery time in milliseconds',
    labelNames: ['asset_type', 'size_category', 'region'],
    buckets: [50, 100, 200, 500, 1000, 2000, 5000, 10000]
  }),
  
  assetDeliveryCount: new promClient.Counter({
    name: 'mvs_vr_asset_delivery_total',
    help: 'Total number of asset deliveries',
    labelNames: ['asset_type', 'status', 'region']
  }),
  
  // CDN metrics
  cdnPerformance: new promClient.Histogram({
    name: 'mvs_vr_cdn_response_time_ms',
    help: 'CDN response time in milliseconds',
    labelNames: ['cdn_provider', 'region'],
    buckets: [10, 50, 100, 200, 500, 1000, 2000]
  }),
  
  cdnErrors: new promClient.Counter({
    name: 'mvs_vr_cdn_errors_total',
    help: 'Total number of CDN errors',
    labelNames: ['cdn_provider', 'error_type', 'region']
  }),
  
  // Cache metrics
  cacheHitRate: new promClient.Gauge({
    name: 'mvs_vr_cache_hit_rate',
    help: 'Cache hit rate percentage',
    labelNames: ['cache_level', 'asset_type']
  }),
  
  cacheSize: new promClient.Gauge({
    name: 'mvs_vr_cache_size_bytes',
    help: 'Current cache size in bytes',
    labelNames: ['cache_level']
  }),
  
  cacheOperations: new promClient.Counter({
    name: 'mvs_vr_cache_operations_total',
    help: 'Total number of cache operations',
    labelNames: ['operation', 'cache_level', 'status']
  }),
  
  // Bandwidth metrics
  bandwidthUsage: new promClient.Counter({
    name: 'mvs_vr_bandwidth_usage_bytes',
    help: 'Total bandwidth usage in bytes',
    labelNames: ['direction', 'asset_type', 'region']
  }),
  
  bandwidthRate: new promClient.Gauge({
    name: 'mvs_vr_bandwidth_rate_bytes_per_second',
    help: 'Current bandwidth usage rate in bytes per second',
    labelNames: ['direction', 'region']
  }),
  
  // Asset metrics
  assetCount: new promClient.Gauge({
    name: 'mvs_vr_asset_count',
    help: 'Total number of assets',
    labelNames: ['asset_type', 'status']
  }),
  
  assetSize: new promClient.Gauge({
    name: 'mvs_vr_asset_size_bytes',
    help: 'Total size of assets in bytes',
    labelNames: ['asset_type']
  })
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Process asset delivery event
 * 
 * @param {Object} data - Asset delivery event data
 */
async function processAssetDeliveryEvent(data) {
  try {
    const { 
      asset_id, 
      asset_type, 
      size_bytes, 
      delivery_time_ms, 
      status, 
      region, 
      cdn_provider, 
      cache_hit,
      client_id
    } = data;
    
    // Categorize asset size
    let sizeCategory = 'small';
    if (size_bytes > 10 * 1024 * 1024) { // > 10MB
      sizeCategory = 'large';
    } else if (size_bytes > 1 * 1024 * 1024) { // > 1MB
      sizeCategory = 'medium';
    }
    
    // Record delivery metrics
    metrics.assetDeliveryTime.observe(
      { asset_type, size_category: sizeCategory, region: region || 'unknown' }, 
      delivery_time_ms
    );
    
    metrics.assetDeliveryCount.inc(
      { asset_type, status, region: region || 'unknown' }
    );
    
    // Record bandwidth usage
    metrics.bandwidthUsage.inc(
      { direction: 'outbound', asset_type, region: region || 'unknown' }, 
      size_bytes
    );
    
    // Record cache metrics
    metrics.cacheOperations.inc(
      { 
        operation: 'read', 
        cache_level: 'cdn', 
        status: cache_hit ? 'hit' : 'miss' 
      }
    );
    
    // Record CDN metrics if available
    if (cdn_provider && delivery_time_ms) {
      metrics.cdnPerformance.observe(
        { cdn_provider, region: region || 'unknown' }, 
        delivery_time_ms
      );
    }
    
    // Check for slow delivery and trigger alert if necessary
    if (delivery_time_ms > 5000) { // > 5 seconds
      triggerAlert('slowAssetDelivery', {
        asset_id,
        asset_type,
        delivery_time_ms,
        size_bytes,
        region,
        client_id
      });
    }
    
    logger.debug('Processed asset delivery event', { asset_id, delivery_time_ms });
  } catch (error) {
    logger.error('Error processing asset delivery event', { error: error.message });
  }
}

/**
 * Update asset delivery metrics
 */
async function updateAssetMetrics() {
  try {
    // Get asset statistics
    const { data: assetStats, error: assetError } = await supabase
      .rpc('get_asset_statistics');
      
    if (assetError) {
      logger.error('Error fetching asset statistics', { error: assetError.message });
    } else if (assetStats) {
      // Update asset count metrics
      assetStats.counts.forEach(stat => {
        metrics.assetCount.set(
          { asset_type: stat.asset_type, status: stat.status }, 
          stat.count
        );
      });
      
      // Update asset size metrics
      assetStats.sizes.forEach(stat => {
        metrics.assetSize.set(
          { asset_type: stat.asset_type }, 
          stat.total_size
        );
      });
    }
    
    // Get cache statistics
    const { data: cacheStats, error: cacheError } = await supabase
      .rpc('get_cache_statistics');
      
    if (cacheError) {
      logger.error('Error fetching cache statistics', { error: cacheError.message });
    } else if (cacheStats) {
      // Update cache hit rate metrics
      cacheStats.hit_rates.forEach(stat => {
        metrics.cacheHitRate.set(
          { cache_level: stat.cache_level, asset_type: stat.asset_type }, 
          stat.hit_rate
        );
      });
      
      // Update cache size metrics
      cacheStats.sizes.forEach(stat => {
        metrics.cacheSize.set(
          { cache_level: stat.cache_level }, 
          stat.size_bytes
        );
      });
    }
    
    logger.debug('Updated asset metrics');
  } catch (error) {
    logger.error('Error updating asset metrics', { error: error.message });
  }
}

// Create Express app
const app = express();
app.use(cors());
app.use(express.json());

// API endpoint for receiving asset delivery events
app.post('/api/events', async (req, res) => {
  try {
    const data = req.body;
    
    // Validate required fields
    if (!data.asset_id || !data.asset_type) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Process event
    await processAssetDeliveryEvent(data);
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/events', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    // Update metrics before responding
    await updateAssetMetrics();
    
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving asset delivery metrics', { error: error.message });
    res.status(500).send('Error collecting asset delivery metrics');
  }
});

// Start server
const PORT = process.env.ASSET_DELIVERY_MONITOR_PORT || 9102;
app.listen(PORT, () => {
  logger.info(`Asset Delivery Monitor listening on port ${PORT}`);
  
  // Schedule regular metrics updates
  setInterval(updateAssetMetrics, 60000); // Update every minute
});

module.exports = {
  processAssetDeliveryEvent,
  updateAssetMetrics
};
