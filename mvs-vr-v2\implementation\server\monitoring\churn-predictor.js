/**
 * Churn Predictor
 * 
 * This service analyzes user behavior patterns and predicts churn risk.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Churn risk thresholds
const CHURN_RISK = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  VERY_HIGH: 'very_high'
};

/**
 * Get user activity data
 * 
 * @param {string} userId - User ID
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User activity data
 */
async function getUserActivityData(userId, days = 90) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Get user session data
 * 
 * @param {string} userId - User ID
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User session data
 */
async function getUserSessionData(userId, days = 90) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user session data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserSessionData', { error: error.message });
    return [];
  }
}

/**
 * Calculate engagement score
 * 
 * @param {Array} activityData - User activity data
 * @param {Array} sessionData - User session data
 * @returns {number} - Engagement score (0-100)
 */
function calculateEngagementScore(activityData, sessionData) {
  if (activityData.length === 0 || sessionData.length === 0) {
    return 0;
  }
  
  // Calculate recency score (0-40)
  const now = new Date();
  const lastActivity = new Date(activityData[activityData.length - 1].created_at);
  const daysSinceLastActivity = (now - lastActivity) / (1000 * 60 * 60 * 24);
  
  let recencyScore = 0;
  if (daysSinceLastActivity <= 1) {
    recencyScore = 40;
  } else if (daysSinceLastActivity <= 3) {
    recencyScore = 30;
  } else if (daysSinceLastActivity <= 7) {
    recencyScore = 20;
  } else if (daysSinceLastActivity <= 14) {
    recencyScore = 10;
  }
  
  // Calculate frequency score (0-30)
  const activityDates = new Set(activityData.map(a => a.created_at.split('T')[0]));
  const uniqueDays = activityDates.size;
  
  let frequencyScore = 0;
  if (uniqueDays >= 30) {
    frequencyScore = 30;
  } else {
    frequencyScore = Math.round((uniqueDays / 30) * 30);
  }
  
  // Calculate duration score (0-30)
  const totalSessionDuration = sessionData.reduce((sum, session) => {
    return sum + (session.duration || 0);
  }, 0);
  
  const avgSessionDuration = totalSessionDuration / sessionData.length;
  
  let durationScore = 0;
  if (avgSessionDuration >= 1800) { // 30 minutes
    durationScore = 30;
  } else {
    durationScore = Math.round((avgSessionDuration / 1800) * 30);
  }
  
  // Calculate total engagement score
  const engagementScore = recencyScore + frequencyScore + durationScore;
  
  return engagementScore;
}

/**
 * Calculate churn risk
 * 
 * @param {Array} activityData - User activity data
 * @param {Array} sessionData - User session data
 * @returns {Object} - Churn risk assessment
 */
function calculateChurnRisk(activityData, sessionData) {
  // Calculate engagement score
  const engagementScore = calculateEngagementScore(activityData, sessionData);
  
  // Calculate activity trend
  const activityByWeek = {};
  
  activityData.forEach(activity => {
    const date = new Date(activity.created_at);
    const weekNumber = Math.floor((date.getTime() - new Date(date.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));
    const weekKey = `${date.getFullYear()}-${weekNumber}`;
    
    if (!activityByWeek[weekKey]) {
      activityByWeek[weekKey] = 0;
    }
    
    activityByWeek[weekKey]++;
  });
  
  const weekKeys = Object.keys(activityByWeek).sort();
  const activityTrend = weekKeys.map(key => activityByWeek[key]);
  
  // Calculate trend score (-1 to 1)
  let trendScore = 0;
  
  if (activityTrend.length >= 2) {
    const recentWeeks = activityTrend.slice(-4);
    const firstHalf = recentWeeks.slice(0, Math.ceil(recentWeeks.length / 2));
    const secondHalf = recentWeeks.slice(Math.ceil(recentWeeks.length / 2));
    
    const firstHalfAvg = firstHalf.reduce((sum, count) => sum + count, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, count) => sum + count, 0) / secondHalf.length;
    
    if (firstHalfAvg > 0 && secondHalfAvg > 0) {
      trendScore = (secondHalfAvg - firstHalfAvg) / firstHalfAvg;
      trendScore = Math.max(-1, Math.min(1, trendScore));
    }
  }
  
  // Determine churn risk
  let churnRisk = CHURN_RISK.LOW;
  let churnProbability = 0;
  
  if (engagementScore < 30) {
    churnRisk = CHURN_RISK.VERY_HIGH;
    churnProbability = 0.9 - (engagementScore / 100);
  } else if (engagementScore < 50) {
    churnRisk = CHURN_RISK.HIGH;
    churnProbability = 0.7 - (engagementScore / 100);
  } else if (engagementScore < 70) {
    churnRisk = CHURN_RISK.MEDIUM;
    churnProbability = 0.5 - (engagementScore / 100);
  } else {
    churnRisk = CHURN_RISK.LOW;
    churnProbability = 0.3 - (engagementScore / 100);
  }
  
  // Adjust based on trend
  if (trendScore < -0.5) {
    // Significant downward trend
    churnProbability += 0.2;
    
    if (churnRisk === CHURN_RISK.LOW) {
      churnRisk = CHURN_RISK.MEDIUM;
    } else if (churnRisk === CHURN_RISK.MEDIUM) {
      churnRisk = CHURN_RISK.HIGH;
    } else if (churnRisk === CHURN_RISK.HIGH) {
      churnRisk = CHURN_RISK.VERY_HIGH;
    }
  } else if (trendScore < -0.2) {
    // Moderate downward trend
    churnProbability += 0.1;
  } else if (trendScore > 0.5) {
    // Significant upward trend
    churnProbability -= 0.2;
    
    if (churnRisk === CHURN_RISK.VERY_HIGH) {
      churnRisk = CHURN_RISK.HIGH;
    } else if (churnRisk === CHURN_RISK.HIGH) {
      churnRisk = CHURN_RISK.MEDIUM;
    } else if (churnRisk === CHURN_RISK.MEDIUM) {
      churnRisk = CHURN_RISK.LOW;
    }
  } else if (trendScore > 0.2) {
    // Moderate upward trend
    churnProbability -= 0.1;
  }
  
  // Ensure probability is between 0 and 1
  churnProbability = Math.max(0, Math.min(1, churnProbability));
  
  return {
    risk: churnRisk,
    probability: churnProbability,
    engagementScore,
    trendScore,
    activityTrend
  };
}

/**
 * Predict churn for a user
 * 
 * @param {string} userId - User ID
 * @returns {Object} - Churn prediction
 */
async function predictChurn(userId) {
  try {
    // Get user data
    const activityData = await getUserActivityData(userId);
    const sessionData = await getUserSessionData(userId);
    
    if (activityData.length === 0 || sessionData.length === 0) {
      return {
        userId,
        churnRisk: CHURN_RISK.VERY_HIGH,
        churnProbability: 0.95,
        engagementScore: 0,
        trendScore: 0,
        activityTrend: [],
        retentionStrategies: [
          'Send welcome email with getting started guide',
          'Offer onboarding assistance',
          'Provide free trial extension'
        ]
      };
    }
    
    // Calculate churn risk
    const churnAssessment = calculateChurnRisk(activityData, sessionData);
    
    // Generate retention strategies
    const retentionStrategies = generateRetentionStrategies(churnAssessment);
    
    return {
      userId,
      churnRisk: churnAssessment.risk,
      churnProbability: churnAssessment.probability,
      engagementScore: churnAssessment.engagementScore,
      trendScore: churnAssessment.trendScore,
      activityTrend: churnAssessment.activityTrend,
      retentionStrategies
    };
  } catch (error) {
    logger.error('Error in predictChurn', { error: error.message });
    
    return {
      userId,
      churnRisk: CHURN_RISK.MEDIUM,
      churnProbability: 0.5,
      engagementScore: 0,
      trendScore: 0,
      activityTrend: [],
      retentionStrategies: []
    };
  }
}

/**
 * Generate retention strategies
 * 
 * @param {Object} churnAssessment - Churn assessment
 * @returns {Array} - Retention strategies
 */
function generateRetentionStrategies(churnAssessment) {
  const strategies = [];
  
  if (churnAssessment.risk === CHURN_RISK.VERY_HIGH) {
    strategies.push(
      'Offer significant discount on renewal',
      'Personal outreach from account manager',
      'Targeted re-engagement campaign',
      'Exit survey to understand pain points'
    );
  } else if (churnAssessment.risk === CHURN_RISK.HIGH) {
    strategies.push(
      'Offer moderate discount on renewal',
      'Personalized feature highlight email',
      'Webinar invitation for advanced usage',
      'Check-in call from customer success'
    );
  } else if (churnAssessment.risk === CHURN_RISK.MEDIUM) {
    strategies.push(
      'Send usage tips and best practices',
      'Highlight unused features',
      'Offer training session',
      'Request feedback on product'
    );
  } else {
    strategies.push(
      'Loyalty rewards program',
      'Early access to new features',
      'Referral incentives',
      'Case study opportunity'
    );
  }
  
  return strategies;
}

// API endpoints
app.get('/api/user/:userId/churn-prediction', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const prediction = await predictChurn(userId);
    res.json(prediction);
  } catch (error) {
    logger.error('Error in GET /api/user/:userId/churn-prediction', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/users/high-churn-risk', async (req, res) => {
  try {
    // Get active users
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, name, created_at')
      .eq('status', 'active')
      .order('created_at', { ascending: false });
      
    if (error) {
      logger.error('Error fetching users', { error: error.message });
      return res.status(500).json({ error: 'Error fetching users' });
    }
    
    // Predict churn for each user
    const predictions = [];
    
    for (const user of users.slice(0, 100)) { // Limit to 100 users for performance
      const prediction = await predictChurn(user.id);
      
      if (prediction.churnRisk === CHURN_RISK.HIGH || prediction.churnRisk === CHURN_RISK.VERY_HIGH) {
        predictions.push({
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            created_at: user.created_at
          },
          prediction
        });
      }
    }
    
    // Sort by churn probability (highest first)
    predictions.sort((a, b) => b.prediction.churnProbability - a.prediction.churnProbability);
    
    res.json({ users: predictions });
  } catch (error) {
    logger.error('Error in GET /api/users/high-churn-risk', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.CHURN_PREDICTOR_PORT || 9104;
app.listen(PORT, () => {
  logger.info(`Churn Predictor listening on port ${PORT}`);
});

module.exports = {
  predictChurn,
  calculateChurnRisk,
  calculateEngagementScore,
  CHURN_RISK
};
