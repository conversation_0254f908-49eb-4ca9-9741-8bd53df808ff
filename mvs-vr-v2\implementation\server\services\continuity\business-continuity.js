/**
 * Business Continuity Service
 * 
 * This service implements business continuity features to ensure that
 * business operations can continue during technical failures.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { EventEmitter } = require('events');
const { Logger } = require('../integration/logger');
const { Counter, Gauge } = require('prom-client');

// Create logger
const logger = new Logger();

// Promisify functions
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);

// Create metrics
const businessImpactScore = new Gauge({
  name: 'business_impact_score',
  help: 'Business impact score for services',
  labelNames: ['service', 'component', 'severity']
});

const businessContinuityEvents = new Counter({
  name: 'business_continuity_events_total',
  help: 'Total number of business continuity events',
  labelNames: ['service', 'component', 'type', 'severity']
});

const recoveryTimeObjective = new Gauge({
  name: 'recovery_time_objective_seconds',
  help: 'Recovery time objective in seconds',
  labelNames: ['service', 'component']
});

const recoveryPointObjective = new Gauge({
  name: 'recovery_point_objective_seconds',
  help: 'Recovery point objective in seconds',
  labelNames: ['service', 'component']
});

/**
 * Business impact levels
 */
const IMPACT_LEVELS = {
  CRITICAL: 5,
  HIGH: 4,
  MEDIUM: 3,
  LOW: 2,
  MINIMAL: 1
};

/**
 * Service status
 */
const SERVICE_STATUS = {
  OPERATIONAL: 'operational',
  DEGRADED: 'degraded',
  OUTAGE: 'outage',
  MAINTENANCE: 'maintenance',
  UNKNOWN: 'unknown'
};

/**
 * Configuration
 */
const config = {
  configPath: process.env.BUSINESS_CONTINUITY_CONFIG_PATH || path.join(__dirname, '../../config/business-continuity.json'),
  reportPath: process.env.BUSINESS_CONTINUITY_REPORT_PATH || path.join(__dirname, '../../logs/business-continuity'),
  checkIntervalMs: parseInt(process.env.BUSINESS_CONTINUITY_CHECK_INTERVAL_MS || '300000', 10), // 5 minutes
  statusEndpoint: process.env.BUSINESS_CONTINUITY_STATUS_ENDPOINT || '/api/status'
};

/**
 * Business Continuity Service
 */
class BusinessContinuityService extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      ...config,
      ...options
    };
    
    this.services = [];
    this.dependencies = new Map();
    this.serviceStatus = new Map();
    this.businessMetrics = new Map();
    
    // Create directories
    mkdirAsync(this.options.reportPath, { recursive: true }).catch(err => {
      logger.error(`Error creating business continuity report directory: ${err.message}`, { error: err });
    });
    
    // Load configuration
    this.loadConfiguration().catch(err => {
      logger.error(`Error loading business continuity configuration: ${err.message}`, { error: err });
    });
    
    // Start check interval
    this.checkInterval = setInterval(() => {
      this.checkServiceHealth().catch(err => {
        logger.error(`Error checking service health: ${err.message}`, { error: err });
      });
    }, this.options.checkIntervalMs);
    
    logger.info('Business continuity service initialized');
  }
  
  /**
   * Load business continuity configuration
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    try {
      // Check if config file exists
      if (!fs.existsSync(this.options.configPath)) {
        logger.warn(`Business continuity configuration file not found: ${this.options.configPath}`);
        return;
      }
      
      // Read config file
      const configData = await readFileAsync(this.options.configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // Load services
      this.services = config.services || [];
      
      // Build dependency map
      this.buildDependencyMap();
      
      // Initialize service status
      for (const service of this.services) {
        this.serviceStatus.set(service.id, SERVICE_STATUS.UNKNOWN);
        
        // Set RTO and RPO metrics
        if (service.recoveryTimeObjective) {
          recoveryTimeObjective.set(
            { service: service.id, component: 'all' },
            service.recoveryTimeObjective
          );
        }
        
        if (service.recoveryPointObjective) {
          recoveryPointObjective.set(
            { service: service.id, component: 'all' },
            service.recoveryPointObjective
          );
        }
      }
      
      logger.info(`Loaded ${this.services.length} services from business continuity configuration`);
    } catch (error) {
      logger.error(`Error loading business continuity configuration: ${error.message}`, { error });
      throw error;
    }
  }
  
  /**
   * Build dependency map
   */
  buildDependencyMap() {
    this.dependencies.clear();
    
    for (const service of this.services) {
      if (!service.dependencies || service.dependencies.length === 0) {
        continue;
      }
      
      this.dependencies.set(service.id, service.dependencies);
      
      // Also track reverse dependencies (what depends on this service)
      for (const dependency of service.dependencies) {
        const reverseDeps = this.dependencies.get(`reverse:${dependency}`) || [];
        reverseDeps.push(service.id);
        this.dependencies.set(`reverse:${dependency}`, reverseDeps);
      }
    }
  }
  
  /**
   * Check service health
   * @returns {Promise<void>}
   */
  async checkServiceHealth() {
    logger.info('Checking service health');
    
    const statusChanges = [];
    
    for (const service of this.services) {
      try {
        // Get current status
        const currentStatus = this.serviceStatus.get(service.id);
        
        // Check service health
        const newStatus = await this.checkServiceStatus(service);
        
        // Update status if changed
        if (currentStatus !== newStatus) {
          this.serviceStatus.set(service.id, newStatus);
          
          statusChanges.push({
            serviceId: service.id,
            serviceName: service.name,
            previousStatus: currentStatus,
            newStatus,
            timestamp: new Date().toISOString()
          });
          
          // Calculate business impact
          const impact = this.calculateBusinessImpact(service, newStatus);
          
          // Track metrics
          businessImpactScore.set(
            { service: service.id, component: 'all', severity: impact.severity },
            impact.score
          );
          
          businessContinuityEvents.inc({
            service: service.id,
            component: 'all',
            type: 'status-change',
            severity: impact.severity
          });
          
          // Emit event
          this.emit('serviceStatusChange', {
            service,
            previousStatus: currentStatus,
            newStatus,
            impact
          });
        }
      } catch (error) {
        logger.error(`Error checking health for service ${service.id}: ${error.message}`, { error });
      }
    }
    
    // If there were status changes, generate a report
    if (statusChanges.length > 0) {
      await this.generateStatusReport(statusChanges);
    }
  }
  
  /**
   * Check service status
   * @param {Object} service - Service to check
   * @returns {Promise<string>} Service status
   */
  async checkServiceStatus(service) {
    // This is a simplified implementation
    // In a real implementation, we would check the actual service status
    
    // For now, return a random status
    const statuses = Object.values(SERVICE_STATUS);
    const randomIndex = Math.floor(Math.random() * statuses.length);
    
    return statuses[randomIndex];
  }
  
  /**
   * Calculate business impact
   * @param {Object} service - Service
   * @param {string} status - Service status
   * @returns {Object} Business impact
   */
  calculateBusinessImpact(service, status) {
    let impactLevel = IMPACT_LEVELS.MINIMAL;
    let impactSeverity = 'info';
    
    switch (status) {
      case SERVICE_STATUS.OPERATIONAL:
        impactLevel = IMPACT_LEVELS.MINIMAL;
        impactSeverity = 'info';
        break;
        
      case SERVICE_STATUS.DEGRADED:
        impactLevel = service.businessImpact?.degraded || IMPACT_LEVELS.MEDIUM;
        impactSeverity = 'warning';
        break;
        
      case SERVICE_STATUS.OUTAGE:
        impactLevel = service.businessImpact?.outage || IMPACT_LEVELS.HIGH;
        impactSeverity = 'error';
        break;
        
      case SERVICE_STATUS.MAINTENANCE:
        impactLevel = service.businessImpact?.maintenance || IMPACT_LEVELS.LOW;
        impactSeverity = 'info';
        break;
        
      default:
        impactLevel = IMPACT_LEVELS.MINIMAL;
        impactSeverity = 'info';
    }
    
    // Calculate impact score based on service priority
    const priorityMultiplier = service.priority || 1;
    const impactScore = impactLevel * priorityMultiplier;
    
    return {
      level: impactLevel,
      score: impactScore,
      severity: impactSeverity,
      status
    };
  }
  
  /**
   * Generate status report
   * @param {Array<Object>} statusChanges - Status changes
   * @returns {Promise<void>}
   */
  async generateStatusReport(statusChanges) {
    try {
      // Create timestamp-based filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `status-report-${timestamp}.json`;
      const filePath = path.join(this.options.reportPath, filename);
      
      // Create report
      const report = {
        timestamp: new Date().toISOString(),
        statusChanges,
        serviceStatus: Object.fromEntries(this.serviceStatus),
        businessMetrics: Object.fromEntries(this.businessMetrics)
      };
      
      // Save report
      await writeFileAsync(filePath, JSON.stringify(report, null, 2));
      
      logger.info(`Generated business continuity status report: ${filePath}`);
    } catch (error) {
      logger.error(`Error generating status report: ${error.message}`, { error });
    }
  }
  
  /**
   * Update business metrics
   * @param {string} metricName - Metric name
   * @param {number} value - Metric value
   * @param {Object} labels - Metric labels
   */
  updateBusinessMetric(metricName, value, labels = {}) {
    const key = `${metricName}:${JSON.stringify(labels)}`;
    
    this.businessMetrics.set(key, {
      name: metricName,
      value,
      labels,
      timestamp: new Date().toISOString()
    });
    
    logger.debug(`Updated business metric: ${metricName}=${value}`, { labels });
  }
  
  /**
   * Get service status
   * @param {string} serviceId - Service ID
   * @returns {string} Service status
   */
  getServiceStatus(serviceId) {
    return this.serviceStatus.get(serviceId) || SERVICE_STATUS.UNKNOWN;
  }
  
  /**
   * Get all service statuses
   * @returns {Object} Service statuses
   */
  getAllServiceStatuses() {
    return Object.fromEntries(this.serviceStatus);
  }
  
  /**
   * Get business metrics
   * @returns {Object} Business metrics
   */
  getBusinessMetrics() {
    return Object.fromEntries(this.businessMetrics);
  }
  
  /**
   * Stop service
   */
  stop() {
    clearInterval(this.checkInterval);
    logger.info('Business continuity service stopped');
  }
}

// Export singleton instance
let instance = null;

/**
 * Get business continuity service instance
 * @param {Object} options - Service options
 * @returns {BusinessContinuityService} Service instance
 */
function getBusinessContinuityService(options = {}) {
  if (!instance) {
    instance = new BusinessContinuityService(options);
  }
  
  return instance;
}

module.exports = {
  BusinessContinuityService,
  getBusinessContinuityService,
  IMPACT_LEVELS,
  SERVICE_STATUS
};
