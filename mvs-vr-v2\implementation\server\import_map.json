{"imports": {"vitest": "npm:vitest", "vitest/": "npm:vitest/", "@testing-library/": "npm:@testing-library/", "node:": "./node_modules/", "@/": "./src/", "@directus/": "./directus/extensions/", "@shared/": "./shared/", "@services/": "./services/", "@tests/": "./tests/", "@setup/": "./tests/setup/", "path": "https://deno.land/std@0.181.0/path/mod.ts", "fs": "https://deno.land/std@0.181.0/fs/mod.ts", "util": "node:util", "express": "npm:express", "papaparse": "npm:papa<PERSON><PERSON>", "pdfkit": "npm:pdfkit", "exceljs": "npm:exceljs", "zod": "npm:zod"}}