/**
 * Authentication System Monitor
 * 
 * This service monitors the authentication system, tracking login attempts,
 * success/failure rates, MFA usage, token refreshes, and password reset flows.
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const { triggerAlert } = require('./alert-manager');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({ register });

// Create custom metrics for authentication monitoring
const metrics = {
  // Login metrics
  loginAttempts: new promClient.Counter({
    name: 'mvs_vr_auth_login_attempts_total',
    help: 'Total number of login attempts',
    labelNames: ['status', 'user_type']
  }),
  
  loginResponseTime: new promClient.Histogram({
    name: 'mvs_vr_auth_login_response_time_ms',
    help: 'Login response time in milliseconds',
    labelNames: ['status', 'user_type'],
    buckets: [50, 100, 200, 500, 1000, 2000, 5000]
  }),
  
  // MFA metrics
  mfaUsage: new promClient.Counter({
    name: 'mvs_vr_auth_mfa_usage_total',
    help: 'Total number of MFA verifications',
    labelNames: ['status', 'method']
  }),
  
  mfaEnrollmentRate: new promClient.Gauge({
    name: 'mvs_vr_auth_mfa_enrollment_rate',
    help: 'Percentage of users with MFA enabled',
    labelNames: ['user_type']
  }),
  
  // Token metrics
  tokenRefreshes: new promClient.Counter({
    name: 'mvs_vr_auth_token_refreshes_total',
    help: 'Total number of token refresh operations',
    labelNames: ['status']
  }),
  
  tokenRefreshResponseTime: new promClient.Histogram({
    name: 'mvs_vr_auth_token_refresh_response_time_ms',
    help: 'Token refresh response time in milliseconds',
    labelNames: ['status'],
    buckets: [50, 100, 200, 500, 1000, 2000]
  }),
  
  // Password reset metrics
  passwordResets: new promClient.Counter({
    name: 'mvs_vr_auth_password_resets_total',
    help: 'Total number of password reset requests',
    labelNames: ['status', 'stage']
  }),
  
  // Session metrics
  activeSessions: new promClient.Gauge({
    name: 'mvs_vr_auth_active_sessions',
    help: 'Number of active user sessions',
    labelNames: ['user_type']
  }),
  
  sessionDuration: new promClient.Histogram({
    name: 'mvs_vr_auth_session_duration_seconds',
    help: 'Session duration in seconds',
    labelNames: ['user_type'],
    buckets: [60, 300, 900, 1800, 3600, 7200, 14400, 28800]
  })
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Process authentication events
 * 
 * @param {Object} data - Authentication event data
 */
async function processAuthEvent(data) {
  try {
    const { event, user, metadata } = data;
    const userType = user?.user_metadata?.role || 'unknown';
    
    // Process based on event type
    switch (event) {
      case 'SIGNED_IN':
        metrics.loginAttempts.inc({ status: 'success', user_type: userType });
        metrics.loginResponseTime.observe({ status: 'success', user_type: userType }, metadata.response_time || 0);
        break;
        
      case 'SIGNED_UP':
        // Track new user registrations
        break;
        
      case 'PASSWORD_RECOVERY':
        metrics.passwordResets.inc({ status: 'requested', stage: 'request' });
        break;
        
      case 'PASSWORD_RESET':
        metrics.passwordResets.inc({ status: 'completed', stage: 'reset' });
        break;
        
      case 'TOKEN_REFRESHED':
        metrics.tokenRefreshes.inc({ status: 'success' });
        metrics.tokenRefreshResponseTime.observe({ status: 'success' }, metadata.response_time || 0);
        break;
        
      case 'MFA_CHALLENGE_VERIFIED':
        metrics.mfaUsage.inc({ status: 'success', method: metadata.method || 'unknown' });
        break;
    }
    
    logger.debug('Processed auth event', { event });
  } catch (error) {
    logger.error('Error processing auth event', { error: error.message });
  }
}

/**
 * Update authentication metrics
 */
async function updateAuthMetrics() {
  try {
    // Get active sessions count
    const { data: sessions, error: sessionsError } = await supabase
      .from('user_sessions')
      .select('user_id, user_type')
      .eq('is_active', true);
      
    if (sessionsError) {
      logger.error('Error fetching active sessions', { error: sessionsError.message });
    } else {
      // Group sessions by user type
      const sessionsByType = {};
      sessions.forEach(session => {
        const userType = session.user_type || 'unknown';
        sessionsByType[userType] = (sessionsByType[userType] || 0) + 1;
      });
      
      // Update metrics
      Object.entries(sessionsByType).forEach(([userType, count]) => {
        metrics.activeSessions.set({ user_type: userType }, count);
      });
    }
    
    // Get MFA enrollment rate
    const { data: mfaStats, error: mfaError } = await supabase
      .rpc('get_mfa_enrollment_stats');
      
    if (mfaError) {
      logger.error('Error fetching MFA stats', { error: mfaError.message });
    } else if (mfaStats) {
      Object.entries(mfaStats).forEach(([userType, rate]) => {
        metrics.mfaEnrollmentRate.set({ user_type: userType }, rate);
      });
    }
    
    logger.debug('Updated auth metrics');
  } catch (error) {
    logger.error('Error updating auth metrics', { error: error.message });
  }
}

// Create Express app
const app = express();
app.use(cors());
app.use(express.json());

// API endpoint for receiving auth events
app.post('/api/events', async (req, res) => {
  try {
    const data = req.body;
    
    // Validate required fields
    if (!data.event) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Process event
    await processAuthEvent(data);
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/events', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    // Update metrics before responding
    await updateAuthMetrics();
    
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving auth metrics', { error: error.message });
    res.status(500).send('Error collecting auth metrics');
  }
});

// Start server
const PORT = process.env.AUTH_MONITOR_PORT || 9101;
app.listen(PORT, () => {
  logger.info(`Auth Monitor listening on port ${PORT}`);
  
  // Schedule regular metrics updates
  setInterval(updateAuthMetrics, 60000); // Update every minute
});

module.exports = {
  processAuthEvent,
  updateAuthMetrics
};
