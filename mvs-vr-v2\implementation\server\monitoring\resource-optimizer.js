/**
 * Resource Optimizer
 * 
 * This service analyzes resource usage patterns and optimizes resource allocation
 * based on predictions of future demand.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const axios = require('axios');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Resource types
const RESOURCE_TYPE = {
  CPU: 'cpu',
  MEMORY: 'memory',
  DISK: 'disk',
  NETWORK: 'network',
  DATABASE: 'database'
};

// Service types
const SERVICE_TYPE = {
  API: 'api',
  FRONTEND: 'frontend',
  BACKEND: 'backend',
  DATABASE: 'database',
  CACHE: 'cache',
  STORAGE: 'storage',
  MONITORING: 'monitoring'
};

// Optimization strategies
const OPTIMIZATION_STRATEGY = {
  SCALE_UP: 'scale_up',
  SCALE_DOWN: 'scale_down',
  REDISTRIBUTE: 'redistribute',
  CACHE: 'cache',
  THROTTLE: 'throttle',
  NONE: 'none'
};

/**
 * Get resource usage data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Resource usage data
 */
async function getResourceUsageData(days = 7) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('system_metrics')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: true });
      
    if (error) {
      logger.error('Error fetching resource usage data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getResourceUsageData', { error: error.message });
    return [];
  }
}

/**
 * Get service metrics data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Service metrics data
 */
async function getServiceMetricsData(days = 7) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('service_metrics')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: true });
      
    if (error) {
      logger.error('Error fetching service metrics data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getServiceMetricsData', { error: error.message });
    return [];
  }
}

/**
 * Get user activity data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User activity data
 */
async function getUserActivityData(days = 7) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Get current resource allocation
 * 
 * @returns {Object} - Current resource allocation
 */
async function getCurrentResourceAllocation() {
  try {
    const { data, error } = await supabase
      .from('resource_allocation')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(1);
      
    if (error) {
      logger.error('Error fetching current resource allocation', { error: error.message });
      return null;
    }
    
    return data && data.length > 0 ? data[0] : null;
  } catch (error) {
    logger.error('Error in getCurrentResourceAllocation', { error: error.message });
    return null;
  }
}

/**
 * Predict resource usage
 * 
 * @param {Array} resourceData - Resource usage data
 * @param {Array} serviceData - Service metrics data
 * @param {Array} activityData - User activity data
 * @returns {Object} - Predicted resource usage
 */
function predictResourceUsage(resourceData, serviceData, activityData) {
  try {
    if (resourceData.length === 0 || serviceData.length === 0) {
      return {
        cpu: 0.5,
        memory: 0.5,
        disk: 0.5,
        network: 0.5,
        database: 0.5,
        confidence: 0.3
      };
    }
    
    // Group resource data by hour
    const resourceByHour = {};
    
    resourceData.forEach(metric => {
      const date = new Date(metric.timestamp);
      const hourKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
      
      if (!resourceByHour[hourKey]) {
        resourceByHour[hourKey] = {
          cpu: [],
          memory: [],
          disk: [],
          network_in: [],
          network_out: []
        };
      }
      
      resourceByHour[hourKey].cpu.push(metric.cpu_usage);
      resourceByHour[hourKey].memory.push(metric.memory_usage);
      resourceByHour[hourKey].disk.push(metric.disk_usage);
      resourceByHour[hourKey].network_in.push(metric.network_in);
      resourceByHour[hourKey].network_out.push(metric.network_out);
    });
    
    // Calculate average resource usage by hour
    const avgResourceByHour = {};
    
    Object.entries(resourceByHour).forEach(([hourKey, resources]) => {
      avgResourceByHour[hourKey] = {
        cpu: resources.cpu.reduce((sum, val) => sum + val, 0) / resources.cpu.length,
        memory: resources.memory.reduce((sum, val) => sum + val, 0) / resources.memory.length,
        disk: resources.disk.reduce((sum, val) => sum + val, 0) / resources.disk.length,
        network: (
          resources.network_in.reduce((sum, val) => sum + val, 0) / resources.network_in.length +
          resources.network_out.reduce((sum, val) => sum + val, 0) / resources.network_out.length
        ) / 2
      };
    });
    
    // Group service data by hour
    const serviceByHour = {};
    
    serviceData.forEach(metric => {
      const date = new Date(metric.timestamp);
      const hourKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
      
      if (!serviceByHour[hourKey]) {
        serviceByHour[hourKey] = {
          api_requests: [],
          database_queries: [],
          cache_hits: [],
          cache_misses: []
        };
      }
      
      serviceByHour[hourKey].api_requests.push(metric.api_requests);
      serviceByHour[hourKey].database_queries.push(metric.database_queries);
      serviceByHour[hourKey].cache_hits.push(metric.cache_hits);
      serviceByHour[hourKey].cache_misses.push(metric.cache_misses);
    });
    
    // Calculate average service metrics by hour
    const avgServiceByHour = {};
    
    Object.entries(serviceByHour).forEach(([hourKey, metrics]) => {
      avgServiceByHour[hourKey] = {
        api_requests: metrics.api_requests.reduce((sum, val) => sum + val, 0) / metrics.api_requests.length,
        database_queries: metrics.database_queries.reduce((sum, val) => sum + val, 0) / metrics.database_queries.length,
        cache_hits: metrics.cache_hits.reduce((sum, val) => sum + val, 0) / metrics.cache_hits.length,
        cache_misses: metrics.cache_misses.reduce((sum, val) => sum + val, 0) / metrics.cache_misses.length
      };
    });
    
    // Group activity data by hour
    const activityByHour = {};
    
    activityData.forEach(activity => {
      const date = new Date(activity.created_at);
      const hourKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
      
      if (!activityByHour[hourKey]) {
        activityByHour[hourKey] = 0;
      }
      
      activityByHour[hourKey]++;
    });
    
    // Get current hour
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Calculate average resource usage for current hour of day and day of week
    const hourlyData = [];
    const dayOfWeekData = [];
    
    Object.entries(avgResourceByHour).forEach(([hourKey, resources]) => {
      const [year, month, day, hour] = hourKey.split('-').map(Number);
      const date = new Date(year, month, day, hour);
      
      if (date.getHours() === currentHour) {
        hourlyData.push(resources);
      }
      
      if (date.getDay() === currentDay) {
        dayOfWeekData.push(resources);
      }
    });
    
    // Calculate predictions based on historical patterns
    const hourlyPrediction = {
      cpu: hourlyData.reduce((sum, res) => sum + res.cpu, 0) / hourlyData.length,
      memory: hourlyData.reduce((sum, res) => sum + res.memory, 0) / hourlyData.length,
      disk: hourlyData.reduce((sum, res) => sum + res.disk, 0) / hourlyData.length,
      network: hourlyData.reduce((sum, res) => sum + res.network, 0) / hourlyData.length
    };
    
    const dayOfWeekPrediction = {
      cpu: dayOfWeekData.reduce((sum, res) => sum + res.cpu, 0) / dayOfWeekData.length,
      memory: dayOfWeekData.reduce((sum, res) => sum + res.memory, 0) / dayOfWeekData.length,
      disk: dayOfWeekData.reduce((sum, res) => sum + res.disk, 0) / dayOfWeekData.length,
      network: dayOfWeekData.reduce((sum, res) => sum + res.network, 0) / dayOfWeekData.length
    };
    
    // Calculate recent trend
    const recentData = resourceData.slice(-24); // Last 24 data points
    
    if (recentData.length < 2) {
      return {
        cpu: hourlyPrediction.cpu,
        memory: hourlyPrediction.memory,
        disk: hourlyPrediction.disk,
        network: hourlyPrediction.network,
        database: hourlyPrediction.database || 0.5,
        confidence: 0.5
      };
    }
    
    const firstHalf = recentData.slice(0, Math.floor(recentData.length / 2));
    const secondHalf = recentData.slice(Math.floor(recentData.length / 2));
    
    const firstHalfAvg = {
      cpu: firstHalf.reduce((sum, metric) => sum + metric.cpu_usage, 0) / firstHalf.length,
      memory: firstHalf.reduce((sum, metric) => sum + metric.memory_usage, 0) / firstHalf.length,
      disk: firstHalf.reduce((sum, metric) => sum + metric.disk_usage, 0) / firstHalf.length,
      network: firstHalf.reduce((sum, metric) => sum + (metric.network_in + metric.network_out) / 2, 0) / firstHalf.length
    };
    
    const secondHalfAvg = {
      cpu: secondHalf.reduce((sum, metric) => sum + metric.cpu_usage, 0) / secondHalf.length,
      memory: secondHalf.reduce((sum, metric) => sum + metric.memory_usage, 0) / secondHalf.length,
      disk: secondHalf.reduce((sum, metric) => sum + metric.disk_usage, 0) / secondHalf.length,
      network: secondHalf.reduce((sum, metric) => sum + (metric.network_in + metric.network_out) / 2, 0) / secondHalf.length
    };
    
    const trend = {
      cpu: secondHalfAvg.cpu - firstHalfAvg.cpu,
      memory: secondHalfAvg.memory - firstHalfAvg.memory,
      disk: secondHalfAvg.disk - firstHalfAvg.disk,
      network: secondHalfAvg.network - firstHalfAvg.network
    };
    
    // Calculate database usage prediction based on service metrics
    const recentServiceData = serviceData.slice(-24); // Last 24 data points
    
    let databasePrediction = 0.5;
    
    if (recentServiceData.length > 0) {
      const avgDatabaseQueries = recentServiceData.reduce((sum, metric) => sum + metric.database_queries, 0) / recentServiceData.length;
      const maxDatabaseQueries = Math.max(...recentServiceData.map(metric => metric.database_queries));
      
      databasePrediction = avgDatabaseQueries / maxDatabaseQueries;
    }
    
    // Combine predictions
    const prediction = {
      cpu: hourlyPrediction.cpu + trend.cpu,
      memory: hourlyPrediction.memory + trend.memory,
      disk: hourlyPrediction.disk + trend.disk,
      network: hourlyPrediction.network + trend.network,
      database: databasePrediction,
      confidence: 0.7
    };
    
    // Clamp values between 0 and 1
    prediction.cpu = Math.max(0, Math.min(1, prediction.cpu));
    prediction.memory = Math.max(0, Math.min(1, prediction.memory));
    prediction.disk = Math.max(0, Math.min(1, prediction.disk));
    prediction.network = Math.max(0, Math.min(1, prediction.network));
    prediction.database = Math.max(0, Math.min(1, prediction.database));
    
    return prediction;
  } catch (error) {
    logger.error('Error in predictResourceUsage', { error: error.message });
    
    return {
      cpu: 0.5,
      memory: 0.5,
      disk: 0.5,
      network: 0.5,
      database: 0.5,
      confidence: 0.3
    };
  }
}

/**
 * Generate resource optimization recommendations
 * 
 * @param {Object} prediction - Predicted resource usage
 * @param {Object} currentAllocation - Current resource allocation
 * @returns {Array} - Optimization recommendations
 */
function generateOptimizationRecommendations(prediction, currentAllocation) {
  try {
    if (!prediction || !currentAllocation) {
      return [];
    }
    
    const recommendations = [];
    
    // CPU optimization
    if (prediction.cpu > 0.8) {
      recommendations.push({
        resource: RESOURCE_TYPE.CPU,
        current_usage: prediction.cpu,
        current_allocation: currentAllocation.cpu_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_UP,
        target_allocation: Math.min(1, currentAllocation.cpu_allocation * 1.2),
        priority: 'high',
        reason: `High CPU usage predicted (${Math.round(prediction.cpu * 100)}%)`
      });
    } else if (prediction.cpu < 0.3 && currentAllocation.cpu_allocation > 0.4) {
      recommendations.push({
        resource: RESOURCE_TYPE.CPU,
        current_usage: prediction.cpu,
        current_allocation: currentAllocation.cpu_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_DOWN,
        target_allocation: Math.max(0.3, currentAllocation.cpu_allocation * 0.8),
        priority: 'medium',
        reason: `Low CPU usage predicted (${Math.round(prediction.cpu * 100)}%)`
      });
    }
    
    // Memory optimization
    if (prediction.memory > 0.8) {
      recommendations.push({
        resource: RESOURCE_TYPE.MEMORY,
        current_usage: prediction.memory,
        current_allocation: currentAllocation.memory_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_UP,
        target_allocation: Math.min(1, currentAllocation.memory_allocation * 1.2),
        priority: 'high',
        reason: `High memory usage predicted (${Math.round(prediction.memory * 100)}%)`
      });
    } else if (prediction.memory < 0.3 && currentAllocation.memory_allocation > 0.4) {
      recommendations.push({
        resource: RESOURCE_TYPE.MEMORY,
        current_usage: prediction.memory,
        current_allocation: currentAllocation.memory_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_DOWN,
        target_allocation: Math.max(0.3, currentAllocation.memory_allocation * 0.8),
        priority: 'medium',
        reason: `Low memory usage predicted (${Math.round(prediction.memory * 100)}%)`
      });
    }
    
    // Disk optimization
    if (prediction.disk > 0.8) {
      recommendations.push({
        resource: RESOURCE_TYPE.DISK,
        current_usage: prediction.disk,
        current_allocation: currentAllocation.disk_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_UP,
        target_allocation: Math.min(1, currentAllocation.disk_allocation * 1.2),
        priority: 'high',
        reason: `High disk usage predicted (${Math.round(prediction.disk * 100)}%)`
      });
    }
    
    // Network optimization
    if (prediction.network > 0.8) {
      recommendations.push({
        resource: RESOURCE_TYPE.NETWORK,
        current_usage: prediction.network,
        current_allocation: currentAllocation.network_allocation,
        strategy: OPTIMIZATION_STRATEGY.CACHE,
        target_allocation: currentAllocation.network_allocation,
        priority: 'medium',
        reason: `High network usage predicted (${Math.round(prediction.network * 100)}%)`
      });
    }
    
    // Database optimization
    if (prediction.database > 0.8) {
      recommendations.push({
        resource: RESOURCE_TYPE.DATABASE,
        current_usage: prediction.database,
        current_allocation: currentAllocation.database_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_UP,
        target_allocation: Math.min(1, currentAllocation.database_allocation * 1.2),
        priority: 'high',
        reason: `High database usage predicted (${Math.round(prediction.database * 100)}%)`
      });
    } else if (prediction.database < 0.3 && currentAllocation.database_allocation > 0.4) {
      recommendations.push({
        resource: RESOURCE_TYPE.DATABASE,
        current_usage: prediction.database,
        current_allocation: currentAllocation.database_allocation,
        strategy: OPTIMIZATION_STRATEGY.SCALE_DOWN,
        target_allocation: Math.max(0.3, currentAllocation.database_allocation * 0.8),
        priority: 'low',
        reason: `Low database usage predicted (${Math.round(prediction.database * 100)}%)`
      });
    }
    
    // Sort recommendations by priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
    
    return recommendations;
  } catch (error) {
    logger.error('Error in generateOptimizationRecommendations', { error: error.message });
    return [];
  }
}

/**
 * Apply resource optimization
 * 
 * @param {Array} recommendations - Optimization recommendations
 * @returns {Object} - Optimization result
 */
async function applyResourceOptimization(recommendations) {
  try {
    if (recommendations.length === 0) {
      return {
        success: true,
        message: 'No optimizations needed',
        changes: []
      };
    }
    
    // Get current allocation
    const currentAllocation = await getCurrentResourceAllocation();
    
    if (!currentAllocation) {
      throw new Error('Failed to get current resource allocation');
    }
    
    // Apply recommendations
    const newAllocation = {
      ...currentAllocation,
      updated_at: new Date().toISOString()
    };
    
    const changes = [];
    
    recommendations.forEach(recommendation => {
      switch (recommendation.resource) {
        case RESOURCE_TYPE.CPU:
          newAllocation.cpu_allocation = recommendation.target_allocation;
          changes.push({
            resource: RESOURCE_TYPE.CPU,
            from: currentAllocation.cpu_allocation,
            to: recommendation.target_allocation,
            strategy: recommendation.strategy
          });
          break;
        case RESOURCE_TYPE.MEMORY:
          newAllocation.memory_allocation = recommendation.target_allocation;
          changes.push({
            resource: RESOURCE_TYPE.MEMORY,
            from: currentAllocation.memory_allocation,
            to: recommendation.target_allocation,
            strategy: recommendation.strategy
          });
          break;
        case RESOURCE_TYPE.DISK:
          newAllocation.disk_allocation = recommendation.target_allocation;
          changes.push({
            resource: RESOURCE_TYPE.DISK,
            from: currentAllocation.disk_allocation,
            to: recommendation.target_allocation,
            strategy: recommendation.strategy
          });
          break;
        case RESOURCE_TYPE.NETWORK:
          newAllocation.network_allocation = recommendation.target_allocation;
          changes.push({
            resource: RESOURCE_TYPE.NETWORK,
            from: currentAllocation.network_allocation,
            to: recommendation.target_allocation,
            strategy: recommendation.strategy
          });
          break;
        case RESOURCE_TYPE.DATABASE:
          newAllocation.database_allocation = recommendation.target_allocation;
          changes.push({
            resource: RESOURCE_TYPE.DATABASE,
            from: currentAllocation.database_allocation,
            to: recommendation.target_allocation,
            strategy: recommendation.strategy
          });
          break;
      }
    });
    
    // Save new allocation
    const { error } = await supabase
      .from('resource_allocation')
      .insert(newAllocation);
      
    if (error) {
      throw error;
    }
    
    // Apply changes to infrastructure
    await applyInfrastructureChanges(changes);
    
    return {
      success: true,
      message: `Applied ${changes.length} optimizations`,
      changes
    };
  } catch (error) {
    logger.error('Error in applyResourceOptimization', { error: error.message });
    
    return {
      success: false,
      message: `Error applying optimizations: ${error.message}`,
      changes: []
    };
  }
}

/**
 * Apply infrastructure changes
 * 
 * @param {Array} changes - Infrastructure changes
 */
async function applyInfrastructureChanges(changes) {
  try {
    // This would integrate with your infrastructure management system
    // For now, we'll just log the changes
    logger.info('Applying infrastructure changes', { changes });
    
    // Example: Call Kubernetes API to scale resources

    // 

    //               name: 'mvs-vr',

m` : undefined,
Mi` : undefined
    //                 }
    //               }
    //             }]
    //           }
    //         }
    //       }
    //     });
    //   }
    // }
  } catch (error) {
    logger.error('Error in applyInfrastructureChanges', { error: error.message });
    throw error;
  }
}

/**
 * Optimize resources
 * 
 * @param {boolean} apply - Whether to apply the optimizations
 * @returns {Object} - Optimization result
 */
async function optimizeResources(apply = false) {
  try {
    // Get data
    const resourceData = await getResourceUsageData();
    const serviceData = await getServiceMetricsData();
    const activityData = await getUserActivityData();
    const currentAllocation = await getCurrentResourceAllocation();
    
    if (!currentAllocation) {
      throw new Error('Failed to get current resource allocation');
    }
    
    // Predict resource usage
    const prediction = predictResourceUsage(resourceData, serviceData, activityData);
    
    // Generate optimization recommendations
    const recommendations = generateOptimizationRecommendations(prediction, currentAllocation);
    
    // Apply optimizations if requested
    if (apply && recommendations.length > 0) {
      const result = await applyResourceOptimization(recommendations);
      
      return {
        prediction,
        recommendations,
        applied: result
      };
    }
    
    return {
      prediction,
      recommendations,
      applied: null
    };
  } catch (error) {
    logger.error('Error in optimizeResources', { error: error.message });
    
    return {
      error: error.message,
      prediction: null,
      recommendations: [],
      applied: null
    };
  }
}

// API endpoints
app.get('/api/resource-prediction', async (req, res) => {
  try {
    const resourceData = await getResourceUsageData();
    const serviceData = await getServiceMetricsData();
    const activityData = await getUserActivityData();
    
    const prediction = predictResourceUsage(resourceData, serviceData, activityData);
    res.json({ prediction });
  } catch (error) {
    logger.error('Error in GET /api/resource-prediction', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/resource-recommendations', async (req, res) => {
  try {
    const resourceData = await getResourceUsageData();
    const serviceData = await getServiceMetricsData();
    const activityData = await getUserActivityData();
    const currentAllocation = await getCurrentResourceAllocation();
    
    if (!currentAllocation) {
      return res.status(404).json({ error: 'Current resource allocation not found' });
    }
    
    const prediction = predictResourceUsage(resourceData, serviceData, activityData);
    const recommendations = generateOptimizationRecommendations(prediction, currentAllocation);
    
    res.json({ prediction, recommendations });
  } catch (error) {
    logger.error('Error in GET /api/resource-recommendations', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/optimize-resources', async (req, res) => {
  try {
    const { apply = false } = req.body;
    
    const result = await optimizeResources(apply);
    res.json(result);
  } catch (error) {
    logger.error('Error in POST /api/optimize-resources', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.RESOURCE_OPTIMIZER_PORT || 9109;
app.listen(PORT, () => {
  logger.info(`Resource Optimizer listening on port ${PORT}`);
});

module.exports = {
  predictResourceUsage,
  generateOptimizationRecommendations,
  optimizeResources
};
