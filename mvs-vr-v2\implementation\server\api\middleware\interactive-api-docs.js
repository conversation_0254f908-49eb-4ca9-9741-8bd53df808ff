/**
 * Interactive API Documentation Middleware
 *
 * This middleware serves interactive API documentation using Stoplight Elements.
 * It provides a more user-friendly and interactive experience compared to the
 * standard Swagger UI, with features like:
 * - Interactive API explorer
 * - Code snippets in multiple languages
 * - Authentication flow documentation
 * - Request/response examples
 * - Schema visualization
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const YAML = require('yamljs');
const SwaggerParser = require('swagger-parser');
const { logger } = require('./auth-middleware');

/**
 * Setup interactive API documentation
 * @param {Object} app - Express app
 * @param {Object} options - Documentation options
 */
function setupInteractiveApiDocs(app, options = {}) {
  const {
    basePath = '/api-docs/interactive',
    yamlPath = path.join(__dirname, '../docs/openapi.yaml'),
    elementsOptions = {
      router: 'hash',
      layout: 'sidebar',
    },
  } = options;

  try {
    // Load OpenAPI specification
    const openApiSpec = YAML.load(yamlPath);

    // Add server URL if not already present
    if (!openApiSpec.servers || openApiSpec.servers.length === 0) {
      openApiSpec.servers = [
        {
          url: process.env.API_BASE_URL || 'http://localhost:3000',
          description: 'Current environment',
        },
      ];
    }

    // Create HTML template for Stoplight Elements
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MVS-VR API Documentation</title>
  <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements/styles.min.css">
  <script src="https://unpkg.com/@stoplight/elements/web-components.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    .api-docs-header {
      background-color: #1a202c;
      color: white;
      padding: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .api-docs-header h1 {
      margin: 0;
      font-size: 1.5rem;
    }
    .api-docs-header .links {
      display: flex;
      gap: 1rem;
    }
    .api-docs-header .links a {
      color: white;
      text-decoration: none;
    }
    .api-docs-header .links a:hover {
      text-decoration: underline;
    }
    elements-api {
      display: block;
      height: calc(100vh - 60px);
    }
  </style>
</head>
<body>
  <div class="api-docs-header">
    <h1>MVS-VR API Documentation</h1>
    <div class="links">
      <a href="/api-docs">Swagger UI</a>
      <a href="/api-docs/openapi.yaml">OpenAPI Spec (YAML)</a>
      <a href="/api-docs/openapi.json">OpenAPI Spec (JSON)</a>
    </div>
  </div>
  <elements-api
    apiDescriptionUrl="/api-docs/openapi.json"
    router="${elementsOptions.router}"
    layout="${elementsOptions.layout}"
    hideExport="true"
    hideInternal="true"
  />
</body>
</html>
    `;

    // Serve the interactive documentation
    app.get(basePath, (req, res) => {
      res.setHeader('Content-Type', 'text/html');
      res.send(htmlTemplate);
    });

    // Validate OpenAPI specification
    validateOpenApiSpec(yamlPath)
      .then(result => {
        if (result.valid) {
          logger.info(`OpenAPI specification validated successfully: ${yamlPath}`);
        } else {
          logger.warn(`OpenAPI specification validation failed: ${result.errors.join(', ')}`);
        }
      })
      .catch(error => {
        logger.error(`Error validating OpenAPI specification: ${error.message}`);
      });

    logger.info(`Interactive API documentation available at ${basePath}`);
  } catch (error) {
    logger.error('Error setting up interactive API documentation:', error);
  }
}

/**
 * Validate OpenAPI specification
 * @param {string} specPath - Path to OpenAPI specification
 * @returns {Promise<Object>} Validation result
 */
async function validateOpenApiSpec(specPath) {
  try {
    await SwaggerParser.validate(specPath);
    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      errors: [error.message],
    };
  }
}

/**
 * Generate code snippets for an API endpoint
 * @param {Object} endpoint - Endpoint configuration
 * @param {string} language - Programming language
 * @returns {string} Code snippet
 */
function generateCodeSnippet(endpoint, language = 'javascript') {
  const { method, path, headers = {}, body } = endpoint;

  // Default headers
  const defaultHeaders = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };

  // Merge headers
  const mergedHeaders = { ...defaultHeaders, ...headers };

  // Generate code snippet based on language
  switch (language.toLowerCase()) {
    case 'javascript':
      return generateJavaScriptSnippet(method, path, mergedHeaders, body);
    case 'python':
      return generatePythonSnippet(method, path, mergedHeaders, body);
    case 'curl':
      return generateCurlSnippet(method, path, mergedHeaders, body);
    case 'csharp':
      return generateCSharpSnippet(method, path, mergedHeaders, body);
    default:
      return `// Code snippet for ${language} not supported yet`;
  }
}

/**
 * Generate JavaScript code snippet
 * @param {string} method - HTTP method
 * @param {string} path - API path
 * @param {Object} headers - HTTP headers
 * @param {Object} body - Request body
 * @returns {string} JavaScript code snippet
 */
function generateJavaScriptSnippet(method, path, headers, body) {
  const headersString = Object.entries(headers)
    .map(([key, value]) => `    '${key}': '${value}'`)
    .join(',\n');

  const bodyString = body ? `\n  body: JSON.stringify(${JSON.stringify(body, null, 2)})` : '';

  return `// Using fetch API
const response = await fetch('${path}', {
  method: '${method.toUpperCase()}',
  headers: {
${headersString}
  },${bodyString}
});

const data = await response.json();
logger.info(data);`;
}

/**
 * Generate Python code snippet
 * @param {string} method - HTTP method
 * @param {string} path - API path
 * @param {Object} headers - HTTP headers
 * @param {Object} body - Request body
 * @returns {string} Python code snippet
 */
function generatePythonSnippet(method, path, headers, body) {
  const headersString = Object.entries(headers)
    .map(([key, value]) => `    '${key}': '${value}'`)
    .join(',\n');

  const bodyString = body ? `\ndata = ${JSON.stringify(body, null, 4)}` : '';

  return `import requests

headers = {
${headersString}
}${bodyString}

response = requests.${method.toLowerCase()}('${path}', headers=headers${body ? ', json=data' : ''})
print(response.json())`;
}

/**
 * Generate cURL code snippet
 * @param {string} method - HTTP method
 * @param {string} path - API path
 * @param {Object} headers - HTTP headers
 * @param {Object} body - Request body
 * @returns {string} cURL code snippet
 */
function generateCurlSnippet(method, path, headers, body) {
  const headersString = Object.entries(headers)
    .map(([key, value]) => `-H '${key}: ${value}'`)
    .join(' \\\n  ');

  const bodyString = body ? `\\\n  -d '${JSON.stringify(body)}'` : '';

  return `curl -X ${method.toUpperCase()} '${path}' \\\n  ${headersString} ${bodyString}`;
}

/**
 * Generate C# code snippet
 * @param {string} method - HTTP method
 * @param {string} path - API path
 * @param {Object} headers - HTTP headers
 * @param {Object} body - Request body
 * @returns {string} C# code snippet
 */
function generateCSharpSnippet(method, path, headers, body) {
  const headersString = Object.entries(headers)
    .map(([key, value]) => `client.DefaultRequestHeaders.Add("${key}", "${value}");`)
    .join('\n');

  const bodyString = body
    ? `\nvar content = new StringContent(JsonConvert.SerializeObject(${JSON.stringify(body, null, 2)}), Encoding.UTF8, "application/json");`
    : '';

  return `using System;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using System.Threading.Tasks;

public async Task<string> MakeRequest()
{
    using (var client = new HttpClient())
    {
        ${headersString}${bodyString}
        
        var response = await client.${method.charAt(0).toUpperCase() + method.slice(1).toLowerCase()}Async("${path}"${body ? ', content' : ''});
        var responseContent = await response.Content.ReadAsStringAsync();
        return responseContent;
    }
}`;
}

module.exports = {
  setupInteractiveApiDocs,
  validateOpenApiSpec,
  generateCodeSnippet,
};
