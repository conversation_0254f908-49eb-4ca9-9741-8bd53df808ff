/**
 * Long-Term Impact Analyzer
 * 
 * This service analyzes the long-term impact of A/B test winners on
 * user behavior, engagement, and business metrics.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

/**
 * Get completed A/B tests
 * 
 * @returns {Array} - Completed A/B tests
 */
async function getCompletedTests() {
  try {
    const { data, error } = await supabase
      .from('ab_tests')
      .select('*')
      .eq('status', 'completed')
      .order('end_date', { ascending: false });
      
    if (error) {
      logger.error('Error fetching completed A/B tests', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getCompletedTests', { error: error.message });
    return [];
  }
}

/**
 * Get test assignments
 * 
 * @param {string} testId - Test ID
 * @returns {Array} - Test assignments
 */
async function getTestAssignments(testId) {
  try {
    const { data, error } = await supabase
      .from('ab_test_assignments')
      .select('*')
      .eq('test_id', testId);
      
    if (error) {
      logger.error('Error fetching test assignments', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getTestAssignments', { error: error.message });
    return [];
  }
}

/**
 * Get user activity data
 * 
 * @param {string} userId - User ID
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @returns {Array} - User activity data
 */
async function getUserActivityData(userId, startDate, endDate) {
  try {
    let query = supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });
      
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate);
    }
    
    const { data, error } = await query;
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Get business metrics data
 * 
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @returns {Array} - Business metrics data
 */
async function getBusinessMetricsData(startDate, endDate) {
  try {
    let query = supabase
      .from('business_metrics')
      .select('*')
      .order('date', { ascending: true });
      
    if (startDate) {
      query = query.gte('date', startDate);
    }
    
    if (endDate) {
      query = query.lte('date', endDate);
    }
    
    const { data, error } = await query;
      
    if (error) {
      logger.error('Error fetching business metrics data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getBusinessMetricsData', { error: error.message });
    return [];
  }
}

/**
 * Analyze long-term impact of a test
 * 
 * @param {string} testId - Test ID
 * @returns {Object} - Long-term impact analysis
 */
async function analyzeLongTermImpact(testId) {
  try {
    // Get test
    const { data: test, error: testError } = await supabase
      .from('ab_tests')
      .select('*')
      .eq('id', testId)
      .single();
      
    if (testError) {
      logger.error('Error fetching test', { error: testError.message });
      throw testError;
    }
    
    // Check if test is completed
    if (test.status !== 'completed') {
      throw new Error(`Test is not completed (status: ${test.status})`);
    }
    
    // Get test assignments
    const assignments = await getTestAssignments(testId);
    
    if (assignments.length === 0) {
      throw new Error('No assignments found for test');
    }
    
    // Group assignments by variant
    const variantAssignments = {};
    
    assignments.forEach(assignment => {
      if (!variantAssignments[assignment.variant_id]) {
        variantAssignments[assignment.variant_id] = {
          variantId: assignment.variant_id,
          variantName: assignment.variant_name,
          isControl: assignment.is_control,
          userIds: []
        };
      }
      
      variantAssignments[assignment.variant_id].userIds.push(assignment.user_id);
    });
    
    // Get winning variant
    const winningVariantId = test.winning_variant_id;
    
    if (!winningVariantId) {
      throw new Error('Test has no winning variant');
    }
    
    const winningVariant = variantAssignments[winningVariantId];
    const controlVariant = Object.values(variantAssignments).find(v => v.isControl);
    
    if (!winningVariant) {
      throw new Error('Winning variant not found in assignments');
    }
    
    if (!controlVariant) {
      throw new Error('Control variant not found in assignments');
    }
    
    // Define analysis periods
    const testEndDate = new Date(test.end_date);
    
    const periods = [
      {
        name: '1 week',
        startDate: new Date(testEndDate),
        endDate: new Date(testEndDate)
      },
      {
        name: '1 month',
        startDate: new Date(testEndDate),
        endDate: new Date(testEndDate)
      },
      {
        name: '3 months',
        startDate: new Date(testEndDate),
        endDate: new Date(testEndDate)
      }
    ];
    
    // Set period dates
    periods[0].startDate.setDate(periods[0].startDate.getDate() + 1);
    periods[0].endDate.setDate(periods[0].endDate.getDate() + 7);
    
    periods[1].startDate.setDate(periods[1].startDate.getDate() + 1);
    periods[1].endDate.setMonth(periods[1].endDate.getMonth() + 1);
    
    periods[2].startDate.setDate(periods[2].startDate.getDate() + 1);
    periods[2].endDate.setMonth(periods[2].endDate.getMonth() + 3);
    
    // Analyze each period
    const periodAnalyses = [];
    
    for (const period of periods) {
      // Skip future periods
      if (period.endDate > new Date()) {
        continue;
      }
      
      // Get user activity data for winning variant users
      const winningVariantActivities = [];
      
      for (const userId of winningVariant.userIds) {
        const activities = await getUserActivityData(
          userId,
          period.startDate.toISOString(),
          period.endDate.toISOString()
        );
        
        winningVariantActivities.push(...activities);
      }
      
      // Get user activity data for control variant users
      const controlVariantActivities = [];
      
      for (const userId of controlVariant.userIds) {
        const activities = await getUserActivityData(
          userId,
          period.startDate.toISOString(),
          period.endDate.toISOString()
        );
        
        controlVariantActivities.push(...activities);
      }
      
      // Calculate engagement metrics
      const winningEngagement = calculateEngagementMetrics(winningVariantActivities, winningVariant.userIds.length);
      const controlEngagement = calculateEngagementMetrics(controlVariantActivities, controlVariant.userIds.length);
      
      // Calculate engagement differences
      const engagementDiff = {
        actionsPerUser: calculatePercentageDiff(controlEngagement.actionsPerUser, winningEngagement.actionsPerUser),
        sessionsPerUser: calculatePercentageDiff(controlEngagement.sessionsPerUser, winningEngagement.sessionsPerUser),
        sessionDuration: calculatePercentageDiff(controlEngagement.sessionDuration, winningEngagement.sessionDuration),
        retentionRate: calculatePercentageDiff(controlEngagement.retentionRate, winningEngagement.retentionRate)
      };
      
      // Get business metrics for the period
      const businessMetrics = await getBusinessMetricsData(
        period.startDate.toISOString(),
        period.endDate.toISOString()
      );
      
      // Calculate average business metrics
      const avgBusinessMetrics = calculateAverageBusinessMetrics(businessMetrics);
      
      // Add period analysis
      periodAnalyses.push({
        period: period.name,
        startDate: period.startDate.toISOString(),
        endDate: period.endDate.toISOString(),
        winningVariantEngagement: winningEngagement,
        controlVariantEngagement: controlEngagement,
        engagementDiff,
        businessMetrics: avgBusinessMetrics
      });
    }
    
    return {
      testId,
      testName: test.name,
      testDescription: test.description,
      testStartDate: test.start_date,
      testEndDate: test.end_date,
      winningVariantId,
      winningVariantName: winningVariant.variantName,
      controlVariantId: controlVariant.variantId,
      controlVariantName: controlVariant.variantName,
      periodAnalyses
    };
  } catch (error) {
    logger.error('Error in analyzeLongTermImpact', { error: error.message });
    throw error;
  }
}

/**
 * Calculate engagement metrics
 * 
 * @param {Array} activities - User activities
 * @param {number} userCount - Number of users
 * @returns {Object} - Engagement metrics
 */
function calculateEngagementMetrics(activities, userCount) {
  if (activities.length === 0 || userCount === 0) {
    return {
      actionsPerUser: 0,
      sessionsPerUser: 0,
      sessionDuration: 0,
      retentionRate: 0
    };
  }
  
  // Calculate actions per user
  const actionsPerUser = activities.length / userCount;
  
  // Group activities by user and session
  const userSessions = {};
  
  activities.forEach(activity => {
    if (!userSessions[activity.user_id]) {
      userSessions[activity.user_id] = [];
    }
    
    // Check if this is a new session (more than 30 minutes since last activity)
    const lastActivity = userSessions[activity.user_id][userSessions[activity.user_id].length - 1];
    
    if (!lastActivity || new Date(activity.created_at) - new Date(lastActivity.created_at) > 30 * 60 * 1000) {
      userSessions[activity.user_id].push([activity]);
    } else {
      userSessions[activity.user_id][userSessions[activity.user_id].length - 1].push(activity);
    }
  });
  
  // Calculate sessions per user
  const totalSessions = Object.values(userSessions).reduce((sum, sessions) => sum + sessions.length, 0);
  const sessionsPerUser = totalSessions / userCount;
  
  // Calculate average session duration
  let totalDuration = 0;
  let sessionCount = 0;
  
  Object.values(userSessions).forEach(sessions => {
    sessions.forEach(session => {
      if (session.length >= 2) {
        const startTime = new Date(session[0].created_at);
        const endTime = new Date(session[session.length - 1].created_at);
        const duration = (endTime - startTime) / 1000; // in seconds
        
        totalDuration += duration;
        sessionCount++;
      }
    });
  });
  
  const sessionDuration = sessionCount > 0 ? totalDuration / sessionCount : 0;
  
  // Calculate retention rate (users with at least one session / total users)
  const activeUsers = Object.keys(userSessions).length;
  const retentionRate = activeUsers / userCount;
  
  return {
    actionsPerUser,
    sessionsPerUser,
    sessionDuration,
    retentionRate
  };
}

/**
 * Calculate average business metrics
 * 
 * @param {Array} metrics - Business metrics
 * @returns {Object} - Average business metrics
 */
function calculateAverageBusinessMetrics(metrics) {
  if (metrics.length === 0) {
    return {
      activeUsers: 0,
      conversionRate: 0,
      revenue: 0,
      retentionRate: 0,
      satisfactionScore: 0
    };
  }
  
  const sum = metrics.reduce((acc, metric) => {
    return {
      activeUsers: acc.activeUsers + metric.active_users,
      conversionRate: acc.conversionRate + metric.conversion_rate,
      revenue: acc.revenue + metric.revenue,
      retentionRate: acc.retentionRate + metric.retention_rate,
      satisfactionScore: acc.satisfactionScore + metric.satisfaction_score
    };
  }, {
    activeUsers: 0,
    conversionRate: 0,
    revenue: 0,
    retentionRate: 0,
    satisfactionScore: 0
  });
  
  return {
    activeUsers: sum.activeUsers / metrics.length,
    conversionRate: sum.conversionRate / metrics.length,
    revenue: sum.revenue / metrics.length,
    retentionRate: sum.retentionRate / metrics.length,
    satisfactionScore: sum.satisfactionScore / metrics.length
  };
}

/**
 * Calculate percentage difference
 * 
 * @param {number} baseline - Baseline value
 * @param {number} current - Current value
 * @returns {number} - Percentage difference
 */
function calculatePercentageDiff(baseline, current) {
  if (baseline === 0) {
    return current > 0 ? 100 : 0;
  }
  
  return ((current - baseline) / baseline) * 100;
}

/**
 * Get long-term impact summary for all completed tests
 * 
 * @returns {Array} - Long-term impact summary
 */
async function getLongTermImpactSummary() {
  try {
    // Get completed tests
    const completedTests = await getCompletedTests();
    
    if (completedTests.length === 0) {
      return [];
    }
    
    // Analyze long-term impact for each test
    const summary = [];
    
    for (const test of completedTests) {
      try {
        const analysis = await analyzeLongTermImpact(test.id);
        
        // Calculate overall impact score
        let impactScore = 0;
        let metricCount = 0;
        
        analysis.periodAnalyses.forEach(periodAnalysis => {
          // Weight periods differently
          let periodWeight = 1;
          
          if (periodAnalysis.period === '1 month') {
            periodWeight = 2;
          } else if (periodAnalysis.period === '3 months') {
            periodWeight = 3;
          }
          
          // Add engagement metrics to impact score
          Object.values(periodAnalysis.engagementDiff).forEach(diff => {
            impactScore += diff * periodWeight;
            metricCount += periodWeight;
          });
        });
        
        // Calculate average impact score
        const avgImpactScore = metricCount > 0 ? impactScore / metricCount : 0;
        
        summary.push({
          testId: test.id,
          testName: test.name,
          testDescription: test.description,
          testStartDate: test.start_date,
          testEndDate: test.end_date,
          winningVariantId: test.winning_variant_id,
          winningVariantName: analysis.winningVariantName,
          impactScore: avgImpactScore,
          periodCount: analysis.periodAnalyses.length
        });
      } catch (error) {
        logger.error(`Error analyzing long-term impact for test ${test.id}`, { error: error.message });
      }
    }
    
    // Sort by impact score (highest first)
    summary.sort((a, b) => b.impactScore - a.impactScore);
    
    return summary;
  } catch (error) {
    logger.error('Error in getLongTermImpactSummary', { error: error.message });
    return [];
  }
}

// API endpoints
app.get('/api/long-term-impact/:testId', async (req, res) => {
  try {
    const { testId } = req.params;
    
    const analysis = await analyzeLongTermImpact(testId);
    res.json(analysis);
  } catch (error) {
    logger.error('Error in GET /api/long-term-impact/:testId', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/long-term-impact', async (req, res) => {
  try {
    const summary = await getLongTermImpactSummary();
    res.json({ tests: summary });
  } catch (error) {
    logger.error('Error in GET /api/long-term-impact', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.LONG_TERM_IMPACT_ANALYZER_PORT || 9108;
app.listen(PORT, () => {
  logger.info(`Long-Term Impact Analyzer listening on port ${PORT}`);
});

module.exports = {
  analyzeLongTermImpact,
  getLongTermImpactSummary
};
