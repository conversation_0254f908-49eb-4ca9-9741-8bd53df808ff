import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { VirtualListRenderer } from '../src/utils/PerformanceOptimizer.js';

// Mock the AnimationEditor component since we can't directly test it
const AnimationEditor = {
  name: 'AnimationEditor',
  props: {
    vendorId: String,
  },
  data() {
    return {
      virtualListRenderer: null,
      animations: [],
      performanceMetrics: {
        loadTime: 0,
        renderTime: 0,
      },
      renderCount: 0,
      cache: {
        get: vi.fn(),
        set: vi.fn(),
      },
    };
  },
  methods: {
    initVirtualListRenderer() {
      this.virtualListRenderer = new VirtualListRenderer([], 60, 400, 5, {
        lazyLoad: true,
        loadMoreItems: this.loadAnimationsWithPagination,
        loadThreshold: 0.7,
        pageSize: 20,
      });
    },
    loadAnimationsWithPagination(_page, _limit) {
      // Implementation
      return Promise.resolve([]);
    },
    handleScroll() {
      // Implementation
    },
    measureRenderPerformance() {
      // Implementation
    },
  },
};

// Mock the API
vi.mock('@directus/extensions-sdk', () => ({
  useApi: () => ({
    get: vi.fn().mockImplementation(url => {
      if (url.includes('animations')) {
        return Promise.resolve({
          data: {
            data: Array.from({ length: 20 }, (_, i) => ({
              id: `animation-${i}`,
              name: `Animation ${i}`,
              duration: 5,
              tracks: [],
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              vendor_id: 'test-vendor',
            })),
          },
        });
      }
      return Promise.resolve({ data: { data: [] } });
    }),
  }),
}));

// Mock performance.now
const originalPerformanceNow = performance.now;
beforeEach(() => {
  let counter = 0;
  performance.now = vi.fn(() => counter++);

  // Restore after tests
  return () => {
    performance.now = originalPerformanceNow;
  };
});

describe('AnimationEditor', () => {
  it('should initialize VirtualListRenderer with lazy loading', async () => {
    const wrapper = mount(AnimationEditor, {
      propsData: {
        vendorId: 'test-vendor',
      },
      global: {
        stubs: ['router-link', 'router-view'],
      },
    });

    // Wait for component to initialize
    await vi.runAllTimersAsync();

    // Check if VirtualListRenderer is initialized
    expect(wrapper.vm.virtualListRenderer).toBeDefined();
    expect(wrapper.vm.virtualListRenderer instanceof VirtualListRenderer).toBe(true);
    expect(wrapper.vm.virtualListRenderer.lazyLoad).toBe(true);
  });

  it('should load animations with pagination', async () => {
    const wrapper = mount(AnimationEditor, {
      propsData: {
        vendorId: 'test-vendor',
      },
      global: {
        stubs: ['router-link', 'router-view'],
      },
    });

    // Mock the API call
    const apiSpy = vi.spyOn(wrapper.vm.$api, 'get');

    // Call loadAnimationsWithPagination
    await wrapper.vm.loadAnimationsWithPagination(2, 10);

    // Check if API was called with correct parameters
    expect(apiSpy).toHaveBeenCalledWith(
      expect.stringContaining(
        '/items/animations?filter[vendor_id][_eq]=test-vendor&limit=10&page=2',
      ),
    );

    // Check if result is cached
    await wrapper.vm.loadAnimationsWithPagination(2, 10);
    expect(apiSpy).toHaveBeenCalledTimes(1); // Should not call API again
  });

  it('should handle scroll events and update VirtualListRenderer', async () => {
    const wrapper = mount(AnimationEditor, {
      propsData: {
        vendorId: 'test-vendor',
      },
      global: {
        stubs: ['router-link', 'router-view'],
      },
    });

    // Wait for component to initialize
    await vi.runAllTimersAsync();

    // Mock the VirtualListRenderer
    wrapper.vm.virtualListRenderer = {
      updateScroll: vi.fn(),
    };

    // Trigger scroll event
    wrapper.vm.handleScroll({ target: { scrollTop: 100, clientHeight: 400, scrollHeight: 1000 } });

    // Check if VirtualListRenderer.updateScroll was called
    expect(wrapper.vm.virtualListRenderer.updateScroll).toHaveBeenCalledWith(100);
  });

  it('should measure render performance', async () => {
    const wrapper = mount(AnimationEditor, {
      propsData: {
        vendorId: 'test-vendor',
      },
      global: {
        stubs: ['router-link', 'router-view'],
      },
    });

    // Wait for component to initialize
    await vi.runAllTimersAsync();

    // Initial performance metrics
    expect(wrapper.vm.performanceMetrics.renderTime).toBe(0);

    // Call measureRenderPerformance
    wrapper.vm.measureRenderPerformance();

    // Check if performance metrics are updated
    expect(wrapper.vm.performanceMetrics.renderTime).toBeGreaterThan(0);
    expect(wrapper.vm.renderCount).toBe(1);
  });
});
