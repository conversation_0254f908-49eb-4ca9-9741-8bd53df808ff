/**
 * Update package.json for Jest to Vitest Migration
 * 
 * This script updates the package.json file to use Vitest instead of Jest.
 * It adds Vitest dependencies and updates test scripts.
 */

const fs = require('fs');
const path = require('path');

// Path to package.json
const packageJsonPath = path.resolve(__dirname, '..', 'package.json');

// Read package.json
console.log('Reading package.json...');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Create a backup
const backupPath = path.resolve(__dirname, '..', 'package.json.backup');
if (!fs.existsSync(backupPath)) {
  console.log('Creating backup of package.json...');
  fs.writeFileSync(backupPath, JSON.stringify(packageJson, null, 2));
}

// Update dependencies
console.log('Updating dependencies...');

// Add Vitest dependencies
if (!packageJson.devDependencies) {
  packageJson.devDependencies = {};
}

const vitestDependencies = {
  'vitest': '^1.6.1',
  'jsdom': '^24.0.0',
  '@vitest/coverage-v8': '^1.6.1',
  'vite-plugin-vue2': '^2.0.3',
};

// Add or update Vitest dependencies
Object.entries(vitestDependencies).forEach(([name, version]) => {
  if (!packageJson.devDependencies[name]) {
    packageJson.devDependencies[name] = version;
    console.log(`  Added ${name}@${version}`);
  } else {
    console.log(`  ${name} already exists: ${packageJson.devDependencies[name]}`);
  }
});

// Update scripts
console.log('Updating scripts...');

if (!packageJson.scripts) {
  packageJson.scripts = {};
}

// Backup original scripts
const originalScripts = { ...packageJson.scripts };

// Update test scripts
Object.keys(packageJson.scripts).forEach(scriptName => {
  if (scriptName.includes('test') && packageJson.scripts[scriptName].includes('jest')) {
    const originalCommand = packageJson.scripts[scriptName];
    console.log(`  Updating script: ${scriptName}`);
    console.log(`    From: ${originalCommand}`);
    
    // Replace jest with vitest
    let newCommand = originalCommand
      .replace(/jest/g, 'vitest')
      .replace(/--coverage/g, 'run --coverage')
      .replace(/--watch/g, 'watch');
    
    console.log(`    To:   ${newCommand}`);
    packageJson.scripts[scriptName] = newCommand;
  }
});

// Add Vitest scripts if they don't exist
if (!packageJson.scripts['test:vitest']) {
  packageJson.scripts['test:vitest'] = 'vitest run';
  console.log('  Added script: test:vitest');
}

if (!packageJson.scripts['test:vitest:watch']) {
  packageJson.scripts['test:vitest:watch'] = 'vitest';
  console.log('  Added script: test:vitest:watch');
}

if (!packageJson.scripts['test:vitest:coverage']) {
  packageJson.scripts['test:vitest:coverage'] = 'vitest run --coverage';
  console.log('  Added script: test:vitest:coverage');
}

// Save updated package.json
console.log('Saving updated package.json...');
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('package.json updated successfully!');

// Print summary
console.log('\nSummary:');
console.log('  Added dependencies:');
Object.entries(vitestDependencies).forEach(([name, version]) => {
  console.log(`    - ${name}@${version}`);
});

console.log('  Updated scripts:');
Object.keys(packageJson.scripts).forEach(scriptName => {
  if (originalScripts[scriptName] !== packageJson.scripts[scriptName]) {
    console.log(`    - ${scriptName}: ${packageJson.scripts[scriptName]}`);
  }
});

console.log('\nNext steps:');
console.log('1. Run npm install to install the new dependencies');
console.log('2. Run npm run test:vitest to run tests with Vitest');
console.log('3. Fix any failing tests');
