/**
 * Predictive Monitoring Service
 *
 * This service implements predictive monitoring to detect potential issues
 * before they occur.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { EventEmitter } = require('events');
const { Logger } = require('../integration/logger');
const { Counter, Gauge } = require('prom-client');
const { AnomalyDetectionService, ALGORITHMS } = require('./anomaly-detection');

// Promisify functions
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);

// Create logger
const logger = new Logger();

// Create metrics
const predictionsGenerated = new Counter({
  name: 'predictions_generated_total',
  help: 'Total number of predictions generated',
  labelNames: ['metric', 'severity'],
});

const predictiveAlertsGenerated = new Counter({
  name: 'predictive_alerts_generated_total',
  help: 'Total number of predictive alerts generated',
  labelNames: ['metric', 'severity'],
});

const predictionAccuracy = new Gauge({
  name: 'prediction_accuracy',
  help: 'Accuracy of predictions',
  labelNames: ['metric', 'window'],
});

// Configuration
const config = {
  modelsPath:
    process.env.MODELS_PATH || path.join(__dirname, '../../config/prediction-models.json'),
  predictionHistoryPath:
    process.env.PREDICTION_HISTORY_PATH || path.join(__dirname, '../../logs/predictions'),
  dataRetentionDays: parseInt(process.env.DATA_RETENTION_DAYS || '30', 10),
  predictionIntervalMs: parseInt(process.env.PREDICTION_INTERVAL_MS || '300000', 10), // 5 minutes
  alertThreshold: parseFloat(process.env.ALERT_THRESHOLD || '0.8'), // 80% confidence
  timeSeriesWindowSize: parseInt(process.env.TIME_SERIES_WINDOW_SIZE || '24', 10), // 24 data points
  seasonalityPeriod: parseInt(process.env.SEASONALITY_PERIOD || '24', 10), // 24 hours
  forecastHorizon: parseInt(process.env.FORECAST_HORIZON || '6', 10), // 6 data points
};

/**
 * Predictive monitoring service
 */
class PredictiveMonitoringService extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    super();

    this.options = {
      ...config,
      ...options,
    };

    this.models = [];
    this.timeSeriesData = new Map();
    this.predictions = new Map();
    this.anomalies = new Map();

    // Create anomaly detection service
    this.anomalyDetection = new AnomalyDetectionService({
      zScoreThreshold: this.options.zScoreThreshold || 3.0,
      madThreshold: this.options.madThreshold || 3.5,
      iqrMultiplier: this.options.iqrMultiplier || 1.5,
      minSamples: this.options.minSamples || 10,
    });

    // Create directories
    mkdirAsync(this.options.predictionHistoryPath, { recursive: true }).catch(err => {
      logger.error(`Error creating prediction history directory: ${err.message}`, { error: err });
    });

    // Load prediction models
    this.loadModels().catch(err => {
      logger.error(`Error loading prediction models: ${err.message}`, { error: err });
    });

    // Start prediction interval
    this.predictionInterval = setInterval(() => {
      this.generatePredictions().catch(err => {
        logger.error(`Error generating predictions: ${err.message}`, { error: err });
      });
    }, this.options.predictionIntervalMs);

    // Start anomaly detection interval
    this.anomalyInterval = setInterval(() => {
      this.detectAnomalies().catch(err => {
        logger.error(`Error detecting anomalies: ${err.message}`, { error: err });
      });
    }, this.options.anomalyIntervalMs || 600000); // Default: 10 minutes

    logger.info('Predictive monitoring service initialized with anomaly detection');
  }

  /**
   * Load prediction models
   * @returns {Promise<void>}
   */
  async loadModels() {
    try {
      const data = await readFileAsync(this.options.modelsPath, 'utf8');
      this.models = JSON.parse(data);

      logger.info(`Loaded ${this.models.length} prediction models`);
    } catch (error) {
      logger.error(`Error loading prediction models: ${error.message}`, { error });

      // Use default models
      this.models = [
        {
          id: 'cpu-usage',
          name: 'CPU Usage Prediction',
          description: 'Predict CPU usage based on historical data',
          metric: 'cpu_usage',
          algorithm: 'exponential-smoothing',
          parameters: {
            alpha: 0.3,
            beta: 0.1,
            gamma: 0.1,
            seasonality: 24,
          },
          thresholds: {
            warning: 80,
            critical: 90,
          },
          groupBy: ['host'],
        },
        {
          id: 'memory-usage',
          name: 'Memory Usage Prediction',
          description: 'Predict memory usage based on historical data',
          metric: 'memory_usage',
          algorithm: 'exponential-smoothing',
          parameters: {
            alpha: 0.3,
            beta: 0.1,
            gamma: 0.1,
            seasonality: 24,
          },
          thresholds: {
            warning: 80,
            critical: 90,
          },
          groupBy: ['host'],
        },
        {
          id: 'disk-usage',
          name: 'Disk Usage Prediction',
          description: 'Predict disk usage based on historical data',
          metric: 'disk_usage',
          algorithm: 'linear-regression',
          parameters: {
            windowSize: 168, // 7 days
          },
          thresholds: {
            warning: 80,
            critical: 90,
          },
          groupBy: ['host', 'mount'],
        },
        {
          id: 'network-traffic',
          name: 'Network Traffic Prediction',
          description: 'Predict network traffic based on historical data',
          metric: 'network_traffic',
          algorithm: 'exponential-smoothing',
          parameters: {
            alpha: 0.3,
            beta: 0.1,
            gamma: 0.1,
            seasonality: 24,
          },
          thresholds: {
            warning: 80,
            critical: 90,
          },
          groupBy: ['host', 'interface'],
        },
        {
          id: 'api-latency',
          name: 'API Latency Prediction',
          description: 'Predict API latency based on historical data',
          metric: 'api_latency',
          algorithm: 'exponential-smoothing',
          parameters: {
            alpha: 0.3,
            beta: 0.1,
            gamma: 0.1,
            seasonality: 24,
          },
          thresholds: {
            warning: 200,
            critical: 500,
          },
          groupBy: ['service', 'endpoint'],
        },
        {
          id: 'database-connections',
          name: 'Database Connections Prediction',
          description: 'Predict database connections based on historical data',
          metric: 'database_connections',
          algorithm: 'exponential-smoothing',
          parameters: {
            alpha: 0.3,
            beta: 0.1,
            gamma: 0.1,
            seasonality: 24,
          },
          thresholds: {
            warning: 80,
            critical: 90,
          },
          groupBy: ['database'],
        },
        {
          id: 'error-rate',
          name: 'Error Rate Prediction',
          description: 'Predict error rate based on historical data',
          metric: 'error_rate',
          algorithm: 'exponential-smoothing',
          parameters: {
            alpha: 0.3,
            beta: 0.1,
            gamma: 0.1,
            seasonality: 24,
          },
          thresholds: {
            warning: 5,
            critical: 10,
          },
          groupBy: ['service'],
        },
      ];

      logger.info(`Using ${this.models.length} default prediction models`);
    }
  }

  /**
   * Save prediction models
   * @returns {Promise<void>}
   */
  async saveModels() {
    try {
      await writeFileAsync(this.options.modelsPath, JSON.stringify(this.models, null, 2));
      logger.info(`Saved ${this.models.length} prediction models`);
    } catch (error) {
      logger.error(`Error saving prediction models: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Add metric data point
   * @param {Object} dataPoint - Metric data point
   * @returns {Promise<void>}
   */
  async addMetricDataPoint(dataPoint) {
    // Validate data point
    if (!this.validateDataPoint(dataPoint)) {
      logger.warn('Invalid data point received', { dataPoint });
      return;
    }

    // Find matching models
    const matchingModels = this.findMatchingModels(dataPoint);

    if (matchingModels.length === 0) {
      return;
    }

    // Add data point to time series
    for (const model of matchingModels) {
      const key = this.getTimeSeriesKey(model, dataPoint);

      if (!this.timeSeriesData.has(key)) {
        this.timeSeriesData.set(key, []);
      }

      const timeSeries = this.timeSeriesData.get(key);

      // Add data point
      timeSeries.push({
        timestamp: dataPoint.timestamp || new Date().toISOString(),
        value: dataPoint.value,
      });

      // Sort by timestamp
      timeSeries.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

      // Trim old data
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.options.dataRetentionDays);

      const cutoffIndex = timeSeries.findIndex(point => new Date(point.timestamp) >= cutoffDate);

      if (cutoffIndex > 0) {
        timeSeries.splice(0, cutoffIndex);
      }
    }
  }

  /**
   * Validate data point
   * @param {Object} dataPoint - Metric data point
   * @returns {boolean} Whether data point is valid
   */
  validateDataPoint(dataPoint) {
    // Check required fields
    if (!dataPoint.metric || dataPoint.value === undefined) {
      return false;
    }

    // Check value is a number
    if (typeof dataPoint.value !== 'number') {
      return false;
    }

    return true;
  }

  /**
   * Find matching models
   * @param {Object} dataPoint - Metric data point
   * @returns {Array<Object>} Matching models
   */
  findMatchingModels(dataPoint) {
    return this.models.filter(model => {
      // Check metric
      if (model.metric !== dataPoint.metric) {
        return false;
      }

      // Check group by fields
      if (model.groupBy && model.groupBy.length > 0) {
        return model.groupBy.every(field => dataPoint[field] !== undefined);
      }

      return true;
    });
  }

  /**
   * Get time series key
   * @param {Object} model - Prediction model
   * @param {Object} dataPoint - Metric data point
   * @returns {string} Time series key
   */
  getTimeSeriesKey(model, dataPoint) {
    const groupValues = model.groupBy
      ? model.groupBy.map(field => dataPoint[field] || 'unknown')
      : [];
    return `${model.id}-${groupValues.join('-')}`;
  }

  /**
   * Generate predictions
   * @returns {Promise<Array<Object>>} Predictions
   */
  async generatePredictions() {
    logger.info('Generating predictions');

    const predictions = [];

    for (const model of this.models) {
      // Get all time series for this model
      const timeSeriesKeys = Array.from(this.timeSeriesData.keys()).filter(key =>
        key.startsWith(`${model.id}-`),
      );

      for (const key of timeSeriesKeys) {
        const timeSeries = this.timeSeriesData.get(key);

        // Skip if not enough data points
        if (timeSeries.length < this.options.timeSeriesWindowSize) {
          continue;
        }

        // Generate prediction
        try {
          const prediction = this.predictTimeSeries(model, timeSeries);

          // Add to predictions
          predictions.push(prediction);

          // Store prediction
          this.predictions.set(key, prediction);

          // Track metric
          predictionsGenerated.inc({
            metric: model.metric,
            severity: prediction.severity,
          });

          // Check if prediction exceeds threshold
          if (prediction.severity !== 'normal') {
            // Generate predictive alert
            const alert = this.generatePredictiveAlert(model, prediction);

            // Track metric
            predictiveAlertsGenerated.inc({
              metric: model.metric,
              severity: prediction.severity,
            });

            // Emit alert event
            this.emit('predictiveAlert', alert);
          }
        } catch (error) {
          logger.error(`Error generating prediction for ${key}: ${error.message}`, { error });
        }
      }
    }

    // Save predictions
    await this.savePredictions(predictions);

    return predictions;
  }

  /**
   * Predict time series
   * @param {Object} model - Prediction model
   * @param {Array<Object>} timeSeries - Time series data
   * @returns {Object} Prediction
   */
  predictTimeSeries(model, timeSeries) {
    // Get recent data points
    const recentPoints = timeSeries.slice(-this.options.timeSeriesWindowSize);

    // Extract values
    const values = recentPoints.map(point => point.value);

    // Generate prediction based on algorithm
    let forecast;
    let confidence;

    switch (model.algorithm) {
      case 'exponential-smoothing':
        forecast = this.exponentialSmoothing(values, model.parameters);
        confidence = 0.8; // Fixed confidence for now
        break;

      case 'linear-regression':
        const result = this.linearRegression(values);
        forecast = result.forecast;
        confidence = result.confidence;
        break;

      default:
        throw new Error(`Unknown algorithm: ${model.algorithm}`);
    }

    // Determine severity
    let severity = 'normal';
    let threshold = 0;

    if (forecast >= model.thresholds.critical) {
      severity = 'critical';
      threshold = model.thresholds.critical;
    } else if (forecast >= model.thresholds.warning) {
      severity = 'warning';
      threshold = model.thresholds.warning;
    }

    // Calculate time to threshold
    const timeToThreshold = this.calculateTimeToThreshold(values, model.thresholds.warning);

    // Parse group values from key
    const keyParts = model.id.split('-');
    const groupValues = keyParts.slice(1);

    // Create prediction object
    const prediction = {
      modelId: model.id,
      metric: model.metric,
      timestamp: new Date().toISOString(),
      currentValue: values[values.length - 1],
      forecastValue: forecast,
      confidence,
      severity,
      threshold,
      timeToThreshold,
      groupValues,
    };

    return prediction;
  }

  /**
   * Exponential smoothing
   * @param {Array<number>} values - Time series values
   * @param {Object} parameters - Algorithm parameters
   * @returns {number} Forecast value
   */
  exponentialSmoothing(values, parameters) {
    const { alpha = 0.3, beta = 0.1, gamma = 0.1, seasonality = 24 } = parameters;

    // Simple implementation of Holt-Winters exponential smoothing
    // In a real implementation, this would be more sophisticated

    // Calculate level, trend, and seasonal components
    let level = values[0];
    let trend = values[1] - values[0];
    const seasonal = Array(seasonality).fill(0);

    // Initialize seasonal components
    for (let i = 0; i < Math.min(seasonality, values.length); i++) {
      seasonal[i] = values[i] / level;
    }

    // Apply smoothing
    for (let i = 1; i < values.length; i++) {
      const oldLevel = level;
      const seasonalIndex = i % seasonality;

      // Update level
      level = alpha * (values[i] / seasonal[seasonalIndex]) + (1 - alpha) * (oldLevel + trend);

      // Update trend
      trend = beta * (level - oldLevel) + (1 - beta) * trend;

      // Update seasonal component
      seasonal[seasonalIndex] = gamma * (values[i] / level) + (1 - gamma) * seasonal[seasonalIndex];
    }

    // Generate forecast
    const forecastHorizon = this.options.forecastHorizon;
    let forecast = 0;

    for (let i = 1; i <= forecastHorizon; i++) {
      const forecastIndex = (values.length + i - 1) % seasonality;
      forecast = (level + i * trend) * seasonal[forecastIndex];
    }

    return forecast;
  }

  /**
   * Linear regression
   * @param {Array<number>} values - Time series values
   * @returns {Object} Forecast and confidence
   */
  linearRegression(values) {
    // Calculate x and y values
    const x = Array.from({ length: values.length }, (_, i) => i);
    const y = values;

    // Calculate means
    const meanX = x.reduce((sum, val) => sum + val, 0) / x.length;
    const meanY = y.reduce((sum, val) => sum + val, 0) / y.length;

    // Calculate slope and intercept
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < x.length; i++) {
      numerator += (x[i] - meanX) * (y[i] - meanY);
      denominator += Math.pow(x[i] - meanX, 2);
    }

    const slope = numerator / denominator;
    const intercept = meanY - slope * meanX;

    // Calculate forecast
    const forecastHorizon = this.options.forecastHorizon;
    const forecast = intercept + slope * (x.length + forecastHorizon - 1);

    // Calculate R-squared (coefficient of determination)
    let ssRes = 0;
    let ssTot = 0;

    for (let i = 0; i < y.length; i++) {
      const prediction = intercept + slope * x[i];
      ssRes += Math.pow(y[i] - prediction, 2);
      ssTot += Math.pow(y[i] - meanY, 2);
    }

    const rSquared = 1 - ssRes / ssTot;
    const confidence = Math.max(0, Math.min(1, rSquared));

    return { forecast, confidence };
  }

  /**
   * Calculate time to threshold
   * @param {Array<number>} values - Time series values
   * @param {number} threshold - Threshold value
   * @returns {number} Time to threshold in hours
   */
  calculateTimeToThreshold(values, threshold) {
    // Calculate x and y values
    const x = Array.from({ length: values.length }, (_, i) => i);
    const y = values;

    // Calculate means
    const meanX = x.reduce((sum, val) => sum + val, 0) / x.length;
    const meanY = y.reduce((sum, val) => sum + val, 0) / y.length;

    // Calculate slope and intercept
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < x.length; i++) {
      numerator += (x[i] - meanX) * (y[i] - meanY);
      denominator += Math.pow(x[i] - meanX, 2);
    }

    const slope = numerator / denominator;
    const intercept = meanY - slope * meanX;

    // Calculate time to threshold
    if (slope <= 0 || values[values.length - 1] >= threshold) {
      return 0;
    }

    const timeToThreshold = (threshold - values[values.length - 1]) / slope;

    // Convert to hours (assuming data points are hourly)
    return Math.ceil(timeToThreshold);
  }

  /**
   * Generate predictive alert
   * @param {Object} model - Prediction model
   * @param {Object} prediction - Prediction
   * @returns {Object} Predictive alert
   */
  generatePredictiveAlert(model, prediction) {
    // Create alert object
    const alert = {
      id: `predictive-${model.id}-${Date.now()}`,
      type: `PREDICTIVE_${model.metric.toUpperCase()}`,
      source: 'predictive-monitoring',
      severity: prediction.severity.toUpperCase(),
      timestamp: prediction.timestamp,
      summary: `Predicted ${model.metric} will exceed ${prediction.threshold} in ${prediction.timeToThreshold} hours`,
      description: `${model.name} predicts that ${model.metric} will reach ${prediction.forecastValue.toFixed(2)} in ${prediction.timeToThreshold} hours, exceeding the ${prediction.severity} threshold of ${prediction.threshold}`,
      metric: model.metric,
      currentValue: prediction.currentValue,
      forecastValue: prediction.forecastValue,
      threshold: prediction.threshold,
      confidence: prediction.confidence,
      timeToThreshold: prediction.timeToThreshold,
    };

    // Add group values
    if (model.groupBy && model.groupBy.length > 0) {
      for (let i = 0; i < model.groupBy.length; i++) {
        alert[model.groupBy[i]] = prediction.groupValues[i];
      }
    }

    return alert;
  }

  /**
   * Save predictions
   * @param {Array<Object>} predictions - Predictions
   * @returns {Promise<void>}
   */
  async savePredictions(predictions) {
    try {
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filePath = path.join(
        this.options.predictionHistoryPath,
        `predictions-${timestamp}.json`,
      );

      await writeFileAsync(filePath, JSON.stringify(predictions, null, 2));

      logger.info(`Saved ${predictions.length} predictions to ${filePath}`);
    } catch (error) {
      logger.error(`Error saving predictions: ${error.message}`, { error });
    }
  }

  /**
   * Get predictions
   * @param {string} metric - Metric name
   * @param {Object} filters - Filters
   * @returns {Array<Object>} Predictions
   */
  getPredictions(metric, filters = {}) {
    const predictions = [];

    for (const [key, prediction] of this.predictions.entries()) {
      // Check metric
      if (metric && prediction.metric !== metric) {
        continue;
      }

      // Check filters
      let matchesFilters = true;

      for (const [filterKey, filterValue] of Object.entries(filters)) {
        if (prediction[filterKey] !== filterValue) {
          matchesFilters = false;
          break;
        }
      }

      if (matchesFilters) {
        predictions.push(prediction);
      }
    }

    return predictions;
  }

  /**
   * Detect anomalies in time series data
   * @returns {Promise<Array<Object>>} Detected anomalies
   */
  async detectAnomalies() {
    logger.info('Detecting anomalies');

    const allAnomalies = [];

    for (const model of this.models) {
      // Get all time series for this model
      const timeSeriesKeys = Array.from(this.timeSeriesData.keys()).filter(key =>
        key.startsWith(`${model.id}-`),
      );

      for (const key of timeSeriesKeys) {
        const timeSeries = this.timeSeriesData.get(key);

        // Skip if not enough data points
        if (timeSeries.length < this.options.minSamples || this.options.minSamples || 10) {
          continue;
        }

        // Add metric name to time series for anomaly detection
        const timeSeriesWithMetric = timeSeries.map(point => ({
          ...point,
          metric: model.metric,
        }));

        // Detect anomalies using different algorithms
        try {
          // Z-Score anomaly detection
          const zScoreAnomalies = this.anomalyDetection.detectAnomalies(timeSeriesWithMetric, {
            algorithm: ALGORITHMS.Z_SCORE,
            metric: model.metric,
            severity: 'warning',
          });

          // MAD anomaly detection
          const madAnomalies = this.anomalyDetection.detectAnomalies(timeSeriesWithMetric, {
            algorithm: ALGORITHMS.MAD,
            metric: model.metric,
            severity: 'warning',
          });

          // IQR anomaly detection
          const iqrAnomalies = this.anomalyDetection.detectAnomalies(timeSeriesWithMetric, {
            algorithm: ALGORITHMS.IQR,
            metric: model.metric,
            severity: 'warning',
          });

          // Combine anomalies
          const anomalies = [...zScoreAnomalies, ...madAnomalies, ...iqrAnomalies];

          // Add model and key information
          const enrichedAnomalies = anomalies.map(anomaly => ({
            ...anomaly,
            modelId: model.id,
            timeSeriesKey: key,
          }));

          // Store anomalies
          this.anomalies.set(key, enrichedAnomalies);

          // Add to all anomalies
          allAnomalies.push(...enrichedAnomalies);

          // Generate alerts for anomalies
          for (const anomaly of enrichedAnomalies) {
            // Generate anomaly alert
            const alert = this.generateAnomalyAlert(model, anomaly);

            // Emit alert event
            this.emit('anomalyAlert', alert);
          }
        } catch (error) {
          logger.error(`Error detecting anomalies for ${key}: ${error.message}`, { error });
        }
      }
    }

    // Save anomalies
    await this.saveAnomalies(allAnomalies);

    return allAnomalies;
  }

  /**
   * Save anomalies
   * @param {Array<Object>} anomalies - Anomalies to save
   * @returns {Promise<void>}
   */
  async saveAnomalies(anomalies) {
    if (anomalies.length === 0) {
      return;
    }

    try {
      // Create timestamp-based filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `anomalies-${timestamp}.json`;
      const filePath = path.join(this.options.predictionHistoryPath, filename);

      // Save anomalies
      await writeFileAsync(filePath, JSON.stringify(anomalies, null, 2));

      logger.info(`Saved ${anomalies.length} anomalies to ${filePath}`);
    } catch (error) {
      logger.error(`Error saving anomalies: ${error.message}`, { error });
    }
  }

  /**
   * Generate anomaly alert
   * @param {Object} model - Prediction model
   * @param {Object} anomaly - Anomaly
   * @returns {Object} Anomaly alert
   */
  generateAnomalyAlert(model, anomaly) {
    // Create alert object
    const alert = {
      id: `anomaly-${model.id}-${Date.now()}`,
      type: `ANOMALY_${model.metric.toUpperCase()}`,
      source: 'anomaly-detection',
      severity: 'WARNING',
      timestamp: new Date().toISOString(),
      summary: `Anomaly detected in ${model.metric} with score ${anomaly.score.toFixed(2)}`,
      description: `${model.name} detected an anomaly with algorithm ${anomaly.algorithm}. Current value: ${anomaly.value}, Score: ${anomaly.score.toFixed(2)}`,
      metric: model.metric,
      value: anomaly.value,
      score: anomaly.score,
      algorithm: anomaly.algorithm,
      threshold: anomaly.threshold,
    };

    // Add group values
    if (model.groupBy && model.groupBy.length > 0) {
      const keyParts = anomaly.timeSeriesKey.split('-');
      const groupValues = keyParts.slice(1);

      for (let i = 0; i < model.groupBy.length; i++) {
        alert[model.groupBy[i]] = groupValues[i];
      }
    }

    return alert;
  }

  /**
   * Stop service
   */
  stop() {
    clearInterval(this.predictionInterval);
    clearInterval(this.anomalyInterval);
    logger.info('Predictive monitoring service stopped');
  }
}

// Export singleton instance
let instance = null;

/**
 * Get predictive monitoring service instance
 * @param {Object} options - Service options
 * @returns {PredictiveMonitoringService} Service instance
 */
function getPredictiveMonitoringService(options = {}) {
  if (!instance) {
    instance = new PredictiveMonitoringService(options);
  }

  return instance;
}

module.exports = {
  PredictiveMonitoringService,
  getPredictiveMonitoringService,
};
