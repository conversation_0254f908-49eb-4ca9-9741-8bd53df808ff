/**
 * Personalization Impact Analyzer
 * 
 * This service analyzes the impact of personalization on user engagement and business metrics.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

/**
 * Get personalization data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Personalization data
 */
async function getPersonalizationData(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('personalization_events')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching personalization data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getPersonalizationData', { error: error.message });
    return [];
  }
}

/**
 * Get user activity data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - User activity data
 */
async function getUserActivityData(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching user activity data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getUserActivityData', { error: error.message });
    return [];
  }
}

/**
 * Get business metrics data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Business metrics data
 */
async function getBusinessMetricsData(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('business_metrics')
      .select('*')
      .gte('date', startDate.toISOString())
      .order('date', { ascending: true });
      
    if (error) {
      logger.error('Error fetching business metrics data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getBusinessMetricsData', { error: error.message });
    return [];
  }
}

/**
 * Analyze personalization impact
 * 
 * @param {number} days - Number of days of history to analyze
 * @returns {Object} - Personalization impact analysis
 */
async function analyzePersonalizationImpact(days = 30) {
  try {
    // Get data
    const personalizationData = await getPersonalizationData(days);
    const activityData = await getUserActivityData(days);
    const businessData = await getBusinessMetricsData(days);
    
    if (personalizationData.length === 0 || activityData.length === 0 || businessData.length === 0) {
      return {
        engagementImpact: {
          sessionDuration: 0,
          pageViews: 0,
          interactionRate: 0,
          returnRate: 0
        },
        businessImpact: {
          conversionRate: 0,
          revenue: 0,
          userRetention: 0,
          customerSatisfaction: 0
        },
        personalizationTypes: [],
        recommendations: []
      };
    }
    
    // Group users by whether they received personalization
    const personalizedUsers = new Set(personalizationData.map(p => p.user_id));
    
    // Group activity data by user
    const activityByUser = {};
    
    activityData.forEach(activity => {
      if (!activityByUser[activity.user_id]) {
        activityByUser[activity.user_id] = [];
      }
      
      activityByUser[activity.user_id].push(activity);
    });
    
    // Calculate engagement metrics
    let personalizedSessionDuration = 0;
    let personalizedPageViews = 0;
    let personalizedInteractions = 0;
    let personalizedReturns = 0;
    let personalizedUserCount = 0;
    
    let nonPersonalizedSessionDuration = 0;
    let nonPersonalizedPageViews = 0;
    let nonPersonalizedInteractions = 0;
    let nonPersonalizedReturns = 0;
    let nonPersonalizedUserCount = 0;
    
    Object.entries(activityByUser).forEach(([userId, activities]) => {
      const isPersonalized = personalizedUsers.has(userId);
      
      // Calculate session duration
      const sessions = [];
      let currentSession = [];
      
      activities.forEach((activity, index) => {
        if (index === 0) {
          currentSession.push(activity);
        } else {
          const prevActivity = activities[index - 1];
          const timeDiff = new Date(activity.created_at) - new Date(prevActivity.created_at);
          
          // If more than 30 minutes between activities, start a new session
          if (timeDiff > 30 * 60 * 1000) {
            sessions.push(currentSession);
            currentSession = [activity];
          } else {
            currentSession.push(activity);
          }
        }
      });
      
      // Add the last session
      if (currentSession.length > 0) {
        sessions.push(currentSession);
      }
      
      // Calculate session durations
      const sessionDurations = sessions.map(session => {
        const startTime = new Date(session[0].created_at);
        const endTime = new Date(session[session.length - 1].created_at);
        
        return (endTime - startTime) / 1000; // in seconds
      });
      
      // Calculate average session duration
      const avgSessionDuration = sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessions.length || 0;
      
      // Calculate page views
      const pageViews = activities.filter(a => a.action_type === 'page_view').length;
      
      // Calculate interactions
      const interactions = activities.filter(a => a.action_type === 'interaction').length;
      
      // Calculate returns (number of sessions)
      const returns = sessions.length;
      
      if (isPersonalized) {
        personalizedSessionDuration += avgSessionDuration;
        personalizedPageViews += pageViews;
        personalizedInteractions += interactions;
        personalizedReturns += returns;
        personalizedUserCount++;
      } else {
        nonPersonalizedSessionDuration += avgSessionDuration;
        nonPersonalizedPageViews += pageViews;
        nonPersonalizedInteractions += interactions;
        nonPersonalizedReturns += returns;
        nonPersonalizedUserCount++;
      }
    });
    
    // Calculate average metrics
    const avgPersonalizedSessionDuration = personalizedUserCount > 0 ? personalizedSessionDuration / personalizedUserCount : 0;
    const avgPersonalizedPageViews = personalizedUserCount > 0 ? personalizedPageViews / personalizedUserCount : 0;
    const avgPersonalizedInteractions = personalizedUserCount > 0 ? personalizedInteractions / personalizedUserCount : 0;
    const avgPersonalizedReturns = personalizedUserCount > 0 ? personalizedReturns / personalizedUserCount : 0;
    
    const avgNonPersonalizedSessionDuration = nonPersonalizedUserCount > 0 ? nonPersonalizedSessionDuration / nonPersonalizedUserCount : 0;
    const avgNonPersonalizedPageViews = nonPersonalizedUserCount > 0 ? nonPersonalizedPageViews / nonPersonalizedUserCount : 0;
    const avgNonPersonalizedInteractions = nonPersonalizedUserCount > 0 ? nonPersonalizedInteractions / nonPersonalizedUserCount : 0;
    const avgNonPersonalizedReturns = nonPersonalizedUserCount > 0 ? nonPersonalizedReturns / nonPersonalizedUserCount : 0;
    
    // Calculate impact percentages
    const sessionDurationImpact = avgNonPersonalizedSessionDuration > 0
      ? ((avgPersonalizedSessionDuration - avgNonPersonalizedSessionDuration) / avgNonPersonalizedSessionDuration) * 100
      : 0;
      
    const pageViewsImpact = avgNonPersonalizedPageViews > 0
      ? ((avgPersonalizedPageViews - avgNonPersonalizedPageViews) / avgNonPersonalizedPageViews) * 100
      : 0;
      
    const interactionRateImpact = avgNonPersonalizedInteractions > 0
      ? ((avgPersonalizedInteractions - avgNonPersonalizedInteractions) / avgNonPersonalizedInteractions) * 100
      : 0;
      
    const returnRateImpact = avgNonPersonalizedReturns > 0
      ? ((avgPersonalizedReturns - avgNonPersonalizedReturns) / avgNonPersonalizedReturns) * 100
      : 0;
    
    // Calculate business impact
    // Group business data by date
    const businessByDate = {};
    
    businessData.forEach(metric => {
      const date = metric.date.split('T')[0];
      businessByDate[date] = metric;
    });
    
    // Group personalization events by date
    const personalizationByDate = {};
    
    personalizationData.forEach(event => {
      const date = event.created_at.split('T')[0];
      
      if (!personalizationByDate[date]) {
        personalizationByDate[date] = 0;
      }
      
      personalizationByDate[date]++;
    });
    
    // Calculate correlation between personalization and business metrics
    const dates = Object.keys(businessByDate).sort();
    
    const personalizationCounts = dates.map(date => personalizationByDate[date] || 0);
    const conversionRates = dates.map(date => businessByDate[date].conversion_rate || 0);
    const revenues = dates.map(date => businessByDate[date].revenue || 0);
    const retentionRates = dates.map(date => businessByDate[date].retention_rate || 0);
    const satisfactionScores = dates.map(date => businessByDate[date].satisfaction_score || 0);
    
    const conversionCorrelation = calculateCorrelation(personalizationCounts, conversionRates);
    const revenueCorrelation = calculateCorrelation(personalizationCounts, revenues);
    const retentionCorrelation = calculateCorrelation(personalizationCounts, retentionRates);
    const satisfactionCorrelation = calculateCorrelation(personalizationCounts, satisfactionScores);
    
    // Analyze personalization types
    const personalizationTypes = {};
    
    personalizationData.forEach(event => {
      if (!personalizationTypes[event.type]) {
        personalizationTypes[event.type] = {
          count: 0,
          engagementImpact: 0,
          businessImpact: 0
        };
      }
      
      personalizationTypes[event.type].count++;
    });
    
    // Calculate impact for each personalization type
    Object.keys(personalizationTypes).forEach(type => {
      const typeEvents = personalizationData.filter(event => event.type === type);
      const typeUsers = new Set(typeEvents.map(event => event.user_id));
      
      let typeSessionDuration = 0;
      let typePageViews = 0;
      let typeInteractions = 0;
      let typeUserCount = 0;
      
      typeUsers.forEach(userId => {
        const userActivities = activityByUser[userId] || [];
        
        if (userActivities.length === 0) {
          return;
        }
        
        // Calculate session duration
        const sessions = [];
        let currentSession = [];
        
        userActivities.forEach((activity, index) => {
          if (index === 0) {
            currentSession.push(activity);
          } else {
            const prevActivity = userActivities[index - 1];
            const timeDiff = new Date(activity.created_at) - new Date(prevActivity.created_at);
            
            // If more than 30 minutes between activities, start a new session
            if (timeDiff > 30 * 60 * 1000) {
              sessions.push(currentSession);
              currentSession = [activity];
            } else {
              currentSession.push(activity);
            }
          }
        });
        
        // Add the last session
        if (currentSession.length > 0) {
          sessions.push(currentSession);
        }
        
        // Calculate session durations
        const sessionDurations = sessions.map(session => {
          const startTime = new Date(session[0].created_at);
          const endTime = new Date(session[session.length - 1].created_at);
          
          return (endTime - startTime) / 1000; // in seconds
        });
        
        // Calculate average session duration
        const avgSessionDuration = sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessions.length || 0;
        
        // Calculate page views
        const pageViews = userActivities.filter(a => a.action_type === 'page_view').length;
        
        // Calculate interactions
        const interactions = userActivities.filter(a => a.action_type === 'interaction').length;
        
        typeSessionDuration += avgSessionDuration;
        typePageViews += pageViews;
        typeInteractions += interactions;
        typeUserCount++;
      });
      
      // Calculate average metrics
      const avgTypeSessionDuration = typeUserCount > 0 ? typeSessionDuration / typeUserCount : 0;
      const avgTypePageViews = typeUserCount > 0 ? typePageViews / typeUserCount : 0;
      const avgTypeInteractions = typeUserCount > 0 ? typeInteractions / typeUserCount : 0;
      
      // Calculate impact percentages
      const typeSessionDurationImpact = avgNonPersonalizedSessionDuration > 0
        ? ((avgTypeSessionDuration - avgNonPersonalizedSessionDuration) / avgNonPersonalizedSessionDuration) * 100
        : 0;
        
      const typePageViewsImpact = avgNonPersonalizedPageViews > 0
        ? ((avgTypePageViews - avgNonPersonalizedPageViews) / avgNonPersonalizedPageViews) * 100
        : 0;
        
      const typeInteractionRateImpact = avgNonPersonalizedInteractions > 0
        ? ((avgTypeInteractions - avgNonPersonalizedInteractions) / avgNonPersonalizedInteractions) * 100
        : 0;
      
      // Calculate overall engagement impact
      personalizationTypes[type].engagementImpact = (typeSessionDurationImpact + typePageViewsImpact + typeInteractionRateImpact) / 3;
      
      // Calculate business impact (simplified)
      personalizationTypes[type].businessImpact = (conversionCorrelation + revenueCorrelation + retentionCorrelation + satisfactionCorrelation) / 4 * 100;
    });
    
    // Sort personalization types by impact
    const sortedTypes = Object.entries(personalizationTypes)
      .map(([type, data]) => ({
        type,
        count: data.count,
        engagementImpact: data.engagementImpact,
        businessImpact: data.businessImpact,
        overallImpact: (data.engagementImpact + data.businessImpact) / 2
      }))
      .sort((a, b) => b.overallImpact - a.overallImpact);
    
    // Generate recommendations
    const recommendations = [];
    
    if (sortedTypes.length > 0) {
      const bestType = sortedTypes[0];
      recommendations.push(`Increase "${bestType.type}" personalization which shows the highest impact (${bestType.overallImpact.toFixed(2)}%)`);
    }
    
    if (sortedTypes.length > 1) {
      const worstType = sortedTypes[sortedTypes.length - 1];
      
      if (worstType.overallImpact < 0) {
        recommendations.push(`Reconsider "${worstType.type}" personalization which shows negative impact (${worstType.overallImpact.toFixed(2)}%)`);
      }
    }
    
    if (sessionDurationImpact > 0) {
      recommendations.push(`Personalization increases session duration by ${sessionDurationImpact.toFixed(2)}%`);
    }
    
    if (pageViewsImpact > 0) {
      recommendations.push(`Personalization increases page views by ${pageViewsImpact.toFixed(2)}%`);
    }
    
    if (interactionRateImpact > 0) {
      recommendations.push(`Personalization increases interaction rate by ${interactionRateImpact.toFixed(2)}%`);
    }
    
    if (returnRateImpact > 0) {
      recommendations.push(`Personalization increases return rate by ${returnRateImpact.toFixed(2)}%`);
    }
    
    if (conversionCorrelation > 0.5) {
      recommendations.push(`Strong correlation between personalization and conversion rate (${conversionCorrelation.toFixed(2)})`);
    }
    
    if (revenueCorrelation > 0.5) {
      recommendations.push(`Strong correlation between personalization and revenue (${revenueCorrelation.toFixed(2)})`);
    }
    
    return {
      engagementImpact: {
        sessionDuration: sessionDurationImpact,
        pageViews: pageViewsImpact,
        interactionRate: interactionRateImpact,
        returnRate: returnRateImpact
      },
      businessImpact: {
        conversionRate: conversionCorrelation * 100,
        revenue: revenueCorrelation * 100,
        userRetention: retentionCorrelation * 100,
        customerSatisfaction: satisfactionCorrelation * 100
      },
      personalizationTypes: sortedTypes,
      recommendations
    };
  } catch (error) {
    logger.error('Error in analyzePersonalizationImpact', { error: error.message });
    
    return {
      engagementImpact: {
        sessionDuration: 0,
        pageViews: 0,
        interactionRate: 0,
        returnRate: 0
      },
      businessImpact: {
        conversionRate: 0,
        revenue: 0,
        userRetention: 0,
        customerSatisfaction: 0
      },
      personalizationTypes: [],
      recommendations: []
    };
  }
}

/**
 * Calculate correlation between two arrays
 * 
 * @param {Array} x - First array
 * @param {Array} y - Second array
 * @returns {number} - Correlation coefficient
 */
function calculateCorrelation(x, y) {
  if (x.length !== y.length) {
    return 0;
  }
  
  const n = x.length;
  
  if (n === 0) {
    return 0;
  }
  
  // Calculate means
  const xMean = x.reduce((sum, val) => sum + val, 0) / n;
  const yMean = y.reduce((sum, val) => sum + val, 0) / n;
  
  // Calculate covariance and variances
  let covariance = 0;
  let xVariance = 0;
  let yVariance = 0;
  
  for (let i = 0; i < n; i++) {
    const xDiff = x[i] - xMean;
    const yDiff = y[i] - yMean;
    
    covariance += xDiff * yDiff;
    xVariance += xDiff * xDiff;
    yVariance += yDiff * yDiff;
  }
  
  // Calculate correlation
  if (xVariance === 0 || yVariance === 0) {
    return 0;
  }
  
  return covariance / Math.sqrt(xVariance * yVariance);
}

// API endpoints
app.get('/api/personalization/impact', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const impact = await analyzePersonalizationImpact(parseInt(days));
    res.json(impact);
  } catch (error) {
    logger.error('Error in GET /api/personalization/impact', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/personalization/types', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const impact = await analyzePersonalizationImpact(parseInt(days));
    res.json({ types: impact.personalizationTypes });
  } catch (error) {
    logger.error('Error in GET /api/personalization/types', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/personalization/recommendations', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const impact = await analyzePersonalizationImpact(parseInt(days));
    res.json({ recommendations: impact.recommendations });
  } catch (error) {
    logger.error('Error in GET /api/personalization/recommendations', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.PERSONALIZATION_IMPACT_ANALYZER_PORT || 9105;
app.listen(PORT, () => {
  logger.info(`Personalization Impact Analyzer listening on port ${PORT}`);
});

module.exports = {
  analyzePersonalizationImpact,
  calculateCorrelation
};
