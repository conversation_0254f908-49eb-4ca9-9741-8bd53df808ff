# Vitest Migration for Vendor Portal Extension

## Overview

This document provides instructions for migrating tests from <PERSON><PERSON> to <PERSON>itest in the vendor portal extension. Vitest is a Vite-native test runner that provides a faster, more modern testing experience while maintaining compatibility with the Jest API.

## Getting Started

### 1. Install Dependencies

The dependencies have already been added to package.json. To install them, run:

```bash
npm install
```

### 2. Run Tests with Vitest

To run tests with Vitest:

```bash
npm run test:vitest
```

To run tests in watch mode:

```bash
npm run test:vitest:watch
```

To run tests with coverage:

```bash
npm run test:vitest:coverage
```

## Migration Scripts

The following scripts have been created to help with the migration:

### 1. Jest to Vitest Migration Script

This script converts Jest syntax to Vitest syntax in test files:

```bash
node scripts/jest-to-vitest-migration.js
```

### 2. Package.json Update Script

This script updates package.json to use Vitest instead of Jest:

```bash
node scripts/update-package-json.js
```

## Example Test Files

The following test files have been created as examples of how to write tests with Vitest:

- `src/components/GuidedSetupWizard/tests/GuidedSetupWizard.vitest.js`
- `src/components/GuidedSetupWizard/steps/tests/CompanyProfileStep.vitest.js`

## Key Changes

### 1. Import Statements

```javascript
// Before (Jest)
// No imports needed, globals available

// After (Vitest)
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
```

### 2. Mocking

```javascript
// Before (Jest)
jest.mock('./module');
jest.fn();
jest.spyOn(object, 'method');

// After (Vitest)
vi.mock('./module');
vi.fn();
vi.spyOn(object, 'method');
```

### 3. Component Cleanup

```javascript
// Before (Jest)
afterEach(() => wrapper.destroy());

// After (Vitest)
afterEach(() => wrapper.unmount());
```

## Common Issues

### 1. Component Mocking

When mocking Vue components, use the following pattern:

```javascript
vi.mock('../Component.vue', () => ({
  name: 'Component',
  render: h => h('div'),
  props: ['propName']
}));
```

### 2. Timer Mocks

```javascript
// Before (Jest)
jest.useFakeTimers();
jest.advanceTimersByTime(1000);

// After (Vitest)
vi.useFakeTimers();
vi.advanceTimersByTime(1000);
```

### 3. Snapshot Testing

Snapshot testing works the same way in Vitest as in Jest:

```javascript
expect(wrapper.html()).toMatchSnapshot();
```

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [Vitest Migration Guide](https://vitest.dev/guide/migration.html)
- [Vue Test Utils Documentation](https://vue-test-utils.vuejs.org/)
- [MVS-VR Jest to Vitest Migration Guide](../../../../../docs/JEST_TO_VITEST_MIGRATION.md)
- [Vitest Migration Summary](./VITEST_MIGRATION_SUMMARY.md)
