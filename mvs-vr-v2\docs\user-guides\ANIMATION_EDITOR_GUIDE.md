# Animation Editor User Guide

## Overview

The Animation Editor is a powerful tool for creating and managing animations for your 3D objects in the MVS-VR platform. This guide will walk you through the basic and advanced features of the Animation Editor, providing detailed instructions and best practices to help you create stunning animations for your virtual showrooms.

## Table of Contents

1. [Getting Started](#getting-started)
   - [Accessing the Animation Editor](#accessing-the-animation-editor)
   - [Understanding the Interface](#understanding-the-interface)
2. [Basic Features](#basic-features)
   - [Creating Animations](#creating-animations)
   - [Managing Tracks](#managing-tracks)
   - [Working with Keyframes](#working-with-keyframes)
   - [Playback Controls](#playback-controls)
3. [Advanced Features](#advanced-features)
   - [Animation Blending](#animation-blending)
   - [Easing Presets](#easing-presets)
   - [Custom Interpolation Curves](#custom-interpolation-curves)
   - [Collaborative Editing](#collaborative-editing)
4. [Tips and Best Practices](#tips-and-best-practices)
5. [Keyboard Shortcuts](#keyboard-shortcuts)
6. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing the Animation Editor

1. Log in to the MVS-VR Vendor Portal with your credentials.
2. Navigate to the "Visual Editors" section in the main navigation menu.
3. Click on the "Animation" tab at the top of the Visual Editors interface.

### Understanding the Interface

The Animation Editor interface consists of several key areas:

![Animation Editor Interface](../assets/images/animation-editor-interface.png)

1. **Editor Header**: Contains the title, save/reset buttons, and blend animations button.
2. **Sidebar**: Lists all available animations with options to create new ones or delete existing ones.
3. **Timeline**: Displays animation tracks and keyframes with a time ruler and playback controls.
4. **Properties Panel**: Provides editing interfaces for animation and keyframe properties.
5. **Blend Dialog**: Modal for creating new animations by blending existing ones (accessed via the "Blend Animations" button).

## Basic Features

### Creating Animations

1. **Open the Animation Editor**: Navigate to the Visual Editors section and select the "Animation" tab.
2. **Create a New Animation**: Click the "Create New Animation" button in the sidebar.
3. **Set Animation Properties**:
   - **Name**: Give your animation a descriptive name (e.g., "Product_Rotation", "Door_Open")
   - **Duration**: Set the length of the animation in seconds (e.g., 5 seconds)
   - **Loop**: Toggle whether the animation should repeat continuously

#### Example: Creating a 5-second Rotating Animation

1. Click "Create New Animation"
2. Name it "Product_Rotation"
3. Set duration to 5 seconds
4. Enable looping if you want the rotation to continue indefinitely
5. Click "Save" to store your animation

### Managing Tracks

Animations are organized into tracks, where each track controls a specific aspect of an object.

1. **Add a Track**: Click the "Add Track" button below the timeline.
2. **Name Your Track**: Give the track a descriptive name (e.g., "Main_Rotation", "Color_Change")
3. **Select Track Type**:
   - **Transform**: Controls position, rotation, and scale
   - **Visibility**: Controls object visibility and opacity
   - **Material**: Controls material properties like color, metalness, and roughness

### Working with Keyframes

Keyframes define the state of an object at specific points in time. By setting keyframes at different times with different values, you create animation through interpolation between these states.

1. **Add a Keyframe**:
   - Click on a track at the desired time position
   - Alternatively, position the timeline scrubber where you want the keyframe and click the "Add Keyframe" button
   - For new tracks, two keyframes are automatically created at the start and end of the animation

2. **Edit a Keyframe**:
   - Select a keyframe by clicking on it in the timeline
   - Modify its properties in the Properties panel on the right
   - For transform keyframes, you can adjust position, rotation, and scale values
   - For visibility keyframes, you can set visibility state and opacity
   - For material keyframes, you can adjust color, metalness, roughness, and other properties

3. **Move a Keyframe**:
   - Click and drag a keyframe along the timeline to change its time position
   - Alternatively, select the keyframe and directly edit the "Time" value in the Properties panel

4. **Delete a Keyframe**:
   - Select a keyframe and press the Delete key
   - Alternatively, right-click on a keyframe and select "Delete Keyframe" from the context menu

#### Example: Creating a Rotation Animation

1. Create a new animation named "Product_Rotation"
2. Add a Transform track named "Rotation_Track"
3. Select the first keyframe (at time 0) and set rotation values to (0, 0, 0)
4. Select the last keyframe (at the end of the timeline) and set rotation values to (0, 360, 0)
5. Play the animation to see the object rotate 360 degrees around the Y axis

### Playback Controls

Use the playback controls to preview your animation:

- **Play/Pause Button**: Start or pause the animation playback
- **Stop Button**: Stop the animation and reset to the beginning
- **Time Display**: Shows the current time position and total duration
- **Timeline Scrubber**: Drag to manually scrub through the animation
- **Loop Toggle**: Enable or disable animation looping in the Animation Properties panel
- **Playback Speed**: Adjust the playback speed (0.1x to 2.0x) in the Animation Properties panel

## Advanced Features

### Animation Blending

Animation blending allows you to create new animations by combining existing ones. This powerful feature enables you to create complex animations from simpler building blocks.

1. **Open the Blend Dialog**:
   - Click the "Blend Animations" button in the editor header
   - Note: You need at least two animations to use this feature

2. **Select Source Animations**:
   - Choose two animations from your library to blend
   - The source animations can have different durations and track structures

3. **Adjust Blend Factor**:
   - Use the slider to control how much each animation contributes to the result
   - A value of 0 means 100% of Animation 1, while 1 means 100% of Animation 2
   - A value of 0.5 means an equal blend of both animations

4. **Preview the Result**:
   - Use the preview window to see how the blended animation will look
   - Adjust the blend factor until you achieve the desired result

5. **Name the Result**:
   - Give your blended animation a descriptive name
   - Consider including the source animation names for clarity

6. **Create Blended Animation**:
   - Click "Create Blended Animation" to generate a new animation
   - The new animation will appear in your animation list

#### How Blending Works

- The blending process creates a new animation with the maximum duration of the two source animations
- Numeric values (position, rotation, scale, etc.) are interpolated based on the blend factor
- Tracks that exist in only one animation are included with scaled values
- Common tracks are blended together
- Keyframe timing is preserved, but values are interpolated

#### Use Cases for Animation Blending

- Create transitions between different animations
- Combine partial animations (e.g., upper body + lower body)
- Create variations of existing animations
- Smooth out or exaggerate movements
- Create complex animations from simple building blocks

### Easing Presets

Easing presets control how values change between keyframes, allowing you to create more natural and appealing animations.

1. **Select a Keyframe**: Click on a keyframe in the timeline.

2. **Choose an Easing Preset**: In the Properties panel, select from available presets:
   - **Linear**: Constant rate of change (no easing)
   - **Ease In Quad**: Slow start, accelerating to end (quadratic)
   - **Ease Out Quad**: Fast start, decelerating to end (quadratic)
   - **Ease In Out Quad**: Slow start, fast middle, slow end (quadratic)
   - **Ease In Cubic**: More pronounced slow start (cubic)
   - **Ease Out Cubic**: More pronounced slow end (cubic)
   - **Ease In Out Cubic**: More pronounced slow start and end (cubic)
   - **Ease In Elastic**: Elastic effect at the start
   - **Ease Out Elastic**: Elastic effect at the end
   - **Ease In Out Elastic**: Elastic effect at both start and end
   - **Ease In Bounce**: Bouncing effect at the start
   - **Ease Out Bounce**: Bouncing effect at the end
   - **Ease In Out Bounce**: Bouncing effect at both start and end
   - **Custom Curve**: Create your own custom easing curve

### Custom Interpolation Curves

For more precise control over animation timing and movement, you can create custom interpolation curves using the Bezier curve editor.

1. **Select a Keyframe**: Click on a keyframe in the timeline.

2. **Select "Custom Curve"**: In the Easing dropdown of the Properties panel, select "Custom Curve".

3. **Use the Curve Editor**:
   - The curve editor will appear with a default curve
   - The X-axis represents time (0 to 1)
   - The Y-axis represents the interpolated value (0 to 1)
   - Two control points (circles) determine the shape of the curve

4. **Adjust Control Points**:
   - Click and drag the control points to shape the curve
   - The first control point affects the beginning of the interpolation
   - The second control point affects the end of the interpolation
   - Experiment with different positions to achieve the desired effect

5. **Preview the Curve**:
   - The curve preview updates in real-time as you adjust the control points
   - The shape of the curve indicates how values will change over time
   - Steep sections indicate rapid change, flat sections indicate slow change

6. **Apply the Curve**:
   - Click "Apply" to use the custom curve
   - Click "Reset" to return to the default curve shape

#### Example Curve Shapes and Their Effects

- **S-Curve**: Control points at (0.25, 0.75) and (0.75, 0.25) create a smooth S-curve with gentle acceleration and deceleration
- **Anticipation**: Control points at (0.2, -0.2) and (0.8, 1.0) create an anticipation effect where values briefly go in the opposite direction before the main movement
- **Overshoot**: Control points at (0.2, 1.2) and (0.8, 0.8) create an overshoot effect where values exceed the target before settling

### Collaborative Editing

The Animation Editor supports real-time collaborative editing, allowing multiple team members to work on the same animation simultaneously.

1. **Join a Collaboration Session**:
   - When you open an animation that someone else is editing, you'll see a notification
   - Click "Join Collaboration" to enter the collaborative editing mode
   - You'll see a list of other users currently editing the animation

2. **User Presence Indicators**:
   - Each user is represented by a colored avatar in the top-right corner
   - Hover over an avatar to see the user's name
   - Active users have a green dot, idle users have a yellow dot

3. **Real-time Updates**:
   - Changes made by other users appear in real-time
   - You can see other users' cursor positions and selections
   - Different users can work on different parts of the animation simultaneously

4. **Chat with Collaborators**:
   - Click the chat icon to open the collaboration chat panel
   - Type messages to communicate with other editors
   - Messages are saved with the collaboration session

5. **Conflict Resolution**:
   - If two users edit the same element, the system automatically merges changes
   - In case of conflicts, the most recent change takes precedence
   - A notification appears when conflicts are resolved

## Tips and Best Practices

- **Keep animations simple**: Focus on one movement per animation for better reusability. Complex animations are harder to reuse and maintain.

- **Use meaningful names**: Name your animations and tracks descriptively. This makes it easier to find and understand them later.

- **Create a library of base animations**: Build a collection of simple, reusable animations that can be combined or blended.

- **Test in context**: Preview animations in the actual environment where they'll be used. What looks good in isolation might not work well in context.

- **Save frequently**: Use the Save button regularly to avoid losing changes. The editor does not auto-save by default.

- **Use keyframes efficiently**: Use as few keyframes as necessary to achieve the desired effect. Too many keyframes can make animations harder to edit and may impact performance.

- **Master easing functions**: Learn how different easing functions affect movement. Proper easing is key to natural-looking animations.

- **Consider performance**: Complex animations with many tracks and keyframes can impact performance, especially on lower-end devices.

- **Use the timeline efficiently**:
  - Zoom in for precise keyframe placement
  - Use the scrubber to preview specific parts of the animation
  - Group related tracks together

- **Document your animations**: Keep notes about what each animation is for and how it should be used.

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| **Space** | Play/Pause animation |
| **Home** | Go to start of animation |
| **End** | Go to end of animation |
| **Delete** | Delete selected keyframe |
| **Ctrl+Z** | Undo last action |
| **Ctrl+Y** | Redo last undone action |
| **Ctrl+S** | Save animations |
| **Ctrl+N** | Create new animation |
| **Ctrl+D** | Duplicate selected animation |
| **Ctrl+K** | Add keyframe at current time |
| **Ctrl+T** | Add new track |
| **+/-** | Zoom in/out on timeline |
| **Arrow keys** | Fine-tune selected keyframe position/values |

## Troubleshooting

### Common Issues and Solutions

#### Animation Not Saving

**Issue**: Changes to animations aren't being saved.
**Solution**:

- Check your internet connection
- Ensure you have the necessary permissions
- Try clicking the Save button manually
- Check for error messages in the editor header

#### Keyframes Not Working as Expected

**Issue**: Animation doesn't play correctly or keyframes don't seem to have an effect.
**Solution**:

- Verify that the track is targeting the correct object
- Check that keyframe values are within valid ranges
- Ensure there are at least two keyframes on the track
- Try a different easing function

#### Performance Issues

**Issue**: The editor becomes slow or unresponsive with large animations.
**Solution**:

- Enable virtual scrolling in the editor settings
- Break complex animations into smaller, simpler ones
- Reduce the number of keyframes and tracks
- Close other applications to free up system resources

#### Collaborative Editing Problems

**Issue**: Can't see other users' changes or experiencing conflicts.
**Solution**:

- Check your internet connection
- Refresh the page to rejoin the collaboration session
- Ask other users to save their changes
- Contact your system administrator if problems persist

If you encounter issues not covered here, please contact support at <<EMAIL>> or consult the MVS-VR knowledge base at <https://knowledge.mvs-vr.com>.
