/**
 * Manual test for AnimationEditor with enhanced performance optimizations
 * 
 * This script simulates the behavior of the AnimationEditor component
 * to test the integration of the enhanced VirtualListRenderer and PerformanceOptimizer
 */

const { VirtualListRenderer, PerformanceOptimizer } = require('../src/utils/PerformanceOptimizer');

// Mock API response
const mockApiResponse = (page, limit) => {
  return {
    data: {
      data: Array.from({ length: limit }, (_, i) => ({
        id: `animation-${(page - 1) * limit + i}`,
        name: `Animation ${(page - 1) * limit + i}`,
        duration: 5,
        tracks: Array.from({ length: 3 }, (_, j) => ({
          id: `track-${(page - 1) * limit + i}-${j}`,
          name: `Track ${j}`,
          keyframes: Array.from({ length: 5 }, (_, k) => ({
            id: `keyframe-${(page - 1) * limit + i}-${j}-${k}`,
            time: k,
            value: { x: k, y: k, z: k }
          }))
        }))
      }))
    }
  };
};

// Mock AnimationEditor component
class MockAnimationEditor {
  constructor() {
    this.animations = [];
    this.isLoading = false;
    this.error = null;
    this.performanceMetrics = {
      loadTime: 0,
      renderTime: 0,
      cacheHits: 0,
      cacheTimes: [],
      apiCalls: 0,
      apiTimes: []
    };
    
    // Initialize cache with memory management
    this.cache = new PerformanceOptimizer(50, 10 * 60 * 1000, {
      maxMemorySize: 10 * 1024 * 1024, // 10MB limit
      evictionThreshold: 0.8,
      trackHitRate: true
    });
    
    // Initialize virtual list renderer
    this.initVirtualListRenderer();
  }
  
  initVirtualListRenderer() {
    this.virtualListRenderer = new VirtualListRenderer(
      this.animations,
      60, // Item height in pixels
      400, // Container height
      5, // Buffer size
      {
        lazyLoad: true,
        loadMoreItems: this.loadAnimationsWithPagination.bind(this),
        loadThreshold: 0.7,
        pageSize: 20,
        prefetch: true,
        prefetchThreshold: 0.5
      }
    );
  }
  
  async loadAnimationsWithPagination(page = 1, limit = 20) {
    const startTime = performance.now();
    
    if (page === 1) {
      this.isLoading = true;
    }
    
    try {
      // Check cache first
      const cacheKey = `animations_page${page}_limit${limit}`;
      const cachedData = this.cache.get(cacheKey);
      
      if (cachedData) {
        console.log(`Using cached data for page ${page}`);
        
        // Track cache performance
        const loadTime = performance.now() - startTime;
        this.performanceMetrics.cacheHits++;
        this.performanceMetrics.cacheTimes.push(loadTime);
        
        return cachedData;
      }
      
      // Simulate API call
      console.log(`Fetching data for page ${page} from API`);
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
      
      const response = mockApiResponse(page, limit);
      const data = response.data.data;
      
      // Calculate approximate size
      const jsonString = JSON.stringify(data);
      const dataSize = jsonString.length * 2;
      
      // Cache the result
      this.cache.set(cacheKey, data, {
        size: dataSize,
        ttl: 10 * 60 * 1000
      });
      
      // Track API performance
      const loadTime = performance.now() - startTime;
      this.performanceMetrics.apiCalls++;
      this.performanceMetrics.apiTimes.push(loadTime);
      
      console.log(`Loaded page ${page} with ${data.length} animations (${Math.round(dataSize / 1024)}KB) in ${loadTime}ms`);
      
      return data;
    } catch (error) {
      console.error('Error loading animations:', error);
      return [];
    } finally {
      if (page === 1) {
        this.isLoading = false;
      }
      
      this.performanceMetrics.loadTime = performance.now() - startTime;
    }
  }
  
  async loadData() {
    console.log('Loading initial data...');
    
    try {
      // Get first page of animations
      const result = await this.loadAnimationsWithPagination(1, 20);
      this.animations = result;
      
      // Update virtual list renderer
      this.virtualListRenderer.updateItems(this.animations, {
        totalItems: 100, // Simulate knowing the total count
        hasMoreItems: true
      });
      
      console.log(`Loaded ${this.animations.length} animations`);
    } catch (error) {
      console.error('Error loading data:', error);
      this.error = 'Failed to load animations';
    }
  }
  
  simulateScroll(scrollPosition) {
    console.log(`Scrolling to position: ${scrollPosition}`);
    this.virtualListRenderer.updateScroll(scrollPosition);
    
    // Get visible items
    const result = this.virtualListRenderer.getVisibleItems();
    console.log(`Visible items: ${result.visibleItems.length}`);
    console.log('Performance metrics:', result.metrics);
  }
  
  cleanup() {
    console.log('Cleaning up resources...');
    
    if (this.virtualListRenderer) {
      this.virtualListRenderer.dispose();
    }
    
    if (this.cache) {
      console.log('Cache statistics:', this.cache.getStats());
      this.cache.dispose();
    }
    
    console.log('Final performance metrics:', this.performanceMetrics);
  }
}

// Run the test
async function runTest() {
  console.log('=== Testing AnimationEditor with Enhanced Performance Optimizations ===');
  
  const editor = new MockAnimationEditor();
  
  // Load initial data
  await editor.loadData();
  
  // Simulate scrolling to trigger lazy loading
  console.log('\n=== Testing Lazy Loading ===');
  const scrollPosition1 = 400; // Scroll down a bit
  editor.simulateScroll(scrollPosition1);
  
  // Wait a bit and scroll more to trigger prefetching
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('\n=== Testing Prefetching ===');
  const scrollPosition2 = 800; // Scroll down more
  editor.simulateScroll(scrollPosition2);
  
  // Wait a bit and scroll to the bottom to trigger loading more items
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('\n=== Testing Loading More Items ===');
  const scrollPosition3 = 1200; // Scroll to bottom
  editor.simulateScroll(scrollPosition3);
  
  // Wait for loading to complete
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Load the same data again to test caching
  console.log('\n=== Testing Caching ===');
  await editor.loadAnimationsWithPagination(1, 20);
  await editor.loadAnimationsWithPagination(2, 20);
  
  // Clean up
  editor.cleanup();
  
  console.log('\n=== Test Completed ===');
}

// Run the test
runTest().catch(console.error);
