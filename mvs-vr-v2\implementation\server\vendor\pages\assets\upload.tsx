import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  Snackbar,
  Paper,
} from '@mui/material';
import { CloudUpload as UploadIcon } from '@mui/icons-material';
import { useRouter } from 'next/router';
import VendorLayout from '../../components/VendorLayout';
import ProtectedRoute from '../../components/ProtectedRoute';
import { assetService } from '../../utils/api/asset-service';

/**
 * Asset upload page
 */
const AssetUpload: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [assetName, setAssetName] = useState('');
  const [assetType, setAssetType] = useState('model');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const selectedFile = event.target.files[0];
      setFile(selectedFile);
      
      // Auto-fill name if not already set
      if (!assetName) {
        setAssetName(selectedFile.name.split('.')[0]);
      }
    }
  };

  // Handle file drop
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const droppedFile = event.dataTransfer.files[0];
      setFile(droppedFile);
      
      // Auto-fill name if not already set
      if (!assetName) {
        setAssetName(droppedFile.name.split('.')[0]);
      }
    }
  }, [assetName]);

  // Prevent default behavior for drag events
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    // Validate form
    if (!file) {
      setError('Please select a file to upload');
      setShowError(true);
      return;
    }
    
    if (!assetName) {
      setError('Asset name is required');
      setShowError(true);
      return;
    }
    
    // Start upload
    try {
      setUploading(true);
      setError(null);
      
      // Create tag array from comma-separated string
      const tagArray = tags.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);
      
      // Upload asset
      await assetService.uploadAsset({
        file,
        name: assetName,
        type: assetType,
        description,
        tags: tagArray
      }, (progress) => {
        setUploadProgress(progress);
      });
      
      // Show success message
      setSuccess('Asset uploaded successfully');
      setShowSuccess(true);
      
      // Redirect to assets page after a delay
      setTimeout(() => {
        router.push('/assets');
      }, 2000);
    } catch (err: unknown) {
      console.error('Error uploading asset:', err);
      setError(err instanceof Error ? err.message : 'Failed to upload asset');
      setShowError(true);
    } finally {
      setUploading(false);
    }
  };

  // Handle error snackbar close
  const handleCloseError = () => {
    setShowError(false);
  };

  // Handle success snackbar close
  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  return (
    <VendorLayout title="Upload Asset">
      {/* Error snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      {/* Success snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Upload Asset
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" gutterBottom>
          Upload a new asset to your vendor portal
        </Typography>
      </Box>

      <Paper sx={{ p: 3, mb: 4 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* File Upload Area */}
            <Grid item xs={12}>
              <Box
                sx={{
                  border: '2px dashed #ccc',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: 'primary.main',
                  },
                }}
                onClick={() => fileInputRef.current?.click()}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                  accept=".glb,.gltf,.fbx,.obj,.usdz,.usd"
                />
                <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Drag and drop your file here or click to browse
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Supported formats: GLB, GLTF, FBX, OBJ, USDZ, USD
                </Typography>
                {file && (
                  <Box mt={2}>
                    <Alert severity="info">
                      Selected file: {file.name} ({(file.size / (1024 * 1024)).toFixed(2)} MB)
                    </Alert>
                  </Box>
                )}
              </Box>
            </Grid>

            {/* Asset Name */}
            <Grid item xs={12} md={6}>
              <TextField
                label="Asset Name"
                fullWidth
                value={assetName}
                onChange={(e) => setAssetName(e.target.value)}
                required
                disabled={uploading}
              />
            </Grid>

            {/* Asset Type */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Asset Type</InputLabel>
                <Select
                  value={assetType}
                  onChange={(e) => setAssetType(e.target.value)}
                  label="Asset Type"
                  disabled={uploading}
                >
                  <MenuItem value="model">3D Model</MenuItem>
                  <MenuItem value="texture">Texture</MenuItem>
                  <MenuItem value="material">Material</MenuItem>
                  <MenuItem value="animation">Animation</MenuItem>
                  <MenuItem value="scene">Scene</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Description */}
            <Grid item xs={12}>
              <TextField
                label="Description"
                fullWidth
                multiline
                rows={4}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={uploading}
              />
            </Grid>

            {/* Tags */}
            <Grid item xs={12}>
              <TextField
                label="Tags"
                fullWidth
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                disabled={uploading}
                helperText="Separate tags with commas"
              />
            </Grid>

            {/* Upload Progress */}
            {uploading && (
              <Grid item xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Uploading: {uploadProgress.toFixed(0)}%
                  </Typography>
                  <LinearProgress variant="determinate" value={uploadProgress} />
                </Box>
              </Grid>
            )}

            {/* Submit Button */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="flex-end">
                <Button
                  type="button"
                  variant="outlined"
                  sx={{ mr: 2 }}
                  disabled={uploading}
                  onClick={() => router.push('/assets')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={uploading || !file}
                  startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
                >
                  {uploading ? 'Uploading...' : 'Upload Asset'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </VendorLayout>
  );
};

// Wrap the AssetUpload component with ProtectedRoute
const ProtectedAssetUpload: React.FC = () => {
  return (
    <ProtectedRoute>
      <AssetUpload />
    </ProtectedRoute>
  );
};

export default ProtectedAssetUpload;
