/**
 * Run Jest to Vitest Migration
 * 
 * This script runs the Jest to Vitest migration script and then
 * updates the package.json test scripts to use Vitest.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Run the migration script
console.log('Running Jest to Vitest migration script...');
require('./jest-to-vitest-migration');

// Update package.json
console.log('\nUpdating package.json...');
const packageJsonPath = path.resolve(__dirname, '..', 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Update test scripts
if (packageJson.scripts) {
  // Backup original scripts
  const originalScripts = { ...packageJson.scripts };
  
  // Update test scripts
  Object.keys(packageJson.scripts).forEach(scriptName => {
    if (scriptName.includes('test') && packageJson.scripts[scriptName].includes('jest')) {
      const originalCommand = packageJson.scripts[scriptName];
      console.log(`  Updating script: ${scriptName}`);
      console.log(`    From: ${originalCommand}`);
      
      // Replace jest with vitest
      let newCommand = originalCommand
        .replace(/jest/g, 'vitest')
        .replace(/--coverage/g, 'run --coverage')
        .replace(/--watch/g, 'watch');
      
      console.log(`    To:   ${newCommand}`);
      packageJson.scripts[scriptName] = newCommand;
    }
  });
  
  // Add vitest scripts if they don't exist
  if (!packageJson.scripts['test:vitest']) {
    packageJson.scripts['test:vitest'] = 'vitest run';
    console.log('  Added script: test:vitest');
  }
  
  if (!packageJson.scripts['test:vitest:watch']) {
    packageJson.scripts['test:vitest:watch'] = 'vitest';
    console.log('  Added script: test:vitest:watch');
  }
  
  if (!packageJson.scripts['test:vitest:coverage']) {
    packageJson.scripts['test:vitest:coverage'] = 'vitest run --coverage';
    console.log('  Added script: test:vitest:coverage');
  }
  
  // Save updated package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('package.json updated successfully!');
}

// Create vitest.config.js if it doesn't exist
const vitestConfigPath = path.resolve(__dirname, '..', 'vitest.config.js');
if (!fs.existsSync(vitestConfigPath)) {
  console.log('\nCreating vitest.config.js...');
  const vitestConfig = `import { defineConfig } from 'vitest/config';
import { createVuePlugin } from 'vite-plugin-vue2';

export default defineConfig({
  plugins: [createVuePlugin()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.setup.js'],
    include: ['**/*.{test,spec,vitest}.?(c|m)[jt]s?(x)', '**/tests/**/*.?(c|m)[jt]s?(x)'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/**',
        'dist/**',
        'coverage/**',
        '**/*.d.ts',
        'vitest.config.js',
        'vitest.setup.js',
      ],
    },
    deps: {
      inline: ['vue', 'vue-router'],
    },
  },
  resolve: {
    alias: {
      '@': '/src',
      'vue': 'vue/dist/vue.js',
    },
  },
});`;
  
  fs.writeFileSync(vitestConfigPath, vitestConfig);
  console.log('vitest.config.js created successfully!');
}

// Create vitest.setup.js if it doesn't exist
const vitestSetupPath = path.resolve(__dirname, '..', 'vitest.setup.js');
if (!fs.existsSync(vitestSetupPath)) {
  console.log('\nCreating vitest.setup.js...');
  const vitestSetup = `/**
 * Vitest setup file for the vendor portal extension
 */

import Vue from 'vue';
import { config } from '@vue/test-utils';
import { vi } from 'vitest';

// Set Vue config
Vue.config.productionTip = false;
Vue.config.devtools = false;

// Mock global objects
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Set up Vue Test Utils config
config.mocks = {
  $t: (key) => key,
  $tc: (key) => key,
  $n: (value) => value,
  $d: (value) => value,
  $te: () => true,
};`;
  
  fs.writeFileSync(vitestSetupPath, vitestSetup);
  console.log('vitest.setup.js created successfully!');
}

console.log('\nMigration completed successfully!');
console.log('\nNext steps:');
console.log('1. Install Vitest dependencies:');
console.log('   npm install -D vitest jsdom @vitest/coverage-v8 vite-plugin-vue2');
console.log('2. Run the tests:');
console.log('   npm run test:vitest');
