import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  Typography,
  Alert,
  Snackbar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CloudDownload as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  Visibility as ViewIcon,
  CalendarToday as CalendarIcon,
  Storage as StorageIcon,
  Label as LabelIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import VendorLayout from '../../components/VendorLayout';
import ProtectedRoute from '../../components/ProtectedRoute';
import { assetService, Asset, AssetVersion } from '../../utils/api/asset-service';
import { format } from 'date-fns';
import Link from 'next/link';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`asset-tabpanel-${index}`}
      aria-labelledby={`asset-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

/**
 * Asset detail page
 */
const AssetDetail: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const [asset, setAsset] = useState<Asset | null>(null);
  const [versions, setVersions] = useState<AssetVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  // Fetch asset details
  useEffect(() => {
    const fetchAssetDetails = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);
        
        // Fetch asset details
        const assetData = await assetService.getAssetById(id as string);
        setAsset(assetData);
        
        // Fetch asset versions
        const versionsData = await assetService.getAssetVersions(id as string);
        setVersions(versionsData);
      } catch (err: unknown) {
        console.error('Error fetching asset details:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch asset details');
        setShowError(true);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAssetDetails();
  }, [id]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle asset deletion
  const handleDeleteAsset = async () => {
    if (!asset) return;
    
    if (!deleteConfirm) {
      setDeleteConfirm(true);
      setTimeout(() => setDeleteConfirm(false), 3000);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Delete asset
      await assetService.deleteAsset(asset.id);
      
      // Show success message
      setSuccess('Asset deleted successfully');
      setShowSuccess(true);
      
      // Redirect to assets page after a delay
      setTimeout(() => {
        router.push('/assets');
      }, 2000);
    } catch (err: unknown) {
      console.error('Error deleting asset:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete asset');
      setShowError(true);
      setLoading(false);
    }
  };

  // Handle version download
  const handleDownloadVersion = async (versionId: string) => {
    try {
      setError(null);
      
      // Get download URL
      const downloadUrl = await assetService.getAssetDownloadUrl(asset!.id, versionId);
      
      // Open download in new tab
      window.open(downloadUrl, '_blank');
    } catch (err: unknown) {
      console.error('Error downloading asset version:', err);
      setError(err instanceof Error ? err.message : 'Failed to download asset version');
      setShowError(true);
    }
  };

  // Handle error snackbar close
  const handleCloseError = () => {
    setShowError(false);
  };

  // Handle success snackbar close
  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading && !asset) {
    return (
      <VendorLayout title="Asset Details">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }

  return (
    <VendorLayout title={asset ? `Asset: ${asset.name}` : 'Asset Details'}>
      {/* Error snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      {/* Success snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      {/* Back button */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => router.push('/assets')}
          sx={{ mb: 2 }}
        >
          Back to Assets
        </Button>
        
        {asset && (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h4" component="h1" gutterBottom>
                {asset.name}
              </Typography>
              <Box>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  sx={{ mr: 1 }}
                  onClick={() => router.push(`/assets/edit/${asset.id}`)}
                >
                  Edit
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleDeleteAsset}
                >
                  {deleteConfirm ? 'Confirm Delete' : 'Delete'}
                </Button>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Chip
                label={asset.type.charAt(0).toUpperCase() + asset.type.slice(1)}
                color="primary"
                size="small"
                sx={{ mr: 1 }}
              />
              <Chip
                label={asset.status}
                color={asset.status === 'active' ? 'success' : 'warning'}
                size="small"
                sx={{ mr: 1 }}
              />
              <Typography variant="body2" color="textSecondary">
                Version {asset.version}
              </Typography>
            </Box>
          </>
        )}
      </Box>

      {asset && (
        <Paper sx={{ mb: 4 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab label="Details" />
            <Tab label="Versions" />
            <Tab label="Analytics" />
          </Tabs>

          {/* Details Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <DescriptionIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Description"
                      secondary={asset.description || 'No description provided'}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem>
                    <ListItemIcon>
                      <CalendarIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Created"
                      secondary={format(new Date(asset.created_at), 'PPP p')}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem>
                    <ListItemIcon>
                      <CalendarIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Last Updated"
                      secondary={format(new Date(asset.updated_at), 'PPP p')}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem>
                    <ListItemIcon>
                      <StorageIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Size"
                      secondary={formatFileSize(asset.size)}
                    />
                  </ListItem>
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ p: 2, border: '1px solid #eee', borderRadius: 1, height: '100%' }}>
                  <Typography variant="h6" gutterBottom>
                    Tags
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {asset.tags && asset.tags.length > 0 ? (
                      asset.tags.map((tag, index) => (
                        <Chip key={index} label={tag} size="small" />
                      ))
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        No tags
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Versions Tab */}
          <TabPanel value={tabValue} index={1}>
            {versions.length > 0 ? (
              <List>
                {versions.map((version) => (
                  <React.Fragment key={version.id}>
                    <ListItem
                      secondaryAction={
                        <Button
                          startIcon={<DownloadIcon />}
                          onClick={() => handleDownloadVersion(version.id)}
                        >
                          Download
                        </Button>
                      }
                    >
                      <ListItemIcon>
                        <HistoryIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={`Version ${version.version_number}`}
                        secondary={`Created: ${format(new Date(version.created_at), 'PPP p')} • Size: ${formatFileSize(version.size)}`}
                      />
                    </ListItem>
                    <Divider component="li" />
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body1" color="textSecondary">
                No version history available
              </Typography>
            )}
          </TabPanel>

          {/* Analytics Tab */}
          <TabPanel value={tabValue} index={2}>
            <Typography variant="body1" color="textSecondary">
              Analytics data is not available for this asset yet.
            </Typography>
          </TabPanel>
        </Paper>
      )}
    </VendorLayout>
  );
};

// Wrap the AssetDetail component with ProtectedRoute
const ProtectedAssetDetail: React.FC = () => {
  return (
    <ProtectedRoute>
      <AssetDetail />
    </ProtectedRoute>
  );
};

export default ProtectedAssetDetail;
