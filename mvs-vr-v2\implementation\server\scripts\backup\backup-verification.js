/**
 * Backup Verification
 * 
 * This script implements automated verification for backups, including
 * checksum validation and cross-region recovery testing.
 */

const { S3Client, GetObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { createHash } = require('crypto');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const stream = require('stream');
const { execSync } = require('child_process');
const pipeline = promisify(stream.pipeline);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);
const unlinkAsync = promisify(fs.unlink);
const existsAsync = promisify(fs.exists);

// Import cross-region replication module
const { verifyAllReplications } = require('./cross-region-replication');
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  primaryRegion: process.env.PRIMARY_REGION || 'us-east-1',
  secondaryRegion: process.env.SECONDARY_REGION || 'us-west-2',
  buckets: {
    database: {
      primary: process.env.PRIMARY_DB_BUCKET || 'mvs-vr-db-backups',
      secondary: process.env.SECONDARY_DB_BUCKET || 'mvs-vr-db-backups-dr'
    },
    files: {
      primary: process.env.PRIMARY_FILES_BUCKET || 'mvs-vr-file-backups',
      secondary: process.env.SECONDARY_FILES_BUCKET || 'mvs-vr-file-backups-dr'
    },
    config: {
      primary: process.env.PRIMARY_CONFIG_BUCKET || 'mvs-vr-config-backups',
      secondary: process.env.SECONDARY_CONFIG_BUCKET || 'mvs-vr-config-backups-dr'
    }
  },
  verificationLogPath: path.join(__dirname, '../../logs/verification.json'),
  tempDir: path.join(__dirname, '../../temp'),
  maxVerificationSamples: 5, // Maximum number of files to verify per bucket
  recoveryTestDir: path.join(__dirname, '../../temp/recovery-test'),
  recoveryScripts: {
    database: path.join(__dirname, '../recovery/database-recovery.js'),
    files: path.join(__dirname, '../recovery/file-recovery.js'),
    config: path.join(__dirname, '../recovery/config-recovery.js')
  }
};

/**
 * Create S3 client for a specific region
 * @param {string} region - AWS region
 * @returns {S3Client} S3 client
 */
function createS3Client(region) {
  return new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
}

/**
 * Load verification log
 * @returns {Object} Verification log
 */
async function loadVerificationLog() {
  try {
    if (await existsAsync(config.verificationLogPath)) {
      const data = await readFileAsync(config.verificationLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading verification log:', error);
  }
  
  return {
    lastRun: null,
    verifications: []
  };
}

/**
 * Save verification log
 * @param {Object} log - Verification log
 */
async function saveVerificationLog(log) {
  try {
    await writeFileAsync(
      config.verificationLogPath,
      JSON.stringify(log, null, 2),
      'utf8'
    );
  } catch (error) {
    console.error('Error saving verification log:', error);
  }
}

/**
 * Calculate checksum for a file
 * @param {string} filePath - Path to file
 * @returns {string} Checksum
 */
async function calculateFileChecksum(filePath) {
  return new Promise((resolve, reject) => {
    const hash = createHash('sha256');
    const stream = fs.createReadStream(filePath);
    
    stream.on('error', err => reject(err));
    stream.on('data', chunk => hash.update(chunk));
    stream.on('end', () => resolve(hash.digest('hex')));
  });
}

/**
 * Download object from S3
 * @param {S3Client} client - S3 client
 * @param {string} bucket - Bucket name
 * @param {string} key - Object key
 * @param {string} outputPath - Output file path
 * @returns {Promise<void>}
 */
async function downloadObject(client, bucket, key, outputPath) {
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key
  });
  
  const response = await client.send(command);
  
  // Ensure directory exists
  const dir = path.dirname(outputPath);
  if (!await existsAsync(dir)) {
    await mkdirAsync(dir, { recursive: true });
  }
  
  // Write file
  const writeStream = fs.createWriteStream(outputPath);
  await pipeline(response.Body, writeStream);
}

/**
 * Get object metadata from S3
 * @param {S3Client} client - S3 client
 * @param {string} bucket - Bucket name
 * @param {string} key - Object key
 * @returns {Promise<Object>} Object metadata
 */
async function getObjectMetadata(client, bucket, key) {
  const command = new HeadObjectCommand({
    Bucket: bucket,
    Key: key
  });
  
  return await client.send(command);
}

/**
 * Verify backup integrity with checksums
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @param {Array} objects - List of objects to verify
 * @returns {Promise<Object>} Verification results
 */
async function verifyBackupIntegrity(bucketType, objects) {
  const primaryClient = createS3Client(config.primaryRegion);
  const secondaryClient = createS3Client(config.secondaryRegion);
  
  const primaryBucket = config.buckets[bucketType].primary;
  const secondaryBucket = config.buckets[bucketType].secondary;
  
  logger.info(`Verifying ${bucketType} backup integrity...`);
  
  // Select a sample of objects to verify (to avoid downloading all objects)
  const samplesToVerify = objects.slice(0, config.maxVerificationSamples);
  
  const results = {
    bucketType,
    totalSamples: samplesToVerify.length,
    verifiedSamples: 0,
    integrityErrors: 0,
    details: []
  };
  
  for (const obj of samplesToVerify) {
    try {
      // Download from primary
      const primaryPath = path.join(config.tempDir, 'primary', bucketType, obj.Key);
      await downloadObject(primaryClient, primaryBucket, obj.Key, primaryPath);
      
      // Download from secondary
      const secondaryPath = path.join(config.tempDir, 'secondary', bucketType, obj.Key);
      await downloadObject(secondaryClient, secondaryBucket, obj.Key, secondaryPath);
      
      // Calculate checksums
      const primaryChecksum = await calculateFileChecksum(primaryPath);
      const secondaryChecksum = await calculateFileChecksum(secondaryPath);
      
      // Compare checksums
      const integrityValid = primaryChecksum === secondaryChecksum;
      
      results.details.push({
        key: obj.Key,
        primaryChecksum,
        secondaryChecksum,
        integrityValid,
        timestamp: new Date().toISOString()
      });
      
      if (integrityValid) {
        results.verifiedSamples++;
        logger.info(`Integrity verified for ${obj.Key}`);
      } else {
        results.integrityErrors++;
        console.error(`Integrity error for ${obj.Key}: checksums do not match`);
      }
      
      // Clean up
      await unlinkAsync(primaryPath);
      await unlinkAsync(secondaryPath);
    } catch (error) {
      console.error(`Error verifying ${obj.Key}:`, error);
      
      results.integrityErrors++;
      results.details.push({
        key: obj.Key,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  return results;
}

/**
 * Test cross-region recovery
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @returns {Promise<Object>} Recovery test results
 */
async function testCrossRegionRecovery(bucketType) {
  const secondaryClient = createS3Client(config.secondaryRegion);
  const secondaryBucket = config.buckets[bucketType].secondary;
  
  logger.info(`Testing ${bucketType} cross-region recovery...`);
  
  // Ensure recovery test directory exists
  if (!await existsAsync(config.recoveryTestDir)) {
    await mkdirAsync(config.recoveryTestDir, { recursive: true });
  }
  
  const results = {
    bucketType,
    recoverySuccess: false,
    recoveryTime: 0,
    details: {}
  };
  
  try {
    // Get latest backup from secondary region
    const command = new ListObjectsV2Command({
      Bucket: secondaryBucket,
      MaxKeys: 1,
      OrderBy: 'LastModified',
      Reverse: true
    });
    
    const response = await secondaryClient.send(command);
    
    if (!response.Contents || response.Contents.length === 0) {
      throw new Error('No backups found in secondary region');
    }
    
    const latestBackup = response.Contents[0];
    
    // Download backup
    const backupPath = path.join(config.recoveryTestDir, path.basename(latestBackup.Key));
    await downloadObject(secondaryClient, secondaryBucket, latestBackup.Key, backupPath);
    
    // Run recovery script
    const startTime = Date.now();
    
    const recoveryScript = config.recoveryScripts[bucketType];
    const recoveryOutput = execSync(`node ${recoveryScript} --test --source=${backupPath} --target=${config.recoveryTestDir}`, {
      encoding: 'utf8'
    });
    
    const endTime = Date.now();
    const recoveryTime = (endTime - startTime) / 1000; // Convert to seconds
    
    results.recoverySuccess = true;
    results.recoveryTime = recoveryTime;
    results.details = {
      backupKey: latestBackup.Key,
      backupSize: latestBackup.Size,
      recoveryOutput,
      timestamp: new Date().toISOString()
    };
    
    logger.info(`Recovery test successful for ${bucketType} in ${recoveryTime} seconds`);
  } catch (error) {
    console.error(`Error testing recovery for ${bucketType}:`, error);
    
    results.details = {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
  
  return results;
}

/**
 * Run automated verification for all bucket types
 * @returns {Promise<Object>} Verification results
 */
async function runAutomatedVerification() {
  const log = await loadVerificationLog();
  const startTime = new Date();
  
  logger.info(`Starting automated verification at ${startTime.toISOString();}`);
  
  const verificationResults = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    replication: {},
    integrity: {},
    recovery: {}
  };
  
  // First, verify replication
  verificationResults.replication = await verifyAllReplications();
  
  // Then, verify integrity for each bucket type
  for (const bucketType of Object.keys(config.buckets)) {
    try {
      // Get objects from replication verification
      const objects = verificationResults.replication.buckets[bucketType].objects || [];
      
      // Verify integrity
      verificationResults.integrity[bucketType] = await verifyBackupIntegrity(bucketType, objects);
      
      // Test recovery
      verificationResults.recovery[bucketType] = await testCrossRegionRecovery(bucketType);
    } catch (error) {
      console.error(`Error during verification for ${bucketType}:`, error);
      
      verificationResults.integrity[bucketType] = {
        error: error.message,
        timestamp: new Date().toISOString()
      };
      
      verificationResults.recovery[bucketType] = {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  const endTime = new Date();
  const durationMs = endTime - startTime;
  
  verificationResults.endTime = endTime.toISOString();
  verificationResults.duration = durationMs / 1000; // Convert to seconds
  
  // Update log
  log.lastRun = endTime.toISOString();
  log.verifications.push(verificationResults);
  
  // Keep only the last 100 verifications
  if (log.verifications.length > 100) {
    log.verifications = log.verifications.slice(-100);
  }
  
  await saveVerificationLog(log);
  
  logger.info(`Completed automated verification in ${verificationResults.duration} seconds`);
  
  return verificationResults;
}

// If script is run directly, run verification
if (require.main === module) {
  runAutomatedVerification()
    .then(results => {
      logger.info('Verification completed:');
      logger.info(JSON.stringify(results, null, 2););
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runAutomatedVerification,
  verifyBackupIntegrity,
  testCrossRegionRecovery
};
