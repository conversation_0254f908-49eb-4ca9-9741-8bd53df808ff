/**
 * API Key Middleware Tests (Vitest)
 *
 * This file contains tests for the API key authentication middleware.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createClient } from '@supabase/supabase-js';

// Import the middleware functions
import {
  authenticateApiKey,
  hasRequiredPermissions,
  hasRequiredScopes,
} from '../../../api/middleware/api-key-middleware.js';

describe('API Key Middleware', () => {
  let req;
  let res;
  let next;
  let mockSupabase;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock request, response, and next function
    req = {
      headers: {
        'x-api-key': 'test_api_key',
      },
      ip: '127.0.0.1',
      originalUrl: '/api/test',
      method: 'GET',
    };

    res = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
    };

    next = vi.fn();

    // Mock Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      update: vi.fn().mockReturnThis(),
    };

    vi.mocked(createClient).mockReturnValue(mockSupabase);
  });

  describe('authenticateApiKey', () => {
    it('should continue if API key is not required', async () => {
      // Remove API key from request
      req.headers['x-api-key'] = undefined;

      // Call middleware with required: false
      await authenticateApiKey({ required: false })(req, res, next);

      // Expect next to be called
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is required but not provided', async () => {
      // Remove API key from request
      req.headers['x-api-key'] = undefined;

      // Call middleware with required: true
      await authenticateApiKey({ required: true })(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'API_KEY_REQUIRED',
          message: 'API key is required',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is invalid', async () => {
      // Mock Supabase response for invalid API key
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: { message: 'API key not found' },
      });

      // Call middleware
      await authenticateApiKey()(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_API_KEY',
          message: 'Invalid API key',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });
  });

  // Tests for hasRequiredPermissions
  describe('hasRequiredPermissions', () => {
    it('should return true if no permissions are required', () => {
      expect(hasRequiredPermissions(['test:read'], [])).toBe(true);
    });

    it('should return true if key has wildcard permission', () => {
      expect(hasRequiredPermissions(['*'], ['test:read'])).toBe(true);
    });

    it('should return true if key has all required permissions', () => {
      expect(hasRequiredPermissions(['test:read', 'test:write'], ['test:read'])).toBe(true);
    });

    it('should return false if key does not have required permissions', () => {
      expect(hasRequiredPermissions(['test:read'], ['test:write'])).toBe(false);
    });
  });

  // Tests for hasRequiredScopes
  describe('hasRequiredScopes', () => {
    it('should return true if no scopes are required', () => {
      expect(hasRequiredScopes(['api'], [])).toBe(true);
    });

    it('should return true if key has wildcard scope', () => {
      expect(hasRequiredScopes(['*'], ['api'])).toBe(true);
    });

    it('should return true if key has all required scopes', () => {
      expect(hasRequiredScopes(['api', 'admin'], ['api'])).toBe(true);
    });

    it('should return false if key does not have required scopes', () => {
      expect(hasRequiredScopes(['api'], ['admin'])).toBe(false);
    });
  });
});
