<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MVS-VR API Documentation</title>
  <link rel="stylesheet" href="/api-docs/assets/custom.css">
  <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements/styles.min.css">
  <script src="https://unpkg.com/@stoplight/elements/web-components.min.js"></script>
  <script src="/api-docs/assets/interactive.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    .api-docs-header {
      background-color: #1a202c;
      color: white;
      padding: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .api-docs-header h1 {
      margin: 0;
      font-size: 1.5rem;
    }
    .api-docs-header .links {
      display: flex;
      gap: 1rem;
    }
    .api-docs-header .links a {
      color: white;
      text-decoration: none;
    }
    .api-docs-header .links a:hover {
      text-decoration: underline;
    }
    elements-api {
      display: block;
      height: calc(100vh - 60px);
    }
  </style>
</head>
<body>
  <div class="api-docs-header">
    <h1>MVS-VR API Documentation</h1>
    <div class="links">
      <a href="/api-docs">Swagger UI</a>
      <a href="/api-docs/openapi.yaml">OpenAPI Spec (YAML)</a>
      <a href="/api-docs/openapi.json">OpenAPI Spec (JSON)</a>
    </div>
  </div>
  <elements-api
    apiDescriptionUrl="/api-docs/openapi.json"
    router="hash"
    layout="sidebar"
    hideExport="true"
    hideInternal="true"
  />
</body>
</html>
