/**
 * Scheduled Backup Verification
 * 
 * This script sets up scheduled verification jobs for backups.
 */

const cron = require('node-cron');
const { runAutomatedVerification } = require('./backup-verification');
const { runReplication } = require('./cross-region-replication');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { logger } = require('../shared/utils/logger');
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const existsAsync = promisify(fs.exists);

// Configuration
const config = {
  replicationSchedule: process.env.REPLICATION_SCHEDULE || '0 */4 * * *', // Every 4 hours
  verificationSchedule: process.env.VERIFICATION_SCHEDULE || '0 2 * * *', // Daily at 2 AM
  reportPath: path.join(__dirname, '../../logs/verification-reports'),
  alertThresholds: {
    missingObjects: 5, // Alert if more than 5 objects are missing
    outdatedObjects: 10, // Alert if more than 10 objects are outdated
    integrityErrors: 1, // Alert if any integrity errors are found
    recoveryFailures: 1 // Alert if any recovery tests fail
  },
  notificationConfig: {
    enabled: process.env.NOTIFICATIONS_ENABLED === 'true',
    email: process.env.NOTIFICATION_EMAIL || '<EMAIL>',
    slack: process.env.SLACK_WEBHOOK_URL || '',
    teams: process.env.TEAMS_WEBHOOK_URL || ''
  }
};

/**
 * Ensure report directory exists
 */
async function ensureReportDirectory() {
  if (!await existsAsync(config.reportPath)) {
    await promisify(fs.mkdir)(config.reportPath, { recursive: true });
  }
}

/**
 * Generate verification report
 * @param {Object} results - Verification results
 * @returns {string} Report content
 */
function generateReport(results) {
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const reportContent = [];
  
  reportContent.push('# Backup Verification Report');
  reportContent.push(`Generated: ${new Date().toISOString()}`);
  reportContent.push('');
  
  // Summary
  reportContent.push('## Summary');
  reportContent.push('');
  reportContent.push(`- Start Time: ${results.startTime}`);
  reportContent.push(`- End Time: ${results.endTime}`);
  reportContent.push(`- Duration: ${results.duration} seconds`);
  reportContent.push('');
  
  // Replication status
  reportContent.push('## Replication Status');
  reportContent.push('');
  
  let totalMissing = 0;
  let totalOutdated = 0;
  
  for (const bucketType of Object.keys(results.replication.buckets)) {
    const bucket = results.replication.buckets[bucketType];
    
    if (bucket.error) {
      reportContent.push(`### ${bucketType} (ERROR)`);
      reportContent.push('');
      reportContent.push(`Error: ${bucket.error}`);
    } else {
      reportContent.push(`### ${bucketType}`);
      reportContent.push('');
      reportContent.push(`- Total Objects: ${bucket.totalObjects}`);
      reportContent.push(`- Replicated Objects: ${bucket.replicatedObjects}`);
      reportContent.push(`- Missing Objects: ${bucket.missingObjects}`);
      reportContent.push(`- Outdated Objects: ${bucket.outdatedObjects}`);
      reportContent.push(`- Replication Lag: ${bucket.replicationLag} seconds`);
      
      totalMissing += bucket.missingObjects;
      totalOutdated += bucket.outdatedObjects;
      
      if (bucket.missingObjects > 0 || bucket.outdatedObjects > 0) {
        reportContent.push('');
        reportContent.push('#### Issues');
        reportContent.push('');
        
        const issues = bucket.details.filter(d => d.status === 'missing' || d.status === 'outdated');
        
        for (const issue of issues.slice(0, 10)) { // Show only first 10 issues
          reportContent.push(`- ${issue.key}: ${issue.status}${issue.lag ? ` (lag: ${issue.lag} seconds)` : ''}`);
        }
        
        if (issues.length > 10) {
          reportContent.push(`- ... and ${issues.length - 10} more issues`);
        }
      }
    }
    
    reportContent.push('');
  }
  
  // Integrity status
  reportContent.push('## Integrity Status');
  reportContent.push('');
  
  let totalIntegrityErrors = 0;
  
  for (const bucketType of Object.keys(results.integrity)) {
    const integrity = results.integrity[bucketType];
    
    if (integrity.error) {
      reportContent.push(`### ${bucketType} (ERROR)`);
      reportContent.push('');
      reportContent.push(`Error: ${integrity.error}`);
    } else {
      reportContent.push(`### ${bucketType}`);
      reportContent.push('');
      reportContent.push(`- Total Samples: ${integrity.totalSamples}`);
      reportContent.push(`- Verified Samples: ${integrity.verifiedSamples}`);
      reportContent.push(`- Integrity Errors: ${integrity.integrityErrors}`);
      
      totalIntegrityErrors += integrity.integrityErrors;
      
      if (integrity.integrityErrors > 0) {
        reportContent.push('');
        reportContent.push('#### Issues');
        reportContent.push('');
        
        const issues = integrity.details.filter(d => !d.integrityValid);
        
        for (const issue of issues) {
          reportContent.push(`- ${issue.key}: Primary checksum (${issue.primaryChecksum.substring(0, 8)}...) does not match secondary (${issue.secondaryChecksum.substring(0, 8)}...)`);
        }
      }
    }
    
    reportContent.push('');
  }
  
  // Recovery status
  reportContent.push('## Recovery Status');
  reportContent.push('');
  
  let totalRecoveryFailures = 0;
  
  for (const bucketType of Object.keys(results.recovery)) {
    const recovery = results.recovery[bucketType];
    
    if (recovery.error) {
      reportContent.push(`### ${bucketType} (ERROR)`);
      reportContent.push('');
      reportContent.push(`Error: ${recovery.error}`);
      totalRecoveryFailures++;
    } else {
      reportContent.push(`### ${bucketType}`);
      reportContent.push('');
      reportContent.push(`- Recovery Success: ${recovery.recoverySuccess ? 'Yes' : 'No'}`);
      reportContent.push(`- Recovery Time: ${recovery.recoveryTime} seconds`);
      
      if (!recovery.recoverySuccess) {
        totalRecoveryFailures++;
      }
      
      if (recovery.details.error) {
        reportContent.push(`- Error: ${recovery.details.error}`);
      } else if (recovery.details.backupKey) {
        reportContent.push(`- Backup Key: ${recovery.details.backupKey}`);
        reportContent.push(`- Backup Size: ${(recovery.details.backupSize / 1024 / 1024).toFixed(2)} MB`);
      }
    }
    
    reportContent.push('');
  }
  
  // Alerts
  const alerts = [];
  
  if (totalMissing > config.alertThresholds.missingObjects) {
    alerts.push(`Missing Objects: ${totalMissing} (threshold: ${config.alertThresholds.missingObjects})`);
  }
  
  if (totalOutdated > config.alertThresholds.outdatedObjects) {
    alerts.push(`Outdated Objects: ${totalOutdated} (threshold: ${config.alertThresholds.outdatedObjects})`);
  }
  
  if (totalIntegrityErrors > config.alertThresholds.integrityErrors) {
    alerts.push(`Integrity Errors: ${totalIntegrityErrors} (threshold: ${config.alertThresholds.integrityErrors})`);
  }
  
  if (totalRecoveryFailures > config.alertThresholds.recoveryFailures) {
    alerts.push(`Recovery Failures: ${totalRecoveryFailures} (threshold: ${config.alertThresholds.recoveryFailures})`);
  }
  
  if (alerts.length > 0) {
    reportContent.push('## Alerts');
    reportContent.push('');
    
    for (const alert of alerts) {
      reportContent.push(`- ⚠️ ${alert}`);
    }
    
    reportContent.push('');
  }
  
  return {
    content: reportContent.join('\n'),
    alerts,
    timestamp
  };
}

/**
 * Save verification report
 * @param {Object} report - Report object
 */
async function saveReport(report) {
  await ensureReportDirectory();
  
  const reportPath = path.join(config.reportPath, `verification-report-${report.timestamp}.md`);
  await writeFileAsync(reportPath, report.content, 'utf8');
  
  logger.info(`Report saved to ${reportPath}`);
  
  return reportPath;
}

/**
 * Send notification
 * @param {Object} report - Report object
 * @param {string} reportPath - Path to report file
 */
async function sendNotification(report, reportPath) {
  if (!config.notificationConfig.enabled) {
    logger.info('Notifications disabled');
    return;
  }
  
  const hasAlerts = report.alerts.length > 0;
  const subject = hasAlerts 
    ? `⚠️ Backup Verification Alerts - ${report.timestamp}`
    : `✅ Backup Verification Successful - ${report.timestamp}`;
  
  logger.info(`Sending notification: ${subject}`);
  
  // Email notification
  if (config.notificationConfig.email) {
    // Implementation would depend on email service
    logger.info(`Would send email to ${config.notificationConfig.email}`);
  }
  
  // Slack notification
  if (config.notificationConfig.slack) {
    // Implementation would depend on Slack API
    logger.info(`Would send Slack notification to webhook`);
  }
  
  // Teams notification
  if (config.notificationConfig.teams) {
    // Implementation would depend on Teams API
    logger.info(`Would send Teams notification to webhook`);
  }
}

/**
 * Run verification job
 */
async function runVerificationJob() {
  logger.info('Starting verification job...');
  
  try {
    const results = await runAutomatedVerification();
    const report = generateReport(results);
    const reportPath = await saveReport(report);
    
    await sendNotification(report, reportPath);
    
    logger.info('Verification job completed');
  } catch (error) {
    console.error('Error running verification job:', error);
  }
}

/**
 * Run replication job
 */
async function runReplicationJob() {
  logger.info('Starting replication job...');
  
  try {
    await runReplication();
    logger.info('Replication job completed');
  } catch (error) {
    console.error('Error running replication job:', error);
  }
}

/**
 * Start scheduled jobs
 */
function startScheduledJobs() {
  logger.info(`Scheduling replication job: ${config.replicationSchedule}`);
  cron.schedule(config.replicationSchedule, runReplicationJob);
  
  logger.info(`Scheduling verification job: ${config.verificationSchedule}`);
  cron.schedule(config.verificationSchedule, runVerificationJob);
  
  logger.info('Scheduled jobs started');
}

// If script is run directly, start scheduled jobs
if (require.main === module) {
  startScheduledJobs();
}

module.exports = {
  startScheduledJobs,
  runVerificationJob,
  runReplicationJob
};
