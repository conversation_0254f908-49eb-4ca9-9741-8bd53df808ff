[{"id": "cpu-memory-correlation", "name": "CPU and Memory Correlation", "description": "Correlate CPU and memory alerts for the same host", "conditions": [{"type": "CPU_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "MEMORY_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["host"], "rootCause": "CPU_HIGH", "action": "CORRELATE", "incidentType": "PERFORMANCE", "incidentSeverity": "HIGH"}, {"id": "disk-io-correlation", "name": "Disk I/O Correlation", "description": "Correlate disk I/O alerts for the same host", "conditions": [{"type": "DISK_IO_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "DISK_LATENCY_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["host"], "rootCause": "DISK_IO_HIGH", "action": "CORRELATE", "incidentType": "PERFORMANCE", "incidentSeverity": "MEDIUM"}, {"id": "network-correlation", "name": "Network Correlation", "description": "Correlate network alerts for the same host", "conditions": [{"type": "NETWORK_TRAFFIC_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "NETWORK_ERRORS_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["host"], "rootCause": "NETWORK_TRAFFIC_HIGH", "action": "CORRELATE", "incidentType": "NETWORK", "incidentSeverity": "MEDIUM"}, {"id": "database-correlation", "name": "Database Correlation", "description": "Correlate database alerts", "conditions": [{"type": "DATABASE_CONNECTIONS_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "DATABASE_QUERY_SLOW", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["database"], "rootCause": "DATABASE_CONNECTIONS_HIGH", "action": "CORRELATE", "incidentType": "DATABASE", "incidentSeverity": "HIGH"}, {"id": "api-correlation", "name": "API Correlation", "description": "Correlate API alerts", "conditions": [{"type": "API_LATENCY_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "API_ERROR_RATE_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["service"], "rootCause": "API_LATENCY_HIGH", "action": "CORRELATE", "incidentType": "SERVICE", "incidentSeverity": "HIGH"}, {"id": "service-dependency-correlation", "name": "Service Dependency Correlation", "description": "Correlate service dependency alerts", "conditions": [{"type": "SERVICE_UNAVAILABLE", "source": "any", "severity": ["CRITICAL", "ERROR"]}, {"type": "DEPENDENCY_UNAVAILABLE", "source": "any", "severity": ["CRITICAL", "ERROR"]}], "timeWindowMs": 300000, "groupBy": ["service"], "rootCause": "DEPENDENCY_UNAVAILABLE", "action": "CORRELATE", "incidentType": "SERVICE", "incidentSeverity": "CRITICAL"}, {"id": "database-error-correlation", "name": "Database Error Correlation", "description": "Correlate database error alerts", "conditions": [{"type": "DATABASE_ERROR", "source": "any", "severity": ["ERROR", "CRITICAL"]}, {"type": "DATABASE_CONNECTION_FAILED", "source": "any", "severity": ["ERROR", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["database"], "rootCause": "DATABASE_CONNECTION_FAILED", "action": "CORRELATE", "incidentType": "DATABASE", "incidentSeverity": "CRITICAL"}, {"id": "storage-correlation", "name": "Storage Correlation", "description": "Correlate storage alerts", "conditions": [{"type": "DISK_SPACE_LOW", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "DISK_INODE_LOW", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["host", "mount"], "rootCause": "DISK_SPACE_LOW", "action": "CORRELATE", "incidentType": "STORAGE", "incidentSeverity": "HIGH"}, {"id": "application-error-correlation", "name": "Application Error Correlation", "description": "Correlate application error alerts", "conditions": [{"type": "APPLICATION_ERROR", "source": "any", "severity": ["ERROR", "CRITICAL"]}, {"type": "APPLICATION_CRASH", "source": "any", "severity": ["ERROR", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["application"], "rootCause": "APPLICATION_CRASH", "action": "CORRELATE", "incidentType": "APPLICATION", "incidentSeverity": "CRITICAL"}, {"id": "security-correlation", "name": "Security Alert Correlation", "description": "Correlate security alerts", "conditions": [{"type": "SECURITY_BREACH", "source": "any", "severity": ["CRITICAL"]}, {"type": "UNAUTHORIZED_ACCESS", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 600000, "groupBy": ["host"], "rootCause": "SECURITY_BREACH", "action": "CORRELATE", "incidentType": "SECURITY", "incidentSeverity": "CRITICAL"}, {"id": "load-balancer-correlation", "name": "Load Balancer Correlation", "description": "Correlate load balancer alerts", "conditions": [{"type": "LOAD_BALANCER_ERROR", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "BACKEND_UNAVAILABLE", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["load_balancer"], "rootCause": "BACKEND_UNAVAILABLE", "action": "CORRELATE", "incidentType": "NETWORK", "incidentSeverity": "HIGH"}, {"id": "cache-correlation", "name": "Cache Correlation", "description": "Correlate cache alerts", "conditions": [{"type": "CACHE_MISS_RATE_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "CACHE_EVICTION_RATE_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["cache"], "rootCause": "CACHE_EVICTION_RATE_HIGH", "action": "CORRELATE", "incidentType": "PERFORMANCE", "incidentSeverity": "MEDIUM"}, {"id": "message-queue-correlation", "name": "Message Queue Correlation", "description": "Correlate message queue alerts", "conditions": [{"type": "QUEUE_DEPTH_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}, {"type": "QUEUE_CONSUMER_LAG_HIGH", "source": "any", "severity": ["WARNING", "CRITICAL"]}], "timeWindowMs": 300000, "groupBy": ["queue"], "rootCause": "QUEUE_CONSUMER_LAG_HIGH", "action": "CORRELATE", "incidentType": "PERFORMANCE", "incidentSeverity": "HIGH"}]