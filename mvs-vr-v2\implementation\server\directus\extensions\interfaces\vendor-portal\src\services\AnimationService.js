/**
 * Animation Service
 *
 * This service handles API interactions for the AnimationEditor component.
 * It provides methods for loading, saving, and managing animations.
 */

/**
 * Get all animations for a vendor
 *
 * @param {Object} api - The API client
 * @param {String} vendorId - The vendor ID
 * @returns {Promise<Array>} - A promise that resolves to an array of animations
 */
export const getAnimations = async (api, vendorId) => {
  try {
    const response = await api.get(`/items/animations?filter[vendor_id][_eq]=${vendorId}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading animations:', error);
    throw error;
  }
};

/**
 * Get a specific animation by ID
 *
 * @param {Object} api - The API client
 * @param {String} animationId - The animation ID
 * @returns {Promise<Object>} - A promise that resolves to the animation object
 */
export const getAnimation = async (api, animationId) => {
  try {
    const response = await api.get(`/items/animations/${animationId}`);
    return response.data.data || null;
  } catch (error) {
    console.error(`Error loading animation ${animationId}:`, error);
    throw error;
  }
};

/**
 * Create a new animation
 *
 * @param {Object} api - The API client
 * @param {Object} animation - The animation object to create
 * @returns {Promise<Object>} - A promise that resolves to the created animation
 */
export const createAnimation = async (api, animation) => {
  try {
    const response = await api.post('/items/animations', animation);
    return response.data.data || null;
  } catch (error) {
    console.error('Error creating animation:', error);
    throw error;
  }
};

/**
 * Update an existing animation
 *
 * @param {Object} api - The API client
 * @param {String} animationId - The animation ID
 * @param {Object} animation - The updated animation object
 * @returns {Promise<Object>} - A promise that resolves to the updated animation
 */
export const updateAnimation = async (api, animationId, animation) => {
  try {
    const response = await api.patch(`/items/animations/${animationId}`, animation);
    return response.data.data || null;
  } catch (error) {
    console.error(`Error updating animation ${animationId}:`, error);
    throw error;
  }
};

/**
 * Delete an animation
 *
 * @param {Object} api - The API client
 * @param {String} animationId - The animation ID
 * @returns {Promise<Boolean>} - A promise that resolves to true if the animation was deleted
 */
export const deleteAnimation = async (api, animationId) => {
  try {
    await api.delete(`/items/animations/${animationId}`);
    return true;
  } catch (error) {
    console.error(`Error deleting animation ${animationId}:`, error);
    throw error;
  }
};

/**
 * Save multiple animations (create or update)
 *
 * @param {Object} api - The API client
 * @param {Array} animations - The array of animation objects to save
 * @param {String} vendorId - The vendor ID
 * @returns {Promise<Array>} - A promise that resolves to the saved animations
 */
export const saveAnimations = async (api, animations, vendorId) => {
  try {
    const savedAnimations = [];

    for (const animation of animations) {
      // Ensure vendor ID is set
      animation.vendor_id = vendorId;

      if (animation.id && animation.id.startsWith('animation_')) {
        // This is a temporary ID, create a new animation
        const newAnimation = { ...animation };
        delete newAnimation.id;
        const created = await createAnimation(api, newAnimation);
        savedAnimations.push(created);
      } else if (animation.id) {
        // This is an existing animation, update it
        const updated = await updateAnimation(api, animation.id, animation);
        savedAnimations.push(updated);
      } else {
        // No ID, create a new animation
        const created = await createAnimation(api, animation);
        savedAnimations.push(created);
      }
    }

    return savedAnimations;
  } catch (error) {
    console.error('Error saving animations:', error);
    throw error;
  }
};

/**
 * Create a blended animation from two existing animations
 *
 * @param {Object} api - The API client
 * @param {Object} animation1 - The first animation
 * @param {Object} animation2 - The second animation
 * @param {Number} factor - The blend factor (0-1)
 * @param {String} name - The name for the new animation
 * @param {String} vendorId - The vendor ID
 * @returns {Promise<Object>} - A promise that resolves to the created animation
 */
export const createBlendedAnimation = async (
  api,
  animation1,
  animation2,
  _factor, // Factor is handled in the component for now
  name,
  vendorId,
) => {
  try {
    // Create a new animation object with blended properties
    const blendedAnimation = {
      name: name || `Blend of ${animation1.name} and ${animation2.name}`,
      duration: Math.max(animation1.duration, animation2.duration),
      vendor_id: vendorId,
      tracks: [],
    };

    // Create the blended animation
    const response = await api.post('/items/animations', blendedAnimation);
    return response.data.data || null;
  } catch (error) {
    console.error('Error creating blended animation:', error);
    throw error;
  }
};

export default {
  getAnimations,
  getAnimation,
  createAnimation,
  updateAnimation,
  deleteAnimation,
  saveAnimations,
  createBlendedAnimation,
};
