import React, { useEffect, useState } from "react";
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  IconButton,
  Paper,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
} from "@mui/material";
import {
  Add as AddIcon,
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
} from "@mui/icons-material";
import VendorLayout from "../../components/VendorLayout";
import ProtectedRoute from "../../components/ProtectedRoute";
import { Asset, assetService } from "../../utils/api/asset-service";
import Link from "next/link";

// Asset interface is imported from asset-service

const Assets: React.FC = () => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState(false);

  const fetchAssets = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch assets with pagination using the asset service
      const response = await assetService.getAssets(page, rowsPerPage);

      setAssets(response.assets);
      setTotalCount(response.totalCount);
    } catch (err: unknown) {
      console.error("Error fetching assets:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch assets");
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssets();
  }, [page, rowsPerPage]);

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<{ value: string }>,
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDeleteAsset = async (id: string) => {
    if (confirm("Are you sure you want to delete this asset?")) {
      try {
        setError(null);
        await assetService.deleteAsset(id);

        // Refresh the assets list
        fetchAssets();
      } catch (err: unknown) {
        console.error("Error deleting asset:", err);
        setError(err instanceof Error ? err.message : "Failed to delete asset");
        setShowError(true);
      }
    }
  };

  const handleCloseError = () => {
    setShowError(false);
  };

  if (loading && assets.length === 0) {
    return (
      <VendorLayout title="Assets">
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress />
        </Box>
      </VendorLayout>
    );
  }

  // No need for a separate errorSnackbar variable as we're including it directly in the return

  return (
    <VendorLayout title="Assets">
      {/* Error snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseError}
          severity="error"
          sx={{ width: "100%" }}
        >
          {error}
        </Alert>
      </Snackbar>

      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h4" component="h1">
            Assets
          </Typography>
          <Link href="/vendor/assets/upload" passHref>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component="a"
            >
              Upload New Asset
            </Button>
          </Link>
        </Box>

        <Paper sx={{ width: "100%", overflow: "hidden" }}>
          <TableContainer sx={{ maxHeight: 440 }}>
            <Table stickyHeader aria-label="assets table">
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell>Version</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {assets.map((asset: Asset) => (
                  <TableRow hover key={asset.id}>
                    <TableCell>{asset.name}</TableCell>
                    <TableCell>{asset.type}</TableCell>
                    <TableCell>{formatFileSize(asset.size)}</TableCell>
                    <TableCell>{asset.version}</TableCell>
                    <TableCell>{asset.status}</TableCell>
                    <TableCell>
                      {new Date(asset.updated_at).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        color="primary"
                        component={Link}
                        href={`/vendor/assets/view/${asset.id}`}
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="secondary"
                        component={Link}
                        href={`/vendor/assets/edit/${asset.id}`}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteAsset(asset.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {assets.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Box sx={{ py: 3 }}>
                        <Typography variant="body1" gutterBottom>
                          No assets found
                        </Typography>
                        <Link href="/vendor/assets/upload" passHref>
                          <Button
                            variant="outlined"
                            startIcon={<UploadIcon />}
                            component="a"
                            sx={{ mt: 1 }}
                          >
                            Upload your first asset
                          </Button>
                        </Link>
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={totalCount}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Box>
    </VendorLayout>
  );
};

// Wrap the Assets component with ProtectedRoute
const ProtectedAssets: React.FC = () => {
  return (
    <ProtectedRoute>
      <Assets />
    </ProtectedRoute>
  );
};

export default ProtectedAssets;
