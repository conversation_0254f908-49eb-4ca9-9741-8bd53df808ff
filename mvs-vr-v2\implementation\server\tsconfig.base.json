{"compilerOptions": {"strict": true, "allowJs": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["ESNext", "DOM", "DOM.Iterable", "WebWorker"], "module": "ESNext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "target": "ESNext", "useDefineForClassFields": true, "types": ["node", "vitest/globals", "@testing-library/jest-dom", "vite/client"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@directus/*": ["directus/extensions/*"], "@shared/*": ["shared/*"], "@services/*": ["services/*"], "@tests/*": ["tests/*"], "@setup/*": ["tests/setup/*"]}}, "ts-node": {"transpileOnly": true, "files": true, "compilerOptions": {"module": "ESNext", "moduleResolution": "node"}}}