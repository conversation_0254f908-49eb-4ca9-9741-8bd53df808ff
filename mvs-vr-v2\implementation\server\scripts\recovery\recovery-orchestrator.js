/**
 * Recovery Orchestrator
 * 
 * This script implements dependency-aware recovery orchestration for the MVS-VR system.
 * It manages the recovery process for multiple components, ensuring they are recovered
 * in the correct order based on their dependencies.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync, spawn } = require('child_process');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);
const logger = require('../../utils/logger').getLogger('recovery-orchestrator');

// Import recovery scripts
const databaseRecovery = require('./database-recovery');
const fileRecovery = require('./file-recovery');
const configRecovery = require('./config-recovery');
const { runAllRecoveryTests } = require('../backup/cross-region-recovery');

// Configuration
const config = {
  recoveryLogPath: path.join(__dirname, '../../logs/recovery-orchestration.json'),
  tempDir: path.join(__dirname, '../../temp/recovery'),
  recoveryTimeoutMs: 60 * 60 * 1000, // 1 hour
  maxRetries: 3,
  retryDelayMs: 30 * 1000, // 30 seconds
  components: {
    config: {
      name: 'Configuration',
      dependencies: [],
      recoveryScript: './config-recovery.js',
      healthCheck: async () => {
        // Check if configuration is accessible
        try {
          const configService = require('../../services/config');
          await configService.getSystemConfig();
          return true;
        } catch (error) {
          logger.error('Config health check failed:', error);
          return false;
        }
      },
      priority: 1
    },
    database: {
      name: 'Database',
      dependencies: ['config'],
      recoveryScript: './database-recovery.js',
      healthCheck: async () => {
        // Check if database is accessible
        try {
          const db = require('../../services/database');
          await db.ping();
          return true;
        } catch (error) {
          logger.error('Database health check failed:', error);
          return false;
        }
      },
      priority: 2
    },
    files: {
      name: 'File Storage',
      dependencies: ['config'],
      recoveryScript: './file-recovery.js',
      healthCheck: async () => {
        // Check if file storage is accessible
        try {
          const storage = require('../../services/storage');
          await storage.checkAccess();
          return true;
        } catch (error) {
          logger.error('File storage health check failed:', error);
          return false;
        }
      },
      priority: 2
    },
    cache: {
      name: 'Cache',
      dependencies: ['database'],
      recoveryScript: './cache-recovery.js',
      healthCheck: async () => {
        // Check if cache is accessible
        try {
          const cache = require('../../services/cache');
          await cache.ping();
          return true;
        } catch (error) {
          logger.error('Cache health check failed:', error);
          return false;
        }
      },
      priority: 3
    },
    api: {
      name: 'API Services',
      dependencies: ['database', 'files', 'cache', 'config'],
      recoveryScript: './api-recovery.js',
      healthCheck: async () => {
        // Check if API is accessible
        try {
          const axios = require('axios');
          const response = await axios.get('http://localhost:3000/api/health');
          return response.status === 200;
        } catch (error) {
          logger.error('API health check failed:', error);
          return false;
        }
      },
      priority: 4
    },
    workers: {
      name: 'Background Workers',
      dependencies: ['database', 'files', 'cache', 'config'],
      recoveryScript: './workers-recovery.js',
      healthCheck: async () => {
        // Check if workers are running
        try {
          const workers = require('../../services/workers');
const { logger } = require('../shared/utils/logger');
          return await workers.checkStatus();
        } catch (error) {
          logger.error('Workers health check failed:', error);
          return false;
        }
      },
      priority: 4
    }
  }
};

/**
 * Load recovery orchestration log
 * @returns {Object} Recovery orchestration log
 */
async function loadRecoveryLog() {
  try {
    if (await existsAsync(config.recoveryLogPath)) {
      const data = await readFileAsync(config.recoveryLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    logger.error('Error loading recovery log:', error);
  }
  
  return {
    lastRun: null,
    recoveries: []
  };
}

/**
 * Save recovery orchestration log
 * @param {Object} log - Recovery orchestration log
 */
async function saveRecoveryLog(log) {
  try {
    // Ensure directory exists
    const dir = path.dirname(config.recoveryLogPath);
    if (!await existsAsync(dir)) {
      await mkdirAsync(dir, { recursive: true });
    }
    
    await writeFileAsync(
      config.recoveryLogPath,
      JSON.stringify(log, null, 2),
      'utf8'
    );
  } catch (error) {
    logger.error('Error saving recovery log:', error);
  }
}

/**
 * Build dependency graph for components
 * @returns {Object} Dependency graph
 */
function buildDependencyGraph() {
  const graph = {};
  
  // Initialize graph
  for (const [id, component] of Object.entries(config.components)) {
    graph[id] = {
      id,
      name: component.name,
      dependencies: component.dependencies,
      dependents: [],
      priority: component.priority,
      recoveryScript: component.recoveryScript,
      healthCheck: component.healthCheck
    };
  }
  
  // Build dependents
  for (const [id, component] of Object.entries(graph)) {
    for (const dependency of component.dependencies) {
      if (graph[dependency]) {
        graph[dependency].dependents.push(id);
      }
    }
  }
  
  return graph;
}

/**
 * Sort components by recovery order
 * @param {Object} graph - Dependency graph
 * @returns {Array} Sorted components
 */
function sortComponentsByRecoveryOrder(graph) {
  // Create a copy of the graph to work with
  const workGraph = JSON.parse(JSON.stringify(graph));
  const result = [];
  const nodesWithNoDependencies = [];
  
  // Find all nodes with no dependencies
  for (const [id, component] of Object.entries(workGraph)) {
    if (component.dependencies.length === 0) {
      nodesWithNoDependencies.push(component);
    }
  }
  
  // Sort nodes with no dependencies by priority
  nodesWithNoDependencies.sort((a, b) => a.priority - b.priority);
  
  // Process nodes with no dependencies
  while (nodesWithNoDependencies.length > 0) {
    const node = nodesWithNoDependencies.shift();
    result.push(node);
    
    // Process dependents
    for (const dependentId of node.dependents) {
      const dependent = workGraph[dependentId];
      
      // Remove the dependency
      dependent.dependencies = dependent.dependencies.filter(dep => dep !== node.id);
      
      // If no more dependencies, add to the queue
      if (dependent.dependencies.length === 0) {
        nodesWithNoDependencies.push(dependent);
        
        // Re-sort by priority
        nodesWithNoDependencies.sort((a, b) => a.priority - b.priority);
      }
    }
  }
  
  // Check for circular dependencies
  const allComponents = Object.keys(workGraph).length;
  if (result.length !== allComponents) {
    const remaining = Object.keys(workGraph).filter(id => !result.find(r => r.id === id));
    logger.error(`Circular dependency detected in components: ${remaining.join(', ')}`);
    throw new Error(`Circular dependency detected in components: ${remaining.join(', ')}`);
  }
  
  return result;
}

/**
 * Run recovery for a specific component
 * @param {Object} component - Component to recover
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverComponent(component, options = {}) {
  const {
    source = null,
    target = config.tempDir,
    test = false,
    retryCount = 0
  } = options;
  
  logger.info(`Recovering component: ${component.name} (${component.id})`);
  
  const result = {
    id: component.id,
    name: component.name,
    startTime: new Date().toISOString(),
    endTime: null,
    duration: null,
    success: false,
    retries: retryCount,
    details: {}
  };
  
  try {
    const startTime = Date.now();
    
    // Run recovery script
    const recoveryScript = path.join(__dirname, component.recoveryScript);
    const recoveryOutput = execSync(`node ${recoveryScript} ${test ? '--test' : ''} ${source ? `--source=${source}` : ''} --target=${target}/${component.id}`, {
      encoding: 'utf8',
      timeout: config.recoveryTimeoutMs
    });
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000; // Convert to seconds
    
    // Run health check
    const healthy = await component.healthCheck();
    
    result.endTime = new Date().toISOString();
    result.duration = duration;
    result.success = healthy;
    result.details = {
      recoveryOutput,
      healthy
    };
    
    if (healthy) {
      logger.info(`Successfully recovered component: ${component.name} (${component.id}) in ${duration} seconds`);
    } else {
      logger.warn(`Component recovery completed but health check failed: ${component.name} (${component.id})`);
      
      // Retry if not at max retries
      if (retryCount < config.maxRetries) {
        logger.info(`Retrying recovery for component: ${component.name} (${component.id}) (retry ${retryCount + 1}/${config.maxRetries})`);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, config.retryDelayMs));
        
        // Retry recovery
        return await recoverComponent(component, {
          ...options,
          retryCount: retryCount + 1
        });
      }
    }
  } catch (error) {
    logger.error(`Error recovering component: ${component.name} (${component.id})`, error);
    
    result.endTime = new Date().toISOString();
    result.duration = 0;
    result.success = false;
    result.details = {
      error: error.message
    };
    
    // Retry if not at max retries
    if (retryCount < config.maxRetries) {
      logger.info(`Retrying recovery for component: ${component.name} (${component.id}) (retry ${retryCount + 1}/${config.maxRetries})`);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, config.retryDelayMs));
      
      // Retry recovery
      return await recoverComponent(component, {
        ...options,
        retryCount: retryCount + 1
      });
    }
  }
  
  return result;
}

/**
 * Run orchestrated recovery for all components
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery results
 */
async function runOrchestratedRecovery(options = {}) {
  const {
    source = null,
    target = config.tempDir,
    test = false,
    components = Object.keys(config.components)
  } = options;
  
  const log = await loadRecoveryLog();
  const startTime = new Date();
  
  logger.info(`Starting orchestrated recovery at ${startTime.toISOString()}`);
  
  const recoveryResults = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    success: false,
    components: {}
  };
  
  try {
    // Ensure temp directory exists
    if (!await existsAsync(target)) {
      await mkdirAsync(target, { recursive: true });
    }
    
    // Build dependency graph
    const graph = buildDependencyGraph();
    
    // Filter components if specified
    const filteredGraph = {};
    for (const id of components) {
      if (graph[id]) {
        filteredGraph[id] = graph[id];
      }
    }
    
    // Sort components by recovery order
    const sortedComponents = sortComponentsByRecoveryOrder(filteredGraph);
    
    logger.info(`Recovery order: ${sortedComponents.map(c => c.name).join(' -> ')}`);
    
    // Recover components in order
    for (const component of sortedComponents) {
      recoveryResults.components[component.id] = await recoverComponent(component, {
        source,
        target,
        test
      });
      
      // If component recovery failed, stop recovery
      if (!recoveryResults.components[component.id].success) {
        logger.error(`Recovery failed for component: ${component.name} (${component.id}). Stopping recovery.`);
        break;
      }
    }
    
    const endTime = new Date();
    const durationMs = endTime - startTime;
    
    recoveryResults.endTime = endTime.toISOString();
    recoveryResults.duration = durationMs / 1000; // Convert to seconds
    
    // Check if all components were recovered successfully
    recoveryResults.success = Object.values(recoveryResults.components).every(c => c.success);
    
    // Update log
    log.lastRun = endTime.toISOString();
    log.recoveries.push(recoveryResults);
    
    // Keep only the last 100 recoveries
    if (log.recoveries.length > 100) {
      log.recoveries = log.recoveries.slice(-100);
    }
    
    await saveRecoveryLog(log);
    
    logger.info(`Completed orchestrated recovery in ${recoveryResults.duration} seconds. Success: ${recoveryResults.success}`);
  } catch (error) {
    logger.error('Error during orchestrated recovery:', error);
    
    const endTime = new Date();
    const durationMs = endTime - startTime;
    
    recoveryResults.endTime = endTime.toISOString();
    recoveryResults.duration = durationMs / 1000; // Convert to seconds
    recoveryResults.error = error.message;
    
    // Update log
    log.lastRun = endTime.toISOString();
    log.recoveries.push(recoveryResults);
    
    // Keep only the last 100 recoveries
    if (log.recoveries.length > 100) {
      log.recoveries = log.recoveries.slice(-100);
    }
    
    await saveRecoveryLog(log);
  }
  
  return recoveryResults;
}

// If script is run directly, run orchestrated recovery
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    source: args.find(arg => arg.startsWith('--source='))?.split('=')[1],
    target: args.find(arg => arg.startsWith('--target='))?.split('=')[1] || config.tempDir,
    components: args.find(arg => arg.startsWith('--components='))?.split('=')[1]?.split(',') || Object.keys(config.components)
  };
  
  runOrchestratedRecovery(options)
    .then(results => {
      logger.info('Orchestrated recovery completed:');
      logger.info(JSON.stringify(results, null, 2););
      process.exit(results.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runOrchestratedRecovery,
  recoverComponent,
  buildDependencyGraph,
  sortComponentsByRecoveryOrder
};
