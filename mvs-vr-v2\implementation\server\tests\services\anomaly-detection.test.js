/**
 * Tests for anomaly detection service
 */

const { AnomalyDetectionService, ALGORITHMS } = require('../../services/monitoring/anomaly-detection');

describe('Anomaly Detection Service', () => {
  let service;
  
  beforeEach(() => {
    service = new AnomalyDetectionService();
  });
  
  test('should detect Z-Score anomalies', () => {
    // Create time series with anomalies
    const timeSeries = [
      { timestamp: '2023-01-01T00:00:00Z', value: 10, metric: 'cpu' },
      { timestamp: '2023-01-01T01:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T02:00:00Z', value: 11, metric: 'cpu' },
      { timestamp: '2023-01-01T03:00:00Z', value: 13, metric: 'cpu' },
      { timestamp: '2023-01-01T04:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T05:00:00Z', value: 11, metric: 'cpu' },
      { timestamp: '2023-01-01T06:00:00Z', value: 10, metric: 'cpu' },
      { timestamp: '2023-01-01T07:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T08:00:00Z', value: 11, metric: 'cpu' },
      { timestamp: '2023-01-01T09:00:00Z', value: 50, metric: 'cpu' }, // Anomaly
      { timestamp: '2023-01-01T10:00:00Z', value: 13, metric: 'cpu' },
      { timestamp: '2023-01-01T11:00:00Z', value: 12, metric: 'cpu' }
    ];
    
    // Detect anomalies
    const anomalies = service.detectAnomalies(timeSeries, {
      algorithm: ALGORITHMS.Z_SCORE,
      threshold: 3.0,
      metric: 'cpu'
    });
    
    // Verify anomalies
    expect(anomalies).toHaveLength(1);
    expect(anomalies[0].timestamp).toBe('2023-01-01T09:00:00Z');
    expect(anomalies[0].value).toBe(50);
    expect(anomalies[0].algorithm).toBe(ALGORITHMS.Z_SCORE);
  });
  
  test('should detect MAD anomalies', () => {
    // Create time series with anomalies
    const timeSeries = [
      { timestamp: '2023-01-01T00:00:00Z', value: 10, metric: 'memory' },
      { timestamp: '2023-01-01T01:00:00Z', value: 12, metric: 'memory' },
      { timestamp: '2023-01-01T02:00:00Z', value: 11, metric: 'memory' },
      { timestamp: '2023-01-01T03:00:00Z', value: 13, metric: 'memory' },
      { timestamp: '2023-01-01T04:00:00Z', value: 12, metric: 'memory' },
      { timestamp: '2023-01-01T05:00:00Z', value: 11, metric: 'memory' },
      { timestamp: '2023-01-01T06:00:00Z', value: 10, metric: 'memory' },
      { timestamp: '2023-01-01T07:00:00Z', value: 12, metric: 'memory' },
      { timestamp: '2023-01-01T08:00:00Z', value: 11, metric: 'memory' },
      { timestamp: '2023-01-01T09:00:00Z', value: 60, metric: 'memory' }, // Anomaly
      { timestamp: '2023-01-01T10:00:00Z', value: 13, metric: 'memory' },
      { timestamp: '2023-01-01T11:00:00Z', value: 12, metric: 'memory' }
    ];
    
    // Detect anomalies
    const anomalies = service.detectAnomalies(timeSeries, {
      algorithm: ALGORITHMS.MAD,
      threshold: 3.5,
      metric: 'memory'
    });
    
    // Verify anomalies
    expect(anomalies).toHaveLength(1);
    expect(anomalies[0].timestamp).toBe('2023-01-01T09:00:00Z');
    expect(anomalies[0].value).toBe(60);
    expect(anomalies[0].algorithm).toBe(ALGORITHMS.MAD);
  });
  
  test('should detect IQR anomalies', () => {
    // Create time series with anomalies
    const timeSeries = [
      { timestamp: '2023-01-01T00:00:00Z', value: 10, metric: 'disk' },
      { timestamp: '2023-01-01T01:00:00Z', value: 12, metric: 'disk' },
      { timestamp: '2023-01-01T02:00:00Z', value: 11, metric: 'disk' },
      { timestamp: '2023-01-01T03:00:00Z', value: 13, metric: 'disk' },
      { timestamp: '2023-01-01T04:00:00Z', value: 12, metric: 'disk' },
      { timestamp: '2023-01-01T05:00:00Z', value: 11, metric: 'disk' },
      { timestamp: '2023-01-01T06:00:00Z', value: 10, metric: 'disk' },
      { timestamp: '2023-01-01T07:00:00Z', value: 12, metric: 'disk' },
      { timestamp: '2023-01-01T08:00:00Z', value: 11, metric: 'disk' },
      { timestamp: '2023-01-01T09:00:00Z', value: 70, metric: 'disk' }, // Anomaly
      { timestamp: '2023-01-01T10:00:00Z', value: 13, metric: 'disk' },
      { timestamp: '2023-01-01T11:00:00Z', value: 12, metric: 'disk' }
    ];
    
    // Detect anomalies
    const anomalies = service.detectAnomalies(timeSeries, {
      algorithm: ALGORITHMS.IQR,
      threshold: 1.5,
      metric: 'disk'
    });
    
    // Verify anomalies
    expect(anomalies).toHaveLength(1);
    expect(anomalies[0].timestamp).toBe('2023-01-01T09:00:00Z');
    expect(anomalies[0].value).toBe(70);
    expect(anomalies[0].algorithm).toBe(ALGORITHMS.IQR);
  });
  
  test('should handle empty time series', () => {
    const timeSeries = [];
    
    const anomalies = service.detectAnomalies(timeSeries, {
      algorithm: ALGORITHMS.Z_SCORE,
      metric: 'cpu'
    });
    
    expect(anomalies).toHaveLength(0);
  });
  
  test('should handle time series with insufficient data points', () => {
    const timeSeries = [
      { timestamp: '2023-01-01T00:00:00Z', value: 10, metric: 'cpu' },
      { timestamp: '2023-01-01T01:00:00Z', value: 12, metric: 'cpu' }
    ];
    
    const anomalies = service.detectAnomalies(timeSeries, {
      algorithm: ALGORITHMS.Z_SCORE,
      metric: 'cpu'
    });
    
    expect(anomalies).toHaveLength(0);
  });
  
  test('should handle time series with no anomalies', () => {
    const timeSeries = [
      { timestamp: '2023-01-01T00:00:00Z', value: 10, metric: 'cpu' },
      { timestamp: '2023-01-01T01:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T02:00:00Z', value: 11, metric: 'cpu' },
      { timestamp: '2023-01-01T03:00:00Z', value: 13, metric: 'cpu' },
      { timestamp: '2023-01-01T04:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T05:00:00Z', value: 11, metric: 'cpu' },
      { timestamp: '2023-01-01T06:00:00Z', value: 10, metric: 'cpu' },
      { timestamp: '2023-01-01T07:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T08:00:00Z', value: 11, metric: 'cpu' },
      { timestamp: '2023-01-01T09:00:00Z', value: 13, metric: 'cpu' },
      { timestamp: '2023-01-01T10:00:00Z', value: 12, metric: 'cpu' },
      { timestamp: '2023-01-01T11:00:00Z', value: 11, metric: 'cpu' }
    ];
    
    const anomalies = service.detectAnomalies(timeSeries, {
      algorithm: ALGORITHMS.Z_SCORE,
      threshold: 3.0,
      metric: 'cpu'
    });
    
    expect(anomalies).toHaveLength(0);
  });
});
