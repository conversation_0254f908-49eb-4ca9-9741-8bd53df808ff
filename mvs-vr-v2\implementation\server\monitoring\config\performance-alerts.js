/**
 * Performance and Error Rate Alert Configurations
 * 
 * This file contains alert configurations for performance degradation and high error rates.
 */

module.exports = {
  // Performance degradation alerts
  performanceDegradation: {
    // API response time degradation
    apiResponseTimeDegradation: {
      id: 'api_response_time_degradation',
      name: 'API Response Time Degradation',
      description: 'API response time has increased significantly compared to baseline',
      severity: 'warning',
      condition: 'increase > 20%', // 20% increase over baseline
      duration: '5m', // Sustained for 5 minutes
      channels: ['slack', 'dashboard'],
      cooldown: 1800, // 30 minutes
      groupBy: ['endpoint'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'API response time for {{endpoint}} has increased by {{percentage}}% (from {{baseline}}ms to {{current}}ms)'
        },
        {
          type: 'dashboard',
          dashboard: 'api-performance'
        }
      ]
    },
    
    // Frontend rendering time degradation
    frontendRenderingDegradation: {
      id: 'frontend_rendering_degradation',
      name: 'Frontend Rendering Degradation',
      description: 'Frontend rendering time has increased significantly',
      severity: 'warning',
      condition: 'increase > 30%', // 30% increase over baseline
      duration: '10m', // Sustained for 10 minutes
      channels: ['slack', 'dashboard'],
      cooldown: 3600, // 1 hour
      groupBy: ['component', 'device_type'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'Rendering time for {{component}} on {{device_type}} has increased by {{percentage}}% (from {{baseline}}ms to {{current}}ms)'
        },
        {
          type: 'dashboard',
          dashboard: 'frontend-performance'
        }
      ]
    },
    
    // Database query performance degradation
    dbQueryDegradation: {
      id: 'db_query_degradation',
      name: 'Database Query Performance Degradation',
      description: 'Database query performance has degraded significantly',
      severity: 'warning',
      condition: 'increase > 50%', // 50% increase over baseline
      duration: '5m', // Sustained for 5 minutes
      channels: ['slack', 'dashboard', 'email'],
      cooldown: 1800, // 30 minutes
      groupBy: ['query_type', 'table'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'Query performance for {{query_type}} on {{table}} has degraded by {{percentage}}% (from {{baseline}}ms to {{current}}ms)'
        },
        {
          type: 'dashboard',
          dashboard: 'database-performance'
        }
      ]
    },
    
    // Asset processing time degradation
    assetProcessingDegradation: {
      id: 'asset_processing_degradation',
      name: 'Asset Processing Time Degradation',
      description: 'Asset processing time has increased significantly',
      severity: 'warning',
      condition: 'increase > 40%', // 40% increase over baseline
      duration: '15m', // Sustained for 15 minutes
      channels: ['slack', 'dashboard'],
      cooldown: 3600, // 1 hour
      groupBy: ['asset_type', 'processing_step'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'Processing time for {{asset_type}} at step {{processing_step}} has increased by {{percentage}}% (from {{baseline}}s to {{current}}s)'
        },
        {
          type: 'dashboard',
          dashboard: 'asset-processing'
        }
      ]
    }
  },
  
  // High error rate alerts
  highErrorRates: {
    // High API error rate
    highApiErrorRate: {
      id: 'high_api_error_rate',
      name: 'High API Error Rate',
      description: 'API error rate is above threshold',
      severity: 'error',
      condition: 'rate > 5%', // Error rate above 5%
      duration: '5m', // Sustained for 5 minutes
      channels: ['slack', 'dashboard', 'email'],
      cooldown: 900, // 15 minutes
      groupBy: ['endpoint', 'status_code'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'High error rate ({{rate}}%) for endpoint {{endpoint}} with status code {{status_code}}'
        },
        {
          type: 'dashboard',
          dashboard: 'api-errors'
        }
      ]
    },
    
    // Authentication failures spike
    authFailuresSpike: {
      id: 'auth_failures_spike',
      name: 'Authentication Failures Spike',
      description: 'Spike in authentication failures',
      severity: 'warning',
      condition: 'rate > 10%', // Failure rate above 10%
      duration: '5m', // Sustained for 5 minutes
      channels: ['slack', 'dashboard'],
      cooldown: 900, // 15 minutes
      groupBy: ['auth_method', 'ip_range'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'Spike in authentication failures ({{rate}}%) for {{auth_method}} from {{ip_range}}'
        },
        {
          type: 'dashboard',
          dashboard: 'auth-security'
        }
      ]
    },
    
    // Asset processing failures
    assetProcessingFailures: {
      id: 'asset_processing_failures',
      name: 'Asset Processing Failures',
      description: 'High rate of asset processing failures',
      severity: 'error',
      condition: 'rate > 10%', // Failure rate above 10%
      duration: '15m', // Sustained for 15 minutes
      channels: ['slack', 'dashboard', 'email'],
      cooldown: 1800, // 30 minutes
      groupBy: ['asset_type', 'error_type'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'High failure rate ({{rate}}%) for {{asset_type}} processing with error {{error_type}}'
        },
        {
          type: 'dashboard',
          dashboard: 'asset-processing'
        }
      ]
    },
    
    // Database connection errors
    dbConnectionErrors: {
      id: 'db_connection_errors',
      name: 'Database Connection Errors',
      description: 'High rate of database connection errors',
      severity: 'critical',
      condition: 'count > 5', // More than 5 connection errors
      duration: '5m', // Within 5 minutes
      channels: ['slack', 'dashboard', 'email'],
      cooldown: 300, // 5 minutes
      groupBy: ['database', 'error_type'],
      enabled: true,
      actions: [
        {
          type: 'notification',
          template: 'Database connection errors ({{count}}) for {{database}} with error {{error_type}}'
        },
        {
          type: 'dashboard',
          dashboard: 'database-health'
        }
      ]
    }
  }
};
