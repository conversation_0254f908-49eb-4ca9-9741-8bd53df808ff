/**
 * Predictive Analyzer
 * 
 * This service implements predictive analysis for the monitoring system to:
 * - Predict system load based on historical patterns
 * - Forecast resource needs based on user growth trends
 * - Identify potential performance bottlenecks before they occur
 * - Recommend scaling actions based on predicted demand
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const { triggerAlert } = require('./alert-manager');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Create a Registry for predictive metrics
const register = new promClient.Registry();

// Create custom metrics for predictions
const metrics = {
  // Predicted resource usage
  predictedCpuUsage: new promClient.Gauge({
    name: 'mvs_vr_predicted_cpu_usage_percent',
    help: 'Predicted CPU usage in percent',
    labelNames: ['timeframe', 'confidence']
  }),
  
  predictedMemoryUsage: new promClient.Gauge({
    name: 'mvs_vr_predicted_memory_usage_percent',
    help: 'Predicted memory usage in percent',
    labelNames: ['timeframe', 'confidence']
  }),
  
  predictedDiskUsage: new promClient.Gauge({
    name: 'mvs_vr_predicted_disk_usage_percent',
    help: 'Predicted disk usage in percent',
    labelNames: ['mount', 'timeframe', 'confidence']
  }),
  
  predictedNetworkTraffic: new promClient.Gauge({
    name: 'mvs_vr_predicted_network_traffic_mbps',
    help: 'Predicted network traffic in Mbps',
    labelNames: ['direction', 'timeframe', 'confidence']
  }),
  
  // Predicted application metrics
  predictedApiRequests: new promClient.Gauge({
    name: 'mvs_vr_predicted_api_requests_per_minute',
    help: 'Predicted API requests per minute',
    labelNames: ['endpoint', 'timeframe', 'confidence']
  }),
  
  predictedApiLatency: new promClient.Gauge({
    name: 'mvs_vr_predicted_api_latency_ms',
    help: 'Predicted API latency in milliseconds',
    labelNames: ['endpoint', 'timeframe', 'confidence']
  }),
  
  predictedActiveUsers: new promClient.Gauge({
    name: 'mvs_vr_predicted_active_users',
    help: 'Predicted number of active users',
    labelNames: ['timeframe', 'confidence']
  }),
  
  // Anomaly scores
  anomalyScore: new promClient.Gauge({
    name: 'mvs_vr_anomaly_score',
    help: 'Anomaly score for various metrics',
    labelNames: ['metric', 'detection_method']
  }),
  
  // Bottleneck prediction
  bottleneckProbability: new promClient.Gauge({
    name: 'mvs_vr_bottleneck_probability_percent',
    help: 'Probability of a bottleneck in percent',
    labelNames: ['component', 'timeframe']
  }),
  
  // Scaling recommendations
  scalingRecommendation: new promClient.Gauge({
    name: 'mvs_vr_scaling_recommendation',
    help: 'Scaling recommendation (1 = scale up, 0 = no change, -1 = scale down)',
    labelNames: ['component', 'urgency']
  })
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

// Timeframes for predictions
const TIMEFRAMES = {
  SHORT: '1h',
  MEDIUM: '6h',
  LONG: '24h'
};

// Confidence levels
const CONFIDENCE = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

// Components that can be scaled
const COMPONENTS = {
  API_SERVERS: 'api_servers',
  DATABASE: 'database',
  STORAGE: 'storage',
  CACHE: 'cache',
  WORKER_NODES: 'worker_nodes'
};

// Urgency levels for scaling
const URGENCY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

/**
 * Get historical metrics for prediction
 * 
 * @param {string} metricName - Metric name
 * @param {number} hours - Hours of history to retrieve
 * @returns {Array} - Historical metrics
 */
async function getHistoricalMetrics(metricName, hours = 24) {
  try {
    const startTime = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await supabase
      .from('metrics_history')
      .select('*')
      .eq('metric_name', metricName)
      .gte('timestamp', startTime)
      .order('timestamp', { ascending: true });
      
    if (error) {
      logger.error('Error fetching historical metrics', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getHistoricalMetrics', { error: error.message });
    return [];
  }
}

/**
 * Predict future values using exponential smoothing
 * 
 * @param {Array} historicalData - Historical data points
 * @param {number} periods - Number of periods to predict
 * @param {Object} params - Smoothing parameters
 * @returns {Array} - Predicted values
 */
function exponentialSmoothing(historicalData, periods = 6, params = {}) {
  if (!historicalData || historicalData.length < 10) {
    return [];
  }
  
  const alpha = params.alpha || 0.3; // Level smoothing
  const beta = params.beta || 0.1;   // Trend smoothing
  const gamma = params.gamma || 0.1; // Seasonal smoothing
  const seasonalPeriods = params.seasonalPeriods || 24; // Hours in a day
  
  // Extract values
  const values = historicalData.map(d => d.value);
  
  // Initialize level, trend, and seasonal components
  let level = values[0];
  let trend = (values[1] - values[0]) / seasonalPeriods;
  
  // Initialize seasonal components
  const seasonals = [];
  for (let i = 0; i < seasonalPeriods; i++) {
    if (i < values.length) {
      seasonals.push(values[i] / level);
    } else {
      // If we don't have enough data, use average
      seasonals.push(1.0);
    }
  }
  
  // Smooth historical data
  for (let i = 0; i < values.length; i++) {
    const oldLevel = level;
    const seasonalIndex = i % seasonalPeriods;
    
    // Update level
    level = alpha * (values[i] / seasonals[seasonalIndex]) + (1 - alpha) * (oldLevel + trend);
    
    // Update trend
    trend = beta * (level - oldLevel) + (1 - beta) * trend;
    
    // Update seasonal component
    seasonals[seasonalIndex] = gamma * (values[i] / level) + (1 - gamma) * seasonals[seasonalIndex];
  }
  
  // Generate predictions
  const predictions = [];
  for (let i = 0; i < periods; i++) {
    const forecastIndex = (values.length + i) % seasonalPeriods;
    const forecastValue = (level + (i + 1) * trend) * seasonals[forecastIndex];
    predictions.push(Math.max(0, forecastValue)); // Ensure non-negative
  }
  
  return predictions;
}

/**
 * Detect anomalies using Z-score method
 * 
 * @param {Array} data - Data points
 * @param {number} threshold - Z-score threshold
 * @returns {Array} - Anomaly scores
 */
function detectAnomaliesZScore(data, threshold = 3.0) {
  if (!data || data.length < 10) {
    return [];
  }
  
  // Calculate mean and standard deviation
  const values = data.map(d => d.value);
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);
  
  // Calculate Z-scores
  return data.map(d => {
    const zScore = Math.abs((d.value - mean) / stdDev);
    return {
      timestamp: d.timestamp,
      value: d.value,
      zScore,
      isAnomaly: zScore > threshold
    };
  });
}

/**
 * Predict resource usage
 */
async function predictResourceUsage() {
  try {
    // Predict CPU usage
    const cpuHistory = await getHistoricalMetrics('cpu_usage', 48);
    if (cpuHistory.length > 0) {
      const predictions = exponentialSmoothing(cpuHistory, 24, {
        seasonalPeriods: 24 // 24 hours in a day
      });
      
      // Update metrics for different timeframes
      if (predictions.length >= 1) {
        metrics.predictedCpuUsage.set({ timeframe: TIMEFRAMES.SHORT, confidence: CONFIDENCE.HIGH }, predictions[0]);
      }
      
      if (predictions.length >= 6) {
        metrics.predictedCpuUsage.set({ timeframe: TIMEFRAMES.MEDIUM, confidence: CONFIDENCE.MEDIUM }, predictions[5]);
      }
      
      if (predictions.length >= 24) {
        metrics.predictedCpuUsage.set({ timeframe: TIMEFRAMES.LONG, confidence: CONFIDENCE.LOW }, predictions[23]);
      }
      
      // Check for potential issues
      if (predictions[0] > 80) {
        // Trigger alert for predicted high CPU usage
        triggerAlert('predictedHighCpuUsage', {
          predicted_value: predictions[0],
          timeframe: TIMEFRAMES.SHORT
        });
        
        // Recommend scaling
        metrics.scalingRecommendation.set({ component: COMPONENTS.API_SERVERS, urgency: URGENCY.MEDIUM }, 1);
      }
    }
    
    // Predict memory usage
    const memoryHistory = await getHistoricalMetrics('memory_usage', 48);
    if (memoryHistory.length > 0) {
      const predictions = exponentialSmoothing(memoryHistory, 24, {
        seasonalPeriods: 24
      });
      
      // Update metrics for different timeframes
      if (predictions.length >= 1) {
        metrics.predictedMemoryUsage.set({ timeframe: TIMEFRAMES.SHORT, confidence: CONFIDENCE.HIGH }, predictions[0]);
      }
      
      if (predictions.length >= 6) {
        metrics.predictedMemoryUsage.set({ timeframe: TIMEFRAMES.MEDIUM, confidence: CONFIDENCE.MEDIUM }, predictions[5]);
      }
      
      if (predictions.length >= 24) {
        metrics.predictedMemoryUsage.set({ timeframe: TIMEFRAMES.LONG, confidence: CONFIDENCE.LOW }, predictions[23]);
      }
      
      // Check for potential issues
      if (predictions[0] > 85) {
        // Trigger alert for predicted high memory usage
        triggerAlert('predictedHighMemoryUsage', {
          predicted_value: predictions[0],
          timeframe: TIMEFRAMES.SHORT
        });
        
        // Recommend scaling
        metrics.scalingRecommendation.set({ component: COMPONENTS.API_SERVERS, urgency: URGENCY.HIGH }, 1);
      }
    }
    
    // Predict disk usage
    const diskHistory = await getHistoricalMetrics('disk_usage', 72);
    if (diskHistory.length > 0) {
      const predictions = exponentialSmoothing(diskHistory, 24, {
        alpha: 0.1, // Lower alpha for disk usage (changes more slowly)
        beta: 0.05,
        gamma: 0.05,
        seasonalPeriods: 24
      });
      
      // Update metrics for different timeframes
      if (predictions.length >= 1) {
        metrics.predictedDiskUsage.set({ mount: 'main', timeframe: TIMEFRAMES.SHORT, confidence: CONFIDENCE.HIGH }, predictions[0]);
      }
      
      if (predictions.length >= 6) {
        metrics.predictedDiskUsage.set({ mount: 'main', timeframe: TIMEFRAMES.MEDIUM, confidence: CONFIDENCE.MEDIUM }, predictions[5]);
      }
      
      if (predictions.length >= 24) {
        metrics.predictedDiskUsage.set({ mount: 'main', timeframe: TIMEFRAMES.LONG, confidence: CONFIDENCE.LOW }, predictions[23]);
      }
      
      // Check for potential issues
      if (predictions[0] > 90) {
        // Trigger alert for predicted high disk usage
        triggerAlert('predictedHighDiskUsage', {
          predicted_value: predictions[0],
          timeframe: TIMEFRAMES.SHORT,
          mount: 'main'
        });
        
        // Recommend scaling
        metrics.scalingRecommendation.set({ component: COMPONENTS.STORAGE, urgency: URGENCY.HIGH }, 1);
      }
    }
    
    logger.info('Resource usage predictions updated');
  } catch (error) {
    logger.error('Error predicting resource usage', { error: error.message });
  }
}

/**
 * Predict application metrics
 */
async function predictApplicationMetrics() {
  try {
    // Predict API requests
    const apiRequestsHistory = await getHistoricalMetrics('api_requests', 48);
    if (apiRequestsHistory.length > 0) {
      const predictions = exponentialSmoothing(apiRequestsHistory, 24, {
        seasonalPeriods: 24
      });
      
      // Update metrics for different timeframes
      if (predictions.length >= 1) {
        metrics.predictedApiRequests.set({ endpoint: 'all', timeframe: TIMEFRAMES.SHORT, confidence: CONFIDENCE.HIGH }, predictions[0]);
      }
      
      if (predictions.length >= 6) {
        metrics.predictedApiRequests.set({ endpoint: 'all', timeframe: TIMEFRAMES.MEDIUM, confidence: CONFIDENCE.MEDIUM }, predictions[5]);
      }
      
      if (predictions.length >= 24) {
        metrics.predictedApiRequests.set({ endpoint: 'all', timeframe: TIMEFRAMES.LONG, confidence: CONFIDENCE.LOW }, predictions[23]);
      }
      
      // Check for potential bottlenecks
      const currentCapacity = 1000; // Requests per minute
      const utilizationPercent = (predictions[0] / currentCapacity) * 100;
      
      if (utilizationPercent > 70) {
        metrics.bottleneckProbability.set({ component: COMPONENTS.API_SERVERS, timeframe: TIMEFRAMES.SHORT }, utilizationPercent);
        
        if (utilizationPercent > 90) {
          // Recommend scaling with high urgency
          metrics.scalingRecommendation.set({ component: COMPONENTS.API_SERVERS, urgency: URGENCY.HIGH }, 1);
          
          // Trigger alert
          triggerAlert('predictedApiBottleneck', {
            predicted_utilization: utilizationPercent,
            timeframe: TIMEFRAMES.SHORT
          });
        } else {
          // Recommend scaling with medium urgency
          metrics.scalingRecommendation.set({ component: COMPONENTS.API_SERVERS, urgency: URGENCY.MEDIUM }, 1);
        }
      }
    }
    
    // Predict active users
    const activeUsersHistory = await getHistoricalMetrics('active_users', 48);
    if (activeUsersHistory.length > 0) {
      const predictions = exponentialSmoothing(activeUsersHistory, 24, {
        seasonalPeriods: 24
      });
      
      // Update metrics for different timeframes
      if (predictions.length >= 1) {
        metrics.predictedActiveUsers.set({ timeframe: TIMEFRAMES.SHORT, confidence: CONFIDENCE.HIGH }, predictions[0]);
      }
      
      if (predictions.length >= 6) {
        metrics.predictedActiveUsers.set({ timeframe: TIMEFRAMES.MEDIUM, confidence: CONFIDENCE.MEDIUM }, predictions[5]);
      }
      
      if (predictions.length >= 24) {
        metrics.predictedActiveUsers.set({ timeframe: TIMEFRAMES.LONG, confidence: CONFIDENCE.LOW }, predictions[23]);
      }
    }
    
    logger.info('Application metrics predictions updated');
  } catch (error) {
    logger.error('Error predicting application metrics', { error: error.message });
  }
}

/**
 * Detect anomalies in metrics
 */
async function detectAnomalies() {
  try {
    // Detect anomalies in API latency
    const apiLatencyHistory = await getHistoricalMetrics('api_latency', 24);
    if (apiLatencyHistory.length > 0) {
      const anomalies = detectAnomaliesZScore(apiLatencyHistory);
      
      // Calculate overall anomaly score
      const anomalyCount = anomalies.filter(a => a.isAnomaly).length;
      const anomalyScore = (anomalyCount / anomalies.length) * 100;
      
      metrics.anomalyScore.set({ metric: 'api_latency', detection_method: 'zscore' }, anomalyScore);
      
      // Check for significant anomalies
      if (anomalyScore > 10) {
        // Trigger alert
        triggerAlert('apiLatencyAnomalies', {
          anomaly_score: anomalyScore,
          anomaly_count: anomalyCount
        });
      }
    }
    
    // Detect anomalies in error rates
    const errorRateHistory = await getHistoricalMetrics('error_rate', 24);
    if (errorRateHistory.length > 0) {
      const anomalies = detectAnomaliesZScore(errorRateHistory);
      
      // Calculate overall anomaly score
      const anomalyCount = anomalies.filter(a => a.isAnomaly).length;
      const anomalyScore = (anomalyCount / anomalies.length) * 100;
      
      metrics.anomalyScore.set({ metric: 'error_rate', detection_method: 'zscore' }, anomalyScore);
      
      // Check for significant anomalies
      if (anomalyScore > 5) {
        // Trigger alert
        triggerAlert('errorRateAnomalies', {
          anomaly_score: anomalyScore,
          anomaly_count: anomalyCount
        });
      }
    }
    
    logger.info('Anomaly detection completed');
  } catch (error) {
    logger.error('Error detecting anomalies', { error: error.message });
  }
}

/**
 * Run all predictive analyses
 */
async function runPredictiveAnalysis() {
  try {
    await predictResourceUsage();
    await predictApplicationMetrics();
    await detectAnomalies();
    
    logger.info('Predictive analysis completed');
  } catch (error) {
    logger.error('Error in predictive analysis', { error: error.message });
  }
}

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    // Collect latest metrics before responding
    await runPredictiveAnalysis();
    
    // Return metrics in Prometheus format
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving predictive metrics', { error: error.message });
    res.status(500).send('Error collecting predictive metrics');
  }
});

// API endpoint for scaling recommendations
app.get('/api/scaling-recommendations', async (req, res) => {
  try {
    // Run analysis
    await runPredictiveAnalysis();
    
    // Get recommendations
    const recommendations = [];
    
    // Check each component
    for (const component of Object.values(COMPONENTS)) {
      for (const urgency of Object.values(URGENCY)) {
        const value = metrics.scalingRecommendation.get({ component, urgency });
        
        if (value && value.value !== 0) {
          recommendations.push({
            component,
            urgency,
            action: value.value > 0 ? 'scale_up' : 'scale_down',
            confidence: urgency === URGENCY.HIGH ? CONFIDENCE.HIGH : 
                       urgency === URGENCY.MEDIUM ? CONFIDENCE.MEDIUM : CONFIDENCE.LOW
          });
        }
      }
    }
    
    res.json({ recommendations });
  } catch (error) {
    logger.error('Error in GET /api/scaling-recommendations', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.PREDICTIVE_ANALYZER_PORT || 9098;
app.listen(PORT, () => {
  logger.info(`Predictive Analyzer listening on port ${PORT}`);
});

// Schedule regular updates
const UPDATE_INTERVAL_MS = 15 * 60 * 1000; // 15 minutes
setInterval(runPredictiveAnalysis, UPDATE_INTERVAL_MS);

// Initial data collection
runPredictiveAnalysis().catch(error => {
  logger.error('Error in initial predictive analysis', { error: error.message });
});

module.exports = {
  runPredictiveAnalysis,
  predictResourceUsage,
  predictApplicationMetrics,
  detectAnomalies
};
