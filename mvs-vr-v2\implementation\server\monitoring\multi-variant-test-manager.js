/**
 * Multi-Variant Test Manager
 * 
 * This service manages multi-variant A/B tests, including variant assignment,
 * traffic allocation, and statistical analysis.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const crypto = require('crypto');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Test status values
const TEST_STATUS = {
  DRAFT: 'draft',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  ARCHIVED: 'archived'
};

/**
 * Get A/B tests
 * 
 * @param {string} status - Test status filter
 * @returns {Array} - A/B tests
 */
async function getTests(status = null) {
  try {
    let query = supabase
      .from('ab_tests')
      .select('*');
      
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
      
    if (error) {
      logger.error('Error fetching A/B tests', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getTests', { error: error.message });
    return [];
  }
}

/**
 * Get A/B test by ID
 * 
 * @param {string} testId - Test ID
 * @returns {Object} - A/B test
 */
async function getTestById(testId) {
  try {
    const { data, error } = await supabase
      .from('ab_tests')
      .select('*')
      .eq('id', testId)
      .single();
      
    if (error) {
      logger.error('Error fetching A/B test', { error: error.message });
      return null;
    }
    
    return data;
  } catch (error) {
    logger.error('Error in getTestById', { error: error.message });
    return null;
  }
}

/**
 * Get test results
 * 
 * @param {string} testId - Test ID
 * @returns {Array} - Test results
 */
async function getTestResults(testId) {
  try {
    const { data, error } = await supabase
      .from('ab_test_results')
      .select('*')
      .eq('test_id', testId)
      .order('date', { ascending: true });
      
    if (error) {
      logger.error('Error fetching test results', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getTestResults', { error: error.message });
    return [];
  }
}

/**
 * Update test status
 * 
 * @param {string} testId - Test ID
 * @param {string} status - New status
 * @returns {Object} - Updated test
 */
async function updateTestStatus(testId, status) {
  try {
    const { data, error } = await supabase
      .from('ab_tests')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', testId)
      .select();
      
    if (error) {
      logger.error('Error updating test status', { error: error.message });
      return null;
    }
    
    return data[0];
  } catch (error) {
    logger.error('Error in updateTestStatus', { error: error.message });
    return null;
  }
}

/**
 * Assign variant to user
 * 
 * @param {string} testId - Test ID
 * @param {string} userId - User ID
 * @returns {Object} - Assigned variant
 */
async function assignVariant(testId, userId) {
  try {
    // Get test
    const test = await getTestById(testId);
    
    if (!test) {
      throw new Error('Test not found');
    }
    
    // Check if test is running
    if (test.status !== TEST_STATUS.RUNNING) {
      throw new Error(`Test is not running (status: ${test.status})`);
    }
    
    // Check if user already has an assignment
    const { data: existingAssignment, error: assignmentError } = await supabase
      .from('ab_test_assignments')
      .select('*')
      .eq('test_id', testId)
      .eq('user_id', userId)
      .single();
      
    if (assignmentError && !assignmentError.message.includes('No rows found')) {
      logger.error('Error checking existing assignment', { error: assignmentError.message });
      throw assignmentError;
    }
    
    if (existingAssignment) {
      return {
        testId,
        userId,
        variantId: existingAssignment.variant_id,
        variantName: existingAssignment.variant_name,
        isControl: existingAssignment.is_control
      };
    }
    
    // Determine if user should be included in test based on traffic allocation
    const hash = crypto.createHash('md5').update(`${testId}:${userId}`).digest('hex');
    const hashValue = parseInt(hash.substring(0, 8), 16) / 0xffffffff; // Value between 0 and 1
    
    if (hashValue > test.traffic_allocation) {
      // User is not included in the test
      return {
        testId,
        userId,
        excluded: true,
        reason: 'traffic_allocation'
      };
    }
    
    // Assign variant
    const variants = test.variants || [];
    
    if (variants.length === 0) {
      throw new Error('Test has no variants');
    }
    
    // Use hash to deterministically assign variant
    const variantIndex = Math.floor(hashValue * variants.length);
    const variant = variants[variantIndex];
    
    // Create assignment
    const { data, error } = await supabase
      .from('ab_test_assignments')
      .insert({
        test_id: testId,
        user_id: userId,
        variant_id: variant.id,
        variant_name: variant.name,
        is_control: variant.is_control || false,
        created_at: new Date().toISOString()
      })
      .select();
      
    if (error) {
      logger.error('Error creating assignment', { error: error.message });
      throw error;
    }
    
    return {
      testId,
      userId,
      variantId: variant.id,
      variantName: variant.name,
      isControl: variant.is_control || false
    };
  } catch (error) {
    logger.error('Error in assignVariant', { error: error.message });
    throw error;
  }
}

/**
 * Record conversion
 * 
 * @param {string} testId - Test ID
 * @param {string} userId - User ID
 * @param {string} goalName - Goal name
 * @param {Object} conversionData - Conversion data
 * @returns {Object} - Recorded conversion
 */
async function recordConversion(testId, userId, goalName, conversionData = {}) {
  try {
    // Get user's variant assignment
    const { data: assignment, error: assignmentError } = await supabase
      .from('ab_test_assignments')
      .select('*')
      .eq('test_id', testId)
      .eq('user_id', userId)
      .single();
      
    if (assignmentError) {
      logger.error('Error fetching assignment', { error: assignmentError.message });
      throw assignmentError;
    }
    
    // Record conversion
    const { data, error } = await supabase
      .from('ab_test_conversions')
      .insert({
        test_id: testId,
        user_id: userId,
        variant_id: assignment.variant_id,
        variant_name: assignment.variant_name,
        goal_name: goalName,
        conversion_data: conversionData,
        created_at: new Date().toISOString()
      })
      .select();
      
    if (error) {
      logger.error('Error recording conversion', { error: error.message });
      throw error;
    }
    
    return data[0];
  } catch (error) {
    logger.error('Error in recordConversion', { error: error.message });
    throw error;
  }
}

/**
 * Analyze test results
 * 
 * @param {string} testId - Test ID
 * @returns {Object} - Test analysis
 */
async function analyzeTestResults(testId) {
  try {
    // Get test
    const test = await getTestById(testId);
    
    if (!test) {
      throw new Error('Test not found');
    }
    
    // Get assignments
    const { data: assignments, error: assignmentsError } = await supabase
      .from('ab_test_assignments')
      .select('*')
      .eq('test_id', testId);
      
    if (assignmentsError) {
      logger.error('Error fetching assignments', { error: assignmentsError.message });
      throw assignmentsError;
    }
    
    // Get conversions
    const { data: conversions, error: conversionsError } = await supabase
      .from('ab_test_conversions')
      .select('*')
      .eq('test_id', testId);
      
    if (conversionsError) {
      logger.error('Error fetching conversions', { error: conversionsError.message });
      throw conversionsError;
    }
    
    // Group assignments by variant
    const variantAssignments = {};
    
    assignments.forEach(assignment => {
      if (!variantAssignments[assignment.variant_id]) {
        variantAssignments[assignment.variant_id] = {
          variantId: assignment.variant_id,
          variantName: assignment.variant_name,
          isControl: assignment.is_control,
          assignments: []
        };
      }
      
      variantAssignments[assignment.variant_id].assignments.push(assignment);
    });
    
    // Group conversions by variant and goal
    const variantConversions = {};
    
    conversions.forEach(conversion => {
      if (!variantConversions[conversion.variant_id]) {
        variantConversions[conversion.variant_id] = {};
      }
      
      if (!variantConversions[conversion.variant_id][conversion.goal_name]) {
        variantConversions[conversion.variant_id][conversion.goal_name] = [];
      }
      
      variantConversions[conversion.variant_id][conversion.goal_name].push(conversion);
    });
    
    // Calculate conversion rates
    const results = [];
    
    Object.values(variantAssignments).forEach(variant => {
      const { variantId, variantName, isControl, assignments } = variant;
      const userCount = assignments.length;
      const goals = {};
      
      // Calculate conversion rate for each goal
      if (variantConversions[variantId]) {
        Object.entries(variantConversions[variantId]).forEach(([goalName, goalConversions]) => {
          // Count unique users who converted
          const convertedUsers = new Set(goalConversions.map(c => c.user_id));
          const conversionCount = convertedUsers.size;
          const conversionRate = userCount > 0 ? conversionCount / userCount : 0;
          
          goals[goalName] = {
            conversionCount,
            conversionRate
          };
        });
      }
      
      results.push({
        variantId,
        variantName,
        isControl,
        userCount,
        goals
      });
    });
    
    // Calculate statistical significance
    const controlVariant = results.find(r => r.isControl);
    const testVariants = results.filter(r => !r.isControl);
    
    if (controlVariant) {
      testVariants.forEach(variant => {
        Object.keys(variant.goals).forEach(goalName => {
          if (controlVariant.goals[goalName]) {
            const controlRate = controlVariant.goals[goalName].conversionRate;
            const variantRate = variant.goals[goalName].conversionRate;
            
            // Calculate z-score
            const controlSampleSize = controlVariant.userCount;
            const variantSampleSize = variant.userCount;
            
            const pooledProportion = (controlVariant.goals[goalName].conversionCount + variant.goals[goalName].conversionCount) / 
                                    (controlSampleSize + variantSampleSize);
                                    
            const standardError = Math.sqrt(pooledProportion * (1 - pooledProportion) * 
                                          (1 / controlSampleSize + 1 / variantSampleSize));
                                          
            const zScore = standardError > 0 ? (variantRate - controlRate) / standardError : 0;
            
            // Calculate p-value
            const pValue = calculatePValue(zScore);
            
            // Calculate confidence
            const confidence = 1 - pValue;
            
            // Calculate improvement
            const improvement = controlRate > 0 ? (variantRate - controlRate) / controlRate : 0;
            
            variant.goals[goalName].comparedToControl = {
              zScore,
              pValue,
              confidence,
              improvement,
              significant: confidence >= 0.95 // 95% confidence
            };
          }
        });
      });
    }
    
    return {
      testId,
      testName: test.name,
      status: test.status,
      startDate: test.start_date,
      endDate: test.end_date,
      results
    };
  } catch (error) {
    logger.error('Error in analyzeTestResults', { error: error.message });
    throw error;
  }
}

/**
 * Calculate p-value from z-score
 * 
 * @param {number} zScore - Z-score
 * @returns {number} - P-value
 */
function calculatePValue(zScore) {
  // Approximation of the cumulative distribution function of the standard normal distribution
  const absZ = Math.abs(zScore);
  const p = Math.exp(-0.5 * absZ * absZ);
  
  let pValue;
  
  if (absZ < 7) {
    const b0 = 0.2316419;
    const b1 = 0.319381530;
    const b2 = -0.356563782;
    const b3 = 1.781477937;
    const b4 = -1.821255978;
    const b5 = 1.330274429;
    
    const t = 1 / (1 + b0 * absZ);
    const cdf = 1 - p * (b1 * t + b2 * t * t + b3 * t * t * t + b4 * t * t * t * t + b5 * t * t * t * t * t);
    
    pValue = zScore < 0 ? 1 - cdf : cdf;
  } else {
    pValue = zScore < 0 ? 0 : 1;
  }
  
  // Two-tailed p-value
  return 2 * Math.min(pValue, 1 - pValue);
}

// API endpoints
app.get('/api/tests', async (req, res) => {
  try {
    const { status } = req.query;
    const tests = await getTests(status);
    res.json({ tests });
  } catch (error) {
    logger.error('Error in GET /api/tests', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/tests/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const test = await getTestById(id);
    
    if (!test) {
      return res.status(404).json({ error: 'Test not found' });
    }
    
    res.json({ test });
  } catch (error) {
    logger.error('Error in GET /api/tests/:id', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/tests/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!Object.values(TEST_STATUS).includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }
    
    const test = await updateTestStatus(id, status);
    
    if (!test) {
      return res.status(404).json({ error: 'Test not found' });
    }
    
    res.json({ test });
  } catch (error) {
    logger.error('Error in POST /api/tests/:id/status', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/tests/:id/assign', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const assignment = await assignVariant(id, userId);
    res.json({ assignment });
  } catch (error) {
    logger.error('Error in POST /api/tests/:id/assign', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/tests/:id/convert', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, goalName, conversionData } = req.body;
    
    if (!userId || !goalName) {
      return res.status(400).json({ error: 'User ID and goal name are required' });
    }
    
    const conversion = await recordConversion(id, userId, goalName, conversionData);
    res.json({ conversion });
  } catch (error) {
    logger.error('Error in POST /api/tests/:id/convert', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/tests/:id/results', async (req, res) => {
  try {
    const { id } = req.params;
    const analysis = await analyzeTestResults(id);
    res.json(analysis);
  } catch (error) {
    logger.error('Error in GET /api/tests/:id/results', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.MULTI_VARIANT_TEST_MANAGER_PORT || 9107;
app.listen(PORT, () => {
  logger.info(`Multi-Variant Test Manager listening on port ${PORT}`);
});

module.exports = {
  getTests,
  getTestById,
  updateTestStatus,
  assignVariant,
  recordConversion,
  analyzeTestResults
};
