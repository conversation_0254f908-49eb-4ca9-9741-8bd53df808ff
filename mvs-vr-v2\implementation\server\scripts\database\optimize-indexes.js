/**
 * Database Index Optimizer
 * 
 * This script analyzes database usage and recommends index optimizations.
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { Logger } = require('../../services/integration/logger');
const { logger } = require('../shared/utils/logger');

// Promisify functions
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);

// Create logger
const logger = new Logger();

// Configuration
const config = {
  database: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DB || 'postgres'
  },
  outputDir: process.env.INDEX_OUTPUT_DIR || path.join(__dirname, '../../../logs/indexes'),
  minTableSize: parseInt(process.env.MIN_TABLE_SIZE || '10000', 10),
  minIndexUsage: parseInt(process.env.MIN_INDEX_USAGE || '10', 10),
  maxIndexesPerTable: parseInt(process.env.MAX_INDEXES_PER_TABLE || '5', 10),
  minQueryCount: parseInt(process.env.MIN_QUERY_COUNT || '100', 10),
  slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000', 10),
  slowQueryLogPath: process.env.SLOW_QUERY_LOG_PATH || path.join(__dirname, '../../../logs/slow-queries.log')
};

// Create database pool
const pool = new Pool(config.database);

/**
 * Get database statistics
 * @returns {Promise<Object>} Database statistics
 */
async function getDatabaseStats() {
  const client = await pool.connect();
  
  try {
    // Get database size
    const dbSizeResult = await client.query(`
      SELECT pg_size_pretty(pg_database_size(current_database())) as size,
             pg_database_size(current_database()) as size_bytes
    `);
    
    // Get table statistics
    const tableStatsResult = await client.query(`
      SELECT
        schemaname,
        relname as table_name,
        n_live_tup as row_count,
        pg_size_pretty(pg_total_relation_size(schemaname || '.' || relname)) as total_size,
        pg_total_relation_size(schemaname || '.' || relname) as total_size_bytes,
        pg_size_pretty(pg_relation_size(schemaname || '.' || relname)) as table_size,
        pg_relation_size(schemaname || '.' || relname) as table_size_bytes,
        pg_size_pretty(pg_total_relation_size(schemaname || '.' || relname) - pg_relation_size(schemaname || '.' || relname)) as index_size,
        pg_total_relation_size(schemaname || '.' || relname) - pg_relation_size(schemaname || '.' || relname) as index_size_bytes,
        seq_scan,
        seq_tup_read,
        idx_scan,
        idx_tup_fetch,
        n_tup_ins,
        n_tup_upd,
        n_tup_del,
        n_tup_hot_upd,
        CASE WHEN seq_scan > 0 THEN seq_tup_read / seq_scan ELSE 0 END as avg_seq_tup_read,
        CASE WHEN idx_scan > 0 THEN idx_tup_fetch / idx_scan ELSE 0 END as avg_idx_tup_fetch
      FROM
        pg_stat_user_tables
      ORDER BY
        pg_total_relation_size(schemaname || '.' || relname) DESC
    `);
    
    // Get index statistics
    const indexStatsResult = await client.query(`
      SELECT
        schemaname,
        relname as table_name,
        indexrelname as index_name,
        pg_size_pretty(pg_relation_size(schemaname || '.' || indexrelname)) as index_size,
        pg_relation_size(schemaname || '.' || indexrelname) as index_size_bytes,
        idx_scan,
        idx_tup_read,
        idx_tup_fetch,
        CASE WHEN idx_scan > 0 THEN idx_tup_read / idx_scan ELSE 0 END as avg_idx_tup_read,
        CASE WHEN idx_scan > 0 THEN idx_tup_fetch / idx_scan ELSE 0 END as avg_idx_tup_fetch
      FROM
        pg_stat_user_indexes
      ORDER BY
        pg_relation_size(schemaname || '.' || indexrelname) DESC
    `);
    
    // Get index definitions
    const indexDefsResult = await client.query(`
      SELECT
        schemaname,
        tablename as table_name,
        indexname as index_name,
        indexdef as index_definition
      FROM
        pg_indexes
      WHERE
        schemaname NOT IN ('pg_catalog', 'information_schema')
      ORDER BY
        schemaname, tablename, indexname
    `);
    
    // Get unused indexes
    const unusedIndexesResult = await client.query(`
      SELECT
        schemaname,
        relname as table_name,
        indexrelname as index_name,
        pg_size_pretty(pg_relation_size(schemaname || '.' || indexrelname)) as index_size,
        pg_relation_size(schemaname || '.' || indexrelname) as index_size_bytes
      FROM
        pg_stat_user_indexes
      WHERE
        idx_scan = 0
        AND indexrelname NOT LIKE 'pg_%'
      ORDER BY
        pg_relation_size(schemaname || '.' || indexrelname) DESC
    `);
    
    // Get duplicate indexes
    const duplicateIndexesResult = await client.query(`
      WITH index_cols AS (
        SELECT
          i.indrelid,
          i.indexrelid,
          array_to_string(array_agg(a.attname ORDER BY x.ordinality), ', ') as cols
        FROM
          pg_index i
          JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
          JOIN LATERAL unnest(i.indkey) WITH ORDINALITY as x(attnum, ordinality) ON true
        GROUP BY
          i.indrelid, i.indexrelid
      )
      SELECT
        ns.nspname as schemaname,
        t.relname as table_name,
        i1.relname as index_name,
        i2.relname as duplicate_index_name,
        ic1.cols as columns,
        pg_size_pretty(pg_relation_size(i1.oid)) as index_size,
        pg_relation_size(i1.oid) as index_size_bytes
      FROM
        index_cols ic1
        JOIN index_cols ic2 ON ic1.indrelid = ic2.indrelid AND ic1.cols = ic2.cols AND ic1.indexrelid < ic2.indexrelid
        JOIN pg_class i1 ON i1.oid = ic1.indexrelid
        JOIN pg_class i2 ON i2.oid = ic2.indexrelid
        JOIN pg_class t ON t.oid = ic1.indrelid
        JOIN pg_namespace ns ON ns.oid = t.relnamespace
      WHERE
        ns.nspname NOT IN ('pg_catalog', 'information_schema')
      ORDER BY
        ns.nspname, t.relname, ic1.cols
    `);
    
    // Get missing indexes
    const missingIndexesResult = await client.query(`
      SELECT
        schemaname,
        relname as table_name,
        seq_scan,
        idx_scan,
        seq_scan - idx_scan as diff,
        CASE WHEN seq_scan > 0 THEN 100 * idx_scan / seq_scan ELSE 0 END as index_use_percent,
        pg_size_pretty(pg_relation_size(schemaname || '.' || relname)) as table_size,
        pg_relation_size(schemaname || '.' || relname) as table_size_bytes
      FROM
        pg_stat_user_tables
      WHERE
        seq_scan > idx_scan
        AND n_live_tup > ${config.minTableSize}
        AND seq_scan > 10
      ORDER BY
        diff DESC
    `);
    
    return {
      database: {
        name: config.database.database,
        size: dbSizeResult.rows[0].size,
        size_bytes: parseInt(dbSizeResult.rows[0].size_bytes, 10)
      },
      tables: tableStatsResult.rows,
      indexes: indexStatsResult.rows,
      indexDefinitions: indexDefsResult.rows,
      unusedIndexes: unusedIndexesResult.rows,
      duplicateIndexes: duplicateIndexesResult.rows,
      missingIndexes: missingIndexesResult.rows
    };
  } finally {
    client.release();
  }
}

/**
 * Analyze slow queries from log
 * @returns {Promise<Object>} Slow query analysis
 */
async function analyzeSlowQueries() {
  try {
    // Read slow query log
    const logContent = await readFileAsync(config.slowQueryLogPath, 'utf8');
    const logLines = logContent.trim().split('\n');
    
    // Parse log entries
    const logEntries = logLines.map(line => {
      try {
        return JSON.parse(line);
      } catch (error) {
        return null;
      }
    }).filter(entry => entry !== null);
    
    // Group queries by normalized form
    const normalizedQueries = new Map();
    
    for (const entry of logEntries) {
      const { query, duration, rowCount, name, type, timestamp } = entry;
      
      if (!query) continue;
      
      // Normalize query by replacing parameter values with placeholders
      const normalizedQuery = query
        .replace(/\$\d+/g, '$N')
        .replace(/'[^']*'/g, "'?'")
        .replace(/\d+/g, 'N')
        .trim();
      
      if (!normalizedQueries.has(normalizedQuery)) {
        normalizedQueries.set(normalizedQuery, {
          query: normalizedQuery,
          originalQuery: query,
          count: 0,
          totalDuration: 0,
          avgDuration: 0,
          maxDuration: 0,
          minDuration: 0,
          totalRows: 0,
          avgRows: 0,
          type,
          name,
          examples: []
        });
      }
      
      const stats = normalizedQueries.get(normalizedQuery);
      
      stats.count++;
      stats.totalDuration += duration;
      stats.avgDuration = stats.totalDuration / stats.count;
      stats.maxDuration = Math.max(stats.maxDuration, duration);
      stats.minDuration = stats.minDuration === 0 ? duration : Math.min(stats.minDuration, duration);
      stats.totalRows += rowCount || 0;
      stats.avgRows = stats.totalRows / stats.count;
      
      if (stats.examples.length < 3) {
        stats.examples.push({
          query,
          duration,
          rowCount,
          timestamp
        });
      }
    }
    
    // Convert to array and sort by total duration
    const queryStats = Array.from(normalizedQueries.values())
      .filter(stats => stats.count >= config.minQueryCount)
      .sort((a, b) => b.totalDuration - a.totalDuration);
    
    return {
      totalQueries: logEntries.length,
      uniqueQueries: normalizedQueries.size,
      frequentSlowQueries: queryStats
    };
  } catch (error) {
    logger.error(`Error analyzing slow queries: ${error.message}`, { error });
    
    return {
      totalQueries: 0,
      uniqueQueries: 0,
      frequentSlowQueries: []
    };
  }
}

/**
 * Generate index recommendations
 * @param {Object} dbStats - Database statistics
 * @param {Object} queryAnalysis - Query analysis
 * @returns {Promise<Object>} Index recommendations
 */
async function generateRecommendations(dbStats, queryAnalysis) {
  // Recommendations
  const recommendations = {
    indexesToRemove: [],
    indexesToAdd: [],
    tablesToAnalyze: [],
    queriesNeedingOptimization: []
  };
  
  // Identify unused indexes to remove
  for (const index of dbStats.unusedIndexes) {
    // Skip primary key and unique indexes
    const indexDef = dbStats.indexDefinitions.find(
      def => def.schemaname === index.schemaname && 
             def.table_name === index.table_name && 
             def.index_name === index.index_name
    );
    
    if (indexDef && (indexDef.index_definition.includes('PRIMARY KEY') || indexDef.index_definition.includes('UNIQUE'))) {
      continue;
    }
    
    recommendations.indexesToRemove.push({
      schemaname: index.schemaname,
      table_name: index.table_name,
      index_name: index.index_name,
      index_size: index.index_size,
      index_size_bytes: index.index_size_bytes,
      reason: 'Unused index',
      sql: `DROP INDEX IF EXISTS ${index.schemaname}.${index.index_name};`
    });
  }
  
  // Identify duplicate indexes to remove
  for (const index of dbStats.duplicateIndexes) {
    recommendations.indexesToRemove.push({
      schemaname: index.schemaname,
      table_name: index.table_name,
      index_name: index.duplicate_index_name,
      index_size: index.index_size,
      index_size_bytes: index.index_size_bytes,
      reason: `Duplicate of ${index.index_name} on columns (${index.columns})`,
      sql: `DROP INDEX IF EXISTS ${index.schemaname}.${index.duplicate_index_name};`
    });
  }
  
  // Identify tables that need indexes
  for (const table of dbStats.missingIndexes) {
    // Check if table already has too many indexes
    const tableIndexes = dbStats.indexes.filter(
      idx => idx.schemaname === table.schemaname && idx.table_name === table.table_name
    );
    
    if (tableIndexes.length >= config.maxIndexesPerTable) {
      recommendations.tablesToAnalyze.push({
        schemaname: table.schemaname,
        table_name: table.table_name,
        seq_scan: table.seq_scan,
        idx_scan: table.idx_scan,
        index_use_percent: table.index_use_percent,
        table_size: table.table_size,
        table_size_bytes: table.table_size_bytes,
        reason: `High number of sequential scans (${table.seq_scan}) but already has ${tableIndexes.length} indexes`
      });
      continue;
    }
    
    recommendations.tablesToAnalyze.push({
      schemaname: table.schemaname,
      table_name: table.table_name,
      seq_scan: table.seq_scan,
      idx_scan: table.idx_scan,
      index_use_percent: table.index_use_percent,
      table_size: table.table_size,
      table_size_bytes: table.table_size_bytes,
      reason: `High number of sequential scans (${table.seq_scan}) with low index usage (${table.index_use_percent.toFixed(2)}%)`
    });
  }
  
  // Identify slow queries that need optimization
  for (const query of queryAnalysis.frequentSlowQueries) {
    if (query.avgDuration >= config.slowQueryThreshold / 1000) {
      recommendations.queriesNeedingOptimization.push({
        query: query.originalQuery,
        normalized_query: query.query,
        count: query.count,
        avg_duration: query.avgDuration,
        max_duration: query.maxDuration,
        avg_rows: query.avgRows,
        type: query.type,
        name: query.name,
        examples: query.examples
      });
    }
  }
  
  return recommendations;
}

/**
 * Run index optimization
 * @returns {Promise<Object>} Optimization results
 */
async function runIndexOptimization() {
  try {
    logger.info('Starting database index optimization');
    
    // Create output directory
    await mkdirAsync(config.outputDir, { recursive: true });
    
    // Get database statistics
    const dbStats = await getDatabaseStats();
    
    // Analyze slow queries
    const queryAnalysis = await analyzeSlowQueries();
    
    // Generate recommendations
    const recommendations = await generateRecommendations(dbStats, queryAnalysis);
    
    // Save results
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const outputPath = path.join(config.outputDir, `index-recommendations-${timestamp}.json`);
    
    await writeFileAsync(outputPath, JSON.stringify({
      timestamp,
      database: dbStats.database,
      recommendations,
      stats: {
        tables: dbStats.tables.length,
        indexes: dbStats.indexes.length,
        unusedIndexes: dbStats.unusedIndexes.length,
        duplicateIndexes: dbStats.duplicateIndexes.length,
        missingIndexes: dbStats.missingIndexes.length,
        slowQueries: queryAnalysis.totalQueries,
        uniqueSlowQueries: queryAnalysis.uniqueQueries,
        frequentSlowQueries: queryAnalysis.frequentSlowQueries.length
      }
    }, null, 2));
    
    logger.info(`Index optimization completed. Recommendations saved to ${outputPath}`);
    
    // Generate SQL script for recommendations
    const sqlPath = path.join(config.outputDir, `index-recommendations-${timestamp}.sql`);
    
    let sql = `-- Database Index Optimization Recommendations\n`;
    sql += `-- Generated: ${timestamp}\n`;
    sql += `-- Database: ${dbStats.database.name}\n\n`;
    
    // Add SQL for removing indexes
    if (recommendations.indexesToRemove.length > 0) {
      sql += `-- Indexes to remove\n`;
      
      for (const index of recommendations.indexesToRemove) {
        sql += `-- ${index.reason}\n`;
        sql += `${index.sql}\n\n`;
      }
    }
    
    // Add SQL for analyzing tables
    if (recommendations.tablesToAnalyze.length > 0) {
      sql += `-- Tables to analyze\n`;
      
      for (const table of recommendations.tablesToAnalyze) {
        sql += `-- ${table.reason}\n`;
        sql += `ANALYZE ${table.schemaname}.${table.table_name};\n\n`;
      }
    }
    
    await writeFileAsync(sqlPath, sql);
    
    logger.info(`SQL script saved to ${sqlPath}`);
    
    return {
      timestamp,
      outputPath,
      sqlPath,
      recommendations
    };
  } catch (error) {
    logger.error(`Error optimizing indexes: ${error.message}`, { error });
    throw error;
  } finally {
    // Close pool
    await pool.end();
  }
}

// If script is run directly, run optimization
if (require.main === module) {
  runIndexOptimization()
    .then(results => {
      logger.info('Index optimization completed:');
      logger.info(`Recommendations saved to ${results.outputPath}`);
      logger.info(`SQL script saved to ${results.sqlPath}`);
      
      logger.info('\nSummary:');
      logger.info(`- Indexes to remove: ${results.recommendations.indexesToRemove.length}`);
      logger.info(`- Tables to analyze: ${results.recommendations.tablesToAnalyze.length}`);
      logger.info(`- Queries needing optimization: ${results.recommendations.queriesNeedingOptimization.length}`);
      
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runIndexOptimization,
  getDatabaseStats,
  analyzeSlowQueries,
  generateRecommendations
};
