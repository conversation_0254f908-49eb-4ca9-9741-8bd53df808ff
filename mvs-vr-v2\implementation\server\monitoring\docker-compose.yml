version: '3.8'

services:
  # Prometheus for metrics storage
  prometheus:
    image: prom/prometheus:latest
    container_name: mvs-vr-prometheus
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - '9090:9090'
    restart: unless-stopped
    networks:
      - monitoring-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: mvs-vr-grafana
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/etc/grafana/dashboards
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel
    ports:
      - '3000:3000'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Alert Manager for alert handling
  alertmanager:
    image: prom/alertmanager:latest
    container_name: mvs-vr-alertmanager
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    ports:
      - '9093:9093'
    restart: unless-stopped
    networks:
      - monitoring-network

  # Node Exporter for host metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: mvs-vr-node-exporter
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - '9101:9100'
    restart: unless-stopped
    networks:
      - monitoring-network

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: mvs-vr-cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - '8080:8080'
    restart: unless-stopped
    networks:
      - monitoring-network

  # Metrics Collector
  metrics-collector:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.metrics-collector
    container_name: mvs-vr-metrics-collector
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - METRICS_COLLECTOR_PORT=9090
    ports:
      - '9090:9090'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Alert Manager Service
  alert-manager-service:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.alert-manager
    container_name: mvs-vr-alert-manager-service
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - ALERT_MANAGER_PORT=9096
      - SLACK_WEBHOOK=${SLACK_WEBHOOK}
      - EMAIL_RECIPIENTS=${EMAIL_RECIPIENTS}
    ports:
      - '9096:9096'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - alertmanager

  # User Segment Analyzer
  user-segment-analyzer:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.user-segment-analyzer
    container_name: mvs-vr-user-segment-analyzer
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - USER_SEGMENT_ANALYZER_PORT=9095
    ports:
      - '9095:9095'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # A/B Test Monitor
  ab-test-monitor:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.ab-test-monitor
    container_name: mvs-vr-ab-test-monitor
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - AB_TEST_MONITOR_PORT=9097
    ports:
      - '9097:9097'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Predictive Analyzer
  predictive-analyzer:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.predictive-analyzer
    container_name: mvs-vr-predictive-analyzer
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - PREDICTIVE_ANALYZER_PORT=9098
    ports:
      - '9098:9098'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # UE Plugin Monitor
  ue-plugin-monitor:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.ue-plugin-monitor
    container_name: mvs-vr-ue-plugin-monitor
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - UE_PLUGIN_MONITOR_PORT=9100
    ports:
      - '9100:9100'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Mobile App Monitor
  mobile-app-monitor:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.mobile-app-monitor
    container_name: mvs-vr-mobile-app-monitor
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - MOBILE_APP_MONITOR_PORT=9101
    ports:
      - '9101:9101'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # ML Alert Analyzer
  ml-alert-analyzer:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.ml-alert-analyzer
    container_name: mvs-vr-ml-alert-analyzer
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - ML_ALERT_ANALYZER_PORT=9102
    ports:
      - '9102:9102'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - alert-manager-service

  # User Behavior Predictor
  user-behavior-predictor:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.user-behavior-predictor
    container_name: mvs-vr-user-behavior-predictor
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - USER_BEHAVIOR_PREDICTOR_PORT=9103
    ports:
      - '9103:9103'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Churn Predictor
  churn-predictor:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.churn-predictor
    container_name: mvs-vr-churn-predictor
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - CHURN_PREDICTOR_PORT=9104
    ports:
      - '9104:9104'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Personalization Impact Analyzer
  personalization-impact-analyzer:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.personalization-impact-analyzer
    container_name: mvs-vr-personalization-impact-analyzer
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - PERSONALIZATION_IMPACT_ANALYZER_PORT=9105
    ports:
      - '9105:9105'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Automated Test Creator
  automated-test-creator:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.automated-test-creator
    container_name: mvs-vr-automated-test-creator
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - AUTOMATED_TEST_CREATOR_PORT=9106
    ports:
      - '9106:9106'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Multi-Variant Test Manager
  multi-variant-test-manager:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.multi-variant-test-manager
    container_name: mvs-vr-multi-variant-test-manager
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - MULTI_VARIANT_TEST_MANAGER_PORT=9107
    ports:
      - '9107:9107'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Long-Term Impact Analyzer
  long-term-impact-analyzer:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.long-term-impact-analyzer
    container_name: mvs-vr-long-term-impact-analyzer
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - LONG_TERM_IMPACT_ANALYZER_PORT=9108
    ports:
      - '9108:9108'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Resource Optimizer
  resource-optimizer:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.resource-optimizer
    container_name: mvs-vr-resource-optimizer
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - RESOURCE_OPTIMIZER_PORT=9109
    ports:
      - '9109:9109'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Proactive Scaler
  proactive-scaler:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.proactive-scaler
    container_name: mvs-vr-proactive-scaler
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - PROACTIVE_SCALER_PORT=9110
    ports:
      - '9110:9110'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Business Impact Predictor
  business-impact-predictor:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.business-impact-predictor
    container_name: mvs-vr-business-impact-predictor
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - BUSINESS_IMPACT_PREDICTOR_PORT=9111
    ports:
      - '9111:9111'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Monitoring Service
  monitoring-service:
    build:
      context: ../
      dockerfile: ./monitoring/Dockerfile.monitoring-service
    container_name: mvs-vr-monitoring-service
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - MONITORING_SERVICE_PORT=9099
      - METRICS_COLLECTOR_PORT=9090
      - ALERT_MANAGER_PORT=9096
      - USER_SEGMENT_ANALYZER_PORT=9095
      - AB_TEST_MONITOR_PORT=9097
      - PREDICTIVE_ANALYZER_PORT=9098
      - UE_PLUGIN_MONITOR_PORT=9100
      - MOBILE_APP_MONITOR_PORT=9101
      - ML_ALERT_ANALYZER_PORT=9102
      - USER_BEHAVIOR_PREDICTOR_PORT=9103
      - CHURN_PREDICTOR_PORT=9104
      - PERSONALIZATION_IMPACT_ANALYZER_PORT=9105
      - AUTOMATED_TEST_CREATOR_PORT=9106
      - MULTI_VARIANT_TEST_MANAGER_PORT=9107
      - LONG_TERM_IMPACT_ANALYZER_PORT=9108
      - RESOURCE_OPTIMIZER_PORT=9109
      - PROACTIVE_SCALER_PORT=9110
      - BUSINESS_IMPACT_PREDICTOR_PORT=9111
    ports:
      - '9099:9099'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - metrics-collector
      - alert-manager-service
      - user-segment-analyzer
      - ab-test-monitor
      - predictive-analyzer
      - ue-plugin-monitor
      - mobile-app-monitor
      - ml-alert-analyzer
      - user-behavior-predictor
      - churn-predictor
      - personalization-impact-analyzer
      - automated-test-creator
      - multi-variant-test-manager
      - long-term-impact-analyzer
      - resource-optimizer
      - proactive-scaler
      - business-impact-predictor

  real-time-anomaly-detector:
    image: mvs-vr/real-time-anomaly-detector:latest
    build:
      context: .
      dockerfile: Dockerfile.real-time-anomaly-detector
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - REAL_TIME_ANOMALY_DETECTOR_PORT=9112
      - METRICS_ENDPOINT=http://prometheus:9090/api/v1/query
      - ALERT_MANAGER_ENDPOINT=http://alert-manager-service:9096/api/alerts
      - KAFKA_BROKERS=kafka:9092
    ports:
      - '9112:9112'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - alert-manager-service

  predictive-scaling-service:
    image: mvs-vr/predictive-scaling-service:latest
    build:
      context: .
      dockerfile: Dockerfile.predictive-scaling-service
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - PREDICTIVE_SCALING_SERVICE_PORT=9113
      - METRICS_ENDPOINT=http://prometheus:9090/api/v1/query
      - KUBERNETES_API_ENDPOINT=http://kubernetes-api:8080
      - KUBERNETES_NAMESPACE=mvs-vr
      - ENABLE_AUTO_SCALING=false
    ports:
      - '9113:9113'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  incident-management-integration:
    image: mvs-vr/incident-management-integration:latest
    build:
      context: .
      dockerfile: Dockerfile.incident-management-integration
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - INCIDENT_MANAGEMENT_INTEGRATION_PORT=9114
      - ALERT_MANAGER_ENDPOINT=http://alert-manager-service:9096/api/alerts
      - PAGERDUTY_ENABLED=false
      - OPSGENIE_ENABLED=false
      - SERVICENOW_ENABLED=false
      - JIRA_ENABLED=false
    ports:
      - '9114:9114'
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - alert-manager-service

networks:
  monitoring-network:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:
