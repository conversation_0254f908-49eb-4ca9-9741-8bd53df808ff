import apiClient from './api-client';
import { createClientSupabaseClient } from '@supabase/auth-helpers-nextjs';

/**
 * Interface for login request
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Interface for login response
 */
export interface LoginResponse {
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
  vendor: {
    id: string;
    name: string;
  };
}

/**
 * Interface for password reset request
 */
export interface PasswordResetRequest {
  email: string;
}

/**
 * Interface for password update request
 */
export interface PasswordUpdateRequest {
  password: string;
  token: string;
}

/**
 * Authentication service for vendor portal
 */
class AuthService {
  /**
   * Login with email and password
   */
  public async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/vendor-login', credentials);
      return response;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * Logout current user
   */
  public async logout(): Promise<void> {
    try {
      // Call logout endpoint
      await apiClient.post('/auth/logout');
      
      // Also sign out from Supabase
      const supabase = createClientSupabaseClient();
      await supabase.auth.signOut();
      
      // Redirect to login page
      window.location.href = '/vendor/login';
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  public async requestPasswordReset(request: PasswordResetRequest): Promise<void> {
    try {
      await apiClient.post('/auth/request-password-reset', request);
    } catch (error) {
      console.error('Password reset request failed:', error);
      throw error;
    }
  }

  /**
   * Update password with reset token
   */
  public async updatePassword(request: PasswordUpdateRequest): Promise<void> {
    try {
      await apiClient.post('/auth/update-password', request);
    } catch (error) {
      console.error('Password update failed:', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    try {
      const supabase = createClientSupabaseClient();
      const { data: { session } } = await supabase.auth.getSession();
      return !!session;
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  /**
   * Get current user info
   */
  public async getCurrentUser(): Promise<any> {
    try {
      const response = await apiClient.get('/auth/user');
      return response;
    } catch (error) {
      console.error('Failed to get current user:', error);
      throw error;
    }
  }

  /**
   * Refresh authentication token
   */
  public async refreshToken(): Promise<void> {
    try {
      const supabase = createClientSupabaseClient();
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) throw error;
      
      return data.session;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const authService = new AuthService();
export default authService;
