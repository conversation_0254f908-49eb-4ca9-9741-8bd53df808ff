/**
 * Y.js Provider for Collaborative Editing
 * 
 * This service integrates Y.js for conflict-free collaborative editing.
 * It provides document synchronization, awareness, and undo/redo functionality.
 */

import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import { IndexeddbPersistence } from 'y-indexeddb';
import { UndoManager } from 'y-undomanager';
import { CollaborationEventTypes } from './websocket-service';

/**
 * Y.js Provider for collaborative editing
 */
export class YjsProvider {
  /**
   * Create a new Y.js provider
   * @param {Object} options - Configuration options
   * @param {string} options.roomId - Unique identifier for the collaboration room
   * @param {string} options.userId - Current user's ID
   * @param {string} options.userName - Current user's display name
   * @param {string} options.userColor - Color to represent the user (hex code)
   * @param {string} options.wsUrl - WebSocket server URL
   * @param {boolean} options.enablePersistence - Whether to enable IndexedDB persistence
   * @param {boolean} options.enableUndoManager - Whether to enable undo/redo functionality
   */
  constructor(options) {
    this.roomId = options.roomId;
    this.userId = options.userId;
    this.userName = options.userName;
    this.userColor = options.userColor;
    this.wsUrl = options.wsUrl;
    this.enablePersistence = options.enablePersistence !== false;
    this.enableUndoManager = options.enableUndoManager !== false;
    
    // Initialize Y.js document
    this.doc = new Y.Doc();
    
    // Initialize shared data structures
    this.animationsMap = this.doc.getMap('animations');
    this.metadataMap = this.doc.getMap('metadata');
    this.cursorsMap = this.doc.getMap('cursors');
    this.selectionsMap = this.doc.getMap('selections');
    this.chatArray = this.doc.getArray('chat');
    
    // Set up WebSocket provider
    this.provider = new WebsocketProvider(
      this.wsUrl,
      this.roomId,
      this.doc,
      {
        connect: true,
        awareness: {
          // Initial local state
          local: {
            userId: this.userId,
            userName: this.userName,
            userColor: this.userColor,
            cursor: null,
            selection: null,
            activeAnimation: null,
            activeTrack: null,
            activeKeyframe: null
          }
        }
      }
    );
    
    // Set up persistence if enabled
    if (this.enablePersistence) {
      this.persistence = new IndexeddbPersistence(
        `animation-editor-${this.roomId}`,
        this.doc
      );
    }
    
    // Set up undo manager if enabled
    if (this.enableUndoManager) {
      this.undoManager = new UndoManager(
        [
          this.animationsMap,
          this.metadataMap,
          this.cursorsMap,
          this.selectionsMap
        ],
        {
          captureTimeout: 500,
          trackedOrigins: new Set([this.userId])
        }
      );
    }
    
    // Set up event handlers
    this.setupEventHandlers();
  }

  /**
   * Set up event handlers for the Y.js document and provider
   */
  setupEventHandlers() {
    // Handle awareness updates
    this.provider.awareness.on('update', ({ added, updated, removed }) => {
      const states = this.provider.awareness.getStates();
      const event = {
        type: CollaborationEventTypes.PRESENCE_UPDATE,
        added: added.map(clientId => states.get(clientId)),
        updated: updated.map(clientId => states.get(clientId)),
        removed
      };
      
      this.triggerEvent('awareness', event);
    });
    
    // Handle document updates
    this.doc.on('update', (update, origin) => {
      this.triggerEvent('update', { update, origin });
    });
    
    // Handle connection status changes
    this.provider.on('status', ({ status }) => {
      this.triggerEvent('status', { status });
    });
    
    // Handle sync events
    this.provider.on('sync', (isSynced) => {
      this.triggerEvent('sync', { isSynced });
    });
  }

  /**
   * Get all connected users
   * @returns {Array} Array of user objects
   */
  getConnectedUsers() {
    const states = this.provider.awareness.getStates();
    const users = [];
    
    states.forEach((state, clientId) => {
      if (state.userId && state.userName) {
        users.push({
          clientId,
          userId: state.userId,
          userName: state.userName,
          userColor: state.userColor,
          cursor: state.cursor,
          selection: state.selection,
          activeAnimation: state.activeAnimation,
          activeTrack: state.activeTrack,
          activeKeyframe: state.activeKeyframe
        });
      }
    });
    
    return users;
  }

  /**
   * Update the local user's awareness state
   * @param {Object} state - Partial state to update
   */
  updateAwareness(state) {
    const currentState = this.provider.awareness.getLocalState() || {};
    this.provider.awareness.setLocalState({
      ...currentState,
      ...state
    });
  }

  /**
   * Update cursor position
   * @param {Object} position - Cursor position { x, y }
   */
  updateCursor(position) {
    this.updateAwareness({
      cursor: position
    });
  }

  /**
   * Update selection
   * @param {Object} selection - Selection object
   */
  updateSelection(selection) {
    this.updateAwareness({
      selection
    });
  }

  /**
   * Update active elements
   * @param {Object} active - Active elements { animationId, trackId, keyframeId }
   */
  updateActiveElements(active) {
    this.updateAwareness({
      activeAnimation: active.animationId,
      activeTrack: active.trackId,
      activeKeyframe: active.keyframeId
    });
  }

  /**
   * Set animations data
   * @param {Array} animations - Array of animation objects
   */
  setAnimations(animations) {
    // Clear existing animations
    this.animationsMap.clear();
    
    // Add each animation to the map
    animations.forEach(animation => {
      this.animationsMap.set(animation.id, animation);
    });
  }

  /**
   * Get all animations
   * @returns {Array} Array of animation objects
   */
  getAnimations() {
    const animations = [];
    this.animationsMap.forEach((value, key) => {
      animations.push(value);
    });
    return animations;
  }

  /**
   * Update a specific animation
   * @param {string} animationId - Animation ID
   * @param {Object} animation - Animation object
   */
  updateAnimation(animationId, animation) {
    this.animationsMap.set(animationId, animation);
  }

  /**
   * Delete an animation
   * @param {string} animationId - Animation ID
   */
  deleteAnimation(animationId) {
    this.animationsMap.delete(animationId);
  }

  /**
   * Add a chat message
   * @param {Object} message - Chat message object
   */
  addChatMessage(message) {
    this.chatArray.push([{
      userId: this.userId,
      userName: this.userName,
      userColor: this.userColor,
      text: message.text,
      timestamp: message.timestamp || Date.now()
    }]);
  }

  /**
   * Get all chat messages
   * @returns {Array} Array of chat message objects
   */
  getChatMessages() {
    return this.chatArray.toArray();
  }

  /**
   * Undo the last operation
   */
  undo() {
    if (this.undoManager) {
      this.undoManager.undo();
    }
  }

  /**
   * Redo the last undone operation
   */
  redo() {
    if (this.undoManager) {
      this.undoManager.redo();
    }
  }

  /**
   * Destroy the provider and clean up resources
   */
  destroy() {
    if (this.undoManager) {
      this.undoManager.destroy();
    }
    
    if (this.persistence) {
      this.persistence.destroy();
    }
    
    this.provider.disconnect();
    this.doc.destroy();
  }

  /**
   * Add an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  on(event, callback) {
    if (event === 'update') {
      this.doc.on('update', callback);
    } else if (event === 'awareness') {
      this.provider.awareness.on('update', callback);
    } else if (event === 'status' || event === 'sync') {
      this.provider.on(event, callback);
    }
  }

  /**
   * Remove an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  off(event, callback) {
    if (event === 'update') {
      this.doc.off('update', callback);
    } else if (event === 'awareness') {
      this.provider.awareness.off('update', callback);
    } else if (event === 'status' || event === 'sync') {
      this.provider.off(event, callback);
    }
  }

  /**
   * Trigger an event
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  triggerEvent(event, data) {
    // This is a helper method for internal use
    // It doesn't actually trigger Y.js events, just helps with code organization
  }
}

export default YjsProvider;
