export enum PhaseType {
  PLANNING = 'planning',
  ASSET_CREATION = 'asset_creation',
  BLUEPRINT_DESIGN = 'blueprint_design',
  SCENE_CONSTRUCTION = 'scene_construction',
  FLOW_VALIDATION = 'flow_validation',
  FINAL_REVIEW = 'final_review',
  PUBLISHED = 'published',
}

export enum PhaseStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  metadata?: Record<string, any>;
}

export interface ScenePhase {
  status: PhaseStatus;
  validation_results?: Record<string, ValidationResult>;
  started_at?: string;
  completed_at?: string;
  metadata?: Record<string, any>;
}

export interface SceneState {
  scene_id: string;
  current_phase: PhaseType;
  phases: Record<PhaseType, ScenePhase>;
  completed_phases: PhaseType[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PhaseTransitionResult {
  success: boolean;
  previous_phase: PhaseType;
  new_phase: PhaseType;
  status: PhaseStatus;
  error?: string;
  missing_validations?: string[];
  metadata?: Record<string, any>;
}

export interface PhaseValidationConfig {
  required: boolean;
  dependencies?: PhaseType[];
  validations: Array<{
    type: string;
    name: string;
    required: boolean;
    validator: () => Promise<ValidationResult>;
  }>;
}

export type PhaseValidationMap = Record<PhaseType, PhaseValidationConfig>;
