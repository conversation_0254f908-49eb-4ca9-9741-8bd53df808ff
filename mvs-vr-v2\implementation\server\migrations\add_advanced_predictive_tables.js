/**
 * Migration: Add Advanced Predictive Tables
 * 
 * This migration adds tables for advanced predictive capabilities, including
 * resource optimization, proactive scaling, and business impact prediction.
 */

const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

async function run() {
  try {
    logger.info('Starting migration: Add Advanced Predictive Tables');
    
    // Create resource_allocation table if it doesn't exist
    const { error: tableExistsError } = await supabase.rpc('table_exists', {
      table_name: 'resource_allocation'
    });
    
    if (tableExistsError || tableExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'resource_allocation',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'cpu_allocation', type: 'float', nullable: false, default: 0.5 },
          { name: 'memory_allocation', type: 'float', nullable: false, default: 0.5 },
          { name: 'disk_allocation', type: 'float', nullable: false, default: 0.5 },
          { name: 'network_allocation', type: 'float', nullable: false, default: 0.5 },
          { name: 'database_allocation', type: 'float', nullable: false, default: 0.5 },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' },
          { name: 'updated_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating resource_allocation table', { error: createTableError.message });
      } else {
        logger.info('Created resource_allocation table');
        
        // Insert initial allocation
        const { error: insertError } = await supabase
          .from('resource_allocation')
          .insert({
            cpu_allocation: 0.5,
            memory_allocation: 0.5,
            disk_allocation: 0.5,
            network_allocation: 0.5,
            database_allocation: 0.5
          });
          
        if (insertError) {
          logger.error('Error inserting initial resource allocation', { error: insertError.message });
        } else {
          logger.info('Inserted initial resource allocation');
        }
      }
    }
    
    // Create service_scaling table if it doesn't exist
    const { error: scalingExistsError } = await supabase.rpc('table_exists', {
      table_name: 'service_scaling'
    });
    
    if (scalingExistsError || scalingExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'service_scaling',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'api_instances', type: 'integer', nullable: false, default: 2 },
          { name: 'api_min_instances', type: 'integer', nullable: false, default: 1 },
          { name: 'api_max_instances', type: 'integer', nullable: false, default: 10 },
          { name: 'frontend_instances', type: 'integer', nullable: false, default: 2 },
          { name: 'frontend_min_instances', type: 'integer', nullable: false, default: 1 },
          { name: 'frontend_max_instances', type: 'integer', nullable: false, default: 10 },
          { name: 'backend_instances', type: 'integer', nullable: false, default: 2 },
          { name: 'backend_min_instances', type: 'integer', nullable: false, default: 1 },
          { name: 'backend_max_instances', type: 'integer', nullable: false, default: 10 },
          { name: 'database_instances', type: 'integer', nullable: false, default: 1 },
          { name: 'database_min_instances', type: 'integer', nullable: false, default: 1 },
          { name: 'database_max_instances', type: 'integer', nullable: false, default: 3 },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' },
          { name: 'updated_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating service_scaling table', { error: createTableError.message });
      } else {
        logger.info('Created service_scaling table');
        
        // Insert initial scaling
        const { error: insertError } = await supabase
          .from('service_scaling')
          .insert({
            api_instances: 2,
            api_min_instances: 1,
            api_max_instances: 10,
            frontend_instances: 2,
            frontend_min_instances: 1,
            frontend_max_instances: 10,
            backend_instances: 2,
            backend_min_instances: 1,
            backend_max_instances: 10,
            database_instances: 1,
            database_min_instances: 1,
            database_max_instances: 3
          });
          
        if (insertError) {
          logger.error('Error inserting initial service scaling', { error: insertError.message });
        } else {
          logger.info('Inserted initial service scaling');
        }
      }
    }
    
    // Create system_incidents table if it doesn't exist
    const { error: incidentsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'system_incidents'
    });
    
    if (incidentsExistsError || incidentsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'system_incidents',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'title', type: 'text', nullable: false },
          { name: 'description', type: 'text', nullable: true },
          { name: 'type', type: 'text', nullable: false },
          { name: 'severity', type: 'text', nullable: false },
          { name: 'status', type: 'text', nullable: false },
          { name: 'start_time', type: 'timestamp with time zone', nullable: false, default: 'now()' },
          { name: 'end_time', type: 'timestamp with time zone', nullable: true },
          { name: 'affected_services', type: 'jsonb', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' },
          { name: 'updated_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating system_incidents table', { error: createTableError.message });
      } else {
        logger.info('Created system_incidents table');
        
        // Create index on type
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'system_incidents',
          column_name: 'type'
        });
        
        if (indexError1) {
          logger.error('Error creating index on type', { error: indexError1.message });
        } else {
          logger.info('Created index on type');
        }
        
        // Create index on severity
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'system_incidents',
          column_name: 'severity'
        });
        
        if (indexError2) {
          logger.error('Error creating index on severity', { error: indexError2.message });
        } else {
          logger.info('Created index on severity');
        }
        
        // Create index on status
        const { error: indexError3 } = await supabase.rpc('create_index', {
          table_name: 'system_incidents',
          column_name: 'status'
        });
        
        if (indexError3) {
          logger.error('Error creating index on status', { error: indexError3.message });
        } else {
          logger.info('Created index on status');
        }
      }
    }
    
    // Create business_metrics table if it doesn't exist
    const { error: metricsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'business_metrics'
    });
    
    if (metricsExistsError || metricsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'business_metrics',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'date', type: 'date', nullable: false },
          { name: 'revenue', type: 'float', nullable: false, default: 0 },
          { name: 'active_users', type: 'integer', nullable: false, default: 0 },
          { name: 'conversion_rate', type: 'float', nullable: false, default: 0 },
          { name: 'retention_rate', type: 'float', nullable: false, default: 0 },
          { name: 'satisfaction_score', type: 'float', nullable: false, default: 0 },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' },
          { name: 'updated_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating business_metrics table', { error: createTableError.message });
      } else {
        logger.info('Created business_metrics table');
        
        // Create index on date
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'business_metrics',
          column_name: 'date'
        });
        
        if (indexError) {
          logger.error('Error creating index on date', { error: indexError.message });
        } else {
          logger.info('Created index on date');
        }
        
        // Create unique index on date
        const { error: uniqueIndexError } = await supabase.rpc('create_unique_index', {
          table_name: 'business_metrics',
          column_names: ['date']
        });
        
        if (uniqueIndexError) {
          logger.error('Error creating unique index on date', { error: uniqueIndexError.message });
        } else {
          logger.info('Created unique index on date');
        }
      }
    }
    
    // Create system_metrics table if it doesn't exist
    const { error: sysMetricsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'system_metrics'
    });
    
    if (sysMetricsExistsError || sysMetricsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'system_metrics',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'timestamp', type: 'timestamp with time zone', nullable: false, default: 'now()' },
          { name: 'cpu_usage', type: 'float', nullable: false, default: 0 },
          { name: 'memory_usage', type: 'float', nullable: false, default: 0 },
          { name: 'disk_usage', type: 'float', nullable: false, default: 0 },
          { name: 'network_in', type: 'float', nullable: false, default: 0 },
          { name: 'network_out', type: 'float', nullable: false, default: 0 },
          { name: 'service', type: 'text', nullable: true },
          { name: 'instance', type: 'text', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating system_metrics table', { error: createTableError.message });
      } else {
        logger.info('Created system_metrics table');
        
        // Create index on timestamp
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'system_metrics',
          column_name: 'timestamp'
        });
        
        if (indexError1) {
          logger.error('Error creating index on timestamp', { error: indexError1.message });
        } else {
          logger.info('Created index on timestamp');
        }
        
        // Create index on service
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'system_metrics',
          column_name: 'service'
        });
        
        if (indexError2) {
          logger.error('Error creating index on service', { error: indexError2.message });
        } else {
          logger.info('Created index on service');
        }
      }
    }
    
    // Create service_metrics table if it doesn't exist
    const { error: serviceMetricsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'service_metrics'
    });
    
    if (serviceMetricsExistsError || serviceMetricsExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'service_metrics',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'timestamp', type: 'timestamp with time zone', nullable: false, default: 'now()' },
          { name: 'service', type: 'text', nullable: false },
          { name: 'api_requests', type: 'integer', nullable: false, default: 0 },
          { name: 'frontend_requests', type: 'integer', nullable: false, default: 0 },
          { name: 'backend_requests', type: 'integer', nullable: false, default: 0 },
          { name: 'database_queries', type: 'integer', nullable: false, default: 0 },
          { name: 'cache_hits', type: 'integer', nullable: false, default: 0 },
          { name: 'cache_misses', type: 'integer', nullable: false, default: 0 },
          { name: 'storage_operations', type: 'integer', nullable: false, default: 0 },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating service_metrics table', { error: createTableError.message });
      } else {
        logger.info('Created service_metrics table');
        
        // Create index on timestamp
        const { error: indexError1 } = await supabase.rpc('create_index', {
          table_name: 'service_metrics',
          column_name: 'timestamp'
        });
        
        if (indexError1) {
          logger.error('Error creating index on timestamp', { error: indexError1.message });
        } else {
          logger.info('Created index on timestamp');
        }
        
        // Create index on service
        const { error: indexError2 } = await supabase.rpc('create_index', {
          table_name: 'service_metrics',
          column_name: 'service'
        });
        
        if (indexError2) {
          logger.error('Error creating index on service', { error: indexError2.message });
        } else {
          logger.info('Created index on service');
        }
      }
    }
    
    logger.info('Migration completed: Add Advanced Predictive Tables');
  } catch (error) {
    logger.error('Error in migration', { error: error.message });
  }
}

// Run migration if called directly
if (require.main === module) {
  run().then(() => {
    process.exit(0);
  }).catch(error => {
    logger.error('Migration failed', { error: error.message });
    process.exit(1);
  });
}

module.exports = { run };
