FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/long-term-impact-analyzer.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV LONG_TERM_IMPACT_ANALYZER_PORT=9108

# Expose port
EXPOSE 9108

# Start the service
CMD ["node", "monitoring/long-term-impact-analyzer.js"]
