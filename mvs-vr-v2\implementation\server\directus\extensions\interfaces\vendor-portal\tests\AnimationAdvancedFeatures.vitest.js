import { mount } from 'npm:@vue/test-utils'; // Use npm specifier
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AnimationEditor from '../src/components/VisualEditors/AnimationEditor.vue';

// Mock API
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
};

describe('AnimationEditor Advanced Features', () => {
  let wrapper;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock API responses
    mockApi.get.mockImplementation(url => {
      if (url.includes('/items/animations')) {
        return Promise.resolve({
          data: {
            data: [
              {
                id: 'anim1',
                name: 'Test Animation',
                duration: 5,
                tracks: [
                  {
                    id: 'track1',
                    name: 'Position',
                    type: 'transform',
                    keyframes: [
                      { id: 'kf1', time: 0, value: { x: 0, y: 0, z: 0 }, easing: 'linear' },
                      { id: 'kf2', time: 2.5, value: { x: 10, y: 5, z: 0 }, easing: 'easeInOut' },
                      { id: 'kf3', time: 5, value: { x: 0, y: 0, z: 0 }, easing: 'linear' },
                    ],
                  },
                ],
              },
              {
                id: 'anim2',
                name: 'Second Animation',
                duration: 3,
                tracks: [
                  {
                    id: 'track1',
                    name: 'Rotation',
                    type: 'rotation',
                    keyframes: [
                      { id: 'kf1', time: 0, value: { x: 0, y: 0, z: 0 }, easing: 'linear' },
                      { id: 'kf2', time: 3, value: { x: 0, y: 360, z: 0 }, easing: 'linear' },
                    ],
                  },
                ],
              },
            ],
          },
        });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    // Create wrapper
    wrapper = mount(AnimationEditor, {
      props: {
        vendorId: 'vendor1',
        animationId: null,
      },
      global: {
        mocks: {
          $api: mockApi,
        },
      },
    });
  });

  // Animation Blending Tests
  describe('Animation Blending', () => {
    it('blends between two animations', async () => {
      // Wait for animations to load
      await wrapper.vm.$nextTick();

      // Check if blendAnimations method exists
      expect(typeof wrapper.vm.blendAnimations).toBe('function');

      // Call the blend method (assuming it's implemented)
      const blendResult = await wrapper.vm.blendAnimations('anim1', 'anim2', 0.5);

      // Check the result
      expect(blendResult).toBeDefined();
      expect(blendResult.name).toContain('Blend');
      expect(blendResult.tracks.length).toBeGreaterThan(0);
    });

    it('creates a new animation from a blend', async () => {
      // Wait for animations to load
      await wrapper.vm.$nextTick();

      // Mock the API response for creating a new animation
      mockApi.post.mockResolvedValue({
        data: {
          data: {
            id: 'blend-anim',
            name: 'Blended Animation',
            duration: 5,
            tracks: [],
          },
        },
      });

      // Call the save blend method (assuming it's implemented)
      await wrapper.vm.saveBlendedAnimation('anim1', 'anim2', 0.5, 'Blended Animation');

      // Check if the API was called
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/animations',
        expect.objectContaining({
          name: 'Blended Animation',
          vendor_id: 'vendor1',
        }),
      );
    });
  });

  // Easing Presets Tests
  describe('Easing Presets', () => {
    it('applies easing preset to keyframe', async () => {
      // Wait for animations to load
      await wrapper.vm.$nextTick();

      // Set animations and select one
      wrapper.vm.animations = [
        {
          id: 'anim1',
          name: 'Test Animation',
          duration: 5,
          tracks: [
            {
              id: 'track1',
              name: 'Position',
              type: 'transform',
              keyframes: [
                { id: 'kf1', time: 0, value: { x: 0, y: 0, z: 0 }, easing: 'linear' },
                { id: 'kf2', time: 5, value: { x: 10, y: 0, z: 0 }, easing: 'linear' },
              ],
            },
          ],
        },
      ];
      await wrapper.vm.selectAnimation('anim1');
      await wrapper.vm.selectTrack('track1');
      await wrapper.vm.selectKeyframe('kf2');

      // Apply easing preset
      await wrapper.vm.applyEasingPreset('easeInOutQuad');

      // Check if the easing was applied
      const keyframe = wrapper.vm.selectedAnimation.tracks[0].keyframes[1];
      expect(keyframe.easing).toBe('easeInOutQuad');
    });

    it('previews easing effect', async () => {
      // Wait for animations to load
      await wrapper.vm.$nextTick();

      // Check if previewEasing method exists
      expect(typeof wrapper.vm.previewEasing).toBe('function');

      // Call the preview method
      const previewData = wrapper.vm.previewEasing('easeInOutQuad', 10);

      // Check the result
      expect(previewData).toBeDefined();
      expect(previewData.length).toBeGreaterThan(0);
      expect(previewData[0].value).toBe(0);
      expect(previewData[previewData.length - 1].value).toBe(1);
    });
  });

  // Advanced Keyframe Interpolation Tests
  describe('Advanced Keyframe Interpolation', () => {
    it('interpolates between keyframes with custom curve', async () => {
      // Wait for animations to load
      await wrapper.vm.$nextTick();

      // Check if interpolateWithCurve method exists
      expect(typeof wrapper.vm.interpolateWithCurve).toBe('function');

      // Call the interpolate method
      const result = wrapper.vm.interpolateWithCurve(
        { x: 0, y: 0, z: 0 },
        { x: 10, y: 0, z: 0 },
        0.5,
        { x1: 0.42, y1: 0, x2: 0.58, y2: 1 }, // Bezier curve control points
      );

      // Check the result
      expect(result).toBeDefined();
      expect(result.x).toBeGreaterThan(0);
      expect(result.x).toBeLessThan(10);
    });

    it('creates custom interpolation curve', async () => {
      // Wait for animations to load
      await wrapper.vm.$nextTick();

      // Set animations and select one
      wrapper.vm.animations = [
        {
          id: 'anim1',
          name: 'Test Animation',
          duration: 5,
          tracks: [
            {
              id: 'track1',
              name: 'Position',
              type: 'transform',
              keyframes: [
                { id: 'kf1', time: 0, value: { x: 0, y: 0, z: 0 }, easing: 'linear' },
                { id: 'kf2', time: 5, value: { x: 10, y: 0, z: 0 }, easing: 'linear' },
              ],
            },
          ],
        },
      ];
      await wrapper.vm.selectAnimation('anim1');
      await wrapper.vm.selectTrack('track1');
      await wrapper.vm.selectKeyframe('kf2');

      // Create custom curve
      await wrapper.vm.setCustomCurve('kf2', { x1: 0.42, y1: 0, x2: 0.58, y2: 1 });

      // Check if the curve was applied
      const keyframe = wrapper.vm.selectedAnimation.tracks[0].keyframes[1];
      expect(keyframe.curve).toBeDefined();
      expect(keyframe.curve.x1).toBe(0.42);
    });
  });
});
