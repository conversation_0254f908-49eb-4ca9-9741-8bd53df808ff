/**
 * Easing Functions for Animation Interpolation
 * 
 * These functions take a progress value between 0 and 1
 * and return a transformed value also between 0 and 1.
 */

const EasingFunctions = {
  /**
   * Linear easing (no easing)
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  linear: (t) => t,

  /**
   * Quadratic ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInQuad: (t) => t * t,

  /**
   * Quadratic ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutQuad: (t) => t * (2 - t),

  /**
   * Quadratic ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutQuad: (t) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),

  /**
   * Cubic ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInCubic: (t) => t * t * t,

  /**
   * Cubic ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutCubic: (t) => --t * t * t + 1,

  /**
   * Cubic ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutCubic: (t) => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),

  /**
   * Quartic ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInQuart: (t) => t * t * t * t,

  /**
   * Quartic ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutQuart: (t) => 1 - --t * t * t * t,

  /**
   * Quartic ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutQuart: (t) => (t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t),

  /**
   * Quintic ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInQuint: (t) => t * t * t * t * t,

  /**
   * Quintic ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutQuint: (t) => 1 + --t * t * t * t * t,

  /**
   * Quintic ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutQuint: (t) => (t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t),

  /**
   * Sine ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInSine: (t) => 1 - Math.cos(t * Math.PI / 2),

  /**
   * Sine ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutSine: (t) => Math.sin(t * Math.PI / 2),

  /**
   * Sine ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutSine: (t) => -(Math.cos(Math.PI * t) - 1) / 2,

  /**
   * Exponential ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInExpo: (t) => (t === 0 ? 0 : Math.pow(2, 10 * (t - 1))),

  /**
   * Exponential ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutExpo: (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t)),

  /**
   * Exponential ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutExpo: (t) => {
    if (t === 0 || t === 1) return t;
    const scaledT = t * 2;
    if (scaledT < 1) return 0.5 * Math.pow(2, 10 * (scaledT - 1));
    return 0.5 * (2 - Math.pow(2, -10 * (scaledT - 1)));
  },

  /**
   * Circular ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInCirc: (t) => 1 - Math.sqrt(1 - t * t),

  /**
   * Circular ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutCirc: (t) => Math.sqrt(1 - --t * t),

  /**
   * Circular ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutCirc: (t) => {
    const scaledT = t * 2;
    if (scaledT < 1) return -0.5 * (Math.sqrt(1 - scaledT * scaledT) - 1);
    return 0.5 * (Math.sqrt(1 - (scaledT - 2) * (scaledT - 2)) + 1);
  },

  /**
   * Elastic ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInElastic: (t) => {
    if (t === 0 || t === 1) return t;
    return -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
  },

  /**
   * Elastic ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutElastic: (t) => {
    if (t === 0 || t === 1) return t;
    return Math.pow(2, -10 * t) * Math.sin((t - 0.1) * 5 * Math.PI) + 1;
  },

  /**
   * Elastic ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutElastic: (t) => {
    if (t === 0 || t === 1) return t;
    const scaledT = t * 2;
    if (scaledT < 1) {
      return -0.5 * Math.pow(2, 10 * (scaledT - 1)) * Math.sin((scaledT - 1.1) * 5 * Math.PI);
    }
    return 0.5 * Math.pow(2, -10 * (scaledT - 1)) * Math.sin((scaledT - 1.1) * 5 * Math.PI) + 1;
  },

  /**
   * Back ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInBack: (t) => {
    const s = 1.70158;
    return t * t * ((s + 1) * t - s);
  },

  /**
   * Back ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutBack: (t) => {
    const s = 1.70158;
    return --t * t * ((s + 1) * t + s) + 1;
  },

  /**
   * Back ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutBack: (t) => {
    const s = 1.70158 * 1.525;
    const scaledT = t * 2;
    if (scaledT < 1) return 0.5 * (scaledT * scaledT * ((s + 1) * scaledT - s));
    return 0.5 * ((scaledT - 2) * (scaledT - 2) * ((s + 1) * (scaledT - 2) + s) + 2);
  },

  /**
   * Bounce ease in
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInBounce: (t) => 1 - EasingFunctions.easeOutBounce(1 - t),

  /**
   * Bounce ease out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeOutBounce: (t) => {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    } else if (t < 2 / 2.75) {
      return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    } else if (t < 2.5 / 2.75) {
      return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    } else {
      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
    }
  },

  /**
   * Bounce ease in and out
   * @param {number} t - Progress from 0 to 1
   * @returns {number} - Transformed value
   */
  easeInOutBounce: (t) => {
    if (t < 0.5) return EasingFunctions.easeInBounce(t * 2) * 0.5;
    return EasingFunctions.easeOutBounce(t * 2 - 1) * 0.5 + 0.5;
  }
};

export default EasingFunctions;
