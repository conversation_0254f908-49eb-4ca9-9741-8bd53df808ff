FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/mobile-app-monitor.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV MOBILE_APP_MONITOR_PORT=9101

# Expose port
EXPOSE 9101

# Start the service
CMD ["node", "monitoring/mobile-app-monitor.js"]
