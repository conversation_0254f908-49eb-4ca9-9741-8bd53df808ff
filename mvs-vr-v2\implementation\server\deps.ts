// Node.js built-in modules
export { resolve, dirname, join } from 'node:path';
export { fileURLToPath, pathToFileURL } from 'node:url';

// Test framework dependencies
export { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll, vi } from 'npm:vitest';
export { defineConfig } from 'npm:vitest/config';
export type { SpyInstance } from 'npm:vitest';

// Vue related dependencies
export { default as vue } from 'npm:@vitejs/plugin-vue';
export { config as vueConfig } from 'npm:@vue/test-utils';

// Testing utilities
export { default as jestDom } from 'npm:@testing-library/jest-dom';

// Environment utilities
export { config as dotenvConfig } from 'npm:dotenv';

// Type augmentations
export { LogLevel, type Logger, type LoggerConfig } from './services/integration/logger.ts';

export {
  type ServiceRegistry,
  type ServiceConfig,
} from './services/integration/serviceRegistry.ts';

// Scene management types
export {
  PhaseType,
  PhaseStatus,
  type ScenePhase,
  type SceneState,
} from './shared/types/scene.types.ts';

// Module resolution mappings
export const MODULE_ALIASES = {
  '@': './src',
  '@directus': './directus/extensions',
  '@shared': './shared',
  '@services': './services',
  '@tests': './tests',
  '@setup': './tests/setup',
} as const;

// External dependency mappings
export const EXTERNAL_MODULES = {
  vitest: 'npm:vitest',
  '@vitejs/plugin-vue': 'npm:@vitejs/plugin-vue',
  '@vue/test-utils': 'npm:@vue/test-utils',
  '@testing-library/jest-dom': 'npm:@testing-library/jest-dom',
} as const;
