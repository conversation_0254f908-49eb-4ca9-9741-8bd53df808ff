
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for vendor-portal/coverage/lcov-report/prettify.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">vendor-portal/coverage/lcov-report</a> prettify.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/536</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/286</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/29</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/* eslint-disable */
<span class="cstat-no" title="statement not covered" >window.PR_SHOULD_USE_CONTINUATION=true;<span class="cstat-no" title="statement not covered" >(<span class="fstat-no" title="function not covered" ></span>fu</span>nction(){var h=<span class="cstat-no" title="statement not covered" >["break,continue,do,else,for,if,return,while"];</span>var u=<span class="cstat-no" title="statement not covered" >[h,"auto,case,char,const,default,double,enum,extern,float,goto,int,long,register,short,signed,sizeof,static,struct,switch,typedef,union,unsigned,void,volatile"];</span>var p=<span class="cstat-no" title="statement not covered" >[u,"catch,class,delete,false,import,new,operator,private,protected,public,this,throw,true,try,typeof"];</span>var l=<span class="cstat-no" title="statement not covered" >[p,"alignof,align_union,asm,axiom,bool,concept,concept_map,const_cast,constexpr,decltype,dynamic_cast,explicit,export,friend,inline,late_check,mutable,namespace,nullptr,reinterpret_cast,static_assert,static_cast,template,typeid,typename,using,virtual,where"];</span>var x=<span class="cstat-no" title="statement not covered" >[p,"abstract,boolean,byte,extends,final,finally,implements,import,instanceof,null,native,package,strictfp,super,synchronized,throws,transient"];</span>var R=<span class="cstat-no" title="statement not covered" >[x,"as,base,by,checked,decimal,delegate,descending,dynamic,event,fixed,foreach,from,group,implicit,in,interface,internal,into,is,lock,object,out,override,orderby,params,partial,readonly,ref,sbyte,sealed,stackalloc,string,select,uint,ulong,unchecked,unsafe,ushort,var"];</span>var r=<span class="cstat-no" title="statement not covered" >"all,and,by,catch,class,else,extends,false,finally,for,if,in,is,isnt,loop,new,no,not,null,of,off,on,or,return,super,then,true,try,unless,until,when,while,yes";</span>var w=<span class="cstat-no" title="statement not covered" >[p,"debugger,eval,export,function,get,null,set,undefined,var,with,Infinity,NaN"];</span>var s=<span class="cstat-no" title="statement not covered" >"caller,delete,die,do,dump,elsif,eval,exit,foreach,for,goto,if,import,last,local,my,next,no,our,print,package,redo,require,sub,undef,unless,until,use,wantarray,while,BEGIN,END";</span>var I=<span class="cstat-no" title="statement not covered" >[h,"and,as,assert,class,def,del,elif,except,exec,finally,from,global,import,in,is,lambda,nonlocal,not,or,pass,print,raise,try,with,yield,False,True,None"];</span>var f=<span class="cstat-no" title="statement not covered" >[h,"alias,and,begin,case,class,def,defined,elsif,end,ensure,false,in,module,next,nil,not,or,redo,rescue,retry,self,super,then,true,undef,unless,until,when,yield,BEGIN,END"];</span>var H=<span class="cstat-no" title="statement not covered" >[h,"case,done,elif,esac,eval,fi,function,in,local,set,then,until"];</span>var A=<span class="cstat-no" title="statement not covered" >[l,R,w,s+I,f,H];</span>var e=<span class="cstat-no" title="statement not covered" >/^(DIR|FILE|vector|(de|priority_)?queue|list|stack|(const_)?iterator|(multi)?(set|map)|bitset|u?(int|float)\d*)/;</span>var C=<span class="cstat-no" title="statement not covered" >"str";</span>var z=<span class="cstat-no" title="statement not covered" >"kwd";</span>var j=<span class="cstat-no" title="statement not covered" >"com";</span>var O=<span class="cstat-no" title="statement not covered" >"typ";</span>var G=<span class="cstat-no" title="statement not covered" >"lit";</span>var L=<span class="cstat-no" title="statement not covered" >"pun";</span>var F=<span class="cstat-no" title="statement not covered" >"pln";</span>var m=<span class="cstat-no" title="statement not covered" >"tag";</span>var E=<span class="cstat-no" title="statement not covered" >"dec";</span>var J=<span class="cstat-no" title="statement not covered" >"src";</span>var P=<span class="cstat-no" title="statement not covered" >"atn";</span>var n=<span class="cstat-no" title="statement not covered" >"atv";</span>var N=<span class="cstat-no" title="statement not covered" >"nocode";</span>var M=<span class="cstat-no" title="statement not covered" >"(?:^^\\.?|[+-]|\\!|\\!=|\\!==|\\#|\\%|\\%=|&amp;|&amp;&amp;|&amp;&amp;=|&amp;=|\\(|\\*|\\*=|\\+=|\\,|\\-=|\\-&gt;|\\/|\\/=|:|::|\\;|&lt;|&lt;&lt;|&lt;&lt;=|&lt;=|=|==|===|&gt;|&gt;=|&gt;&gt;|&gt;&gt;=|&gt;&gt;&gt;|&gt;&gt;&gt;=|\\?|\\@|\\[|\\^|\\^=|\\^\\^|\\^\\^=|\\{|\\||\\|=|\\|\\||\\|\\|=|\\~|break|case|continue|delete|do|else|finally|instanceof|return|throw|try|typeof)\\s*";</span>function <span class="fstat-no" title="function not covered" >k(</span>Z){var ad=<span class="cstat-no" title="statement not covered" >0;</span>var S=<span class="cstat-no" title="statement not covered" >false;</span>var ac=<span class="cstat-no" title="statement not covered" >false;<span class="cstat-no" title="statement not covered" ></span>for(var V=<span class="cstat-no" title="statement not covered" >0,</span>U=<span class="cstat-no" title="statement not covered" >Z.length;</span>V&lt;U;++V){var ae=<span class="cstat-no" title="statement not covered" >Z[V];<span class="cstat-no" title="statement not covered" ></span>if(ae.ignoreCase){<span class="cstat-no" title="statement not covered" >ac=true}</span>else{<span class="cstat-no" title="statement not covered" >if(/[a-z]/i.test(ae.source.replace(/\\u[0-9a-f]{4}|\\x[0-9a-f]{2}|\\[^ux]/gi,""))){<span class="cstat-no" title="statement not covered" >S=true;<span class="cstat-no" title="statement not covered" >a</span>c=false;<span class="cstat-no" title="statement not covered" >b</span>reak}</span>}</span>}</span>v</span>ar Y=<span class="cstat-no" title="statement not covered" >{b:8,t:9,n:10,v:11,f:12,r:13};</span>function <span class="fstat-no" title="function not covered" >ab(</span>ah){var ag=<span class="cstat-no" title="statement not covered" >ah.charCodeAt(0);<span class="cstat-no" title="statement not covered" ></span>if(ag!==92){<span class="cstat-no" title="statement not covered" >return ag}</span>v</span>ar af=<span class="cstat-no" title="statement not covered" >ah.charAt(1);<span class="cstat-no" title="statement not covered" ></span>ag=Y[af];<span class="cstat-no" title="statement not covered" >i</span>f(ag){<span class="cstat-no" title="statement not covered" >return ag}</span>else{<span class="cstat-no" title="statement not covered" >if("0"&lt;=af&amp;&amp;af&lt;="7"){<span class="cstat-no" title="statement not covered" >return parseInt(ah.substring(1),8)}</span>else{<span class="cstat-no" title="statement not covered" >if(af==="u"||af==="x"){<span class="cstat-no" title="statement not covered" >return parseInt(ah.substring(2),16)}</span>else{<span class="cstat-no" title="statement not covered" >return ah.charCodeAt(1)}</span>}</span>}</span>}</span>function <span class="fstat-no" title="function not covered" >T(</span>af){<span class="cstat-no" title="statement not covered" >if(af&lt;32){<span class="cstat-no" title="statement not covered" >return(af&lt;16?"\\x0":"\\x")+af.toString(16)}</span>v</span>ar ag=<span class="cstat-no" title="statement not covered" >String.fromCharCode(af);<span class="cstat-no" title="statement not covered" ></span>if(ag==="\\"||ag==="-"||ag==="["||ag==="]"){<span class="cstat-no" title="statement not covered" >ag="\\"+ag}<span class="cstat-no" title="statement not covered" ></span>r</span>eturn ag}</span>function <span class="fstat-no" title="function not covered" >X(</span>am){var aq=<span class="cstat-no" title="statement not covered" >am.substring(1,am.length-1).match(new RegExp("\\\\u[0-9A-Fa-f]{4}|\\\\x[0-9A-Fa-f]{2}|\\\\[0-3][0-7]{0,2}|\\\\[0-7]{1,2}|\\\\[\\s\\S]|-|[^-\\\\]","g"));</span>var ak=<span class="cstat-no" title="statement not covered" >[];</span>var af=<span class="cstat-no" title="statement not covered" >[];</span>var ao=<span class="cstat-no" title="statement not covered" >aq[0]==="^";<span class="cstat-no" title="statement not covered" ></span>for(var ar=<span class="cstat-no" title="statement not covered" >ao?1:0,</span>aj=<span class="cstat-no" title="statement not covered" >aq.length;</span>ar&lt;aj;++ar){var ah=<span class="cstat-no" title="statement not covered" >aq[ar];<span class="cstat-no" title="statement not covered" ></span>if(/\\[bdsw]/i.test(ah)){<span class="cstat-no" title="statement not covered" >ak.push(ah)}</span>else{var ag=<span class="cstat-no" title="statement not covered" >ab(ah);</span>var al;<span class="cstat-no" title="statement not covered" >if(ar+2&lt;aj&amp;&amp;"-"===aq[ar+1]){<span class="cstat-no" title="statement not covered" >al=ab(aq[ar+2]);<span class="cstat-no" title="statement not covered" >a</span>r+=2}</span>else{<span class="cstat-no" title="statement not covered" >al=ag}<span class="cstat-no" title="statement not covered" ></span>a</span>f.push([ag,al]);<span class="cstat-no" title="statement not covered" >i</span>f(!(al&lt;65||ag&gt;122)){<span class="cstat-no" title="statement not covered" >if(!(al&lt;65||ag&gt;90)){<span class="cstat-no" title="statement not covered" >af.push([Math.max(65,ag)|32,Math.min(al,90)|32])}<span class="cstat-no" title="statement not covered" ></span>i</span>f(!(al&lt;97||ag&gt;122)){<span class="cstat-no" title="statement not covered" >af.push([Math.max(97,ag)&amp;~32,Math.min(al,122)&amp;~32])}</span>}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>a</span>f.sort(<span class="fstat-no" title="function not covered" >fu</span>nction(av,au){<span class="cstat-no" title="statement not covered" >return(av[0]-au[0])||(au[1]-av[1])}</span>);v</span>ar ai=<span class="cstat-no" title="statement not covered" >[];</span>var ap=<span class="cstat-no" title="statement not covered" >[NaN,NaN];<span class="cstat-no" title="statement not covered" ></span>for(var ar=<span class="cstat-no" title="statement not covered" >0;</span>ar&lt;af.length;++ar){var at=<span class="cstat-no" title="statement not covered" >af[ar];<span class="cstat-no" title="statement not covered" ></span>if(at[0]&lt;=ap[1]+1){<span class="cstat-no" title="statement not covered" >ap[1]=Math.max(ap[1],at[1])}</span>else{<span class="cstat-no" title="statement not covered" >ai.push(ap=at)}</span>}</span>v</span>ar an=<span class="cstat-no" title="statement not covered" >["["];<span class="cstat-no" title="statement not covered" ></span>if(ao){<span class="cstat-no" title="statement not covered" >an.push("^")}<span class="cstat-no" title="statement not covered" ></span>a</span>n.push.apply(an,ak);<span class="cstat-no" title="statement not covered" >f</span>or(var ar=<span class="cstat-no" title="statement not covered" >0;</span>ar&lt;ai.length;++ar){var at=<span class="cstat-no" title="statement not covered" >ai[ar];<span class="cstat-no" title="statement not covered" ></span>an.push(T(at[0]));<span class="cstat-no" title="statement not covered" >i</span>f(at[1]&gt;at[0]){<span class="cstat-no" title="statement not covered" >if(at[1]+1&gt;at[0]){<span class="cstat-no" title="statement not covered" >an.push("-")}<span class="cstat-no" title="statement not covered" ></span>a</span>n.push(T(at[1]))}</span>}<span class="cstat-no" title="statement not covered" ></span>a</span>n.push("]");<span class="cstat-no" title="statement not covered" >r</span>eturn an.join("")}</span>function <span class="fstat-no" title="function not covered" >W(</span>al){var aj=<span class="cstat-no" title="statement not covered" >al.source.match(new RegExp("(?:\\[(?:[^\\x5C\\x5D]|\\\\[\\s\\S])*\\]|\\\\u[A-Fa-f0-9]{4}|\\\\x[A-Fa-f0-9]{2}|\\\\[0-9]+|\\\\[^ux0-9]|\\(\\?[:!=]|[\\(\\)\\^]|[^\\x5B\\x5C\\(\\)\\^]+)","g"));</span>var ah=<span class="cstat-no" title="statement not covered" >aj.length;</span>var an=<span class="cstat-no" title="statement not covered" >[];<span class="cstat-no" title="statement not covered" ></span>for(var ak=<span class="cstat-no" title="statement not covered" >0,</span>am=<span class="cstat-no" title="statement not covered" >0;</span>ak&lt;ah;++ak){var ag=<span class="cstat-no" title="statement not covered" >aj[ak];<span class="cstat-no" title="statement not covered" ></span>if(ag==="("){<span class="cstat-no" title="statement not covered" >++am}</span>else{<span class="cstat-no" title="statement not covered" >if("\\"===ag.charAt(0)){var af=<span class="cstat-no" title="statement not covered" >+ag.substring(1);<span class="cstat-no" title="statement not covered" ></span>if(af&amp;&amp;af&lt;=am){<span class="cstat-no" title="statement not covered" >an[af]=-1}</span>}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>f</span>or(var ak=<span class="cstat-no" title="statement not covered" >1;</span>ak&lt;an.length;++ak){<span class="cstat-no" title="statement not covered" >if(-1===an[ak]){<span class="cstat-no" title="statement not covered" >an[ak]=++ad}</span>}<span class="cstat-no" title="statement not covered" ></span>f</span>or(var ak=<span class="cstat-no" title="statement not covered" >0,</span>am=<span class="cstat-no" title="statement not covered" >0;</span>ak&lt;ah;++ak){var ag=<span class="cstat-no" title="statement not covered" >aj[ak];<span class="cstat-no" title="statement not covered" ></span>if(ag==="("){<span class="cstat-no" title="statement not covered" >++am;<span class="cstat-no" title="statement not covered" >i</span>f(an[am]===undefined){<span class="cstat-no" title="statement not covered" >aj[ak]="(?:"}</span>}</span>else{<span class="cstat-no" title="statement not covered" >if("\\"===ag.charAt(0)){var af=<span class="cstat-no" title="statement not covered" >+ag.substring(1);<span class="cstat-no" title="statement not covered" ></span>if(af&amp;&amp;af&lt;=am){<span class="cstat-no" title="statement not covered" >aj[ak]="\\"+an[am]}</span>}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>f</span>or(var ak=<span class="cstat-no" title="statement not covered" >0,</span>am=<span class="cstat-no" title="statement not covered" >0;</span>ak&lt;ah;++ak){<span class="cstat-no" title="statement not covered" >if("^"===aj[ak]&amp;&amp;"^"!==aj[ak+1]){<span class="cstat-no" title="statement not covered" >aj[ak]=""}</span>}<span class="cstat-no" title="statement not covered" ></span>i</span>f(al.ignoreCase&amp;&amp;S){<span class="cstat-no" title="statement not covered" >for(var ak=<span class="cstat-no" title="statement not covered" >0;</span>ak&lt;ah;++ak){var ag=<span class="cstat-no" title="statement not covered" >aj[ak];</span>var ai=<span class="cstat-no" title="statement not covered" >ag.charAt(0);<span class="cstat-no" title="statement not covered" ></span>if(ag.length&gt;=2&amp;&amp;ai==="["){<span class="cstat-no" title="statement not covered" >aj[ak]=X(ag)}</span>else{<span class="cstat-no" title="statement not covered" >if(ai!=="\\"){<span class="cstat-no" title="statement not covered" >aj[ak]=ag.replace(/[a-zA-Z]/g,<span class="fstat-no" title="function not covered" >fu</span>nction(ao){var ap=<span class="cstat-no" title="statement not covered" >ao.charCodeAt(0);<span class="cstat-no" title="statement not covered" ></span>return"["+String.fromCharCode(ap&amp;~32,ap|32)+"]"}</span>)}</span>}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>r</span>eturn aj.join("")}</span>var aa=<span class="cstat-no" title="statement not covered" >[];<span class="cstat-no" title="statement not covered" ></span>for(var V=<span class="cstat-no" title="statement not covered" >0,</span>U=<span class="cstat-no" title="statement not covered" >Z.length;</span>V&lt;U;++V){var ae=<span class="cstat-no" title="statement not covered" >Z[V];<span class="cstat-no" title="statement not covered" ></span>if(ae.global||ae.multiline){<span class="cstat-no" title="statement not covered" >throw new Error(""+ae)}<span class="cstat-no" title="statement not covered" ></span>a</span>a.push("(?:"+W(ae)+")")}<span class="cstat-no" title="statement not covered" ></span>r</span>eturn new RegExp(aa.join("|"),ac?"gi":"g")}</span>function <span class="fstat-no" title="function not covered" >a(</span>V){var U=<span class="cstat-no" title="statement not covered" >/(?:^|\s)nocode(?:\s|$)/;</span>var X=<span class="cstat-no" title="statement not covered" >[];</span>var T=<span class="cstat-no" title="statement not covered" >0;</span>var Z=<span class="cstat-no" title="statement not covered" >[];</span>var W=<span class="cstat-no" title="statement not covered" >0;</span>var S;<span class="cstat-no" title="statement not covered" >if(V.currentStyle){<span class="cstat-no" title="statement not covered" >S=V.currentStyle.whiteSpace}</span>else{<span class="cstat-no" title="statement not covered" >if(window.getComputedStyle){<span class="cstat-no" title="statement not covered" >S=document.defaultView.getComputedStyle(V,null).getPropertyValue("white-space")}</span>}</span>v</span>ar Y=<span class="cstat-no" title="statement not covered" >S&amp;&amp;"pre"===S.substring(0,3);</span>function <span class="fstat-no" title="function not covered" >aa(</span>ab){<span class="cstat-no" title="statement not covered" >switch(ab.nodeType){case 1:<span class="cstat-no" title="statement not covered" >if(U.test(ab.className)){<span class="cstat-no" title="statement not covered" >return}<span class="cstat-no" title="statement not covered" ></span>f</span>or(var ae=<span class="cstat-no" title="statement not covered" >ab.firstChild;</span>ae;ae=ae.nextSibling){<span class="cstat-no" title="statement not covered" >aa(ae)}</span>v</span>ar ad=<span class="cstat-no" title="statement not covered" >ab.nodeName;<span class="cstat-no" title="statement not covered" ></span>if("BR"===ad||"LI"===ad){<span class="cstat-no" title="statement not covered" >X[W]="\n";<span class="cstat-no" title="statement not covered" >Z</span>[W&lt;&lt;1]=T++;<span class="cstat-no" title="statement not covered" >Z</span>[(W++&lt;&lt;1)|1]=ab}<span class="cstat-no" title="statement not covered" ></span>b</span>reak;c</span>ase 3:case 4:var ac=<span class="cstat-no" title="statement not covered" >ab.nodeValue;<span class="cstat-no" title="statement not covered" ></span>if(ac.length){<span class="cstat-no" title="statement not covered" >if(!Y){<span class="cstat-no" title="statement not covered" >ac=ac.replace(/[ \t\r\n]+/g," ")}</span>else{<span class="cstat-no" title="statement not covered" >ac=ac.replace(/\r\n?/g,"\n")}<span class="cstat-no" title="statement not covered" ></span>X</span>[W]=ac;<span class="cstat-no" title="statement not covered" >Z</span>[W&lt;&lt;1]=T;<span class="cstat-no" title="statement not covered" >T</span>+=ac.length;<span class="cstat-no" title="statement not covered" >Z</span>[(W++&lt;&lt;1)|1]=ab}<span class="cstat-no" title="statement not covered" ></span>b</span>reak}</span>}<span class="cstat-no" title="statement not covered" ></span>aa(V);<span class="cstat-no" title="statement not covered" >r</span>eturn{sourceCode:X.join("").replace(/\n$/,""),spans:Z}}</span>function <span class="fstat-no" title="function not covered" >B(</span>S,U,W,T){<span class="cstat-no" title="statement not covered" >if(!U){<span class="cstat-no" title="statement not covered" >return}</span>v</span>ar V=<span class="cstat-no" title="statement not covered" >{sourceCode:U,basePos:S};<span class="cstat-no" title="statement not covered" ></span>W(V);<span class="cstat-no" title="statement not covered" >T</span>.push.apply(T,V.decorations)}</span>var v=<span class="cstat-no" title="statement not covered" >/\S/;</span>function <span class="fstat-no" title="function not covered" >o(</span>S){var V=<span class="cstat-no" title="statement not covered" >undefined;<span class="cstat-no" title="statement not covered" ></span>for(var U=<span class="cstat-no" title="statement not covered" >S.firstChild;</span>U;U=U.nextSibling){var T=<span class="cstat-no" title="statement not covered" >U.nodeType;<span class="cstat-no" title="statement not covered" ></span>V=(T===1)?(V?S:U):(T===3)?(v.test(U.nodeValue)?S:V):V}<span class="cstat-no" title="statement not covered" ></span>r</span>eturn V===S?undefined:V}</span>function <span class="fstat-no" title="function not covered" >g(</span>U,T){var S=<span class="cstat-no" title="statement not covered" >{};</span>var V;<span class="cstat-no" title="statement not covered" >(<span class="fstat-no" title="function not covered" >fu</span>nction(){var ad=<span class="cstat-no" title="statement not covered" >U.concat(T);</span>var ah=<span class="cstat-no" title="statement not covered" >[];</span>var ag=<span class="cstat-no" title="statement not covered" >{};<span class="cstat-no" title="statement not covered" ></span>for(var ab=<span class="cstat-no" title="statement not covered" >0,</span>Z=<span class="cstat-no" title="statement not covered" >ad.length;</span>ab&lt;Z;++ab){var Y=<span class="cstat-no" title="statement not covered" >ad[ab];</span>var ac=<span class="cstat-no" title="statement not covered" >Y[3];<span class="cstat-no" title="statement not covered" ></span>if(ac){<span class="cstat-no" title="statement not covered" >for(var ae=<span class="cstat-no" title="statement not covered" >ac.length;</span>--ae&gt;=0;){<span class="cstat-no" title="statement not covered" >S[ac.charAt(ae)]=Y}</span>}</span>v</span>ar af=<span class="cstat-no" title="statement not covered" >Y[1];</span>var aa=<span class="cstat-no" title="statement not covered" >""+af;<span class="cstat-no" title="statement not covered" ></span>if(!ag.hasOwnProperty(aa)){<span class="cstat-no" title="statement not covered" >ah.push(af);<span class="cstat-no" title="statement not covered" >a</span>g[aa]=null}</span>}<span class="cstat-no" title="statement not covered" ></span>a</span>h.push(/[\0-\uffff]/);<span class="cstat-no" title="statement not covered" >V</span>=k(ah)}</span>)();v</span>ar X=<span class="cstat-no" title="statement not covered" >T.length;</span>var W=<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >fu</span>nction(ah){var Z=<span class="cstat-no" title="statement not covered" >ah.sourceCode,</span>Y=<span class="cstat-no" title="statement not covered" >ah.basePos;</span>var ad=<span class="cstat-no" title="statement not covered" >[Y,F];</span>var af=<span class="cstat-no" title="statement not covered" >0;</span>var an=<span class="cstat-no" title="statement not covered" >Z.match(V)||[];</span>var aj=<span class="cstat-no" title="statement not covered" >{};<span class="cstat-no" title="statement not covered" ></span>for(var ae=<span class="cstat-no" title="statement not covered" >0,</span>aq=<span class="cstat-no" title="statement not covered" >an.length;</span>ae&lt;aq;++ae){var ag=<span class="cstat-no" title="statement not covered" >an[ae];</span>var ap=<span class="cstat-no" title="statement not covered" >aj[ag];</span>var ai=<span class="cstat-no" title="statement not covered" >void 0;</span>var am;<span class="cstat-no" title="statement not covered" >if(typeof ap==="string"){<span class="cstat-no" title="statement not covered" >am=false}</span>else{var aa=<span class="cstat-no" title="statement not covered" >S[ag.charAt(0)];<span class="cstat-no" title="statement not covered" ></span>if(aa){<span class="cstat-no" title="statement not covered" >ai=ag.match(aa[1]);<span class="cstat-no" title="statement not covered" >a</span>p=aa[0]}</span>else{<span class="cstat-no" title="statement not covered" >for(var ao=<span class="cstat-no" title="statement not covered" >0;</span>ao&lt;X;++ao){<span class="cstat-no" title="statement not covered" >aa=T[ao];<span class="cstat-no" title="statement not covered" >a</span>i=ag.match(aa[1]);<span class="cstat-no" title="statement not covered" >i</span>f(ai){<span class="cstat-no" title="statement not covered" >ap=aa[0];<span class="cstat-no" title="statement not covered" >b</span>reak}</span>}<span class="cstat-no" title="statement not covered" ></span>i</span>f(!ai){<span class="cstat-no" title="statement not covered" >ap=F}</span>}<span class="cstat-no" title="statement not covered" ></span>a</span>m=ap.length&gt;=5&amp;&amp;"lang-"===ap.substring(0,5);<span class="cstat-no" title="statement not covered" >i</span>f(am&amp;&amp;!(ai&amp;&amp;typeof ai[1]==="string")){<span class="cstat-no" title="statement not covered" >am=false;<span class="cstat-no" title="statement not covered" >a</span>p=J}<span class="cstat-no" title="statement not covered" ></span>i</span>f(!am){<span class="cstat-no" title="statement not covered" >aj[ag]=ap}</span>}</span>v</span>ar ab=<span class="cstat-no" title="statement not covered" >af;<span class="cstat-no" title="statement not covered" ></span>af+=ag.length;<span class="cstat-no" title="statement not covered" >i</span>f(!am){<span class="cstat-no" title="statement not covered" >ad.push(Y+ab,ap)}</span>else{var al=<span class="cstat-no" title="statement not covered" >ai[1];</span>var ak=<span class="cstat-no" title="statement not covered" >ag.indexOf(al);</span>var ac=<span class="cstat-no" title="statement not covered" >ak+al.length;<span class="cstat-no" title="statement not covered" ></span>if(ai[2]){<span class="cstat-no" title="statement not covered" >ac=ag.length-ai[2].length;<span class="cstat-no" title="statement not covered" >a</span>k=ac-al.length}</span>v</span>ar ar=<span class="cstat-no" title="statement not covered" >ap.substring(5);<span class="cstat-no" title="statement not covered" ></span>B(Y+ab,ag.substring(0,ak),W,ad);<span class="cstat-no" title="statement not covered" >B</span>(Y+ab+ak,al,q(ar,al),ad);<span class="cstat-no" title="statement not covered" >B</span>(Y+ab+ac,ag.substring(ac),W,ad)}</span>}<span class="cstat-no" title="statement not covered" ></span>a</span>h.decorations=ad}</span>;<span class="cstat-no" title="statement not covered" ></span>return W}</span>function <span class="fstat-no" title="function not covered" >i(</span>T){var W=<span class="cstat-no" title="statement not covered" >[],</span>S=<span class="cstat-no" title="statement not covered" >[];<span class="cstat-no" title="statement not covered" ></span>if(T.tripleQuotedStrings){<span class="cstat-no" title="statement not covered" >W.push([C,/^(?:\'\'\'(?:[^\'\\]|\\[\s\S]|\'{1,2}(?=[^\']))*(?:\'\'\'|$)|\"\"\"(?:[^\"\\]|\\[\s\S]|\"{1,2}(?=[^\"]))*(?:\"\"\"|$)|\'(?:[^\\\']|\\[\s\S])*(?:\'|$)|\"(?:[^\\\"]|\\[\s\S])*(?:\"|$))/,null,"'\""])}</span>else{<span class="cstat-no" title="statement not covered" >if(T.multiLineStrings){<span class="cstat-no" title="statement not covered" >W.push([C,/^(?:\'(?:[^\\\']|\\[\s\S])*(?:\'|$)|\"(?:[^\\\"]|\\[\s\S])*(?:\"|$)|\`(?:[^\\\`]|\\[\s\S])*(?:\`|$))/,null,"'\"`"])}</span>else{<span class="cstat-no" title="statement not covered" >W.push([C,/^(?:\'(?:[^\\\'\r\n]|\\.)*(?:\'|$)|\"(?:[^\\\"\r\n]|\\.)*(?:\"|$))/,null,"\"'"])}</span>}<span class="cstat-no" title="statement not covered" ></span>i</span>f(T.verbatimStrings){<span class="cstat-no" title="statement not covered" >S.push([C,/^@\"(?:[^\"]|\"\")*(?:\"|$)/,null])}</span>v</span>ar Y=<span class="cstat-no" title="statement not covered" >T.hashComments;<span class="cstat-no" title="statement not covered" ></span>if(Y){<span class="cstat-no" title="statement not covered" >if(T.cStyleComments){<span class="cstat-no" title="statement not covered" >if(Y&gt;1){<span class="cstat-no" title="statement not covered" >W.push([j,/^#(?:##(?:[^#]|#(?!##))*(?:###|$)|.*)/,null,"#"])}</span>else{<span class="cstat-no" title="statement not covered" >W.push([j,/^#(?:(?:define|elif|else|endif|error|ifdef|include|ifndef|line|pragma|undef|warning)\b|[^\r\n]*)/,null,"#"])}<span class="cstat-no" title="statement not covered" ></span>S</span>.push([C,/^&lt;(?:(?:(?:\.\.\/)*|\/?)(?:[\w-]+(?:\/[\w-]+)+)?[\w-]+\.h|[a-z]\w*)&gt;/,null])}</span>else{<span class="cstat-no" title="statement not covered" >W.push([j,/^#[^\r\n]*/,null,"#"])}</span>}<span class="cstat-no" title="statement not covered" ></span>i</span>f(T.cStyleComments){<span class="cstat-no" title="statement not covered" >S.push([j,/^\/\/[^\r\n]*/,null]);<span class="cstat-no" title="statement not covered" >S</span>.push([j,/^\/\*[\s\S]*?(?:\*\/|$)/,null])}<span class="cstat-no" title="statement not covered" ></span>i</span>f(T.regexLiterals){var X=(<span class="cstat-no" title="statement not covered" >"/(?=[^/*])(?:[^/\\x5B\\x5C]|\\x5C[\\s\\S]|\\x5B(?:[^\\x5C\\x5D]|\\x5C[\\s\\S])*(?:\\x5D|$))+/")</span>;<span class="cstat-no" title="statement not covered" >S.push(["lang-regex",new RegExp("^"+M+"("+X+")")])}</span>v</span>ar V=<span class="cstat-no" title="statement not covered" >T.types;<span class="cstat-no" title="statement not covered" ></span>if(V){<span class="cstat-no" title="statement not covered" >S.push([O,V])}</span>v</span>ar U=<span class="cstat-no" title="statement not covered" >(""+T.keywords).replace(/^ | $/g,"");<span class="cstat-no" title="statement not covered" ></span>if(U.length){<span class="cstat-no" title="statement not covered" >S.push([z,new RegExp("^(?:"+U.replace(/[\s,]+/g,"|")+")\\b"),null])}<span class="cstat-no" title="statement not covered" ></span>W</span>.push([F,/^\s+/,null," \r\n\t\xA0"]);<span class="cstat-no" title="statement not covered" >S</span>.push([G,/^@[a-z_$][a-z_$@0-9]*/i,null],[O,/^(?:[@_]?[A-Z]+[a-z][A-Za-z_$@0-9]*|\w+_t\b)/,null],[F,/^[a-z_$][a-z_$@0-9]*/i,null],[G,new RegExp("^(?:0x[a-f0-9]+|(?:\\d(?:_\\d+)*\\d*(?:\\.\\d*)?|\\.\\d\\+)(?:e[+\\-]?\\d+)?)[a-z]*","i"),null,"0123456789"],[F,/^\\[\s\S]?/,null],[L,/^.[^\s\w\.$@\'\"\`\/\#\\]*/,null]);<span class="cstat-no" title="statement not covered" >r</span>eturn g(W,S)}</span>var K=<span class="cstat-no" title="statement not covered" >i({keywords:A,hashComments:true,cStyleComments:true,multiLineStrings:true,regexLiterals:true});</span>function <span class="fstat-no" title="function not covered" >Q(</span>V,ag){var U=<span class="cstat-no" title="statement not covered" >/(?:^|\s)nocode(?:\s|$)/;</span>var ab=<span class="cstat-no" title="statement not covered" >/\r\n?|\n/;</span>var ac=<span class="cstat-no" title="statement not covered" >V.ownerDocument;</span>var S;<span class="cstat-no" title="statement not covered" >if(V.currentStyle){<span class="cstat-no" title="statement not covered" >S=V.currentStyle.whiteSpace}</span>else{<span class="cstat-no" title="statement not covered" >if(window.getComputedStyle){<span class="cstat-no" title="statement not covered" >S=ac.defaultView.getComputedStyle(V,null).getPropertyValue("white-space")}</span>}</span>v</span>ar Z=<span class="cstat-no" title="statement not covered" >S&amp;&amp;"pre"===S.substring(0,3);</span>var af=<span class="cstat-no" title="statement not covered" >ac.createElement("LI");<span class="cstat-no" title="statement not covered" ></span>while(V.firstChild){<span class="cstat-no" title="statement not covered" >af.appendChild(V.firstChild)}</span>v</span>ar W=<span class="cstat-no" title="statement not covered" >[af];</span>function <span class="fstat-no" title="function not covered" >ae(</span>al){<span class="cstat-no" title="statement not covered" >switch(al.nodeType){case 1:<span class="cstat-no" title="statement not covered" >if(U.test(al.className)){<span class="cstat-no" title="statement not covered" >break}<span class="cstat-no" title="statement not covered" ></span>i</span>f("BR"===al.nodeName){<span class="cstat-no" title="statement not covered" >ad(al);<span class="cstat-no" title="statement not covered" >i</span>f(al.parentNode){<span class="cstat-no" title="statement not covered" >al.parentNode.removeChild(al)}</span>}</span>else{<span class="cstat-no" title="statement not covered" >for(var an=<span class="cstat-no" title="statement not covered" >al.firstChild;</span>an;an=an.nextSibling){<span class="cstat-no" title="statement not covered" >ae(an)}</span>}<span class="cstat-no" title="statement not covered" ></span>b</span>reak;c</span>ase 3:case 4:<span class="cstat-no" title="statement not covered" >if(Z){var am=<span class="cstat-no" title="statement not covered" >al.nodeValue;</span>var aj=<span class="cstat-no" title="statement not covered" >am.match(ab);<span class="cstat-no" title="statement not covered" ></span>if(aj){var ai=<span class="cstat-no" title="statement not covered" >am.substring(0,aj.index);<span class="cstat-no" title="statement not covered" ></span>al.nodeValue=ai;v</span>ar ah=<span class="cstat-no" title="statement not covered" >am.substring(aj.index+aj[0].length);<span class="cstat-no" title="statement not covered" ></span>if(ah){var ak=<span class="cstat-no" title="statement not covered" >al.parentNode;<span class="cstat-no" title="statement not covered" ></span>ak.insertBefore(ac.createTextNode(ah),al.nextSibling)}<span class="cstat-no" title="statement not covered" ></span>a</span>d(al);<span class="cstat-no" title="statement not covered" >i</span>f(!ai){<span class="cstat-no" title="statement not covered" >al.parentNode.removeChild(al)}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>b</span>reak}</span>}</span>function <span class="fstat-no" title="function not covered" >ad(</span>ak){<span class="cstat-no" title="statement not covered" >while(!ak.nextSibling){<span class="cstat-no" title="statement not covered" >ak=ak.parentNode;<span class="cstat-no" title="statement not covered" >i</span>f(!ak){<span class="cstat-no" title="statement not covered" >return}</span>}</span>f</span>unction <span class="fstat-no" title="function not covered" >ai(</span>al,ar){var aq=<span class="cstat-no" title="statement not covered" >ar?al.cloneNode(false):al;</span>var ao=<span class="cstat-no" title="statement not covered" >al.parentNode;<span class="cstat-no" title="statement not covered" ></span>if(ao){var ap=<span class="cstat-no" title="statement not covered" >ai(ao,1);</span>var an=<span class="cstat-no" title="statement not covered" >al.nextSibling;<span class="cstat-no" title="statement not covered" ></span>ap.appendChild(aq);<span class="cstat-no" title="statement not covered" >f</span>or(var am=<span class="cstat-no" title="statement not covered" >an;</span>am;am=an){<span class="cstat-no" title="statement not covered" >an=am.nextSibling;<span class="cstat-no" title="statement not covered" >a</span>p.appendChild(am)}</span>}<span class="cstat-no" title="statement not covered" ></span>r</span>eturn aq}</span>var ah=<span class="cstat-no" title="statement not covered" >ai(ak.nextSibling,0);<span class="cstat-no" title="statement not covered" ></span>for(var aj;(aj=ah.parentNode)&amp;&amp;aj.nodeType===1;){<span class="cstat-no" title="statement not covered" >ah=aj}<span class="cstat-no" title="statement not covered" ></span>W</span>.push(ah)}<span class="cstat-no" title="statement not covered" ></span>for(var Y=<span class="cstat-no" title="statement not covered" >0;</span>Y&lt;W.length;++Y){<span class="cstat-no" title="statement not covered" >ae(W[Y])}<span class="cstat-no" title="statement not covered" ></span>i</span>f(ag===(ag|0)){<span class="cstat-no" title="statement not covered" >W[0].setAttribute("value",ag)}</span>v</span>ar aa=<span class="cstat-no" title="statement not covered" >ac.createElement("OL");<span class="cstat-no" title="statement not covered" ></span>aa.className="linenums";v</span>ar X=<span class="cstat-no" title="statement not covered" >Math.max(0,((ag-1))|0)||0;<span class="cstat-no" title="statement not covered" ></span>for(var Y=<span class="cstat-no" title="statement not covered" >0,</span>T=<span class="cstat-no" title="statement not covered" >W.length;</span>Y&lt;T;++Y){<span class="cstat-no" title="statement not covered" >af=W[Y];<span class="cstat-no" title="statement not covered" >a</span>f.className="L"+((Y+X)%10);<span class="cstat-no" title="statement not covered" >i</span>f(!af.firstChild){<span class="cstat-no" title="statement not covered" >af.appendChild(ac.createTextNode("\xA0"))}<span class="cstat-no" title="statement not covered" ></span>a</span>a.appendChild(af)}<span class="cstat-no" title="statement not covered" ></span>V</span>.appendChild(aa)}</span>function <span class="fstat-no" title="function not covered" >D(</span>ac){var aj=<span class="cstat-no" title="statement not covered" >/\bMSIE\b/.test(navigator.userAgent);</span>var am=<span class="cstat-no" title="statement not covered" >/\n/g;</span>var al=<span class="cstat-no" title="statement not covered" >ac.sourceCode;</span>var an=<span class="cstat-no" title="statement not covered" >al.length;</span>var V=<span class="cstat-no" title="statement not covered" >0;</span>var aa=<span class="cstat-no" title="statement not covered" >ac.spans;</span>var T=<span class="cstat-no" title="statement not covered" >aa.length;</span>var ah=<span class="cstat-no" title="statement not covered" >0;</span>var X=<span class="cstat-no" title="statement not covered" >ac.decorations;</span>var Y=<span class="cstat-no" title="statement not covered" >X.length;</span>var Z=<span class="cstat-no" title="statement not covered" >0;<span class="cstat-no" title="statement not covered" ></span>X[Y]=an;v</span>ar ar,aq;<span class="cstat-no" title="statement not covered" >for(aq=ar=0;aq&lt;Y;){<span class="cstat-no" title="statement not covered" >if(X[aq]!==X[aq+2]){<span class="cstat-no" title="statement not covered" >X[ar++]=X[aq++];<span class="cstat-no" title="statement not covered" >X</span>[ar++]=X[aq++]}</span>else{<span class="cstat-no" title="statement not covered" >aq+=2}</span>}<span class="cstat-no" title="statement not covered" ></span>Y</span>=ar;<span class="cstat-no" title="statement not covered" >f</span>or(aq=ar=0;aq&lt;Y;){var at=<span class="cstat-no" title="statement not covered" >X[aq];</span>var ab=<span class="cstat-no" title="statement not covered" >X[aq+1];</span>var W=<span class="cstat-no" title="statement not covered" >aq+2;<span class="cstat-no" title="statement not covered" ></span>while(W+2&lt;=Y&amp;&amp;X[W+1]===ab){<span class="cstat-no" title="statement not covered" >W+=2}<span class="cstat-no" title="statement not covered" ></span>X</span>[ar++]=at;<span class="cstat-no" title="statement not covered" >X</span>[ar++]=ab;<span class="cstat-no" title="statement not covered" >a</span>q=W}<span class="cstat-no" title="statement not covered" ></span>Y</span>=X.length=ar;v</span>ar ae=<span class="cstat-no" title="statement not covered" >null;<span class="cstat-no" title="statement not covered" ></span>while(ah&lt;T){var af=<span class="cstat-no" title="statement not covered" >aa[ah];</span>var S=<span class="cstat-no" title="statement not covered" >aa[ah+2]||an;</span>var ag=<span class="cstat-no" title="statement not covered" >X[Z];</span>var ap=<span class="cstat-no" title="statement not covered" >X[Z+2]||an;</span>var W=<span class="cstat-no" title="statement not covered" >Math.min(S,ap);</span>var ak=<span class="cstat-no" title="statement not covered" >aa[ah+1];</span>var U;<span class="cstat-no" title="statement not covered" >if(ak.nodeType!==1&amp;&amp;(U=al.substring(V,W))){<span class="cstat-no" title="statement not covered" >if(aj){<span class="cstat-no" title="statement not covered" >U=U.replace(am,"\r")}<span class="cstat-no" title="statement not covered" ></span>a</span>k.nodeValue=U;v</span>ar ai=<span class="cstat-no" title="statement not covered" >ak.ownerDocument;</span>var ao=<span class="cstat-no" title="statement not covered" >ai.createElement("SPAN");<span class="cstat-no" title="statement not covered" ></span>ao.className=X[Z+1];v</span>ar ad=<span class="cstat-no" title="statement not covered" >ak.parentNode;<span class="cstat-no" title="statement not covered" ></span>ad.replaceChild(ao,ak);<span class="cstat-no" title="statement not covered" >a</span>o.appendChild(ak);<span class="cstat-no" title="statement not covered" >i</span>f(V&lt;S){<span class="cstat-no" title="statement not covered" >aa[ah+1]=ak=ai.createTextNode(al.substring(W,S));<span class="cstat-no" title="statement not covered" >a</span>d.insertBefore(ak,ao.nextSibling)}</span>}<span class="cstat-no" title="statement not covered" ></span>V</span>=W;<span class="cstat-no" title="statement not covered" >i</span>f(V&gt;=S){<span class="cstat-no" title="statement not covered" >ah+=2}<span class="cstat-no" title="statement not covered" ></span>i</span>f(V&gt;=ap){<span class="cstat-no" title="statement not covered" >Z+=2}</span>}</span>}</span>var t=<span class="cstat-no" title="statement not covered" >{};</span>function <span class="fstat-no" title="function not covered" >c(</span>U,V){<span class="cstat-no" title="statement not covered" >for(var S=<span class="cstat-no" title="statement not covered" >V.length;</span>--S&gt;=0;){var T=<span class="cstat-no" title="statement not covered" >V[S];<span class="cstat-no" title="statement not covered" ></span>if(!t.hasOwnProperty(T)){<span class="cstat-no" title="statement not covered" >t[T]=U}</span>else{<span class="cstat-no" title="statement not covered" >if(window.console){<span class="cstat-no" title="statement not covered" >console.warn("cannot override language handler %s",T)}</span>}</span>}</span>}</span>function <span class="fstat-no" title="function not covered" >q(</span>T,S){<span class="cstat-no" title="statement not covered" >if(!(T&amp;&amp;t.hasOwnProperty(T))){<span class="cstat-no" title="statement not covered" >T=/^\s*&lt;/.test(S)?"default-markup":"default-code"}<span class="cstat-no" title="statement not covered" ></span>r</span>eturn t[T]}<span class="cstat-no" title="statement not covered" ></span>c(K,["default-code"]);<span class="cstat-no" title="statement not covered" >c</span>(g([],[[F,/^[^&lt;?]+/],[E,/^&lt;!\w[^&gt;]*(?:&gt;|$)/],[j,/^&lt;\!--[\s\S]*?(?:-\-&gt;|$)/],["lang-",/^&lt;\?([\s\S]+?)(?:\?&gt;|$)/],["lang-",/^&lt;%([\s\S]+?)(?:%&gt;|$)/],[L,/^(?:&lt;[%?]|[%?]&gt;)/],["lang-",/^&lt;xmp\b[^&gt;]*&gt;([\s\S]+?)&lt;\/xmp\b[^&gt;]*&gt;/i],["lang-js",/^&lt;script\b[^&gt;]*&gt;([\s\S]*?)(&lt;\/script\b[^&gt;]*&gt;)/i],["lang-css",/^&lt;style\b[^&gt;]*&gt;([\s\S]*?)(&lt;\/style\b[^&gt;]*&gt;)/i],["lang-in.tag",/^(&lt;\/?[a-z][^&lt;&gt;]*&gt;)/i]]),["default-markup","htm","html","mxml","xhtml","xml","xsl"]);<span class="cstat-no" title="statement not covered" >c</span>(g([[F,/^[\s]+/,null," \t\r\n"],[n,/^(?:\"[^\"]*\"?|\'[^\']*\'?)/,null,"\"'"]],[[m,/^^&lt;\/?[a-z](?:[\w.:-]*\w)?|\/?&gt;$/i],[P,/^(?!style[\s=]|on)[a-z](?:[\w:-]*\w)?/i],["lang-uq.val",/^=\s*([^&gt;\'\"\s]*(?:[^&gt;\'\"\s\/]|\/(?=\s)))/],[L,/^[=&lt;&gt;\/]+/],["lang-js",/^on\w+\s*=\s*\"([^\"]+)\"/i],["lang-js",/^on\w+\s*=\s*\'([^\']+)\'/i],["lang-js",/^on\w+\s*=\s*([^\"\'&gt;\s]+)/i],["lang-css",/^style\s*=\s*\"([^\"]+)\"/i],["lang-css",/^style\s*=\s*\'([^\']+)\'/i],["lang-css",/^style\s*=\s*([^\"\'&gt;\s]+)/i]]),["in.tag"]);<span class="cstat-no" title="statement not covered" >c</span>(g([],[[n,/^[\s\S]+/]]),["uq.val"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:l,hashComments:true,cStyleComments:true,types:e}),["c","cc","cpp","cxx","cyc","m"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:"null,true,false"}),["json"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:R,hashComments:true,cStyleComments:true,verbatimStrings:true,types:e}),["cs"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:x,cStyleComments:true}),["java"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:H,hashComments:true,multiLineStrings:true}),["bsh","csh","sh"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:I,hashComments:true,multiLineStrings:true,tripleQuotedStrings:true}),["cv","py"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:s,hashComments:true,multiLineStrings:true,regexLiterals:true}),["perl","pl","pm"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:f,hashComments:true,multiLineStrings:true,regexLiterals:true}),["rb"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:w,cStyleComments:true,regexLiterals:true}),["js"]);<span class="cstat-no" title="statement not covered" >c</span>(i({keywords:r,hashComments:3,cStyleComments:true,multilineStrings:true,tripleQuotedStrings:true,regexLiterals:true}),["coffee"]);<span class="cstat-no" title="statement not covered" >c</span>(g([],[[C,/^[\s\S]+/]]),["regex"]);f</span>unction <span class="fstat-no" title="function not covered" >d(</span>V){var U=<span class="cstat-no" title="statement not covered" >V.langExtension;<span class="cstat-no" title="statement not covered" ></span>try{var S=<span class="cstat-no" title="statement not covered" >a(V.sourceNode);</span>var T=<span class="cstat-no" title="statement not covered" >S.sourceCode;<span class="cstat-no" title="statement not covered" ></span>V.sourceCode=T;<span class="cstat-no" title="statement not covered" >V</span>.spans=S.spans;<span class="cstat-no" title="statement not covered" >V</span>.basePos=0;<span class="cstat-no" title="statement not covered" >q</span>(U,T)(V);<span class="cstat-no" title="statement not covered" >D</span>(V)}</span>catch(W){<span class="cstat-no" title="statement not covered" >if("console" in window){<span class="cstat-no" title="statement not covered" >console.log(W&amp;&amp;W.stack?W.stack:W)}</span>}</span>}</span>function <span class="fstat-no" title="function not covered" >y(</span>W,V,U){var S=<span class="cstat-no" title="statement not covered" >document.createElement("PRE");<span class="cstat-no" title="statement not covered" ></span>S.innerHTML=W;<span class="cstat-no" title="statement not covered" >i</span>f(U){<span class="cstat-no" title="statement not covered" >Q(S,U)}</span>v</span>ar T=<span class="cstat-no" title="statement not covered" >{langExtension:V,numberLines:U,sourceNode:S};<span class="cstat-no" title="statement not covered" ></span>d(T);<span class="cstat-no" title="statement not covered" >r</span>eturn S.innerHTML}</span>function <span class="fstat-no" title="function not covered" >b(</span>ad){function <span class="fstat-no" title="function not covered" >Y(</span>af){<span class="cstat-no" title="statement not covered" >return document.getElementsByTagName(af)}</span>var ac=<span class="cstat-no" title="statement not covered" >[Y("pre"),Y("code"),Y("xmp")];</span>var T=<span class="cstat-no" title="statement not covered" >[];<span class="cstat-no" title="statement not covered" ></span>for(var aa=<span class="cstat-no" title="statement not covered" >0;</span>aa&lt;ac.length;++aa){<span class="cstat-no" title="statement not covered" >for(var Z=<span class="cstat-no" title="statement not covered" >0,</span>V=<span class="cstat-no" title="statement not covered" >ac[aa].length;</span>Z&lt;V;++Z){<span class="cstat-no" title="statement not covered" >T.push(ac[aa][Z])}</span>}<span class="cstat-no" title="statement not covered" ></span>a</span>c=null;v</span>ar W=<span class="cstat-no" title="statement not covered" >Date;<span class="cstat-no" title="statement not covered" ></span>if(!W.now){<span class="cstat-no" title="statement not covered" >W={now:<span class="fstat-no" title="function not covered" >fu</span>nction(){<span class="cstat-no" title="statement not covered" >return +(new Date)}</span>}}</span>v</span>ar X=<span class="cstat-no" title="statement not covered" >0;</span>var S;var ab=<span class="cstat-no" title="statement not covered" >/\blang(?:uage)?-([\w.]+)(?!\S)/;</span>var ae=<span class="cstat-no" title="statement not covered" >/\bprettyprint\b/;</span>function <span class="fstat-no" title="function not covered" >U(</span>){var ag=(<span class="cstat-no" title="statement not covered" >window.PR_SHOULD_USE_CONTINUATION?W.now()+250:Infinity)</span>;<span class="cstat-no" title="statement not covered" >for(;X&lt;T.length&amp;&amp;W.now()&lt;ag;X++){var aj=<span class="cstat-no" title="statement not covered" >T[X];</span>var ai=<span class="cstat-no" title="statement not covered" >aj.className;<span class="cstat-no" title="statement not covered" ></span>if(ai.indexOf("prettyprint")&gt;=0){var ah=<span class="cstat-no" title="statement not covered" >ai.match(ab);</span>var am;<span class="cstat-no" title="statement not covered" >if(!ah&amp;&amp;(am=o(aj))&amp;&amp;"CODE"===am.tagName){<span class="cstat-no" title="statement not covered" >ah=am.className.match(ab)}<span class="cstat-no" title="statement not covered" ></span>i</span>f(ah){<span class="cstat-no" title="statement not covered" >ah=ah[1]}</span>v</span>ar al=<span class="cstat-no" title="statement not covered" >false;<span class="cstat-no" title="statement not covered" ></span>for(var ak=<span class="cstat-no" title="statement not covered" >aj.parentNode;</span>ak;ak=ak.parentNode){<span class="cstat-no" title="statement not covered" >if((ak.tagName==="pre"||ak.tagName==="code"||ak.tagName==="xmp")&amp;&amp;ak.className&amp;&amp;ak.className.indexOf("prettyprint")&gt;=0){<span class="cstat-no" title="statement not covered" >al=true;<span class="cstat-no" title="statement not covered" >b</span>reak}</span>}<span class="cstat-no" title="statement not covered" ></span>i</span>f(!al){var af=<span class="cstat-no" title="statement not covered" >aj.className.match(/\blinenums\b(?::(\d+))?/);<span class="cstat-no" title="statement not covered" ></span>af=af?af[1]&amp;&amp;af[1].length?+af[1]:true:false;<span class="cstat-no" title="statement not covered" >i</span>f(af){<span class="cstat-no" title="statement not covered" >Q(aj,af)}<span class="cstat-no" title="statement not covered" ></span>S</span>={langExtension:ah,sourceNode:aj,numberLines:af};<span class="cstat-no" title="statement not covered" >d</span>(S)}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>i</span>f(X&lt;T.length){<span class="cstat-no" title="statement not covered" >setTimeout(U,250)}</span>else{<span class="cstat-no" title="statement not covered" >if(ad){<span class="cstat-no" title="statement not covered" >ad()}</span>}</span>}<span class="cstat-no" title="statement not covered" ></span>U()}<span class="cstat-no" title="statement not covered" ></span>window.prettyPrintOne=y;<span class="cstat-no" title="statement not covered" >w</span>indow.prettyPrint=b;<span class="cstat-no" title="statement not covered" >w</span>indow.PR={createSimpleLexer:g,registerLangHandler:c,sourceDecorator:i,PR_ATTRIB_NAME:P,PR_ATTRIB_VALUE:n,PR_COMMENT:j,PR_DECLARATION:E,PR_KEYWORD:z,PR_LITERAL:G,PR_NOCODE:N,PR_PLAIN:F,PR_PUNCTUATION:L,PR_SOURCE:J,PR_STRING:C,PR_TAG:m,PR_TYPE:O}}</span>)();<span class="cstat-no" title="statement not covered" >P</span>R.registerLangHandler(PR.createSimpleLexer([],[[PR.PR_DECLARATION,/^&lt;!\w[^&gt;]*(?:&gt;|$)/],[PR.PR_COMMENT,/^&lt;\!--[\s\S]*?(?:-\-&gt;|$)/],[PR.PR_PUNCTUATION,/^(?:&lt;[%?]|[%?]&gt;)/],["lang-",/^&lt;\?([\s\S]+?)(?:\?&gt;|$)/],["lang-",/^&lt;%([\s\S]+?)(?:%&gt;|$)/],["lang-",/^&lt;xmp\b[^&gt;]*&gt;([\s\S]+?)&lt;\/xmp\b[^&gt;]*&gt;/i],["lang-handlebars",/^&lt;script\b[^&gt;]*type\s*=\s*['"]?text\/x-handlebars-template['"]?\b[^&gt;]*&gt;([\s\S]*?)(&lt;\/script\b[^&gt;]*&gt;)/i],["lang-js",/^&lt;script\b[^&gt;]*&gt;([\s\S]*?)(&lt;\/script\b[^&gt;]*&gt;)/i],["lang-css",/^&lt;style\b[^&gt;]*&gt;([\s\S]*?)(&lt;\/style\b[^&gt;]*&gt;)/i],["lang-in.tag",/^(&lt;\/?[a-z][^&lt;&gt;]*&gt;)/i],[PR.PR_DECLARATION,/^{{[#^&gt;/]?\s*[\w.][^}]*}}/],[PR.PR_DECLARATION,/^{{&amp;?\s*[\w.][^}]*}}/],[PR.PR_DECLARATION,/^{{{&gt;?\s*[\w.][^}]*}}}/],[PR.PR_COMMENT,/^{{![^}]*}}/]]),["handlebars","hbs"]);<span class="cstat-no" title="statement not covered" >P</span>R.registerLangHandler(PR.createSimpleLexer([[PR.PR_PLAIN,/^[ \t\r\n\f]+/,null," \t\r\n\f"]],[[PR.PR_STRING,/^\"(?:[^\n\r\f\\\"]|\\(?:\r\n?|\n|\f)|\\[\s\S])*\"/,null],[PR.PR_STRING,/^\'(?:[^\n\r\f\\\']|\\(?:\r\n?|\n|\f)|\\[\s\S])*\'/,null],["lang-css-str",/^url\(([^\)\"\']*)\)/i],[PR.PR_KEYWORD,/^(?:url|rgb|\!important|@import|@page|@media|@charset|inherit)(?=[^\-\w]|$)/i,null],["lang-css-kw",/^(-?(?:[_a-z]|(?:\\[0-9a-f]+ ?))(?:[_a-z0-9\-]|\\(?:\\[0-9a-f]+ ?))*)\s*:/i],[PR.PR_COMMENT,/^\/\*[^*]*\*+(?:[^\/*][^*]*\*+)*\//],[PR.PR_COMMENT,/^(?:&lt;!--|--&gt;)/],[PR.PR_LITERAL,/^(?:\d+|\d*\.\d+)(?:%|[a-z]+)?/i],[PR.PR_LITERAL,/^#(?:[0-9a-f]{3}){1,2}/i],[PR.PR_PLAIN,/^-?(?:[_a-z]|(?:\\[\da-f]+ ?))(?:[_a-z\d\-]|\\(?:\\[\da-f]+ ?))*/i],[PR.PR_PUNCTUATION,/^[^\s\w\'\"]+/]]),["css"]);<span class="cstat-no" title="statement not covered" >P</span>R.registerLangHandler(PR.createSimpleLexer([],[[PR.PR_KEYWORD,/^-?(?:[_a-z]|(?:\\[\da-f]+ ?))(?:[_a-z\d\-]|\\(?:\\[\da-f]+ ?))*/i]]),["css-kw"]);<span class="cstat-no" title="statement not covered" >P</span>R.registerLangHandler(PR.createSimpleLexer([],[[PR.PR_STRING,/^[^\)\"\']+/]]),["css-str"]);</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-22T09:53:53.588Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    