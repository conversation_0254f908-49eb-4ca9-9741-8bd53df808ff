/**
 * Business Impact Predictor
 * 
 * This service predicts the business impact of technical issues and system changes.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Impact severity levels
const IMPACT_SEVERITY = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
  NEGLIGIBLE: 'negligible'
};

// Business metrics
const BUSINESS_METRIC = {
  REVENUE: 'revenue',
  ACTIVE_USERS: 'active_users',
  CONVERSION_RATE: 'conversion_rate',
  RETENTION_RATE: 'retention_rate',
  SATISFACTION_SCORE: 'satisfaction_score'
};

// Technical issue types
const ISSUE_TYPE = {
  PERFORMANCE: 'performance',
  AVAILABILITY: 'availability',
  ERROR_RATE: 'error_rate',
  LATENCY: 'latency',
  RESOURCE_EXHAUSTION: 'resource_exhaustion'
};

/**
 * Get business metrics data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Business metrics data
 */
async function getBusinessMetricsData(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('business_metrics')
      .select('*')
      .gte('date', startDate.toISOString())
      .order('date', { ascending: true });
      
    if (error) {
      logger.error('Error fetching business metrics data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getBusinessMetricsData', { error: error.message });
    return [];
  }
}

/**
 * Get system incidents data
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - System incidents data
 */
async function getSystemIncidentsData(days = 90) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('system_incidents')
      .select('*')
      .gte('start_time', startDate.toISOString())
      .order('start_time', { ascending: true });
      
    if (error) {
      logger.error('Error fetching system incidents data', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getSystemIncidentsData', { error: error.message });
    return [];
  }
}

/**
 * Calculate business impact of past incidents
 * 
 * @param {Array} incidents - System incidents data
 * @param {Array} businessMetrics - Business metrics data
 * @returns {Object} - Business impact data
 */
function calculatePastImpact(incidents, businessMetrics) {
  try {
    if (incidents.length === 0 || businessMetrics.length === 0) {
      return {
        impacts: [],
        correlations: {}
      };
    }
    
    // Group business metrics by date
    const metricsByDate = {};
    
    businessMetrics.forEach(metric => {
      const date = metric.date.split('T')[0];
      metricsByDate[date] = metric;
    });
    
    // Calculate baseline metrics (average over the period)
    const baselineMetrics = {
      revenue: businessMetrics.reduce((sum, metric) => sum + metric.revenue, 0) / businessMetrics.length,
      active_users: businessMetrics.reduce((sum, metric) => sum + metric.active_users, 0) / businessMetrics.length,
      conversion_rate: businessMetrics.reduce((sum, metric) => sum + metric.conversion_rate, 0) / businessMetrics.length,
      retention_rate: businessMetrics.reduce((sum, metric) => sum + metric.retention_rate, 0) / businessMetrics.length,
      satisfaction_score: businessMetrics.reduce((sum, metric) => sum + metric.satisfaction_score, 0) / businessMetrics.length
    };
    
    // Calculate impact for each incident
    const impacts = [];
    
    incidents.forEach(incident => {
      // Get metrics for incident period
      const incidentStart = new Date(incident.start_time);
      const incidentEnd = incident.end_time ? new Date(incident.end_time) : new Date(incidentStart);
      
      // Look at metrics for the day of the incident and the next 3 days
      const impactDays = [];
      
      for (let i = 0; i < 4; i++) {
        const date = new Date(incidentStart);
        date.setDate(date.getDate() + i);
        const dateStr = date.toISOString().split('T')[0];
        
        if (metricsByDate[dateStr]) {
          impactDays.push(metricsByDate[dateStr]);
        }
      }
      
      if (impactDays.length === 0) {
        return;
      }
      
      // Calculate average metrics during impact period
      const impactMetrics = {
        revenue: impactDays.reduce((sum, metric) => sum + metric.revenue, 0) / impactDays.length,
        active_users: impactDays.reduce((sum, metric) => sum + metric.active_users, 0) / impactDays.length,
        conversion_rate: impactDays.reduce((sum, metric) => sum + metric.conversion_rate, 0) / impactDays.length,
        retention_rate: impactDays.reduce((sum, metric) => sum + metric.retention_rate, 0) / impactDays.length,
        satisfaction_score: impactDays.reduce((sum, metric) => sum + metric.satisfaction_score, 0) / impactDays.length
      };
      
      // Calculate percentage change from baseline
      const impact = {
        incident_id: incident.id,
        incident_type: incident.type,
        incident_severity: incident.severity,
        incident_duration: incident.end_time 
          ? (new Date(incident.end_time) - new Date(incident.start_time)) / (1000 * 60) // in minutes
          : 0,
        revenue_impact: ((impactMetrics.revenue - baselineMetrics.revenue) / baselineMetrics.revenue) * 100,
        active_users_impact: ((impactMetrics.active_users - baselineMetrics.active_users) / baselineMetrics.active_users) * 100,
        conversion_rate_impact: ((impactMetrics.conversion_rate - baselineMetrics.conversion_rate) / baselineMetrics.conversion_rate) * 100,
        retention_rate_impact: ((impactMetrics.retention_rate - baselineMetrics.retention_rate) / baselineMetrics.retention_rate) * 100,
        satisfaction_score_impact: ((impactMetrics.satisfaction_score - baselineMetrics.satisfaction_score) / baselineMetrics.satisfaction_score) * 100
      };
      
      impacts.push(impact);
    });
    
    // Calculate correlations between incident types and business metrics
    const correlations = {
      performance: {
        revenue: 0,
        active_users: 0,
        conversion_rate: 0,
        retention_rate: 0,
        satisfaction_score: 0
      },
      availability: {
        revenue: 0,
        active_users: 0,
        conversion_rate: 0,
        retention_rate: 0,
        satisfaction_score: 0
      },
      error_rate: {
        revenue: 0,
        active_users: 0,
        conversion_rate: 0,
        retention_rate: 0,
        satisfaction_score: 0
      },
      latency: {
        revenue: 0,
        active_users: 0,
        conversion_rate: 0,
        retention_rate: 0,
        satisfaction_score: 0
      },
      resource_exhaustion: {
        revenue: 0,
        active_users: 0,
        conversion_rate: 0,
        retention_rate: 0,
        satisfaction_score: 0
      }
    };
    
    // Group impacts by incident type
    const impactsByType = {
      performance: [],
      availability: [],
      error_rate: [],
      latency: [],
      resource_exhaustion: []
    };
    
    impacts.forEach(impact => {
      if (impactsByType[impact.incident_type]) {
        impactsByType[impact.incident_type].push(impact);
      }
    });
    
    // Calculate average impact for each incident type
    Object.entries(impactsByType).forEach(([type, typeImpacts]) => {
      if (typeImpacts.length === 0) {
        return;
      }
      
      correlations[type] = {
        revenue: typeImpacts.reduce((sum, impact) => sum + impact.revenue_impact, 0) / typeImpacts.length,
        active_users: typeImpacts.reduce((sum, impact) => sum + impact.active_users_impact, 0) / typeImpacts.length,
        conversion_rate: typeImpacts.reduce((sum, impact) => sum + impact.conversion_rate_impact, 0) / typeImpacts.length,
        retention_rate: typeImpacts.reduce((sum, impact) => sum + impact.retention_rate_impact, 0) / typeImpacts.length,
        satisfaction_score: typeImpacts.reduce((sum, impact) => sum + impact.satisfaction_score_impact, 0) / typeImpacts.length
      };
    });
    
    return {
      impacts,
      correlations
    };
  } catch (error) {
    logger.error('Error in calculatePastImpact', { error: error.message });
    
    return {
      impacts: [],
      correlations: {}
    };
  }
}

/**
 * Predict business impact of a technical issue
 * 
 * @param {Object} issue - Technical issue data
 * @param {Object} correlations - Correlation data
 * @returns {Object} - Predicted business impact
 */
function predictBusinessImpact(issue, correlations) {
  try {
    if (!issue || !correlations || !correlations[issue.type]) {
      return {
        revenue_impact: 0,
        active_users_impact: 0,
        conversion_rate_impact: 0,
        retention_rate_impact: 0,
        satisfaction_score_impact: 0,
        severity: IMPACT_SEVERITY.NEGLIGIBLE,
        confidence: 0.3
      };
    }
    
    // Get correlation data for this issue type
    const typeCorrelation = correlations[issue.type];
    
    // Calculate severity multiplier based on issue severity
    let severityMultiplier = 1;
    
    switch (issue.severity) {
      case 'critical':
        severityMultiplier = 2.0;
        break;
      case 'high':
        severityMultiplier = 1.5;
        break;
      case 'medium':
        severityMultiplier = 1.0;
        break;
      case 'low':
        severityMultiplier = 0.5;
        break;
      default:
        severityMultiplier = 0.2;
    }
    
    // Calculate duration multiplier (impact increases with duration, but not linearly)
    const durationMultiplier = Math.min(2.0, Math.sqrt(issue.estimated_duration / 60)); // duration in minutes
    
    // Calculate predicted impact
    const predictedImpact = {
      revenue_impact: typeCorrelation.revenue * severityMultiplier * durationMultiplier,
      active_users_impact: typeCorrelation.active_users * severityMultiplier * durationMultiplier,
      conversion_rate_impact: typeCorrelation.conversion_rate * severityMultiplier * durationMultiplier,
      retention_rate_impact: typeCorrelation.retention_rate * severityMultiplier * durationMultiplier,
      satisfaction_score_impact: typeCorrelation.satisfaction_score * severityMultiplier * durationMultiplier
    };
    
    // Calculate overall severity
    let overallSeverity = IMPACT_SEVERITY.NEGLIGIBLE;
    const revenueImpact = Math.abs(predictedImpact.revenue_impact);
    
    if (revenueImpact > 20) {
      overallSeverity = IMPACT_SEVERITY.CRITICAL;
    } else if (revenueImpact > 10) {
      overallSeverity = IMPACT_SEVERITY.HIGH;
    } else if (revenueImpact > 5) {
      overallSeverity = IMPACT_SEVERITY.MEDIUM;
    } else if (revenueImpact > 1) {
      overallSeverity = IMPACT_SEVERITY.LOW;
    }
    
    // Calculate confidence based on amount of historical data
    const confidence = Math.min(0.9, 0.3 + (issue.historical_data_points || 0) * 0.05);
    
    return {
      ...predictedImpact,
      severity: overallSeverity,
      confidence
    };
  } catch (error) {
    logger.error('Error in predictBusinessImpact', { error: error.message });
    
    return {
      revenue_impact: 0,
      active_users_impact: 0,
      conversion_rate_impact: 0,
      retention_rate_impact: 0,
      satisfaction_score_impact: 0,
      severity: IMPACT_SEVERITY.NEGLIGIBLE,
      confidence: 0.3
    };
  }
}

/**
 * Generate mitigation recommendations
 * 
 * @param {Object} issue - Technical issue data
 * @param {Object} impact - Predicted business impact
 * @returns {Array} - Mitigation recommendations
 */
function generateMitigationRecommendations(issue, impact) {
  try {
    if (!issue || !impact) {
      return [];
    }
    
    const recommendations = [];
    
    // Add general recommendations based on issue type
    switch (issue.type) {
      case ISSUE_TYPE.PERFORMANCE:
        recommendations.push({
          action: 'Optimize database queries',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Implement caching',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Scale up resources',
          priority: 'medium',
          estimated_effort: 'low',
          estimated_impact: 'medium'
        });
        break;
        
      case ISSUE_TYPE.AVAILABILITY:
        recommendations.push({
          action: 'Implement redundancy',
          priority: 'high',
          estimated_effort: 'high',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Set up automatic failover',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Improve monitoring and alerting',
          priority: 'medium',
          estimated_effort: 'medium',
          estimated_impact: 'medium'
        });
        break;
        
      case ISSUE_TYPE.ERROR_RATE:
        recommendations.push({
          action: 'Implement retry logic',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Add error handling',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Improve logging and monitoring',
          priority: 'medium',
          estimated_effort: 'medium',
          estimated_impact: 'medium'
        });
        break;
        
      case ISSUE_TYPE.LATENCY:
        recommendations.push({
          action: 'Optimize network routes',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Implement CDN',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Optimize asset delivery',
          priority: 'medium',
          estimated_effort: 'medium',
          estimated_impact: 'medium'
        });
        break;
        
      case ISSUE_TYPE.RESOURCE_EXHAUSTION:
        recommendations.push({
          action: 'Implement auto-scaling',
          priority: 'high',
          estimated_effort: 'medium',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Optimize resource usage',
          priority: 'high',
          estimated_effort: 'high',
          estimated_impact: 'high'
        });
        recommendations.push({
          action: 'Implement resource limits',
          priority: 'medium',
          estimated_effort: 'low',
          estimated_impact: 'medium'
        });
        break;
    }
    
    // Add specific recommendations based on impact severity
    if (impact.severity === IMPACT_SEVERITY.CRITICAL || impact.severity === IMPACT_SEVERITY.HIGH) {
      recommendations.push({
        action: 'Notify executive team',
        priority: 'high',
        estimated_effort: 'low',
        estimated_impact: 'medium'
      });
      recommendations.push({
        action: 'Prepare customer communication',
        priority: 'high',
        estimated_effort: 'medium',
        estimated_impact: 'high'
      });
    }
    
    if (Math.abs(impact.revenue_impact) > 10) {
      recommendations.push({
        action: 'Prepare financial impact report',
        priority: 'high',
        estimated_effort: 'medium',
        estimated_impact: 'medium'
      });
    }
    
    if (Math.abs(impact.satisfaction_score_impact) > 10) {
      recommendations.push({
        action: 'Prepare customer retention plan',
        priority: 'high',
        estimated_effort: 'high',
        estimated_impact: 'high'
      });
    }
    
    // Sort recommendations by priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
    
    return recommendations;
  } catch (error) {
    logger.error('Error in generateMitigationRecommendations', { error: error.message });
    return [];
  }
}

// API endpoints
app.get('/api/past-impact', async (req, res) => {
  try {
    const incidents = await getSystemIncidentsData();
    const businessMetrics = await getBusinessMetricsData();
    
    const pastImpact = calculatePastImpact(incidents, businessMetrics);
    res.json(pastImpact);
  } catch (error) {
    logger.error('Error in GET /api/past-impact', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/predict-impact', async (req, res) => {
  try {
    const { issue } = req.body;
    
    if (!issue || !issue.type || !issue.severity || !issue.estimated_duration) {
      return res.status(400).json({ error: 'Invalid issue data' });
    }
    
    const incidents = await getSystemIncidentsData();
    const businessMetrics = await getBusinessMetricsData();
    
    const { correlations } = calculatePastImpact(incidents, businessMetrics);
    const impact = predictBusinessImpact(issue, correlations);
    const recommendations = generateMitigationRecommendations(issue, impact);
    
    res.json({ impact, recommendations });
  } catch (error) {
    logger.error('Error in POST /api/predict-impact', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.BUSINESS_IMPACT_PREDICTOR_PORT || 9111;
app.listen(PORT, () => {
  logger.info(`Business Impact Predictor listening on port ${PORT}`);
});

module.exports = {
  calculatePastImpact,
  predictBusinessImpact,
  generateMitigationRecommendations
};
