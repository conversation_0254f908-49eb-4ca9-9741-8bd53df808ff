/**
 * Rate Limit Monitoring Service
 *
 * This service monitors rate limiting events and provides metrics and alerts.
 */

const { redis, logger } = require('../../api/middleware/auth-middleware');
const { EventEmitter } = require('events');

// Create event emitter for rate limit events
const rateLimitEvents = new EventEmitter();

// Configure alert thresholds
const ALERT_THRESHOLDS = {
  // Alert if more than 10 rate limit events in 1 minute
  RATE_LIMIT_EXCEEDED: {
    count: 10,
    windowMs: 60 * 1000,
  },
  // Alert if more than 5 IP blocks in 5 minutes
  IP_BLOCKED: {
    count: 5,
    windowMs: 5 * 60 * 1000,
  },
};

// Track event counts for alerting
const eventCounts = {
  RATE_LIMIT_EXCEEDED: {
    count: 0,
    lastReset: Date.now(),
  },
  IP_BLOCKED: {
    count: 0,
    lastReset: Date.now(),
  },
};

/**
 * Track a rate limit event
 * @param {string} type - Event type (RATE_LIMIT_EXCEEDED, IP_BLOCKED)
 * @param {Object} data - Event data
 */
async function trackRateLimitEvent(type, data) {
  try {
    // Increment event count
    eventCounts[type].count++;
    
    // Check if we need to reset the count
    const now = Date.now();
    if (now - eventCounts[type].lastReset > ALERT_THRESHOLDS[type].windowMs) {
      eventCounts[type].count = 1;
      eventCounts[type].lastReset = now;
    }
    
    // Store event in Redis for metrics
    const eventData = JSON.stringify({
      timestamp: now,
      ...data,
    });
    
    await redis.lpush(`rate-limit:events:${type}`, eventData);
    await redis.ltrim(`rate-limit:events:${type}`, 0, 999); // Keep last 1000 events
    
    // Update metrics
    await updateMetrics(type, data);
    
    // Check if we need to trigger an alert
    if (eventCounts[type].count >= ALERT_THRESHOLDS[type].count) {
      triggerAlert(type, eventCounts[type].count, data);
    }
    
    // Emit event
    rateLimitEvents.emit(type, data);
  } catch (error) {
    logger.error('Error tracking rate limit event:', error);
  }
}

/**
 * Update metrics for rate limit events
 * @param {string} type - Event type
 * @param {Object} data - Event data
 */
async function updateMetrics(type, data) {
  try {
    const now = Date.now();
    const minute = Math.floor(now / 60000); // Current minute
    const hour = Math.floor(now / 3600000); // Current hour
    const day = Math.floor(now / 86400000); // Current day
    
    // Increment counters for different time windows
    await redis.hincrby(`rate-limit:metrics:${type}:minute:${minute}`, 'count', 1);
    await redis.hincrby(`rate-limit:metrics:${type}:hour:${hour}`, 'count', 1);
    await redis.hincrby(`rate-limit:metrics:${type}:day:${day}`, 'count', 1);
    
    // Set expiry for metrics keys
    await redis.expire(`rate-limit:metrics:${type}:minute:${minute}`, 60 * 60); // 1 hour
    await redis.expire(`rate-limit:metrics:${type}:hour:${hour}`, 24 * 60 * 60); // 1 day
    await redis.expire(`rate-limit:metrics:${type}:day:${day}`, 30 * 24 * 60 * 60); // 30 days
    
    // Track by IP
    if (data.ip) {
      await redis.hincrby(`rate-limit:metrics:${type}:ip:${data.ip}`, 'count', 1);
      await redis.expire(`rate-limit:metrics:${type}:ip:${data.ip}`, 24 * 60 * 60); // 1 day
    }
    
    // Track by path
    if (data.path) {
      await redis.hincrby(`rate-limit:metrics:${type}:path:${data.path}`, 'count', 1);
      await redis.expire(`rate-limit:metrics:${type}:path:${data.path}`, 24 * 60 * 60); // 1 day
    }
    
    // Track by user ID
    if (data.userId) {
      await redis.hincrby(`rate-limit:metrics:${type}:user:${data.userId}`, 'count', 1);
      await redis.expire(`rate-limit:metrics:${type}:user:${data.userId}`, 24 * 60 * 60); // 1 day
    }
    
    // Track by API key ID
    if (data.apiKeyId) {
      await redis.hincrby(`rate-limit:metrics:${type}:apikey:${data.apiKeyId}`, 'count', 1);
      await redis.expire(`rate-limit:metrics:${type}:apikey:${data.apiKeyId}`, 24 * 60 * 60); // 1 day
    }
  } catch (error) {
    logger.error('Error updating rate limit metrics:', error);
  }
}

/**
 * Trigger an alert for rate limit events
 * @param {string} type - Event type
 * @param {number} count - Event count
 * @param {Object} data - Event data
 */
function triggerAlert(type, count, data) {
  // Log alert
  logger.warn(`Rate limit alert: ${type}`, {
    type,
    count,
    threshold: ALERT_THRESHOLDS[type].count,
    window: ALERT_THRESHOLDS[type].windowMs,
    ...data,
  });
  
  // In a real implementation, this would send alerts to monitoring systems
  // such as Slack, PagerDuty, or email
}

/**
 * Get rate limit metrics
 * @param {string} type - Event type
 * @param {string} timeframe - Timeframe (minute, hour, day)
 * @param {number} value - Timeframe value
 * @returns {Promise<Object>} Metrics
 */
async function getMetrics(type, timeframe, value) {
  try {
    const metrics = await redis.hgetall(`rate-limit:metrics:${type}:${timeframe}:${value}`);
    return metrics;
  } catch (error) {
    logger.error('Error getting rate limit metrics:', error);
    return {};
  }
}

/**
 * Get top rate limited IPs
 * @param {string} type - Event type
 * @param {number} limit - Limit
 * @returns {Promise<Array>} Top IPs
 */
async function getTopRateLimitedIPs(type, limit = 10) {
  try {
    // Get all IP metrics keys
    const keys = await redis.keys(`rate-limit:metrics:${type}:ip:*`);
    
    // Get counts for each IP
    const results = [];
    for (const key of keys) {
      const ip = key.split(':').pop();
      const count = await redis.hget(key, 'count');
      
      results.push({
        ip,
        count: parseInt(count, 10),
      });
    }
    
    // Sort by count (descending) and limit
    return results
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  } catch (error) {
    logger.error('Error getting top rate limited IPs:', error);
    return [];
  }
}

module.exports = {
  trackRateLimitEvent,
  getMetrics,
  getTopRateLimitedIPs,
  rateLimitEvents,
};
