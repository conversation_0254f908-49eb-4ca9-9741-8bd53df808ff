FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/personalization-impact-analyzer.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV PERSONALIZATION_IMPACT_ANALYZER_PORT=9105

# Expose port
EXPOSE 9105

# Start the service
CMD ["node", "monitoring/personalization-impact-analyzer.js"]
