# A/B Testing Enhancements

The A/B Testing Enhancements system provides advanced capabilities for creating, managing, and analyzing A/B tests, including automated test creation, multi-variant testing, and long-term impact analysis.

## Components

### 1. Automated Test Creator

The Automated Test Creator analyzes user behavior patterns and suggests A/B tests that could improve key metrics. It provides:

- Identification of test opportunities based on user behavior
- Detection of drop-off points in user flows
- Analysis of low-engagement features
- Generation of test variants with descriptions
- Prioritization of test opportunities by potential impact

### 2. Multi-Variant Test Manager

The Multi-Variant Test Manager handles the execution and analysis of A/B tests with multiple variants. It provides:

- Test creation and management
- Variant assignment based on traffic allocation
- Conversion tracking for multiple goals
- Statistical analysis of test results
- Determination of winning variants

### 3. Long-Term Impact Analyzer

The Long-Term Impact Analyzer measures the long-term impact of A/B test winners on user behavior and business metrics. It provides:

- Analysis of post-test user behavior
- Measurement of engagement differences between variants
- Correlation with business metrics
- Impact scoring for test winners
- Recommendations for future tests

## API Endpoints

### Automated Test Creator

#### `GET /api/test-opportunities`

Get test opportunities based on user behavior analysis.

**Response:**

```json
{
  "opportunities": [
    {
      "type": "conversion",
      "element_type": "flow",
      "element_path": "checkout_step_1",
      "current_value": "45.5%",
      "goal": "increase_conversion_to_checkout_step_2",
      "potential_impact": "high",
      "description": "High drop-off rate (54.5%) between \"checkout_step_1\" and \"checkout_step_2\" steps",
      "suggested_variants": [
        {
          "name": "simplified_flow",
          "description": "Simplify the flow by reducing required steps or fields"
        },
        {
          "name": "improved_guidance",
          "description": "Add more guidance or help text to assist users"
        },
        {
          "name": "visual_emphasis",
          "description": "Increase visual emphasis on the next step or call to action"
        }
      ]
    },
    ...
  ]
}
```

#### `POST /api/tests`

Create a new A/B test.

**Request:**

```json
{
  "name": "Checkout Flow Optimization",
  "description": "Test different checkout flow variants to improve conversion",
  "element_type": "flow",
  "element_path": "checkout_step_1",
  "goal": "increase_conversion_to_checkout_step_2",
  "metrics": ["conversion_rate", "average_order_value"],
  "variants": [
    {
      "id": "control",
      "name": "control",
      "description": "Current checkout flow",
      "is_control": true
    },
    {
      "id": "simplified_flow",
      "name": "simplified_flow",
      "description": "Simplified checkout flow with fewer fields"
    },
    {
      "id": "improved_guidance",
      "name": "improved_guidance",
      "description": "Current flow with enhanced guidance and help text"
    }
  ],
  "traffic_allocation": 1.0
}
```

**Response:**

```json
{
  "test": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Checkout Flow Optimization",
    "description": "Test different checkout flow variants to improve conversion",
    "element_type": "flow",
    "element_path": "checkout_step_1",
    "goal": "increase_conversion_to_checkout_step_2",
    "metrics": ["conversion_rate", "average_order_value"],
    "variants": [...],
    "traffic_allocation": 1.0,
    "status": "draft",
    "created_at": "2023-06-01T12:00:00Z"
  }
}
```

### Multi-Variant Test Manager

#### `GET /api/tests`

Get all A/B tests.

**Response:**

```json
{
  "tests": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "Checkout Flow Optimization",
      "description": "Test different checkout flow variants to improve conversion",
      "element_type": "flow",
      "element_path": "checkout_step_1",
      "goal": "increase_conversion_to_checkout_step_2",
      "metrics": ["conversion_rate", "average_order_value"],
      "variants": [...],
      "traffic_allocation": 1.0,
      "status": "running",
      "start_date": "2023-06-01T12:00:00Z",
      "created_at": "2023-06-01T12:00:00Z"
    },
    ...
  ]
}
```

#### `GET /api/tests/:id`

Get a specific A/B test.

**Response:**

```json
{
  "test": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Checkout Flow Optimization",
    "description": "Test different checkout flow variants to improve conversion",
    "element_type": "flow",
    "element_path": "checkout_step_1",
    "goal": "increase_conversion_to_checkout_step_2",
    "metrics": ["conversion_rate", "average_order_value"],
    "variants": [...],
    "traffic_allocation": 1.0,
    "status": "running",
    "start_date": "2023-06-01T12:00:00Z",
    "created_at": "2023-06-01T12:00:00Z"
  }
}
```

#### `POST /api/tests/:id/status`

Update the status of an A/B test.

**Request:**

```json
{
  "status": "running"
}
```

**Response:**

```json
{
  "test": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Checkout Flow Optimization",
    "status": "running",
    "start_date": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:00:00Z"
  }
}
```

#### `POST /api/tests/:id/assign`

Assign a variant to a user.

**Request:**

```json
{
  "userId": "user-123"
}
```

**Response:**

```json
{
  "assignment": {
    "testId": "123e4567-e89b-12d3-a456-************",
    "userId": "user-123",
    "variantId": "simplified_flow",
    "variantName": "simplified_flow",
    "isControl": false
  }
}
```

#### `POST /api/tests/:id/convert`

Record a conversion for a user.

**Request:**

```json
{
  "userId": "user-123",
  "goalName": "increase_conversion_to_checkout_step_2",
  "conversionData": {
    "value": 100.0,
    "items": 3
  }
}
```

**Response:**

```json
{
  "conversion": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "test_id": "123e4567-e89b-12d3-a456-************",
    "user_id": "user-123",
    "variant_id": "simplified_flow",
    "variant_name": "simplified_flow",
    "goal_name": "increase_conversion_to_checkout_step_2",
    "conversion_data": {
      "value": 100.0,
      "items": 3
    },
    "created_at": "2023-06-01T12:00:00Z"
  }
}
```

#### `GET /api/tests/:id/results`

Get the results of an A/B test.

**Response:**

```json
{
  "testId": "123e4567-e89b-12d3-a456-************",
  "testName": "Checkout Flow Optimization",
  "status": "running",
  "startDate": "2023-06-01T12:00:00Z",
  "results": [
    {
      "variantId": "control",
      "variantName": "control",
      "isControl": true,
      "userCount": 1000,
      "goals": {
        "increase_conversion_to_checkout_step_2": {
          "conversionCount": 450,
          "conversionRate": 0.45
        }
      }
    },
    {
      "variantId": "simplified_flow",
      "variantName": "simplified_flow",
      "isControl": false,
      "userCount": 1000,
      "goals": {
        "increase_conversion_to_checkout_step_2": {
          "conversionCount": 550,
          "conversionRate": 0.55,
          "comparedToControl": {
            "zScore": 4.47,
            "pValue": 0.000008,
            "confidence": 0.999992,
            "improvement": 0.222,
            "significant": true
          }
        }
      }
    },
    ...
  ]
}
```

### Long-Term Impact Analyzer

#### `GET /api/long-term-impact/:testId`

Get the long-term impact analysis for a specific test.

**Response:**

```json
{
  "testId": "123e4567-e89b-12d3-a456-************",
  "testName": "Checkout Flow Optimization",
  "testDescription": "Test different checkout flow variants to improve conversion",
  "testStartDate": "2023-06-01T12:00:00Z",
  "testEndDate": "2023-06-15T12:00:00Z",
  "winningVariantId": "simplified_flow",
  "winningVariantName": "simplified_flow",
  "controlVariantId": "control",
  "controlVariantName": "control",
  "periodAnalyses": [
    {
      "period": "1 week",
      "startDate": "2023-06-16T00:00:00Z",
      "endDate": "2023-06-23T00:00:00Z",
      "winningVariantEngagement": {
        "actionsPerUser": 25.5,
        "sessionsPerUser": 3.2,
        "sessionDuration": 450,
        "retentionRate": 0.85
      },
      "controlVariantEngagement": {
        "actionsPerUser": 20.1,
        "sessionsPerUser": 2.8,
        "sessionDuration": 380,
        "retentionRate": 0.75
      },
      "engagementDiff": {
        "actionsPerUser": 26.87,
        "sessionsPerUser": 14.29,
        "sessionDuration": 18.42,
        "retentionRate": 13.33
      },
      "businessMetrics": {
        "activeUsers": 5000,
        "conversionRate": 0.12,
        "revenue": 50000,
        "retentionRate": 0.85,
        "satisfactionScore": 4.2
      }
    },
    ...
  ]
}
```

#### `GET /api/long-term-impact`

Get a summary of long-term impact for all completed tests.

**Response:**

```json
{
  "tests": [
    {
      "testId": "123e4567-e89b-12d3-a456-************",
      "testName": "Checkout Flow Optimization",
      "testDescription": "Test different checkout flow variants to improve conversion",
      "testStartDate": "2023-06-01T12:00:00Z",
      "testEndDate": "2023-06-15T12:00:00Z",
      "winningVariantId": "simplified_flow",
      "winningVariantName": "simplified_flow",
      "impactScore": 18.23,
      "periodCount": 3
    },
    ...
  ]
}
```

## Database Schema

### `ab_tests` Table

Stores A/B test definitions:

- `id` (uuid): Primary key
- `name` (text): Test name
- `description` (text): Test description
- `element_type` (text): Element type (ui, content, feature, flow, pricing)
- `element_path` (text): Element path
- `goal` (text): Test goal
- `metrics` (jsonb): Test metrics
- `variants` (jsonb): Test variants
- `traffic_allocation` (float): Traffic allocation (0-1)
- `status` (text): Test status (draft, running, paused, completed, archived)
- `start_date` (timestamp): Test start date
- `end_date` (timestamp): Test end date
- `winning_variant_id` (text): Winning variant ID
- `created_at` (timestamp): Creation timestamp
- `updated_at` (timestamp): Update timestamp

### `ab_test_assignments` Table

Stores variant assignments:

- `id` (uuid): Primary key
- `test_id` (uuid): Foreign key to the `ab_tests` table
- `user_id` (uuid): User ID
- `variant_id` (text): Variant ID
- `variant_name` (text): Variant name
- `is_control` (boolean): Whether the variant is the control
- `created_at` (timestamp): Creation timestamp

### `ab_test_conversions` Table

Stores conversion events:

- `id` (uuid): Primary key
- `test_id` (uuid): Foreign key to the `ab_tests` table
- `user_id` (uuid): User ID
- `variant_id` (text): Variant ID
- `variant_name` (text): Variant name
- `goal_name` (text): Goal name
- `conversion_data` (jsonb): Conversion data
- `created_at` (timestamp): Creation timestamp

### `ab_test_results` Table

Stores test results:

- `id` (uuid): Primary key
- `test_id` (uuid): Foreign key to the `ab_tests` table
- `date` (date): Result date
- `variant_id` (text): Variant ID
- `variant_name` (text): Variant name
- `user_count` (integer): Number of users
- `conversion_count` (integer): Number of conversions
- `conversion_rate` (float): Conversion rate
- `improvement` (float): Improvement over control
- `confidence` (float): Statistical confidence
- `created_at` (timestamp): Creation timestamp

### `ab_test_long_term_impact` Table

Stores long-term impact analysis:

- `id` (uuid): Primary key
- `test_id` (uuid): Foreign key to the `ab_tests` table
- `period` (text): Analysis period (1 week, 1 month, 3 months)
- `start_date` (date): Period start date
- `end_date` (date): Period end date
- `winning_variant_id` (text): Winning variant ID
- `control_variant_id` (text): Control variant ID
- `engagement_diff` (jsonb): Engagement differences
- `business_metrics` (jsonb): Business metrics
- `impact_score` (float): Impact score
- `created_at` (timestamp): Creation timestamp

## Configuration

The A/B Testing Enhancements system can be configured with the following environment variables:

- `AUTOMATED_TEST_CREATOR_PORT`: The port for the Automated Test Creator (default: 9106)
- `MULTI_VARIANT_TEST_MANAGER_PORT`: The port for the Multi-Variant Test Manager (default: 9107)
- `LONG_TERM_IMPACT_ANALYZER_PORT`: The port for the Long-Term Impact Analyzer (default: 9108)
- `SUPABASE_URL`: The URL of the Supabase instance
- `SUPABASE_KEY`: The service key for the Supabase instance

## Deployment

The A/B Testing Enhancements system is deployed as Docker containers and integrated with the existing monitoring system.

### Docker Compose

```yaml
# Automated Test Creator
automated-test-creator:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.automated-test-creator
  container_name: mvs-vr-automated-test-creator
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - AUTOMATED_TEST_CREATOR_PORT=9106
  ports:
    - "9106:9106"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus

# Multi-Variant Test Manager
multi-variant-test-manager:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.multi-variant-test-manager
  container_name: mvs-vr-multi-variant-test-manager
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - MULTI_VARIANT_TEST_MANAGER_PORT=9107
  ports:
    - "9107:9107"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus

# Long-Term Impact Analyzer
long-term-impact-analyzer:
  build:
    context: ../
    dockerfile: ./monitoring/Dockerfile.long-term-impact-analyzer
  container_name: mvs-vr-long-term-impact-analyzer
  environment:
    - NODE_ENV=production
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - LONG_TERM_IMPACT_ANALYZER_PORT=9108
  ports:
    - "9108:9108"
  restart: unless-stopped
  networks:
    - monitoring-network
  depends_on:
    - prometheus
```

### Prometheus Configuration

```yaml
# Automated Test Creator
- job_name: "automated-test-creator"
  static_configs:
    - targets: ["automated-test-creator:9106"]

# Multi-Variant Test Manager
- job_name: "multi-variant-test-manager"
  static_configs:
    - targets: ["multi-variant-test-manager:9107"]

# Long-Term Impact Analyzer
- job_name: "long-term-impact-analyzer"
  static_configs:
    - targets: ["long-term-impact-analyzer:9108"]
```
