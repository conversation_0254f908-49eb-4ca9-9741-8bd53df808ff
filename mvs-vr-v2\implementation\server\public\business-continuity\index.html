<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Business Continuity Dashboard</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding-top: 20px;
    }
    .card {
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      font-weight: bold;
      border-bottom: 1px solid #dee2e6;
    }
    .status-indicator {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }
    .status-operational {
      background-color: #28a745;
    }
    .status-degraded {
      background-color: #ffc107;
    }
    .status-outage {
      background-color: #dc3545;
    }
    .status-maintenance {
      background-color: #17a2b8;
    }
    .status-unknown {
      background-color: #6c757d;
    }
    .service-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    .service-item:last-child {
      border-bottom: none;
    }
    .impact-score {
      font-weight: bold;
    }
    .impact-minimal {
      color: #28a745;
    }
    .impact-low {
      color: #20c997;
    }
    .impact-medium {
      color: #ffc107;
    }
    .impact-high {
      color: #fd7e14;
    }
    .impact-critical {
      color: #dc3545;
    }
    .metric-card {
      height: 100%;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
    }
    .dependency-graph {
      width: 100%;
      height: 400px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background-color: #fff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="mb-4">
      <i class="bi bi-shield-check"></i>
      Business Continuity Dashboard
    </h1>
    
    <div class="row mb-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            System Status
          </div>
          <div class="card-body" id="system-status">
            <div class="d-flex align-items-center">
              <div class="status-indicator status-unknown"></div>
              <h3 class="mb-0">Loading system status...</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            Service Status
          </div>
          <div class="card-body p-0">
            <div id="service-list" class="list-group">
              <div class="service-item text-center">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                Loading services...
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            Business Metrics
          </div>
          <div class="card-body p-0">
            <div id="business-metrics" class="list-group">
              <div class="service-item text-center">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                Loading metrics...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mb-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            Service Dependency Graph
          </div>
          <div class="card-body">
            <div id="dependency-graph" class="dependency-graph">
              <div class="d-flex justify-content-center align-items-center h-100">
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Loading dependency graph...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
  <script>
    // API endpoints
    const API_STATUS = '/api/business-continuity/status';
    const API_METRICS = '/api/business-continuity/metrics';
    const API_DEPENDENCIES = '/api/business-continuity/dependencies';
    
    // Status colors
    const STATUS_COLORS = {
      'operational': '#28a745',
      'degraded': '#ffc107',
      'outage': '#dc3545',
      'maintenance': '#17a2b8',
      'unknown': '#6c757d'
    };
    
    // Impact levels
    const IMPACT_LEVELS = {
      1: { name: 'Minimal', class: 'impact-minimal' },
      2: { name: 'Low', class: 'impact-low' },
      3: { name: 'Medium', class: 'impact-medium' },
      4: { name: 'High', class: 'impact-high' },
      5: { name: 'Critical', class: 'impact-critical' }
    };
    
    // Load data
    async function loadData() {
      try {
        // Load status
        const statusResponse = await fetch(API_STATUS);
        const statusData = await statusResponse.json();
        updateSystemStatus(statusData);
        updateServiceList(statusData);
        
        // Load metrics
        const metricsResponse = await fetch(API_METRICS);
        const metricsData = await metricsResponse.json();
        updateBusinessMetrics(metricsData);
        
        // Load dependencies
        const dependenciesResponse = await fetch(API_DEPENDENCIES);
        const dependenciesData = await dependenciesResponse.json();
        renderDependencyGraph(dependenciesData);
      } catch (error) {
        console.error('Error loading data:', error);
        showError('Failed to load data. Please try again later.');
      }
    }
    
    // Update system status
    function updateSystemStatus(data) {
      const statusElement = document.getElementById('system-status');
      const status = data.status || 'unknown';
      
      statusElement.innerHTML = `
        <div class="d-flex align-items-center">
          <div class="status-indicator status-${status}"></div>
          <h3 class="mb-0">System Status: ${status.charAt(0).toUpperCase() + status.slice(1)}</h3>
        </div>
        <div class="mt-3">
          <p>Last updated: ${new Date(data.timestamp).toLocaleString()}</p>
          <p>
            <strong>Services:</strong> 
            ${data.statusCounts.operational || 0} Operational, 
            ${data.statusCounts.degraded || 0} Degraded, 
            ${data.statusCounts.outage || 0} Outage, 
            ${data.statusCounts.maintenance || 0} Maintenance
          </p>
        </div>
      `;
    }
    
    // Update service list
    function updateServiceList(data) {
      const serviceListElement = document.getElementById('service-list');
      const services = Object.entries(data.services);
      
      if (services.length === 0) {
        serviceListElement.innerHTML = '<div class="service-item text-center">No services found</div>';
        return;
      }
      
      serviceListElement.innerHTML = services.map(([serviceId, status]) => `
        <div class="service-item">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <div class="status-indicator status-${status}"></div>
              <strong>${serviceId}</strong>
            </div>
            <span class="badge bg-${getStatusBadgeColor(status)}">${status}</span>
          </div>
        </div>
      `).join('');
    }
    
    // Update business metrics
    function updateBusinessMetrics(data) {
      const metricsElement = document.getElementById('business-metrics');
      const metrics = Object.values(data.metrics || {});
      
      if (metrics.length === 0) {
        metricsElement.innerHTML = '<div class="service-item text-center">No metrics found</div>';
        return;
      }
      
      metricsElement.innerHTML = metrics.map(metric => `
        <div class="service-item">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <strong>${metric.name}</strong>
              <div class="text-muted small">${Object.entries(metric.labels || {}).map(([key, value]) => `${key}: ${value}`).join(', ')}</div>
            </div>
            <span class="metric-value">${metric.value}</span>
          </div>
        </div>
      `).join('');
    }
    
    // Render dependency graph
    function renderDependencyGraph(data) {
      const graphElement = document.getElementById('dependency-graph');
      
      // Clear loading indicator
      graphElement.innerHTML = '';
      
      // Create SVG
      const width = graphElement.clientWidth;
      const height = graphElement.clientHeight;
      
      const svg = d3.select(graphElement)
        .append('svg')
        .attr('width', width)
        .attr('height', height);
      
      // Create simulation
      const simulation = d3.forceSimulation(data.nodes)
        .force('link', d3.forceLink(data.edges).id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-300))
        .force('center', d3.forceCenter(width / 2, height / 2));
      
      // Create links
      const link = svg.append('g')
        .selectAll('line')
        .data(data.edges)
        .enter()
        .append('line')
        .attr('stroke', '#999')
        .attr('stroke-opacity', 0.6)
        .attr('stroke-width', 1);
      
      // Create nodes
      const node = svg.append('g')
        .selectAll('circle')
        .data(data.nodes)
        .enter()
        .append('circle')
        .attr('r', d => 5 + d.priority)
        .attr('fill', d => STATUS_COLORS[d.status] || STATUS_COLORS.unknown)
        .call(d3.drag()
          .on('start', dragstarted)
          .on('drag', dragged)
          .on('end', dragended));
      
      // Add labels
      const label = svg.append('g')
        .selectAll('text')
        .data(data.nodes)
        .enter()
        .append('text')
        .text(d => d.label)
        .attr('font-size', 10)
        .attr('dx', 8)
        .attr('dy', 3);
      
      // Update positions
      simulation.on('tick', () => {
        link
          .attr('x1', d => d.source.x)
          .attr('y1', d => d.source.y)
          .attr('x2', d => d.target.x)
          .attr('y2', d => d.target.y);
        
        node
          .attr('cx', d => d.x)
          .attr('cy', d => d.y);
        
        label
          .attr('x', d => d.x)
          .attr('y', d => d.y);
      });
      
      // Drag functions
      function dragstarted(event, d) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      }
      
      function dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
      }
      
      function dragended(event, d) {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      }
    }
    
    // Get status badge color
    function getStatusBadgeColor(status) {
      switch (status) {
        case 'operational': return 'success';
        case 'degraded': return 'warning';
        case 'outage': return 'danger';
        case 'maintenance': return 'info';
        default: return 'secondary';
      }
    }
    
    // Show error
    function showError(message) {
      const systemStatusElement = document.getElementById('system-status');
      systemStatusElement.innerHTML = `
        <div class="alert alert-danger" role="alert">
          <i class="bi bi-exclamation-triangle-fill"></i>
          ${message}
        </div>
      `;
    }
    
    // Load data on page load
    document.addEventListener('DOMContentLoaded', () => {
      loadData();
      
      // Refresh data every 30 seconds
      setInterval(loadData, 30000);
    });
  </script>
</body>
</html>
