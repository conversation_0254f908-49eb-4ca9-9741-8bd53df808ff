import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  getAnimations,
  getAnimation,
  createAnimation,
  updateAnimation,
  deleteAnimation,
  saveAnimations
} from '../AnimationService';

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

describe('AnimationService', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
  });

  describe('getAnimations', () => {
    it('should fetch animations for a vendor', async () => {
      // Mock response
      const mockAnimations = [
        { id: '1', name: 'Animation 1', vendor_id: 'vendor1' },
        { id: '2', name: 'Animation 2', vendor_id: 'vendor1' }
      ];
      mockApi.get.mockResolvedValue({ data: { data: mockAnimations } });

      // Call function
      const result = await getAnimations(mockApi, 'vendor1');

      // Assertions
      expect(mockApi.get).toHaveBeenCalledWith('/items/animations?filter[vendor_id][_eq]=vendor1');
      expect(result).toEqual(mockAnimations);
    });

    it('should handle empty response', async () => {
      // Mock response
      mockApi.get.mockResolvedValue({ data: { data: [] } });

      // Call function
      const result = await getAnimations(mockApi, 'vendor1');

      // Assertions
      expect(result).toEqual([]);
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('API error');
      mockApi.get.mockRejectedValue(error);

      // Call function and expect error
      await expect(getAnimations(mockApi, 'vendor1')).rejects.toThrow(error);
    });
  });

  describe('getAnimation', () => {
    it('should fetch a specific animation', async () => {
      // Mock response
      const mockAnimation = { id: '1', name: 'Animation 1', vendor_id: 'vendor1' };
      mockApi.get.mockResolvedValue({ data: { data: mockAnimation } });

      // Call function
      const result = await getAnimation(mockApi, '1');

      // Assertions
      expect(mockApi.get).toHaveBeenCalledWith('/items/animations/1');
      expect(result).toEqual(mockAnimation);
    });

    it('should handle null response', async () => {
      // Mock response
      mockApi.get.mockResolvedValue({ data: { data: null } });

      // Call function
      const result = await getAnimation(mockApi, '1');

      // Assertions
      expect(result).toBeNull();
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('API error');
      mockApi.get.mockRejectedValue(error);

      // Call function and expect error
      await expect(getAnimation(mockApi, '1')).rejects.toThrow(error);
    });
  });

  describe('createAnimation', () => {
    it('should create a new animation', async () => {
      // Mock animation and response
      const mockAnimation = { name: 'New Animation', vendor_id: 'vendor1' };
      const mockResponse = { ...mockAnimation, id: '3' };
      mockApi.post.mockResolvedValue({ data: { data: mockResponse } });

      // Call function
      const result = await createAnimation(mockApi, mockAnimation);

      // Assertions
      expect(mockApi.post).toHaveBeenCalledWith('/items/animations', mockAnimation);
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('API error');
      mockApi.post.mockRejectedValue(error);

      // Call function and expect error
      await expect(createAnimation(mockApi, {})).rejects.toThrow(error);
    });
  });

  describe('updateAnimation', () => {
    it('should update an existing animation', async () => {
      // Mock animation and response
      const mockAnimation = { name: 'Updated Animation' };
      const mockResponse = { id: '1', name: 'Updated Animation', vendor_id: 'vendor1' };
      mockApi.patch.mockResolvedValue({ data: { data: mockResponse } });

      // Call function
      const result = await updateAnimation(mockApi, '1', mockAnimation);

      // Assertions
      expect(mockApi.patch).toHaveBeenCalledWith('/items/animations/1', mockAnimation);
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('API error');
      mockApi.patch.mockRejectedValue(error);

      // Call function and expect error
      await expect(updateAnimation(mockApi, '1', {})).rejects.toThrow(error);
    });
  });

  describe('deleteAnimation', () => {
    it('should delete an animation', async () => {
      // Mock response
      mockApi.delete.mockResolvedValue({});

      // Call function
      const result = await deleteAnimation(mockApi, '1');

      // Assertions
      expect(mockApi.delete).toHaveBeenCalledWith('/items/animations/1');
      expect(result).toBe(true);
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('API error');
      mockApi.delete.mockRejectedValue(error);

      // Call function and expect error
      await expect(deleteAnimation(mockApi, '1')).rejects.toThrow(error);
    });
  });

  describe('saveAnimations', () => {
    it('should create new animations with temporary IDs', async () => {
      // Mock animations
      const mockAnimations = [
        { id: 'animation_temp1', name: 'New Animation 1' }
      ];
      
      // Mock responses
      const mockCreatedAnimation = { id: '3', name: 'New Animation 1', vendor_id: 'vendor1' };
      mockApi.post.mockResolvedValue({ data: { data: mockCreatedAnimation } });

      // Call function
      const result = await saveAnimations(mockApi, mockAnimations, 'vendor1');

      // Assertions
      expect(mockApi.post).toHaveBeenCalledWith('/items/animations', expect.objectContaining({
        name: 'New Animation 1',
        vendor_id: 'vendor1'
      }));
      expect(result).toEqual([mockCreatedAnimation]);
    });

    it('should update existing animations', async () => {
      // Mock animations
      const mockAnimations = [
        { id: '1', name: 'Updated Animation 1' }
      ];
      
      // Mock responses
      const mockUpdatedAnimation = { id: '1', name: 'Updated Animation 1', vendor_id: 'vendor1' };
      mockApi.patch.mockResolvedValue({ data: { data: mockUpdatedAnimation } });

      // Call function
      const result = await saveAnimations(mockApi, mockAnimations, 'vendor1');

      // Assertions
      expect(mockApi.patch).toHaveBeenCalledWith('/items/animations/1', expect.objectContaining({
        id: '1',
        name: 'Updated Animation 1',
        vendor_id: 'vendor1'
      }));
      expect(result).toEqual([mockUpdatedAnimation]);
    });

    it('should create new animations without IDs', async () => {
      // Mock animations
      const mockAnimations = [
        { name: 'Brand New Animation' }
      ];
      
      // Mock responses
      const mockCreatedAnimation = { id: '4', name: 'Brand New Animation', vendor_id: 'vendor1' };
      mockApi.post.mockResolvedValue({ data: { data: mockCreatedAnimation } });

      // Call function
      const result = await saveAnimations(mockApi, mockAnimations, 'vendor1');

      // Assertions
      expect(mockApi.post).toHaveBeenCalledWith('/items/animations', expect.objectContaining({
        name: 'Brand New Animation',
        vendor_id: 'vendor1'
      }));
      expect(result).toEqual([mockCreatedAnimation]);
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('API error');
      mockApi.post.mockRejectedValue(error);

      // Call function and expect error
      await expect(saveAnimations(mockApi, [{ name: 'Test' }], 'vendor1')).rejects.toThrow(error);
    });
  });
});
