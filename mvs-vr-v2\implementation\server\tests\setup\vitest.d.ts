/// <reference types="npm:vitest/globals" />
/// <reference types="npm:@testing-library/jest-dom" />
import 'npm:@testing-library/jest-dom';

declare module 'npm:vitest' {
  export interface SpyInstance<T extends (...args: any[]) => any> {
    getMockName(): string;
    mock: {
      calls: Parameters<T>[];
      results: Array<{ type: string; value: ReturnType<T> }>;
      instances: any[];
      contexts: any[];
      lastCall: Parameters<T>;
    };
    mockClear(): this;
    mockReset(): this;
    mockRestore(): void;
    mockImplementation(fn: T): this;
    mockImplementationOnce(fn: T): this;
    mockName(name: string): this;
    mockReturnThis(): this;
    mockReturnValue(value: ReturnType<T>): this;
    mockReturnValueOnce(value: ReturnType<T>): this;
    mockResolvedValue<U extends ReturnType<T>>(value: Awaited<U>): this;
    mockResolvedValueOnce<U extends ReturnType<T>>(value: Awaited<U>): this;
    mockRejectedValue(value: unknown): this;
    mockRejectedValueOnce(value: unknown): this;
  }

  export interface Assertion extends jest.Matchers<any> {
    toBeInTheDocument(): void;
    toBeVisible(): void;
    toBeEmpty(): void;
    toHaveAttribute(attr: string, value?: string): void;
    toHaveStyle(style: Record<string, any>): void;
    toHaveClass(...classNames: string[]): void;
  }

  export interface Vi {
    spyOn<T extends object, M extends keyof T>(
      object: T,
      method: M,
      accessType?: 'get' | 'set',
    ): SpyInstance<Required<T>[M] extends (...args: any[]) => any ? Required<T>[M] : never>;

    fn<T extends (...args: any[]) => any>(implementation?: T): SpyInstance<T>;

    mock<T extends string | Record<string, unknown>>(path: T, factory?: () => unknown): void;

    clearAllMocks(): void;
    resetAllMocks(): void;
    restoreAllMocks(): void;

    stubGlobal<T extends keyof typeof globalThis>(
      name: T,
      value: (typeof globalThis)[T],
    ): () => void;

    unstubAllGlobals(): void;

    useFakeTimers(): void;
    useRealTimers(): void;
    runAllTimers(): void;
    runOnlyPendingTimers(): void;
    advanceTimersByTime(ms: number): void;
    advanceTimersToNextTimer(): void;

    setSystemTime(time: number | Date): void;
    getMockedSystemTime(): Date | null;
    getRealSystemTime(): number;
  }
}

declare global {
  const vi: Vi;
  namespace Vi {
    interface JestAssertion<T = any> extends jest.Matchers<void, T> {}
    interface AsymmetricMatchersContaining extends jest.Matchers<void, any> {}
  }

  interface ImportMeta {
    vitest: boolean;
  }
}
