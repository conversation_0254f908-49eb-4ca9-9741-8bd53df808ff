{"lastRun": "2025-05-23T08:43:29.705Z", "recoveries": [{"startTime": "2025-05-22T12:44:00.515Z", "endTime": "2025-05-22T12:44:00.542Z", "duration": 0.027, "buckets": {"database": {"bucketType": "database", "startTime": "2025-05-22T12:44:00.518Z", "endTime": "2025-05-22T12:44:00.537Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}, "files": {"bucketType": "files", "startTime": "2025-05-22T12:44:00.537Z", "endTime": "2025-05-22T12:44:00.539Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}, "config": {"bucketType": "config", "startTime": "2025-05-22T12:44:00.540Z", "endTime": "2025-05-22T12:44:00.542Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}}}, {"startTime": "2025-05-22T16:08:06.160Z", "endTime": "2025-05-22T16:08:06.229Z", "duration": 0.069, "buckets": {"database": {"bucketType": "database", "startTime": "2025-05-22T16:08:06.163Z", "endTime": "2025-05-22T16:08:06.213Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}, "files": {"bucketType": "files", "startTime": "2025-05-22T16:08:06.219Z", "endTime": "2025-05-22T16:08:06.224Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}, "config": {"bucketType": "config", "startTime": "2025-05-22T16:08:06.224Z", "endTime": "2025-05-22T16:08:06.229Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}}}, {"startTime": "2025-05-23T08:43:29.612Z", "endTime": "2025-05-23T08:43:29.705Z", "duration": 0.093, "buckets": {"database": {"bucketType": "database", "startTime": "2025-05-23T08:43:29.654Z", "endTime": "2025-05-23T08:43:29.694Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}, "files": {"bucketType": "files", "startTime": "2025-05-23T08:43:29.694Z", "endTime": "2025-05-23T08:43:29.699Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}, "config": {"bucketType": "config", "startTime": "2025-05-23T08:43:29.700Z", "endTime": "2025-05-23T08:43:29.705Z", "duration": 0, "success": false, "details": {"error": "Resolved credential object is not valid"}}}}]}