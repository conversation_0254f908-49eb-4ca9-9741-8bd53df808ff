import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import VisualEditors from '../src/components/VisualEditors/VisualEditors.vue';

// Mock the child components
vi.mock('../src/components/VisualEditors/ShowroomLayoutEditor.vue', () => ({
  default: {
    name: 'ShowroomLayoutEditor',
    render: h => h('div', { class: 'mock-showroom-layout-editor' }),
    props: ['vendorId', 'showroomId']
  }
}));

vi.mock('../src/components/VisualEditors/ProductConfigurator.vue', () => ({
  default: {
    name: 'ProductConfigurator',
    render: h => h('div', { class: 'mock-product-configurator' }),
    props: ['vendorId', 'productId']
  }
}));

vi.mock('../src/components/VisualEditors/MaterialTextureEditor.vue', () => ({
  default: {
    name: 'MaterialTextureEditor',
    render: h => h('div', { class: 'mock-material-texture-editor' }),
    props: ['vendorId', 'materialId']
  }
}));

vi.mock('../src/components/VisualEditors/LightingEditor.vue', () => ({
  default: {
    name: 'LightingEditor',
    render: h => h('div', { class: 'mock-lighting-editor' }),
    props: ['vendorId', 'showroomId']
  }
}));

vi.mock('../src/components/VisualEditors/AnimationEditor.vue', () => ({
  default: {
    name: 'AnimationEditor',
    render: h => h('div', { class: 'mock-animation-editor' }),
    props: ['vendorId', 'animationId']
  }
}));

// Mock the API
const mockApi = {
  get: vi.fn()
};

// Generate large test datasets
function generateLargeDataset(prefix, count) {
  return Array.from({ length: count }, (_, i) => ({
    id: `${prefix}${i}`,
    name: `${prefix.charAt(0).toUpperCase() + prefix.slice(1)} ${i}`,
    // Add more properties to simulate real data
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    vendor_id: 'vendor1',
    status: i % 2 === 0 ? 'published' : 'draft',
    metadata: {
      description: `This is a test ${prefix} with a long description to simulate real data.`,
      tags: [`tag1`, `tag2`, `tag3`],
      properties: {
        prop1: `value${i}`,
        prop2: i * 10,
        prop3: i % 2 === 0
      }
    }
  }));
}

describe('VisualEditors Performance', () => {
  let wrapper;
  let startTime;
  
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock performance.now if not available
    if (!window.performance) {
      window.performance = { now: () => new Date().getTime() };
    }
    
    // Start timing
    startTime = performance.now();
  });

  it('renders within acceptable time with small dataset', async () => {
    // Mock API responses with small dataset
    mockApi.get.mockImplementation((url) => {
      if (url.includes('showroom_layouts')) {
        return Promise.resolve({ data: { data: generateLargeDataset('showroom', 10) } });
      } else if (url.includes('products')) {
        return Promise.resolve({ data: { data: generateLargeDataset('product', 10) } });
      } else if (url.includes('materials')) {
        return Promise.resolve({ data: { data: generateLargeDataset('material', 10) } });
      } else if (url.includes('animations')) {
        return Promise.resolve({ data: { data: generateLargeDataset('animation', 10) } });
      }
      return Promise.resolve({ data: { data: [] } });
    });
    
    // Mount component
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
    
    // Wait for all promises to resolve
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 0));
    
    // Check render time
    const renderTime = performance.now() - startTime;
    expect(renderTime).toBeLessThan(100); // Should render in less than 100ms
    
    // Check that data is loaded
    expect(wrapper.vm.showrooms.length).toBe(10);
    expect(wrapper.vm.products.length).toBe(10);
    expect(wrapper.vm.materials.length).toBe(10);
    expect(wrapper.vm.animations.length).toBe(10);
  });

  it('renders within acceptable time with large dataset', async () => {
    // Mock API responses with large dataset
    mockApi.get.mockImplementation((url) => {
      if (url.includes('showroom_layouts')) {
        return Promise.resolve({ data: { data: generateLargeDataset('showroom', 100) } });
      } else if (url.includes('products')) {
        return Promise.resolve({ data: { data: generateLargeDataset('product', 100) } });
      } else if (url.includes('materials')) {
        return Promise.resolve({ data: { data: generateLargeDataset('material', 100) } });
      } else if (url.includes('animations')) {
        return Promise.resolve({ data: { data: generateLargeDataset('animation', 100) } });
      }
      return Promise.resolve({ data: { data: [] } });
    });
    
    // Mount component
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
    
    // Wait for all promises to resolve
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 0));
    
    // Check render time
    const renderTime = performance.now() - startTime;
    expect(renderTime).toBeLessThan(500); // Should render in less than 500ms with large dataset
    
    // Check that data is loaded
    expect(wrapper.vm.showrooms.length).toBe(100);
    expect(wrapper.vm.products.length).toBe(100);
    expect(wrapper.vm.materials.length).toBe(100);
    expect(wrapper.vm.animations.length).toBe(100);
  });

  it('handles tab switching efficiently', async () => {
    // Mock API responses
    mockApi.get.mockResolvedValue({ data: { data: generateLargeDataset('item', 50) } });
    
    // Mount component
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
    
    // Wait for initial render
    await wrapper.vm.$nextTick();
    
    // Measure tab switching performance
    const tabSwitchStart = performance.now();
    
    // Switch tabs multiple times
    for (let i = 0; i < 5; i++) {
      await wrapper.findAll('.tab-button')[i].trigger('click');
      await wrapper.vm.$nextTick();
    }
    
    const tabSwitchTime = performance.now() - tabSwitchStart;
    expect(tabSwitchTime / 5).toBeLessThan(50); // Average tab switch should be less than 50ms
  });

  it('handles updates efficiently', async () => {
    // Mock API responses
    mockApi.get.mockResolvedValue({ data: { data: [] } });
    
    // Mount component
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
    
    // Wait for initial render
    await wrapper.vm.$nextTick();
    
    // Measure update performance
    const updateStart = performance.now();
    
    // Perform multiple updates
    for (let i = 0; i < 10; i++) {
      wrapper.vm.handleLayoutUpdate({ id: `layout${i}`, name: `Updated Layout ${i}` });
      await wrapper.vm.$nextTick();
    }
    
    const updateTime = performance.now() - updateStart;
    expect(updateTime / 10).toBeLessThan(10); // Average update should be less than 10ms
  });
});
