// Core dependencies
export {
  describe,
  it,
  test,
  expect,
  beforeAll,
  afterAll,
  beforeEach,
  afterEach,
  vi,
} from 'npm:vitest';

// Testing library
export {
  render,
  screen,
  fireEvent,
  waitFor,
  within,
  queries,
  getQueriesForElement,
} from 'npm:@testing-library/dom';

// Jest DOM matchers
export * from 'npm:@testing-library/jest-dom';

// Path handling
import { join, dirname, normalize } from 'npm:@deno/path';
export { join, dirname, normalize };

// File system
import { existsSync, readFileSync } from 'npm:@deno/fs';
export { existsSync, readFileSync };

// Environment
import * as dotenv from 'npm:dotenv';
export { dotenv };

// Types
export type {
  SpyInstance,
  MockInstance,
  TestContext,
  TestFunction,
  Suite,
  TestAPI,
} from 'npm:vitest';

export type { Matcher, MatcherState, SyncExpectationResult } from 'npm:expect';

// Test utilities
export type {
  RenderOptions,
  RenderResult,
  Screen,
  FireObject,
  BoundFunctions,
  queries as Queries,
} from 'npm:@testing-library/dom';

// Custom types
export interface TestEnvironment {
  beforeAll?: () => void | Promise<void>;
  afterAll?: () => void | Promise<void>;
  beforeEach?: () => void | Promise<void>;
  afterEach?: () => void | Promise<void>;
}

export interface TestSuite {
  name: string;
  tests: TestFunction[];
  beforeAll?: () => void | Promise<void>;
  afterAll?: () => void | Promise<void>;
  beforeEach?: () => void | Promise<void>;
  afterEach?: () => void | Promise<void>;
}

// Utility functions
export function isNode(): boolean {
  return typeof process !== 'undefined' && process.versions && process.versions.node;
}

export function isDeno(): boolean {
  return typeof Deno !== 'undefined';
}

export function isBrowser(): boolean {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

// Initialize environment
dotenv.config({
  path: '.env.test',
});
