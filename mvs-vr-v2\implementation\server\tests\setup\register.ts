import { register } from 'node:module';
import { pathToFileURL } from 'node:url';
import { resolve } from 'node:path';

// Register custom loader for Node.js ESM
register('./test-loader.ts', {
  parentURL: pathToFileURL(resolve(__dirname)),
  data: { type: 'module' },
});

// Configure module resolution for test environment
const environment = process.env.TEST_ENV || 'node';

if (environment === 'deno') {
  // @ts-ignore: Deno specific
  if (typeof Deno !== 'undefined') {
    // @ts-ignore: Deno specific
    Deno.env.set('NODE_PATH', resolve(__dirname, '../../node_modules'));
  }
}

// Load dotenv config
import('../setup/test-loader').then(({ moduleLoader }) => {
  moduleLoader.load('dotenv').then(dotenv => {
    dotenv.config({ path: resolve(__dirname, '../../.env.test') });
  });
});

// Import and register test framework globals
import('vitest/globals').then(globals => {
  Object.entries(globals).forEach(([key, value]) => {
    // @ts-ignore: Dynamic global assignment
    global[key] = value;
  });
});

// Import and register Jest DOM matchers
import('@testing-library/jest-dom/vitest').then(jestDom => {
  // @ts-ignore: Jest DOM extension
  expect.extend(jestDom.matchers);
});
