/**
 * Recovery Orchestrator Tests
 *
 * This file contains tests for the recovery orchestrator.
 */

// Mock the logger
vi.mock('../../utils/logger', () => {
  return {
    getLogger: vi.fn().mockReturnValue({
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
    }),
  };
});

const { expect } = require('chai');
const sinon = require('sinon');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const {
  runOrchestratedRecovery,
  recoverComponent,
  buildDependencyGraph,
  sortComponentsByRecoveryOrder,
} = require('../scripts/recovery/recovery-orchestrator');

describe('Recovery Orchestrator', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('buildDependencyGraph', () => {
    it('should build a dependency graph from components', () => {
      // Mock the config object
      const originalConfig = require('../scripts/recovery/recovery-orchestrator').config;

      // Create a mock config with test components
      const mockConfig = {
        components: {
          config: {
            name: 'Configuration',
            dependencies: [],
            recoveryScript: './config-recovery.js',
            healthCheck: async () => true,
            priority: 1,
          },
          database: {
            name: 'Database',
            dependencies: ['config'],
            recoveryScript: './database-recovery.js',
            healthCheck: async () => true,
            priority: 2,
          },
          api: {
            name: 'API Services',
            dependencies: ['database', 'config'],
            recoveryScript: './api-recovery.js',
            healthCheck: async () => true,
            priority: 3,
          },
        },
      };

      // Replace the config object
      sandbox
        .stub(require('../scripts/recovery/recovery-orchestrator'), 'config')
        .value(mockConfig);

      // Build the dependency graph
      const graph = buildDependencyGraph();

      // Verify the graph structure
      expect(graph).to.be.an('object');
      expect(Object.keys(graph)).to.have.lengthOf(3);

      // Check config node
      expect(graph.config).to.be.an('object');
      expect(graph.config.name).to.equal('Configuration');
      expect(graph.config.dependencies).to.be.an('array').that.is.empty;
      expect(graph.config.dependents).to.be.an('array').that.includes('database', 'api');
      expect(graph.config.priority).to.equal(1);

      // Check database node
      expect(graph.database).to.be.an('object');
      expect(graph.database.name).to.equal('Database');
      expect(graph.database.dependencies).to.be.an('array').that.includes('config');
      expect(graph.database.dependents).to.be.an('array').that.includes('api');
      expect(graph.database.priority).to.equal(2);

      // Check api node
      expect(graph.api).to.be.an('object');
      expect(graph.api.name).to.equal('API Services');
      expect(graph.api.dependencies).to.be.an('array').that.includes('database', 'config');
      expect(graph.api.dependents).to.be.an('array').that.is.empty;
      expect(graph.api.priority).to.equal(3);
    });
  });

  describe('sortComponentsByRecoveryOrder', () => {
    it('should sort components by recovery order based on dependencies and priority', () => {
      // Create a test dependency graph
      const graph = {
        config: {
          id: 'config',
          name: 'Configuration',
          dependencies: [],
          dependents: ['database', 'files', 'api'],
          priority: 1,
          recoveryScript: './config-recovery.js',
          healthCheck: async () => true,
        },
        database: {
          id: 'database',
          name: 'Database',
          dependencies: ['config'],
          dependents: ['api', 'cache'],
          priority: 2,
          recoveryScript: './database-recovery.js',
          healthCheck: async () => true,
        },
        files: {
          id: 'files',
          name: 'File Storage',
          dependencies: ['config'],
          dependents: ['api'],
          priority: 2,
          recoveryScript: './file-recovery.js',
          healthCheck: async () => true,
        },
        cache: {
          id: 'cache',
          name: 'Cache',
          dependencies: ['database'],
          dependents: ['api'],
          priority: 3,
          recoveryScript: './cache-recovery.js',
          healthCheck: async () => true,
        },
        api: {
          id: 'api',
          name: 'API Services',
          dependencies: ['database', 'files', 'cache', 'config'],
          dependents: [],
          priority: 4,
          recoveryScript: './api-recovery.js',
          healthCheck: async () => true,
        },
      };

      // Sort the components
      const sortedComponents = sortComponentsByRecoveryOrder(graph);

      // Verify the sorted order
      expect(sortedComponents).to.be.an('array').that.has.lengthOf(5);

      // Config should be first
      expect(sortedComponents[0].id).to.equal('config');

      // Database and files should be next (both have priority 2)
      // The exact order between database and files is not important as they have the same priority
      expect(sortedComponents[1].id).to.be.oneOf(['database', 'files']);
      expect(sortedComponents[2].id).to.be.oneOf(['database', 'files']);
      expect(sortedComponents[1].id).to.not.equal(sortedComponents[2].id);

      // Cache should be next
      expect(sortedComponents[3].id).to.equal('cache');

      // API should be last
      expect(sortedComponents[4].id).to.equal('api');
    });

    it('should throw an error if there are circular dependencies', () => {
      // Create a test dependency graph with circular dependencies
      const graph = {
        a: {
          id: 'a',
          name: 'Component A',
          dependencies: ['b'],
          dependents: ['c'],
          priority: 1,
        },
        b: {
          id: 'b',
          name: 'Component B',
          dependencies: ['c'],
          dependents: ['a'],
          priority: 2,
        },
        c: {
          id: 'c',
          name: 'Component C',
          dependencies: ['a'],
          dependents: ['b'],
          priority: 3,
        },
      };

      // Sorting should throw an error
      expect(() => sortComponentsByRecoveryOrder(graph)).to.throw(/Circular dependency detected/);
    });
  });

  describe('recoverComponent', () => {
    it('should recover a component successfully', async () => {
      // Create a test component
      const component = {
        id: 'test',
        name: 'Test Component',
        recoveryScript: './test-recovery.js',
        healthCheck: async () => true,
      };

      // Mock execSync
      const execSyncStub = sandbox
        .stub(require('child_process'), 'execSync')
        .returns('Recovery output');

      // Recover the component
      const result = await recoverComponent(component, {
        test: true,
        source: '/path/to/source',
        target: '/path/to/target',
      });

      // Verify the result
      expect(result).to.be.an('object');
      expect(result.id).to.equal('test');
      expect(result.name).to.equal('Test Component');
      expect(result.success).to.be.true;
      expect(result.retries).to.equal(0);
      expect(result.details).to.be.an('object');
      expect(result.details.recoveryOutput).to.equal('Recovery output');
      expect(result.details.healthy).to.be.true;

      // Verify execSync was called with the correct arguments
      expect(execSyncStub.calledOnce).to.be.true;
      expect(execSyncStub.firstCall.args[0]).to.include('./test-recovery.js');
      expect(execSyncStub.firstCall.args[0]).to.include('--test');
      expect(execSyncStub.firstCall.args[0]).to.include('--source=/path/to/source');
      expect(execSyncStub.firstCall.args[0]).to.include('--target=/path/to/target/test');
    });

    it('should retry recovery if health check fails', async () => {
      // Create a test component with a health check that fails once then succeeds
      let healthCheckCallCount = 0;
      const component = {
        id: 'test',
        name: 'Test Component',
        recoveryScript: './test-recovery.js',
        healthCheck: async () => {
          healthCheckCallCount++;
          return healthCheckCallCount > 1;
        },
      };

      // Mock execSync
      const execSyncStub = sandbox
        .stub(require('child_process'), 'execSync')
        .returns('Recovery output');

      // Mock setTimeout to avoid waiting
      const setTimeoutStub = sandbox.stub(global, 'setTimeout').callsFake(callback => callback());

      // Recover the component
      const result = await recoverComponent(component, {
        test: true,
        retryCount: 0,
      });

      // Verify the result
      expect(result).to.be.an('object');
      expect(result.id).to.equal('test');
      expect(result.success).to.be.true;
      expect(result.retries).to.equal(1);

      // Verify execSync was called twice
      expect(execSyncStub.calledTwice).to.be.true;

      // Verify setTimeout was called once
      expect(setTimeoutStub.calledOnce).to.be.true;
    });
  });

  describe('runOrchestratedRecovery', () => {
    it('should orchestrate recovery of multiple components', async () => {
      // Mock the dependency graph
      const mockGraph = {
        config: {
          id: 'config',
          name: 'Configuration',
          dependencies: [],
          dependents: ['database'],
          priority: 1,
          recoveryScript: './config-recovery.js',
          healthCheck: async () => true,
        },
        database: {
          id: 'database',
          name: 'Database',
          dependencies: ['config'],
          dependents: [],
          priority: 2,
          recoveryScript: './database-recovery.js',
          healthCheck: async () => true,
        },
      };

      // Mock buildDependencyGraph
      sandbox
        .stub(require('../scripts/recovery/recovery-orchestrator'), 'buildDependencyGraph')
        .returns(mockGraph);

      // Mock recoverComponent
      const recoverComponentStub = sandbox.stub(
        require('../scripts/recovery/recovery-orchestrator'),
        'recoverComponent',
      );
      recoverComponentStub.withArgs(sinon.match({ id: 'config' })).resolves({
        id: 'config',
        name: 'Configuration',
        success: true,
        details: {},
      });
      recoverComponentStub.withArgs(sinon.match({ id: 'database' })).resolves({
        id: 'database',
        name: 'Database',
        success: true,
        details: {},
      });

      // Mock fs functions
      sandbox.stub(fs, 'existsSync').returns(false);
      sandbox.stub(fs.promises, 'mkdir').resolves();
      sandbox.stub(fs.promises, 'writeFile').resolves();

      // Run orchestrated recovery
      const result = await runOrchestratedRecovery({
        test: true,
        target: '/path/to/target',
      });

      // Verify the result
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.components).to.be.an('object');
      expect(result.components.config).to.be.an('object');
      expect(result.components.config.success).to.be.true;
      expect(result.components.database).to.be.an('object');
      expect(result.components.database.success).to.be.true;

      // Verify recoverComponent was called for each component in the correct order
      expect(recoverComponentStub.calledTwice).to.be.true;
      expect(recoverComponentStub.firstCall.args[0].id).to.equal('config');
      expect(recoverComponentStub.secondCall.args[0].id).to.equal('database');
    });

    it('should stop recovery if a component fails', async () => {
      // Mock the dependency graph
      const mockGraph = {
        config: {
          id: 'config',
          name: 'Configuration',
          dependencies: [],
          dependents: ['database'],
          priority: 1,
          recoveryScript: './config-recovery.js',
          healthCheck: async () => true,
        },
        database: {
          id: 'database',
          name: 'Database',
          dependencies: ['config'],
          dependents: [],
          priority: 2,
          recoveryScript: './database-recovery.js',
          healthCheck: async () => true,
        },
      };

      // Mock buildDependencyGraph
      sandbox
        .stub(require('../scripts/recovery/recovery-orchestrator'), 'buildDependencyGraph')
        .returns(mockGraph);

      // Mock recoverComponent
      const recoverComponentStub = sandbox.stub(
        require('../scripts/recovery/recovery-orchestrator'),
        'recoverComponent',
      );
      recoverComponentStub.withArgs(sinon.match({ id: 'config' })).resolves({
        id: 'config',
        name: 'Configuration',
        success: false,
        details: { error: 'Recovery failed' },
      });

      // Mock fs functions
      sandbox.stub(fs, 'existsSync').returns(false);
      sandbox.stub(fs.promises, 'mkdir').resolves();
      sandbox.stub(fs.promises, 'writeFile').resolves();

      // Run orchestrated recovery
      const result = await runOrchestratedRecovery({
        test: true,
      });

      // Verify the result
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.components).to.be.an('object');
      expect(result.components.config).to.be.an('object');
      expect(result.components.config.success).to.be.false;

      // Verify recoverComponent was called only for the first component
      expect(recoverComponentStub.calledOnce).to.be.true;
      expect(recoverComponentStub.firstCall.args[0].id).to.equal('config');
    });
  });
});
