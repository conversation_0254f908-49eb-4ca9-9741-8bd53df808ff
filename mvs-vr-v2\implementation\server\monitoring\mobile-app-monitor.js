/**
 * Mobile App Monitor
 * 
 * This service collects and processes metrics from mobile applications.
 * It integrates with the existing monitoring system to provide comprehensive
 * mobile performance and usage monitoring.
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json({ limit: '5mb' }));

// Create a Registry for mobile app metrics
const register = new promClient.Registry();

// Create custom metrics for mobile apps
const metrics = {
  // Performance metrics
  appStartTime: new promClient.Histogram({
    name: 'mvs_vr_mobile_app_start_time_ms',
    help: 'Application start time in milliseconds',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version'],
    buckets: [100, 250, 500, 1000, 2000, 5000, 10000]
  }),
  
  frameRate: new promClient.Gauge({
    name: 'mvs_vr_mobile_frame_rate',
    help: 'Frame rate in frames per second',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version']
  }),
  
  memoryUsage: new promClient.Gauge({
    name: 'mvs_vr_mobile_memory_usage_mb',
    help: 'Memory usage in megabytes',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version']
  }),
  
  batteryLevel: new promClient.Gauge({
    name: 'mvs_vr_mobile_battery_level_percent',
    help: 'Battery level in percent',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version']
  }),
  
  // Network metrics
  networkLatency: new promClient.Histogram({
    name: 'mvs_vr_mobile_network_latency_ms',
    help: 'Network latency in milliseconds',
    labelNames: ['app_id', 'endpoint', 'network_type', 'app_version'],
    buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000]
  }),
  
  networkBandwidth: new promClient.Gauge({
    name: 'mvs_vr_mobile_network_bandwidth_kbps',
    help: 'Network bandwidth in kilobits per second',
    labelNames: ['app_id', 'direction', 'network_type', 'app_version']
  }),
  
  // API metrics
  apiRequestCount: new promClient.Counter({
    name: 'mvs_vr_mobile_api_request_count',
    help: 'Number of API requests',
    labelNames: ['app_id', 'endpoint', 'method', 'status', 'app_version']
  }),
  
  apiResponseTime: new promClient.Histogram({
    name: 'mvs_vr_mobile_api_response_time_ms',
    help: 'API response time in milliseconds',
    labelNames: ['app_id', 'endpoint', 'method', 'app_version'],
    buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000]
  }),
  
  // Asset loading metrics
  assetLoadTime: new promClient.Histogram({
    name: 'mvs_vr_mobile_asset_load_time_seconds',
    help: 'Asset loading time in seconds',
    labelNames: ['app_id', 'asset_type', 'app_version'],
    buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
  }),
  
  assetCacheHitRate: new promClient.Gauge({
    name: 'mvs_vr_mobile_asset_cache_hit_rate_percent',
    help: 'Asset cache hit rate in percent',
    labelNames: ['app_id', 'asset_type', 'app_version']
  }),
  
  // User interaction metrics
  screenLoadTime: new promClient.Histogram({
    name: 'mvs_vr_mobile_screen_load_time_ms',
    help: 'Screen load time in milliseconds',
    labelNames: ['app_id', 'screen_name', 'app_version'],
    buckets: [50, 100, 200, 500, 1000, 2000, 5000]
  }),
  
  interactionLatency: new promClient.Histogram({
    name: 'mvs_vr_mobile_interaction_latency_ms',
    help: 'Interaction latency in milliseconds',
    labelNames: ['app_id', 'interaction_type', 'app_version'],
    buckets: [10, 20, 50, 100, 200, 500, 1000]
  }),
  
  // Session metrics
  sessionDuration: new promClient.Histogram({
    name: 'mvs_vr_mobile_session_duration_seconds',
    help: 'Session duration in seconds',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version'],
    buckets: [10, 30, 60, 300, 600, 1800, 3600, 7200]
  }),
  
  screenViewCount: new promClient.Counter({
    name: 'mvs_vr_mobile_screen_view_count',
    help: 'Number of screen views',
    labelNames: ['app_id', 'screen_name', 'app_version']
  }),
  
  // AR/VR specific metrics
  arSessionDuration: new promClient.Histogram({
    name: 'mvs_vr_mobile_ar_session_duration_seconds',
    help: 'AR session duration in seconds',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version'],
    buckets: [10, 30, 60, 300, 600, 1800, 3600]
  }),
  
  trackingQuality: new promClient.Gauge({
    name: 'mvs_vr_mobile_tracking_quality',
    help: 'AR tracking quality score (0-100)',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version']
  }),
  
  // Error metrics
  errorCount: new promClient.Counter({
    name: 'mvs_vr_mobile_error_count',
    help: 'Number of errors',
    labelNames: ['app_id', 'error_type', 'app_version', 'component']
  }),
  
  crashCount: new promClient.Counter({
    name: 'mvs_vr_mobile_crash_count',
    help: 'Number of crashes',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version']
  }),
  
  anrCount: new promClient.Counter({
    name: 'mvs_vr_mobile_anr_count',
    help: 'Number of Application Not Responding events',
    labelNames: ['app_id', 'device_type', 'os_version', 'app_version']
  })
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Process mobile app metrics
 * 
 * @param {Object} data - Metrics data from mobile app
 */
async function processMobileAppMetrics(data) {
  try {
    const { appId, deviceType, osVersion, appVersion, timestamp, metrics: metricsData } = data;
    
    // Process performance metrics
    if (metricsData.performance) {
      const perf = metricsData.performance;
      
      if (perf.appStartTime !== undefined) {
        metrics.appStartTime.observe({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, perf.appStartTime);
      }
      
      if (perf.frameRate !== undefined) {
        metrics.frameRate.set({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, perf.frameRate);
      }
      
      if (perf.memoryUsage !== undefined) {
        metrics.memoryUsage.set({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, perf.memoryUsage);
      }
      
      if (perf.batteryLevel !== undefined) {
        metrics.batteryLevel.set({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, perf.batteryLevel);
      }
    }
    
    // Process network metrics
    if (metricsData.network) {
      const network = metricsData.network;
      
      if (network.latencies) {
        Object.entries(network.latencies).forEach(([endpoint, data]) => {
          metrics.networkLatency.observe({ 
            app_id: appId, 
            endpoint, 
            network_type: data.networkType || 'unknown', 
            app_version: appVersion 
          }, data.latency);
        });
      }
      
      if (network.bandwidth) {
        if (network.bandwidth.download) {
          metrics.networkBandwidth.set({ 
            app_id: appId, 
            direction: 'download', 
            network_type: network.bandwidth.networkType || 'unknown', 
            app_version: appVersion 
          }, network.bandwidth.download);
        }
        
        if (network.bandwidth.upload) {
          metrics.networkBandwidth.set({ 
            app_id: appId, 
            direction: 'upload', 
            network_type: network.bandwidth.networkType || 'unknown', 
            app_version: appVersion 
          }, network.bandwidth.upload);
        }
      }
    }
    
    // Process API metrics
    if (metricsData.api) {
      const api = metricsData.api;
      
      if (api.requests) {
        api.requests.forEach(request => {
          metrics.apiRequestCount.inc({ 
            app_id: appId, 
            endpoint: request.endpoint, 
            method: request.method, 
            status: request.status, 
            app_version: appVersion 
          });
          
          if (request.responseTime !== undefined) {
            metrics.apiResponseTime.observe({ 
              app_id: appId, 
              endpoint: request.endpoint, 
              method: request.method, 
              app_version: appVersion 
            }, request.responseTime);
          }
        });
      }
    }
    
    // Process asset loading metrics
    if (metricsData.assetLoading) {
      const assets = metricsData.assetLoading;
      
      if (assets.loadTimes) {
        Object.entries(assets.loadTimes).forEach(([assetType, loadTime]) => {
          metrics.assetLoadTime.observe({ app_id: appId, asset_type: assetType, app_version: appVersion }, loadTime);
        });
      }
      
      if (assets.cacheHitRates) {
        Object.entries(assets.cacheHitRates).forEach(([assetType, hitRate]) => {
          metrics.assetCacheHitRate.set({ app_id: appId, asset_type: assetType, app_version: appVersion }, hitRate);
        });
      }
    }
    
    // Process user interaction metrics
    if (metricsData.interactions) {
      const interactions = metricsData.interactions;
      
      if (interactions.screenLoadTimes) {
        Object.entries(interactions.screenLoadTimes).forEach(([screenName, loadTime]) => {
          metrics.screenLoadTime.observe({ app_id: appId, screen_name: screenName, app_version: appVersion }, loadTime);
        });
      }
      
      if (interactions.latencies) {
        Object.entries(interactions.latencies).forEach(([type, latency]) => {
          metrics.interactionLatency.observe({ app_id: appId, interaction_type: type, app_version: appVersion }, latency);
        });
      }
      
      if (interactions.screenViews) {
        Object.entries(interactions.screenViews).forEach(([screenName, count]) => {
          metrics.screenViewCount.inc({ app_id: appId, screen_name: screenName, app_version: appVersion }, count);
        });
      }
    }
    
    // Process session metrics
    if (metricsData.session) {
      const session = metricsData.session;
      
      if (session.duration !== undefined) {
        metrics.sessionDuration.observe({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, session.duration);
      }
    }
    
    // Process AR/VR metrics
    if (metricsData.ar) {
      const ar = metricsData.ar;
      
      if (ar.sessionDuration !== undefined) {
        metrics.arSessionDuration.observe({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, ar.sessionDuration);
      }
      
      if (ar.trackingQuality !== undefined) {
        metrics.trackingQuality.set({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, ar.trackingQuality);
      }
    }
    
    // Process error metrics
    if (metricsData.errors) {
      Object.entries(metricsData.errors).forEach(([errorType, components]) => {
        Object.entries(components).forEach(([component, count]) => {
          metrics.errorCount.inc({ app_id: appId, error_type: errorType, app_version: appVersion, component }, count);
        });
      });
      
      if (metricsData.errors.crashes) {
        metrics.crashCount.inc({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, metricsData.errors.crashes);
      }
      
      if (metricsData.errors.anr) {
        metrics.anrCount.inc({ app_id: appId, device_type: deviceType, os_version: osVersion, app_version: appVersion }, metricsData.errors.anr);
      }
    }
    
    // Store metrics in database for historical analysis
    await storeMetricsInDatabase(data);
    
    logger.info(`Processed mobile app metrics from app ${appId}`);
  } catch (error) {
    logger.error('Error processing mobile app metrics', { error: error.message });
  }
}

/**
 * Store metrics in database for historical analysis
 * 
 * @param {Object} data - Metrics data from mobile app
 */
async function storeMetricsInDatabase(data) {
  try {
    const { appId, deviceType, osVersion, appVersion, timestamp, metrics: metricsData } = data;
    
    // Store in database
    const { error } = await supabase
      .from('mobile_app_metrics')
      .insert({
        app_id: appId,
        device_type: deviceType,
        os_version: osVersion,
        app_version: appVersion,
        timestamp: timestamp || new Date().toISOString(),
        metrics_data: metricsData
      });
      
    if (error) {
      logger.error('Error storing mobile app metrics in database', { error: error.message });
    }
  } catch (error) {
    logger.error('Error in storeMetricsInDatabase', { error: error.message });
  }
}

// API endpoint for receiving metrics from mobile apps
app.post('/api/metrics', async (req, res) => {
  try {
    const data = req.body;
    
    // Validate required fields
    if (!data.appId || !data.metrics) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Process metrics
    await processMobileAppMetrics(data);
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/metrics', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving mobile app metrics', { error: error.message });
    res.status(500).send('Error collecting mobile app metrics');
  }
});

// Start server
const PORT = process.env.MOBILE_APP_MONITOR_PORT || 9101;
app.listen(PORT, () => {
  logger.info(`Mobile App Monitor listening on port ${PORT}`);
});

module.exports = {
  processMobileAppMetrics,
  storeMetricsInDatabase
};
