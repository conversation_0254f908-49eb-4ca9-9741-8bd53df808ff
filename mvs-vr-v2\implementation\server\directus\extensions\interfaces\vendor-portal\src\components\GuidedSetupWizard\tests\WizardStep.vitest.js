/**
 * Vitest tests for WizardStep component
 */

import { describe, it, expect, beforeEach, vi } from 'npm:vitest';
import { mount } from 'npm:@vue/test-utils';
import WizardStep from '../WizardStep.vue'; // Use the real component

describe('WizardStep', () => {
  let wrapper;

  beforeEach(() => {
    // Reset the wrapper before each test
    wrapper = null;
  });

  it('renders correctly with default props', () => {
    // Arrange
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'This is a test step',
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
    });

    // Assert
    expect(wrapper.find('.step-title').text()).toBe('Test Step');
    expect(wrapper.find('.step-description').text()).toBe('This is a test step');
    // Assuming 'active' and 'completed' classes are applied to the root element
    expect(wrapper.classes()).toContain('wizard-step');
    // The component itself doesn't seem to apply 'active' or 'completed' classes based on the provided .vue file.
    // If these classes are applied by a parent component, these assertions should be removed or adjusted.
    // For now, I will remove the assertions related to 'active' and 'completed' classes on the root element.
    // expect(wrapper.classes()).toContain('active');
    // expect(wrapper.classes()).not.toContain('completed');
  });

  it('renders correctly without description', () => {
    // Arrange
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
    });

    // Assert
    expect(wrapper.find('.step-title').text()).toBe('Test Step');
    expect(wrapper.find('.step-description').exists()).toBe(false);
  });

  it('renders correctly without title and description', () => {
    // Arrange
    wrapper = mount(WizardStep, {
      propsData: {
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
    });

    // Assert
    expect(wrapper.find('.step-header').exists()).toBe(false);
  });

  it('displays validation errors when present', async () => {
    // Arrange
    const validationErrors = ['Error 1', 'Error 2'];
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'This is a test step',
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
    });

    // Act - Manually set validation errors for testing display
    wrapper.vm.validationErrors = validationErrors;
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.find('.validation-errors').exists()).toBe(true);
    const errorItems = wrapper.findAll('.error-item');
    expect(errorItems).toHaveLength(validationErrors.length);
    expect(errorItems.at(0).text()).toBe('Error 1');
    expect(errorItems.at(1).text()).toBe('Error 2');
  });

  it('does not display validation errors when none are present', () => {
    // Arrange
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'This is a test step',
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
    });

    // Assert
    expect(wrapper.find('.validation-errors').exists()).toBe(false);
  });

  it('renders slot content', () => {
    // Arrange
    const slotContent = '<div class="test-slot-content">Slot Content</div>';
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'This is a test step',
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
      slots: {
        default: slotContent,
      },
    });

    // Assert
    expect(wrapper.find('.test-slot-content').exists()).toBe(true);
    expect(wrapper.find('.test-slot-content').text()).toBe('Slot Content');
  });

  it('initializes localStepData with stepData prop', () => {
    // Arrange
    const stepData = { name: 'Test', email: '<EMAIL>' };

    // Act
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'This is a test step',
        isActive: true,
        isCompleted: false,
        stepData,
      },
    });

    // Assert
    expect(wrapper.vm.localStepData).toEqual(stepData);
  });

  it('emits update:step-data when updateStepData is called', async () => {
    // Arrange
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'This is a test step',
        isActive: true,
        isCompleted: false,
        stepData: {},
      },
    });

    const newData = { name: 'Updated', email: '<EMAIL>' };

    // Act
    wrapper.vm.updateStepData('name', 'Updated');
    wrapper.vm.updateStepData('email', '<EMAIL>');
    await wrapper.vm.$nextTick();

    // Assert
    const emitted = wrapper.emitted('update:step-data');
    expect(emitted).toBeTruthy();
    // Check the last emitted value as updateStepData is called twice
    expect(emitted[emitted.length - 1][0]).toEqual(newData);
  });

  describe('validation', () => {
    it('should validate successfully with no schema', () => {
      // Arrange
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData: {},
          validationSchema: null, // No schema
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(true);
      expect(wrapper.vm.validationErrors).toHaveLength(0);
      expect(validateSpy).toHaveBeenCalledWith('validate', true);
    });

    it('should validate successfully with a valid schema and data', () => {
      // Arrange
      const validationSchema = {
        name: { required: true, label: 'Name' },
        email: {
          required: true,
          pattern: /.+@.+\..+/,
          patternMessage: 'Invalid email format',
          label: 'Email',
        },
      };
      const stepData = { name: 'Test User', email: '<EMAIL>' };
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(true);
      expect(wrapper.vm.validationErrors).toHaveLength(0);
      expect(validateSpy).toHaveBeenCalledWith('validate', true);
    });

    it('should fail validation with required fields missing', () => {
      // Arrange
      const validationSchema = {
        name: { required: true, label: 'Name' },
        email: { required: true, label: 'Email' },
      };
      const stepData = { name: 'Test User' }; // Missing email
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Email is required');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should fail validation with invalid pattern', () => {
      // Arrange
      const validationSchema = {
        email: {
          required: true,
          pattern: /.+@.+\..+/,
          patternMessage: 'Invalid email format',
          label: 'Email',
        },
      };
      const stepData = { email: 'invalid-email' }; // Invalid email format
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Invalid email format');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should fail validation with minLength rule', () => {
      // Arrange
      const validationSchema = {
        password: { required: true, minLength: 8, label: 'Password' },
      };
      const stepData = { password: 'short' }; // Less than 8 characters
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Password must be at least 8 characters');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should fail validation with maxLength rule', () => {
      // Arrange
      const validationSchema = {
        username: { required: true, maxLength: 10, label: 'Username' },
      };
      const stepData = { username: 'verylongusername' }; // More than 10 characters
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Username must be at most 10 characters');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should fail validation with min value rule', () => {
      // Arrange
      const validationSchema = {
        age: { required: true, min: 18, label: 'Age' },
      };
      const stepData = { age: 17 }; // Less than 18
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Age must be at least 18');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should fail validation with max value rule', () => {
      // Arrange
      const validationSchema = {
        quantity: { required: true, max: 100, label: 'Quantity' },
      };
      const stepData = { quantity: 101 }; // More than 100
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Quantity must be at most 100');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should fail validation with custom validate function', () => {
      // Arrange
      const validationSchema = {
        customField: {
          required: true,
          label: 'Custom Field',
          validate: value => (value === 'valid' ? true : 'Custom field must be "valid"'),
        },
      };
      const stepData = { customField: 'invalid' }; // Fails custom validation
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors).toHaveLength(1);
      expect(wrapper.vm.validationErrors).toContain('Custom field must be "valid"');
      expect(validateSpy).toHaveBeenCalledWith('validate', false);
    });

    it('should pass validation with custom validate function returning true', () => {
      // Arrange
      const validationSchema = {
        customField: {
          required: true,
          label: 'Custom Field',
          validate: value => (value === 'valid' ? true : 'Custom field must be "valid"'),
        },
      };
      const stepData = { customField: 'valid' }; // Passes custom validation
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, '$emit');

      // Act
      const isValid = wrapper.vm.validate();

      // Assert
      expect(isValid).toBe(true);
      expect(wrapper.vm.validationErrors).toHaveLength(0);
      expect(validateSpy).toHaveBeenCalledWith('validate', true);
    });

    it('should validate on mount if validateOnMount is true', () => {
      // Arrange
      const validationSchema = { name: { required: true, label: 'Name' } };
      const stepData = {}; // Missing required field
      const validateSpy = vi.spyOn(WizardStep.methods, 'validate'); // Spy on the method

      // Act
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
          validateOnMount: true,
        },
      });

      // Assert
      expect(validateSpy).toHaveBeenCalled();
      // Restore the original method after the test
      validateSpy.mockRestore();
    });

    it('should not validate on mount if validateOnMount is false', () => {
      // Arrange
      const validationSchema = { name: { required: true, label: 'Name' } };
      const stepData = {}; // Missing required field
      const validateSpy = vi.spyOn(WizardStep.methods, 'validate'); // Spy on the method

      // Act
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
          validateOnMount: false,
        },
      });

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
      // Restore the original method after the test
      validateSpy.mockRestore();
    });

    it('should validate on change if validateOnChange is true', async () => {
      // Arrange
      const validationSchema = { name: { required: true, label: 'Name' } };
      const stepData = {}; // Missing required field
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
          validateOnChange: true,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, 'validate'); // Spy on the instance method

      // Act
      wrapper.vm.updateStepData('name', 'Test User');
      await wrapper.vm.$nextTick();

      // Assert
      expect(validateSpy).toHaveBeenCalled();
    });

    it('should not validate on change if validateOnChange is false', async () => {
      // Arrange
      const validationSchema = { name: { required: true, label: 'Name' } };
      const stepData = {}; // Missing required field
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData,
          validationSchema,
          validateOnChange: false,
        },
      });
      const validateSpy = vi.spyOn(wrapper.vm, 'validate'); // Spy on the instance method

      // Act
      wrapper.vm.updateStepData('name', 'Test User');
      await wrapper.vm.$nextTick();

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });

    it('should reset validation errors when resetValidation is called', () => {
      // Arrange
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          stepData: {},
        },
      });
      wrapper.vm.validationErrors = ['Error 1', 'Error 2'];

      // Act
      wrapper.vm.resetValidation();

      // Assert
      expect(wrapper.vm.validationErrors).toHaveLength(0);
    });
  });

  describe('help tips', () => {
    it('should display help tips when showHelpTips is true and helpTips array is not empty', () => {
      // Arrange
      const helpTips = [{ title: 'Tip 1', text: 'This is tip 1' }];
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          helpTips,
          showHelpTips: true,
        },
      });

      // Assert
      expect(wrapper.find('.help-tips').exists()).toBe(true);
      expect(wrapper.findAll('.help-tip')).toHaveLength(helpTips.length);
      expect(wrapper.find('.tip-title').text()).toBe('Tip 1');
      expect(wrapper.find('.tip-text').text()).toBe('This is tip 1');
    });

    it('should not display help tips when showHelpTips is false', () => {
      // Arrange
      const helpTips = [{ title: 'Tip 1', text: 'This is tip 1' }];
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          helpTips,
          showHelpTips: false,
        },
      });

      // Assert
      expect(wrapper.find('.help-tips').exists()).toBe(false);
    });

    it('should not display help tips when helpTips array is empty', () => {
      // Arrange
      const helpTips = [];
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          helpTips,
          showHelpTips: true,
        },
      });

      // Assert
      expect(wrapper.find('.help-tips').exists()).toBe(false);
    });
  });

  describe('disabled fields', () => {
    it('should return true for isFieldDisabled if field is in disabledFields array', () => {
      // Arrange
      const disabledFields = ['fieldName'];
      wrapper = mount(WizardStep, {
        propsData: {
          title: 'Test Step',
          disabledFields,
        },
      });

      // Assert
      expect(wrapper.vm.isFieldDisabled('fieldName')).toBe(true);
      expect(wrapper.vm.isFieldDisabled('anotherField')).toBe(false);
    });
  });
});
