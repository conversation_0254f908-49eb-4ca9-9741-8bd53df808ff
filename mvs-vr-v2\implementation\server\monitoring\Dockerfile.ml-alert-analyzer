FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/ml-alert-analyzer.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV ML_ALERT_ANALYZER_PORT=9102

# Expose port
EXPOSE 9102

# Start the service
CMD ["node", "monitoring/ml-alert-analyzer.js"]
