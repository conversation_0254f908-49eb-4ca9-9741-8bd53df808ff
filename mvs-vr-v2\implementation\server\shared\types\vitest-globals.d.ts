/// <reference types="npm:vitest/globals" />
/// <reference types="npm:@testing-library/jest-dom" />

declare module 'npm:vitest' {
  export * from 'vitest';

  // Core test functions
  export const describe: typeof import('vitest').describe;
  export const it: typeof import('vitest').it;
  export const test: typeof import('vitest').test;
  export const expect: typeof import('vitest').expect;

  // Hooks
  export const beforeAll: typeof import('vitest').beforeAll;
  export const afterAll: typeof import('vitest').afterAll;
  export const beforeEach: typeof import('vitest').beforeEach;
  export const afterEach: typeof import('vitest').afterEach;

  // Mocking
  export type MockInstance<T extends (...args: any[]) => any> = {
    getMockName(): string;
    mock: {
      calls: Parameters<T>[];
      results: Array<{ type: string; value: ReturnType<T> }>;
      instances: any[];
      contexts: any[];
      lastCall: Parameters<T>;
    };
    mockClear(): MockInstance<T>;
    mockReset(): MockInstance<T>;
    mockRestore(): void;
    mockImplementation(fn: T): MockInstance<T>;
    mockImplementationOnce(fn: T): MockInstance<T>;
    mockName(name: string): MockInstance<T>;
    mockReturnThis(): MockInstance<T>;
    mockReturnValue(value: ReturnType<T>): MockInstance<T>;
    mockReturnValueOnce(value: ReturnType<T>): MockInstance<T>;
    mockResolvedValue<U extends ReturnType<T>>(value: Awaited<U>): MockInstance<T>;
    mockResolvedValueOnce<U extends ReturnType<T>>(value: Awaited<U>): MockInstance<T>;
    mockRejectedValue(value: unknown): MockInstance<T>;
    mockRejectedValueOnce(value: unknown): MockInstance<T>;
  };

  // Vi object
  export const vi: {
    fn<T extends (...args: any[]) => any>(implementation?: T): MockInstance<T>;
    spyOn<T extends object, M extends keyof T>(
      object: T,
      method: M,
      accessType?: 'get' | 'set',
    ): MockInstance<T[M]>;
    mock: typeof import('vitest').vi.mock;
    unmock: typeof import('vitest').vi.unmock;
    doMock: typeof import('vitest').vi.doMock;
    stubGlobal: typeof import('vitest').vi.stubGlobal;
    unstubAllGlobals: typeof import('vitest').vi.unstubAllGlobals;
    stubEnv: typeof import('vitest').vi.stubEnv;
    unstubAllEnvs: typeof import('vitest').vi.unstubAllEnvs;
    setSystemTime: typeof import('vitest').vi.setSystemTime;
    advanceTimersByTime: typeof import('vitest').vi.advanceTimersByTime;
    runAllTimers: typeof import('vitest').vi.runAllTimers;
    clearAllTimers: typeof import('vitest').vi.clearAllTimers;
    restoreCurrentDate: typeof import('vitest').vi.restoreCurrentDate;
  };
}

declare module 'npm:@testing-library/jest-dom' {
  export * from '@testing-library/jest-dom';
}

declare module 'npm:@testing-library/jest-dom/vitest' {
  export * from '@testing-library/jest-dom/vitest';
}
