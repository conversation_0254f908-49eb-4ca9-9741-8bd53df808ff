# MVS-VR Monitoring System

This directory contains the enhanced monitoring system for the MVS-VR platform, providing comprehensive monitoring, alerting, user segmentation analysis, A/B testing integration, and predictive analysis capabilities.

## Components

### 1. Metrics Collector

The core metrics collection service that gathers system, application, and business metrics from various components of the MVS-VR platform.

- **Port**: 9090
- **Metrics Endpoint**: `/metrics`
- **Key Features**:
  - Server metrics (CPU, memory, disk)
  - Application metrics (HTTP requests, active users)
  - Business metrics (asset uploads, showroom visits)
  - Authentication metrics (login attempts, MFA usage)
  - Asset delivery metrics (delivery times, CDN performance)

### 2. Alert Manager

Manages alerts based on predefined thresholds and sends notifications through various channels.

- **Port**: 9096
- **API Endpoints**:
  - `/api/alerts` - Get active alerts
  - `/api/alert-config` - Get/update alert configuration
  - `/api/alerts/:id/acknowledge` - Acknowledge an alert
  - `/api/alerts/:id/resolve` - Resolve an alert
- **Notification Channels**:
  - Slack
  - Email
  - Dashboard
- **Key Features**:
  - Performance degradation alerts
  - High error rate alerts
  - Fine-tuned thresholds to reduce false positives
  - Authentication security alerts
  - Asset delivery monitoring

### 3. User Segment Analyzer

Analyzes user behavior and segments users based on their activity patterns.

- **Port**: 9095
- **Metrics Endpoint**: `/metrics`
- **User Segments**:
  - Activity-based segments (power users, new users, inactive users, returning users)
  - Role-based segments (vendor admin, client user)
  - Device-based segments (mobile, desktop, VR)
  - Time-based segments (morning, afternoon, evening, weekend users)
  - Feature usage segments (asset uploaders, showroom viewers, configuration users)
  - Performance experience segments (high latency users, error-prone users)
- **Key Features**:
  - User activity tracking
  - Behavioral segmentation
  - Segment performance analysis
  - Segment-based targeting

### 4. A/B Test Monitor

Integrates with the A/B testing framework to monitor performance metrics for each test variant.

- **Port**: 9097
- **Metrics Endpoint**: `/metrics`
- **Key Features**:
  - Performance comparison between variants
  - Error rate comparison
  - User engagement metrics
  - Statistical significance calculation
  - Optimization strategy comparison
  - Performance improvement metrics
  - Engagement improvement metrics
  - Conversion improvement metrics
  - Overall strategy scoring

### 5. Predictive Analyzer

Implements predictive analysis to anticipate performance issues before they occur.

- **Port**: 9098
- **Metrics Endpoint**: `/metrics`
- **API Endpoints**:
  - `/api/scaling-recommendations` - Get scaling recommendations
- **Key Features**:
  - Resource usage prediction
  - Application metrics prediction
  - Anomaly detection
  - Bottleneck prediction

### 6. Monitoring Service

Integrates all monitoring components and provides a unified API for the dashboard.

- **Port**: 9099
- **API Endpoints**:
  - `/api/dashboard` - Get dashboard data
  - `/api/health` - Get component health status
  - `/metrics` - Get all metrics
  - `/api/user-segments` - Get user segments
  - `/api/ab-tests` - Get A/B tests
  - `/api/predictions` - Get predictions

## Infrastructure

The monitoring system uses the following infrastructure components:

- **Prometheus**: Time-series database for metrics storage
- **Grafana**: Visualization and dashboarding
- **AlertManager**: Alert routing and notification
- **Node Exporter**: Host metrics collection
- **cAdvisor**: Container metrics collection

## Setup

### Prerequisites

- Docker and Docker Compose
- Node.js 16+
- Supabase account with API keys

### Environment Variables

Create a `.env` file in the `monitoring` directory with the following variables:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_service_key
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_secure_password
SLACK_WEBHOOK=your_slack_webhook_url
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
```

### Starting the Monitoring System

```bash
cd monitoring
docker-compose up -d
```

This will start all monitoring components in Docker containers.

### Accessing the Dashboards

- **Grafana**: <http://localhost:3000>
- **Prometheus**: <http://localhost:9090>
- **AlertManager**: <http://localhost:9093>

## Usage

### Monitoring Dashboards

The monitoring dashboards are available in Grafana at <http://localhost:3000>. They provide a comprehensive view of the system, including:

#### Main Dashboard

- System metrics (CPU, memory, disk)
- Application metrics (API requests, active users)
- Business metrics (asset uploads, showroom visits)
- User segments
- A/B test results
- Predictions and anomalies

#### Authentication Monitoring Dashboard

- Login attempts and success/failure rates
- MFA enrollment rates
- Token refresh performance
- Session information

#### Asset Delivery Monitoring Dashboard

- Asset delivery rates
- CDN cache hit rates
- Delivery performance by asset type
- Bandwidth usage

#### Performance Alerts Dashboard

- Active performance degradation alerts
- API performance metrics
- Database performance metrics
- Error rates

#### User Segmentation Dashboard

- User segment distribution
- Performance metrics by segment
- Error rates by segment
- Session duration by segment

#### A/B Testing Optimization Dashboard

- Optimization strategy scores
- Performance improvements
- Engagement improvements
- Conversion improvements
- Statistical significance

### Alerts

Alerts are configured in the `alertmanager/alertmanager.yml` file and the rules are defined in the `prometheus/rules/` directory. The system includes:

- **Performance Degradation Alerts**: Detect when performance metrics degrade compared to historical baselines
- **High Error Rate Alerts**: Detect when error rates exceed thresholds
- **Authentication Security Alerts**: Detect suspicious authentication patterns
- **Resource Utilization Alerts**: Detect when system resources are running low
- **Business Metric Alerts**: Detect anomalies in business metrics

Alert thresholds have been fine-tuned to reduce false positives while ensuring important issues are caught. You can customize alert thresholds and notification channels.

### User Segmentation

User segmentation is performed automatically by the User Segment Analyzer. You can view the segments in the Grafana dashboard or through the API.

### A/B Testing

A/B test monitoring is integrated with the existing A/B testing framework. You can view test results in the Grafana dashboard or through the API.

### Predictive Analysis

Predictive analysis is performed automatically by the Predictive Analyzer. You can view predictions in the Grafana dashboard or through the API.

## API Reference

### Monitoring Service API

- `GET /api/dashboard` - Get dashboard data
- `GET /api/health` - Get component health status
- `GET /metrics` - Get all metrics
- `GET /api/user-segments` - Get user segments
- `GET /api/user-segments/:segment/metrics` - Get segment metrics
- `GET /api/ab-tests` - Get A/B tests
- `GET /api/ab-tests/:id/metrics` - Get test metrics
- `GET /api/predictions` - Get predictions
- `GET /api/anomalies` - Get anomalies

### Alert Manager API

- `GET /api/alerts` - Get alerts
- `GET /api/alert-config` - Get alert configuration
- `PUT /api/alert-config/:id` - Update alert configuration
- `POST /api/alerts/:id/acknowledge` - Acknowledge an alert
- `POST /api/alerts/:id/resolve` - Resolve an alert

## Extending the Monitoring System

### Adding New Metrics

To add new metrics, modify the appropriate service file and add the metric definition. For example, to add a new metric to the User Segment Analyzer:

```javascript
// In user-segment-analyzer.js
const newMetric = new promClient.Gauge({
  name: 'mvs_vr_new_metric',
  help: 'Description of the new metric',
  labelNames: ['label1', 'label2']
});

register.registerMetric(newMetric);
```

### Adding New User Segments

To add a new user segment, modify the `USER_SEGMENTS` object in `user-segment-analyzer.js` and add the segmentation logic in the `identifyUserSegments` function.

### Adding New Alerts

To add a new alert, modify the `defaultAlertConfig` object in `alert-manager.js` and add the alert definition.

### Adding New Predictions

To add a new prediction, modify the appropriate function in `predictive-analyzer.js` and add the prediction logic.

## Troubleshooting

### Common Issues

- **Component not starting**: Check the Docker logs for errors
- **Metrics not showing up**: Verify that the component is running and the metrics endpoint is accessible
- **Alerts not firing**: Check the alert configuration and thresholds

### Logs

Logs are available in the Docker container logs:

```bash
docker logs mvs-vr-metrics-collector
docker logs mvs-vr-alert-manager-service
docker logs mvs-vr-user-segment-analyzer
docker logs mvs-vr-ab-test-monitor
docker logs mvs-vr-predictive-analyzer
docker logs mvs-vr-monitoring-service
```

## Contributing

When contributing to the monitoring system, please follow these guidelines:

1. Add appropriate documentation for new features
2. Add unit tests for new functionality
3. Follow the existing code style
4. Update the README with any new components or features
