/**
 * Jest to Vitest Migration Script
 * 
 * This script migrates Jest test files to Vitest syntax.
 * It handles:
 * - Replacing jest.mock with vi.mock
 * - Replacing jest.fn with vi.fn
 * - Replacing jest.spyOn with vi.spyOn
 * - Replacing afterEach(wrapper.destroy) with afterEach(() => wrapper.unmount())
 * - Adding import { vi } from 'vitest' where needed
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Get all test files
const testFiles = glob.sync('src/**/*.{test,spec}.{js,ts,jsx,tsx}', {
  cwd: path.resolve(__dirname, '..'),
  absolute: true,
});

console.log(`Found ${testFiles.length} test files to migrate`);

// Process each file
testFiles.forEach(filePath => {
  console.log(`Processing ${path.basename(filePath)}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if file already uses Vitest
  if (content.includes('import { vi }') || content.includes("import {vi}")) {
    console.log(`  Skipping - already using Vitest`);
    return;
  }
  
  // Create a backup
  const backupPath = `${filePath}.jest-backup`;
  if (!fs.existsSync(backupPath)) {
    fs.writeFileSync(backupPath, content);
    console.log(`  Created backup at ${path.basename(backupPath)}`);
  }
  
  // Replace imports
  if (content.includes('import { mount }') || content.includes('import {mount}')) {
    // Already has mount import, just need to add vi
    if (!content.includes('import { vi }') && !content.includes('import {vi}')) {
      if (content.includes('import { describe, it, expect')) {
        content = content.replace(
          /import \{ describe, it, expect([^}]*)\} from ['"](@?\w+)['"];?/,
          'import { describe, it, expect$1, vi } from \'vitest\';'
        );
      } else {
        content = content.replace(
          /import \{ mount \} from ['"](@vue\/test-utils)['"];?/,
          'import { mount } from \'@vue/test-utils\';\nimport { vi } from \'vitest\';'
        );
      }
    }
  }
  
  // Replace jest.mock with vi.mock
  content = content.replace(/jest\.mock/g, 'vi.mock');
  
  // Replace jest.fn with vi.fn
  content = content.replace(/jest\.fn/g, 'vi.fn');
  
  // Replace jest.spyOn with vi.spyOn
  content = content.replace(/jest\.spyOn/g, 'vi.spyOn');
  
  // Replace afterEach(wrapper.destroy) with afterEach(() => wrapper.unmount())
  content = content.replace(
    /afterEach\(\s*\(\)\s*=>\s*wrapper\.destroy\(\)\s*\);?/g,
    'afterEach(() => wrapper.unmount());'
  );
  content = content.replace(
    /afterEach\(\s*wrapper\.destroy\s*\);?/g,
    'afterEach(() => wrapper.unmount());'
  );
  
  // Replace wrapper.destroy() with wrapper.unmount()
  content = content.replace(/wrapper\.destroy\(\)/g, 'wrapper.unmount()');
  
  // Save the modified file
  fs.writeFileSync(filePath, content);
  console.log(`  Updated ${path.basename(filePath)}`);
  
  // Create a Vitest version of the file
  const vitestPath = filePath.replace(/\.(test|spec)\.(js|ts|jsx|tsx)$/, '.vitest.$1.$2');
  if (!fs.existsSync(vitestPath)) {
    fs.writeFileSync(vitestPath, content);
    console.log(`  Created Vitest version at ${path.basename(vitestPath)}`);
  }
});

console.log('Migration completed!');
