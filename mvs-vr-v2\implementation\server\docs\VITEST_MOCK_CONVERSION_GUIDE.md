# Vitest Mock Conversion Guide

This guide provides detailed instructions for converting Jest mocks to Vitest mocks in the MVS-VR project.

## Table of Contents

1. [Basic Mock Conversions](#basic-mock-conversions)
2. [Module Mocking](#module-mocking)
3. [Class Mocking](#class-mocking)
4. [Timer Mocks](#timer-mocks)
5. [Using the Mock Helpers](#using-the-mock-helpers)
6. [Common Issues and Solutions](#common-issues-and-solutions)

## Basic Mock Conversions

| Jest | Vitest |
|------|--------|
| `jest.fn()` | `vi.fn()` |
| `jest.spyOn(object, 'method')` | `vi.spyOn(object, 'method')` |
| `jest.mock('module-name')` | `vi.mock('module-name')` |
| `jest.clearAllMocks()` | `vi.clearAllMocks()` |
| `jest.resetAllMocks()` | `vi.resetAllMocks()` |
| `jest.restoreAllMocks()` | `vi.restoreAllMocks()` |

## Module Mocking

### Jest Style

```javascript
// Jest
jest.mock('module-name', () => {
  return jest.fn().mockImplementation(() => ({
    method1: jest.fn(),
    method2: jest.fn(),
  }));
});
```

### Vitest Style

```javascript
// Vitest - For modules with default export
vi.mock('module-name', () => {
  const MockModule = vi.fn().mockImplementation(() => ({
    method1: vi.fn(),
    method2: vi.fn(),
  }));
  return { default: MockModule };
});

// Vitest - For modules with named exports
vi.mock('module-name', () => {
  return {
    namedExport1: vi.fn(),
    namedExport2: vi.fn(),
  };
});

// Using mock helpers
import { createMockModule, createMockClass } from '../helpers/mock-helpers';

vi.mock('module-name', () => {
  const MockClass = createMockClass({
    method1: () => 'mocked-value-1',
    method2: () => 'mocked-value-2',
  });
  
  return createMockModule(MockClass, {
    namedExport: vi.fn(),
  });
});
```

## Class Mocking

### Jest Style

```javascript
// Jest
const mockInstance = {
  method1: jest.fn(),
  method2: jest.fn(),
};

const MockClass = jest.fn().mockImplementation(() => mockInstance);

jest.mock('class-module', () => {
  return MockClass;
});
```

### Vitest Style

```javascript
// Vitest
const mockInstance = {
  method1: vi.fn(),
  method2: vi.fn(),
};

const MockClass = vi.fn().mockImplementation(() => mockInstance);

vi.mock('class-module', () => {
  return { default: MockClass };
});

// Using mock helpers
import { createMockClass } from '../helpers/mock-helpers';

const MockClass = createMockClass({
  method1: () => 'mocked-value-1',
  method2: () => 'mocked-value-2',
});

vi.mock('class-module', () => {
  return { default: MockClass };
});
```

## Timer Mocks

### Jest Style

```javascript
// Jest
jest.useFakeTimers();
jest.advanceTimersByTime(1000);
jest.runAllTimers();
jest.useRealTimers();
```

### Vitest Style

```javascript
// Vitest
vi.useFakeTimers();
vi.advanceTimersByTime(1000);
vi.runAllTimers();
vi.useRealTimers();
```

## Using the Mock Helpers

The project includes helper functions to standardize mock creation. These are available in `tests/helpers/mock-helpers.ts`.

```javascript
import { 
  createMockFn, 
  createMockObject, 
  createMockClass, 
  createMockModule,
  createMockSupabaseClient,
  createMockExpressRequest,
  createMockExpressResponse,
  createMockExpressNext
} from '../helpers/mock-helpers';

// Create a mock function
const mockFn = createMockFn(() => 'mocked-value');

// Create a mock object
const mockObject = createMockObject({
  property1: 'value1',
  method1: () => 'mocked-method',
});

// Create a mock class
const MockClass = createMockClass({
  method1: () => 'mocked-value-1',
  method2: () => 'mocked-value-2',
});

// Create a mock module
const mockModule = createMockModule(MockClass, {
  namedExport: vi.fn(),
});

// Mock Supabase client
const mockSupabase = createMockSupabaseClient();

// Mock Express objects
const mockReq = createMockExpressRequest({ method: 'POST', body: { key: 'value' } });
const mockRes = createMockExpressResponse();
const mockNext = createMockExpressNext();
```

## Common Issues and Solutions

### Issue: Module has no default export

**Problem:**
```
TypeError: module_1.default is not a function
```

**Solution:**
```javascript
// Before
vi.mock('module-name', () => {
  return vi.fn();
});

// After
vi.mock('module-name', () => {
  return { default: vi.fn() };
});
```

### Issue: Named exports are missing

**Problem:**
```
TypeError: Cannot destructure property 'namedExport' of 'module_1' as it is undefined.
```

**Solution:**
```javascript
// Before
vi.mock('module-name', () => {
  return { default: vi.fn() };
});

// After
vi.mock('module-name', () => {
  return { 
    default: vi.fn(),
    namedExport: vi.fn()
  };
});
```

### Issue: Mock implementation not working

**Problem:**
```
Expected mock function to have been called with: ["arg1", "arg2"]
```

**Solution:**
```javascript
// Before
const mockFn = vi.fn();
mockFn.mockImplementation(() => 'mocked-value');

// After
const mockFn = vi.fn().mockImplementation(() => 'mocked-value');
```

### Issue: Type errors with mocks

**Problem:**
```
Type 'Mock<any, any>' is not assignable to type 'YourType'
```

**Solution:**
```typescript
// Before
(myFunction as jest.Mock).mockReturnValue('test');

// After
(myFunction as ReturnType<typeof vi.fn>).mockReturnValue('test');
```
