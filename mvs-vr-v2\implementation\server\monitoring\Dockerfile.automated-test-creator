FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/automated-test-creator.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV AUTOMATED_TEST_CREATOR_PORT=9106

# Expose port
EXPOSE 9106

# Start the service
CMD ["node", "monitoring/automated-test-creator.js"]
