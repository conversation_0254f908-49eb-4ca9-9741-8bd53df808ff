FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/churn-predictor.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV CHURN_PREDICTOR_PORT=9104

# Expose port
EXPOSE 9104

# Start the service
CMD ["node", "monitoring/churn-predictor.js"]
