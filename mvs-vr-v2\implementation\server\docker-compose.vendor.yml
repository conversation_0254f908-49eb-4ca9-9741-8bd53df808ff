version: '3.8'

services:
  # Supabase local development
  supabase-db:
    image: supabase/postgres:*********
    ports:
      - '54322:5432'
    environment:
      - POSTGRES_PASSWORD=postgres
    volumes:
      - supabase-db-data:/var/lib/postgresql/data
    networks:
      - mvs-vr-vendor-network

  # Directus CMS
  directus:
    image: directus/directus:latest
    ports:
      - '8055:8055'
    environment:
      # Database Connection
      - DB_CLIENT=pg
      - DB_HOST=supabase-db
      - DB_PORT=5432
      - DB_DATABASE=postgres
      - DB_USER=postgres
      - DB_PASSWORD=postgres

      # Directus Configuration
      - KEY=directus-secret-key
      - SECRET=directus-secret
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin

      # Directus Settings
      - PUBLIC_URL=http://localhost:8055
      - LOG_LEVEL=info
      - LOG_STYLE=pretty

      # Cache Configuration
      - CACHE_ENABLED=true
      - CACHE_STORE=memory
      - CACHE_TTL=300

      # Storage Configuration
      - STORAGE_LOCATIONS=local
      - STORAGE_LOCAL_ROOT=/directus/uploads

      # Security Settings
      - CORS_ENABLED=true
      - CORS_ORIGIN=*
      - RATE_LIMITER_ENABLED=true
      - RATE_LIMITER_STORE=memory
      - RATE_LIMITER_POINTS=50
      - RATE_LIMITER_DURATION=1

      # Real-time Settings
      - WEBSOCKETS_ENABLED=true
    volumes:
      - directus-uploads:/directus/uploads
      - ./directus/extensions:/directus/extensions
      - ./directus/snapshots:/directus/snapshots
    networks:
      - mvs-vr-vendor-network
    depends_on:
      - supabase-db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8055/server/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  # Redis for Directus caching (optional but recommended)
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mvs-vr-vendor-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

networks:
  mvs-vr-vendor-network:
    driver: bridge

volumes:
  supabase-db-data:
  directus-uploads:
  redis-data:
