{"extends": ["../.eslintrc.json", "plugin:vitest/recommended", "plugin:testing-library/dom"], "plugins": ["vitest", "testing-library", "jest-dom"], "env": {"jest": true, "vitest/env": true}, "settings": {"import/resolver": {"typescript": {"project": "./tsconfig.json"}}}, "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "vitest/expect-expect": ["error", {"assertFunctionNames": ["expect", "assert*"]}], "vitest/no-test-return-statement": "error", "testing-library/await-async-queries": "error", "testing-library/no-await-sync-queries": "error", "testing-library/no-container": "error", "testing-library/no-node-access": "error", "testing-library/prefer-screen-queries": "error", "jest-dom/prefer-checked": "error", "jest-dom/prefer-empty": "error", "jest-dom/prefer-enabled-disabled": "error", "jest-dom/prefer-focus": "error", "jest-dom/prefer-in-document": "error", "jest-dom/prefer-required": "error", "jest-dom/prefer-to-have-attribute": "error", "jest-dom/prefer-to-have-text-content": "error", "jest-dom/prefer-to-have-value": "error"}, "overrides": [{"files": ["**/*.{test,spec,vitest}.{ts,tsx}", "setup/**/*.ts"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "import/no-extraneous-dependencies": ["error", {"devDependencies": true}]}}]}