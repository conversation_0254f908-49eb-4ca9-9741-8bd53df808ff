/**
 * Automated Disaster Recovery Testing
 * 
 * This script implements automated disaster recovery testing to validate
 * recovery procedures and measure recovery time objectives (RTOs).
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync, exec } = require('child_process');
const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
const { Pool } = require('pg');
const axios = require('axios');
const rtoMeasurement = require('./rto-measurement');
const { Logger } = require('../../services/integration/logger');
const { logger } = require('../shared/utils/logger');

// Promisify functions
const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);

// Create logger
const logger = new Logger();

// Configuration
const config = {
  testEnvironment: process.env.DR_TEST_ENVIRONMENT || 'dr-test',
  primaryRegion: process.env.PRIMARY_REGION || 'us-east-1',
  secondaryRegion: process.env.SECONDARY_REGION || 'us-west-2',
  database: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DB || 'postgres'
  },
  buckets: {
    database: {
      primary: process.env.PRIMARY_DB_BUCKET || 'mvs-vr-db-backups',
      secondary: process.env.SECONDARY_DB_BUCKET || 'mvs-vr-db-backups-dr'
    },
    files: {
      primary: process.env.PRIMARY_FILES_BUCKET || 'mvs-vr-file-backups',
      secondary: process.env.SECONDARY_FILES_BUCKET || 'mvs-vr-file-backups-dr'
    },
    config: {
      primary: process.env.PRIMARY_CONFIG_BUCKET || 'mvs-vr-config-backups',
      secondary: process.env.SECONDARY_CONFIG_BUCKET || 'mvs-vr-config-backups-dr'
    }
  },
  services: [
    {
      name: 'api',
      url: process.env.API_URL || 'http://localhost:3000/health',
      dependencies: ['database.main']
    },
    {
      name: 'admin',
      url: process.env.ADMIN_URL || 'http://localhost:3001/health',
      dependencies: ['api']
    },
    {
      name: 'vendor',
      url: process.env.VENDOR_URL || 'http://localhost:3002/health',
      dependencies: ['api']
    }
  ],
  reportPath: path.join(__dirname, '../../logs/dr-test-reports'),
  tempDir: path.join(__dirname, '../../temp/dr-test')
};

/**
 * Create S3 client for a specific region
 * @param {string} region - AWS region
 * @returns {S3Client} S3 client
 */
function createS3Client(region) {
  return new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
}

/**
 * Create database pool
 * @param {Object} options - Database options
 * @returns {Pool} Database pool
 */
function createDatabasePool(options = {}) {
  return new Pool({
    host: options.host || config.database.host,
    port: options.port || config.database.port,
    user: options.user || config.database.user,
    password: options.password || config.database.password,
    database: options.database || config.database.database
  });
}

/**
 * Test database recovery
 * @returns {Promise<Object>} Test results
 */
async function testDatabaseRecovery() {
  const incidentId = `dr-test-db-${Date.now()}`;
  const recoveryId = rtoMeasurement.startRecovery(
    'database.main',
    incidentId,
    'DR test: Database recovery'
  );
  
  logger.info('Starting database recovery test');
  
  try {
    // Create temp directory
    await mkdirAsync(config.tempDir, { recursive: true });
    
    // Get latest backup from secondary region
    const s3Client = createS3Client(config.secondaryRegion);
    const listCommand = {
      Bucket: config.buckets.database.secondary,
      MaxKeys: 10
    };
    
    const response = await s3Client.send(new GetObjectCommand(listCommand));
    const backups = response.Contents || [];
    
    if (backups.length === 0) {
      throw new Error('No database backups found in secondary region');
    }
    
    // Sort by last modified (newest first)
    backups.sort((a, b) => b.LastModified - a.LastModified);
    
    const latestBackup = backups[0];
    logger.info(`Found latest backup: ${latestBackup.Key}`);
    
    // Download backup
    const backupPath = path.join(config.tempDir, path.basename(latestBackup.Key));
    const getCommand = {
      Bucket: config.buckets.database.secondary,
      Key: latestBackup.Key
    };
    
    const backupResponse = await s3Client.send(new GetObjectCommand(getCommand));
    await writeFileAsync(backupPath, backupResponse.Body);
    
    logger.info(`Downloaded backup to ${backupPath}`);
    
    // Create test database
    const testDbName = `dr_test_${Date.now()}`;
    await execAsync(`PGPASSWORD=${config.database.password} createdb -h ${config.database.host} -p ${config.database.port} -U ${config.database.user} ${testDbName}`);
    
    logger.info(`Created test database: ${testDbName}`);
    
    // Restore backup
    await execAsync(`PGPASSWORD=${config.database.password} pg_restore -h ${config.database.host} -p ${config.database.port} -U ${config.database.user} -d ${testDbName} ${backupPath}`);
    
    logger.info(`Restored backup to test database`);
    
    // Verify database
    const pool = createDatabasePool({ database: testDbName });
    
    try {
      // Check if tables exist
      const tablesResult = await pool.query(`
        SELECT count(*) as table_count
        FROM information_schema.tables
        WHERE table_schema = 'public'
      `);
      
      const tableCount = parseInt(tablesResult.rows[0].table_count, 10);
      
      if (tableCount === 0) {
        throw new Error('No tables found in restored database');
      }
      
      logger.info(`Verified database: ${tableCount} tables found`);
      
      // Check if data exists
      const dataResult = await pool.query(`
        SELECT
          (SELECT count(*) FROM users) as user_count,
          (SELECT count(*) FROM vendors) as vendor_count,
          (SELECT count(*) FROM assets) as asset_count
      `);
      
      const { user_count, vendor_count, asset_count } = dataResult.rows[0];
      
      logger.info(`Verified data: ${user_count} users, ${vendor_count} vendors, ${asset_count} assets`);
      
      // End recovery measurement
      const recovery = await rtoMeasurement.endRecovery(
        recoveryId,
        'success',
        `Database recovery test completed successfully. Restored ${tableCount} tables with ${user_count} users, ${vendor_count} vendors, ${asset_count} assets.`
      );
      
      return {
        success: true,
        recoveryId,
        recovery,
        details: {
          tableCount,
          userCount: parseInt(user_count, 10),
          vendorCount: parseInt(vendor_count, 10),
          assetCount: parseInt(asset_count, 10)
        }
      };
    } finally {
      // Close pool
      await pool.end();
      
      // Drop test database
      await execAsync(`PGPASSWORD=${config.database.password} dropdb -h ${config.database.host} -p ${config.database.port} -U ${config.database.user} ${testDbName}`);
      
      logger.info(`Dropped test database: ${testDbName}`);
    }
  } catch (error) {
    logger.error(`Database recovery test failed: ${error.message}`, { error });
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );
    
    return {
      success: false,
      recoveryId,
      recovery,
      error: error.message
    };
  }
}

/**
 * Test file storage recovery
 * @returns {Promise<Object>} Test results
 */
async function testFileStorageRecovery() {
  const incidentId = `dr-test-files-${Date.now()}`;
  const recoveryId = rtoMeasurement.startRecovery(
    'storage.assets',
    incidentId,
    'DR test: File storage recovery'
  );
  
  logger.info('Starting file storage recovery test');
  
  try {
    // Create temp directory
    await mkdirAsync(config.tempDir, { recursive: true });
    
    // Get latest backup from secondary region
    const s3Client = createS3Client(config.secondaryRegion);
    const listCommand = {
      Bucket: config.buckets.files.secondary,
      MaxKeys: 10
    };
    
    const response = await s3Client.send(new GetObjectCommand(listCommand));
    const backups = response.Contents || [];
    
    if (backups.length === 0) {
      throw new Error('No file backups found in secondary region');
    }
    
    // Sort by last modified (newest first)
    backups.sort((a, b) => b.LastModified - a.LastModified);
    
    const latestBackup = backups[0];
    logger.info(`Found latest backup: ${latestBackup.Key}`);
    
    // Download backup
    const backupPath = path.join(config.tempDir, path.basename(latestBackup.Key));
    const getCommand = {
      Bucket: config.buckets.files.secondary,
      Key: latestBackup.Key
    };
    
    const backupResponse = await s3Client.send(new GetObjectCommand(getCommand));
    await writeFileAsync(backupPath, backupResponse.Body);
    
    logger.info(`Downloaded backup to ${backupPath}`);
    
    // Extract backup
    const extractDir = path.join(config.tempDir, 'extract');
    await mkdirAsync(extractDir, { recursive: true });
    
    if (backupPath.endsWith('.zip')) {
      await execAsync(`unzip -o ${backupPath} -d ${extractDir}`);
    } else if (backupPath.endsWith('.tar.gz') || backupPath.endsWith('.tgz')) {
      await execAsync(`tar -xzf ${backupPath} -C ${extractDir}`);
    } else if (backupPath.endsWith('.tar')) {
      await execAsync(`tar -xf ${backupPath} -C ${extractDir}`);
    } else {
      throw new Error(`Unsupported backup format: ${path.extname(backupPath)}`);
    }
    
    logger.info(`Extracted backup to ${extractDir}`);
    
    // Verify files
    const fileCount = parseInt(execSync(`find ${extractDir} -type f | wc -l`).toString().trim(), 10);
    
    if (fileCount === 0) {
      throw new Error('No files found in extracted backup');
    }
    
    logger.info(`Verified files: ${fileCount} files found`);
    
    // End recovery measurement
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'success',
      `File storage recovery test completed successfully. Restored ${fileCount} files.`
    );
    
    return {
      success: true,
      recoveryId,
      recovery,
      details: {
        fileCount
      }
    };
  } catch (error) {
    logger.error(`File storage recovery test failed: ${error.message}`, { error });
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );
    
    return {
      success: false,
      recoveryId,
      recovery,
      error: error.message
    };
  }
}

/**
 * Test service recovery
 * @param {Object} service - Service configuration
 * @returns {Promise<Object>} Test results
 */
async function testServiceRecovery(service) {
  const incidentId = `dr-test-${service.name}-${Date.now()}`;
  const recoveryId = rtoMeasurement.startRecovery(
    `application.${service.name}`,
    incidentId,
    `DR test: ${service.name} service recovery`
  );
  
  logger.info(`Starting ${service.name} service recovery test`);
  
  try {
    // Check if service is available
    const response = await axios.get(service.url, {
      timeout: 5000,
      validateStatus: () => true
    });
    
    if (response.status !== 200) {
      throw new Error(`Service returned status ${response.status}`);
    }
    
    logger.info(`Service ${service.name} is available`);
    
    // End recovery measurement
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'success',
      `Service recovery test completed successfully. Service is available.`
    );
    
    return {
      success: true,
      recoveryId,
      recovery,
      details: {
        status: response.status,
        data: response.data
      }
    };
  } catch (error) {
    logger.error(`Service recovery test failed: ${error.message}`, { error });
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );
    
    return {
      success: false,
      recoveryId,
      recovery,
      error: error.message
    };
  }
}

/**
 * Run disaster recovery test
 * @returns {Promise<Object>} Test results
 */
async function runDRTest() {
  logger.info('Starting disaster recovery test');
  
  // Create report directory
  await mkdirAsync(config.reportPath, { recursive: true });
  
  const startTime = new Date();
  const results = {
    timestamp: startTime.toISOString(),
    environment: config.testEnvironment,
    duration: null,
    database: null,
    fileStorage: null,
    services: {}
  };
  
  try {
    // Test database recovery
    results.database = await testDatabaseRecovery();
    
    // Test file storage recovery
    results.fileStorage = await testFileStorageRecovery();
    
    // Test service recovery
    for (const service of config.services) {
      results.services[service.name] = await testServiceRecovery(service);
    }
    
    // Calculate duration
    const endTime = new Date();
    results.duration = (endTime - startTime) / 1000; // in seconds
    
    // Generate RTO compliance report
    const rtoReport = rtoMeasurement.generateRtoReport();
    results.rtoReport = rtoReport;
    
    // Save results
    const reportPath = path.join(config.reportPath, `dr-test-${startTime.toISOString().replace(/:/g, '-')}.json`);
    await writeFileAsync(reportPath, JSON.stringify(results, null, 2));
    
    logger.info(`Disaster recovery test completed in ${results.duration} seconds`);
    logger.info(`Report saved to ${reportPath}`);
    
    return results;
  } catch (error) {
    logger.error(`Disaster recovery test failed: ${error.message}`, { error });
    
    // Calculate duration
    const endTime = new Date();
    results.duration = (endTime - startTime) / 1000; // in seconds
    results.error = error.message;
    
    // Save results
    const reportPath = path.join(config.reportPath, `dr-test-${startTime.toISOString().replace(/:/g, '-')}.json`);
    await writeFileAsync(reportPath, JSON.stringify(results, null, 2));
    
    logger.info(`Disaster recovery test failed in ${results.duration} seconds`);
    logger.info(`Report saved to ${reportPath}`);
    
    throw error;
  }
}

// If script is run directly, run DR test
if (require.main === module) {
  runDRTest()
    .then(results => {
      logger.info('Disaster recovery test completed:');
      logger.info(`Duration: ${results.duration} seconds`);
      logger.info(`Database recovery: ${results.database.success ? 'Success' : 'Failed'}`);
      logger.info(`File storage recovery: ${results.fileStorage.success ? 'Success' : 'Failed'}`);
      
      for (const [serviceName, serviceResult] of Object.entries(results.services)) {
        logger.info(`${serviceName} service recovery: ${serviceResult.success ? 'Success' : 'Failed'}`);
      }
      
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runDRTest,
  testDatabaseRecovery,
  testFileStorageRecovery,
  testServiceRecovery
};
