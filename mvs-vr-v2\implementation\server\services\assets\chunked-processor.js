/**
 * Chunked Asset Processor
 *
 * This module provides optimized processing for large assets using chunking
 * and parallel processing.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { pipeline } = require('stream');
const { createReadStream, createWriteStream } = require('fs');
const { createGzip, createGunzip } = require('zlib');
const { Worker } = require('worker_threads');
const crypto = require('crypto');
const os = require('os');
const { Logger } = require('../integration/logger');
const { Gauge, Counter, Histogram } = require('prom-client');

// Promisify functions
const pipelineAsync = promisify(pipeline);
const statAsync = promisify(fs.stat);
const mkdirAsync = promisify(fs.mkdir);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const unlinkAsync = promisify(fs.unlink);

// Create logger
const logger = new Logger();

// Create metrics
const assetProcessingDuration = new Histogram({
  name: 'asset_processing_duration_seconds',
  help: 'Duration of asset processing in seconds',
  labelNames: ['operation', 'asset_type', 'status'],
});

const assetProcessingTotal = new Counter({
  name: 'asset_processing_total',
  help: 'Total number of assets processed',
  labelNames: ['operation', 'asset_type', 'status'],
});

const assetProcessingSize = new Histogram({
  name: 'asset_processing_size_bytes',
  help: 'Size of processed assets in bytes',
  labelNames: ['operation', 'asset_type'],
  buckets: [
    1024 * 1024, // 1 MB
    10 * 1024 * 1024, // 10 MB
    50 * 1024 * 1024, // 50 MB
    100 * 1024 * 1024, // 100 MB
    500 * 1024 * 1024, // 500 MB
    1024 * 1024 * 1024, // 1 GB
    5 * 1024 * 1024 * 1024, // 5 GB
  ],
});

const assetProcessingChunks = new Histogram({
  name: 'asset_processing_chunks',
  help: 'Number of chunks processed',
  labelNames: ['operation', 'asset_type'],
  buckets: [1, 5, 10, 20, 50, 100, 200, 500, 1000],
});

const assetProcessingWorkers = new Gauge({
  name: 'asset_processing_workers',
  help: 'Number of active worker threads',
  labelNames: ['operation'],
});

/**
 * Configuration
 */
const config = {
  tempDir: process.env.ASSET_TEMP_DIR || path.join(os.tmpdir(), 'mvs-vr-assets'),
  chunkSize: parseInt(process.env.ASSET_CHUNK_SIZE || '10485760', 10), // 10 MB default
  maxWorkers: parseInt(
    process.env.ASSET_MAX_WORKERS || Math.max(1, Math.floor(os.cpus().length * 0.75)),
    10,
  ),
  compressionLevel: parseInt(process.env.ASSET_COMPRESSION_LEVEL || '6', 10), // 0-9, higher is better compression but slower
  hashAlgorithm: process.env.ASSET_HASH_ALGORITHM || 'sha256',
};

/**
 * Ensure temporary directory exists
 */
async function ensureTempDir() {
  try {
    await mkdirAsync(config.tempDir, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

/**
 * Generate a unique temporary file path
 * @param {string} prefix - File name prefix
 * @param {string} extension - File extension
 * @returns {string} Temporary file path
 */
function getTempFilePath(prefix, extension) {
  const uniqueId = crypto.randomBytes(8).toString('hex');
  return path.join(config.tempDir, `${prefix}-${uniqueId}${extension ? `.${extension}` : ''}`);
}

/**
 * Split a file into chunks
 * @param {string} filePath - Path to file
 * @param {number} chunkSize - Size of each chunk in bytes
 * @returns {Promise<Array<string>>} Array of chunk file paths
 */
async function splitFileIntoChunks(filePath, chunkSize = config.chunkSize) {
  const stats = await statAsync(filePath);
  const fileSize = stats.size;
  const numChunks = Math.ceil(fileSize / chunkSize);
  const chunkPaths = [];

  logger.info(`Splitting file into chunks`, {
    filePath,
    fileSize,
    chunkSize,
    numChunks,
  });

  // Create read stream
  const readStream = createReadStream(filePath);

  // Process chunks
  let chunkIndex = 0;
  let bytesRead = 0;
  let currentChunkPath = null;
  let currentChunkStream = null;

  // Create a new chunk file
  function createChunkFile() {
    if (currentChunkStream) {
      currentChunkStream.end();
    }

    currentChunkPath = getTempFilePath(`chunk-${chunkIndex}`, 'bin');
    chunkPaths.push(currentChunkPath);
    currentChunkStream = createWriteStream(currentChunkPath);
    chunkIndex++;

    return currentChunkStream;
  }

  // Create first chunk file
  createChunkFile();

  // Process data chunks
  for await (const chunk of readStream) {
    const chunkLength = chunk.length;

    // If adding this chunk would exceed the chunk size, create a new chunk file
    if (bytesRead + chunkLength > chunkSize && bytesRead > 0) {
      createChunkFile();
      bytesRead = 0;
    }

    // Write chunk to current chunk file
    currentChunkStream.write(chunk);
    bytesRead += chunkLength;
  }

  // Close the last chunk file
  if (currentChunkStream) {
    currentChunkStream.end();
  }

  logger.info(`File split into ${chunkPaths.length} chunks`, {
    filePath,
    numChunks: chunkPaths.length,
  });

  return chunkPaths;
}

/**
 * Merge chunks into a single file
 * @param {Array<string>} chunkPaths - Array of chunk file paths
 * @param {string} outputPath - Output file path
 * @returns {Promise<void>}
 */
async function mergeChunks(chunkPaths, outputPath) {
  logger.info(`Merging ${chunkPaths.length} chunks into file`, {
    outputPath,
    numChunks: chunkPaths.length,
  });

  // Create write stream
  const writeStream = createWriteStream(outputPath);

  // Process chunks in order
  for (const chunkPath of chunkPaths) {
    const readStream = createReadStream(chunkPath);
    await pipelineAsync(readStream, writeStream, { end: false });
  }

  // Close write stream
  writeStream.end();

  logger.info(`Chunks merged into file`, {
    outputPath,
    numChunks: chunkPaths.length,
  });
}

/**
 * Process chunks in parallel
 * @param {Array<string>} chunkPaths - Array of chunk file paths
 * @param {Function} processFn - Processing function
 * @param {Object} options - Processing options
 * @returns {Promise<Array<string>>} Array of processed chunk file paths
 */
async function processChunksInParallel(chunkPaths, processFn, options = {}) {
  const { maxWorkers = config.maxWorkers, operation = 'process', assetType = 'unknown' } = options;

  logger.info(`Processing ${chunkPaths.length} chunks in parallel`, {
    numChunks: chunkPaths.length,
    maxWorkers,
  });

  // Track active workers
  let activeWorkers = 0;
  assetProcessingWorkers.set({ operation }, 0);

  // Process chunks
  const processedChunkPaths = [];
  const chunkQueue = [...chunkPaths];

  // Process a chunk
  async function processChunk(chunkPath) {
    const outputPath = getTempFilePath(`processed-${path.basename(chunkPath)}`, 'bin');

    try {
      await processFn(chunkPath, outputPath);
      processedChunkPaths.push(outputPath);
      return outputPath;
    } catch (error) {
      logger.error(`Error processing chunk`, {
        chunkPath,
        error,
      });
      throw error;
    }
  }

  // Process chunks in parallel
  const promises = [];

  while (chunkQueue.length > 0) {
    // Process up to maxWorkers chunks at a time
    while (activeWorkers < maxWorkers && chunkQueue.length > 0) {
      const chunkPath = chunkQueue.shift();
      activeWorkers++;
      assetProcessingWorkers.set({ operation }, activeWorkers);

      const promise = processChunk(chunkPath).finally(() => {
        activeWorkers--;
        assetProcessingWorkers.set({ operation }, activeWorkers);
      });

      promises.push(promise);
    }

    // Wait for at least one worker to finish
    if (promises.length > 0) {
      await Promise.race(promises);
    }
  }

  // Wait for all workers to finish
  await Promise.all(promises);

  logger.info(`Processed ${processedChunkPaths.length} chunks`, {
    numChunks: processedChunkPaths.length,
  });

  // Track metrics
  assetProcessingChunks.observe({ operation, asset_type: assetType }, processedChunkPaths.length);

  return processedChunkPaths;
}

/**
 * Compress a file using adaptive compression
 * @param {string} inputPath - Input file path
 * @param {string} outputPath - Output file path
 * @param {Object} options - Compression options
 * @returns {Promise<Object>} Compression result
 */
async function compressFile(inputPath, outputPath, options = {}) {
  const {
    level = config.compressionLevel,
    assetType = 'unknown',
    mimeType = 'application/octet-stream',
    clientCapabilities = null,
  } = options;

  const startTime = process.hrtime();
  const stats = await statAsync(inputPath);
  const fileSize = stats.size;

  logger.info(`Compressing file`, {
    inputPath,
    outputPath,
    fileSize,
    level,
    assetType,
    mimeType,
  });

  try {
    // Use adaptive compression if available
    const adaptiveCompression = require('./adaptive-compression');

    // Compress file with adaptive compression
    const result = await adaptiveCompression.compressFile(inputPath, outputPath, {
      mimeType,
      clientCapabilities,
      fileSize,
    });

    // Track metrics
    assetProcessingDuration.observe(
      { operation: 'compress', asset_type: assetType, status: 'success' },
      result.duration,
    );
    assetProcessingTotal.inc({ operation: 'compress', asset_type: assetType, status: 'success' });
    assetProcessingSize.observe({ operation: 'compress', asset_type: assetType }, fileSize);

    return result;
  } catch (error) {
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;

    logger.error(
      `Error compressing file with adaptive compression, falling back to basic compression`,
      {
        inputPath,
        outputPath,
        error,
      },
    );

    // Track metrics
    assetProcessingDuration.observe(
      { operation: 'compress', asset_type: assetType, status: 'error' },
      duration,
    );
    assetProcessingTotal.inc({ operation: 'compress', asset_type: assetType, status: 'error' });

    // Fall back to basic gzip compression if adaptive compression fails
    try {
      // Create streams
      const readStream = createReadStream(inputPath);
      const gzipStream = createGzip({ level });
      const writeStream = createWriteStream(outputPath);

      // Compress file
      await pipelineAsync(readStream, gzipStream, writeStream);

      // Calculate compression ratio
      const compressedStats = await statAsync(outputPath);
      const compressedSize = compressedStats.size;
      const compressionRatio = fileSize / compressedSize;

      const [fallbackSeconds, fallbackNanoseconds] = process.hrtime(startTime);
      const fallbackDuration = fallbackSeconds + fallbackNanoseconds / 1e9;

      logger.info(`File compressed with fallback method`, {
        inputPath,
        outputPath,
        fileSize,
        compressedSize,
        compressionRatio,
        duration: fallbackDuration,
      });

      return {
        success: true,
        inputPath,
        outputPath,
        fileSize,
        compressedSize,
        compressionRatio,
        duration: fallbackDuration,
        algorithm: 'gzip',
        level,
      };
    } catch (fallbackError) {
      logger.error(`Error compressing file with fallback method`, {
        inputPath,
        outputPath,
        error: fallbackError,
      });
      throw fallbackError;
    }
  }
}

/**
 * Decompress a gzipped file
 * @param {string} inputPath - Input file path
 * @param {string} outputPath - Output file path
 * @param {Object} options - Decompression options
 * @returns {Promise<void>}
 */
async function decompressFile(inputPath, outputPath, options = {}) {
  const { assetType = 'unknown' } = options;
  const startTime = process.hrtime();
  const stats = await statAsync(inputPath);
  const fileSize = stats.size;

  logger.info(`Decompressing file`, {
    inputPath,
    outputPath,
    fileSize,
  });

  try {
    // Create streams
    const readStream = createReadStream(inputPath);
    const gunzipStream = createGunzip();
    const writeStream = createWriteStream(outputPath);

    // Decompress file
    await pipelineAsync(readStream, gunzipStream, writeStream);

    // Calculate decompression ratio
    const decompressedStats = await statAsync(outputPath);
    const decompressedSize = decompressedStats.size;
    const decompressionRatio = decompressedSize / fileSize;

    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;

    logger.info(`File decompressed`, {
      inputPath,
      outputPath,
      fileSize,
      decompressedSize,
      decompressionRatio,
      duration,
    });

    // Track metrics
    assetProcessingDuration.observe(
      { operation: 'decompress', asset_type: assetType, status: 'success' },
      duration,
    );
    assetProcessingTotal.inc({ operation: 'decompress', asset_type: assetType, status: 'success' });
    assetProcessingSize.observe({ operation: 'decompress', asset_type: assetType }, fileSize);
  } catch (error) {
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;

    logger.error(`Error decompressing file`, {
      inputPath,
      outputPath,
      error,
    });

    // Track metrics
    assetProcessingDuration.observe(
      { operation: 'decompress', asset_type: assetType, status: 'error' },
      duration,
    );
    assetProcessingTotal.inc({ operation: 'decompress', asset_type: assetType, status: 'error' });

    throw error;
  }
}

/**
 * Calculate file hash
 * @param {string} filePath - File path
 * @param {string} algorithm - Hash algorithm
 * @returns {Promise<string>} File hash
 */
async function calculateFileHash(filePath, algorithm = config.hashAlgorithm) {
  const hash = crypto.createHash(algorithm);
  const readStream = createReadStream(filePath);

  for await (const chunk of readStream) {
    hash.update(chunk);
  }

  return hash.digest('hex');
}

/**
 * Process a large asset using chunking and parallel processing
 * @param {string} inputPath - Input file path
 * @param {string} outputPath - Output file path
 * @param {Function} processFn - Processing function for each chunk
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} Processing result
 */
async function processLargeAsset(inputPath, outputPath, processFn, options = {}) {
  const {
    chunkSize = config.chunkSize,
    maxWorkers = config.maxWorkers,
    operation = 'process',
    assetType = 'unknown',
    calculateHash = true,
  } = options;

  const startTime = process.hrtime();
  const stats = await statAsync(inputPath);
  const fileSize = stats.size;

  logger.info(`Processing large asset`, {
    inputPath,
    outputPath,
    fileSize,
    chunkSize,
    maxWorkers,
    operation,
    assetType,
  });

  try {
    // Ensure temp directory exists
    await ensureTempDir();

    // Split file into chunks
    const chunkPaths = await splitFileIntoChunks(inputPath, chunkSize);

    // Process chunks in parallel
    const processedChunkPaths = await processChunksInParallel(chunkPaths, processFn, {
      maxWorkers,
      operation,
      assetType,
    });

    // Merge processed chunks
    await mergeChunks(processedChunkPaths, outputPath);

    // Calculate hash if requested
    let hash = null;
    if (calculateHash) {
      hash = await calculateFileHash(outputPath);
    }

    // Clean up temporary files
    const tempFiles = [...chunkPaths, ...processedChunkPaths];
    await Promise.all(tempFiles.map(tempFile => unlinkAsync(tempFile).catch(() => {})));

    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;

    const processedStats = await statAsync(outputPath);
    const processedSize = processedStats.size;

    logger.info(`Large asset processed`, {
      inputPath,
      outputPath,
      fileSize,
      processedSize,
      duration,
      numChunks: chunkPaths.length,
      hash,
    });

    // Track metrics
    assetProcessingDuration.observe(
      { operation, asset_type: assetType, status: 'success' },
      duration,
    );
    assetProcessingTotal.inc({ operation, asset_type: assetType, status: 'success' });
    assetProcessingSize.observe({ operation, asset_type: assetType }, fileSize);

    return {
      inputPath,
      outputPath,
      fileSize,
      processedSize,
      duration,
      numChunks: chunkPaths.length,
      hash,
    };
  } catch (error) {
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;

    logger.error(`Error processing large asset`, {
      inputPath,
      outputPath,
      error,
    });

    // Track metrics
    assetProcessingDuration.observe(
      { operation, asset_type: assetType, status: 'error' },
      duration,
    );
    assetProcessingTotal.inc({ operation, asset_type: assetType, status: 'error' });

    throw error;
  }
}

module.exports = {
  processLargeAsset,
  compressFile,
  decompressFile,
  calculateFileHash,
  splitFileIntoChunks,
  mergeChunks,
  processChunksInParallel,
  ensureTempDir,
  getTempFilePath,
};
