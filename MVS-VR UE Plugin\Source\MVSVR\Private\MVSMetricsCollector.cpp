#include "MVSMetricsCollector.h"
#include "MVSVRModule.h"
#include "MVSClient.h"
#include "MVSAnalyticsManager.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "JsonObjectConverter.h"
#include "Misc/ScopeLock.h"
#include "Engine/Engine.h"
#include "RenderCore.h"
#include "RHI.h"
#include "RHIResources.h"
#include "EngineStats.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformProcess.h"

DEFINE_LOG_CATEGORY(LogMVSMetrics);

UMVSMetricsCollector::UMVSMetricsCollector()
  : CollectionInterval(10.0f)
  , bEnableMetricsCollection(true)
  , bEnableDetailedMetrics(false)
  , bEnableAutomaticSubmission(true)
  , MetricsSubmissionInterval(60.0f)
  , LastCollectionTime(0.0f)
  , LastSubmissionTime(0.0f)
  , ClientId("")
  , DeviceType("")
  , UEVersion("")
{
}

void UMVSMetricsCollector::Initialize(UMVSClient* InClient)
{
  Client = InClient;
  
  // Get client ID from analytics manager if available
  UMVSAnalyticsManager* AnalyticsManager = Client ? Client->GetAnalyticsManager() : nullptr;
  if (AnalyticsManager)
  {
    ClientId = AnalyticsManager->GetSessionId();
  }
  else
  {
    // Generate a unique client ID if analytics manager is not available
    ClientId = FGuid::NewGuid().ToString();
  }
  
  // Determine device type
  FString Platform = UGameplayStatics::GetPlatformName();
  if (Platform.Contains(TEXT("Windows")))
  {
    DeviceType = TEXT("Windows");
  }
  else if (Platform.Contains(TEXT("Android")))
  {
    DeviceType = TEXT("Android");
  }
  else if (Platform.Contains(TEXT("IOS")))
  {
    DeviceType = TEXT("iOS");
  }
  else if (Platform.Contains(TEXT("Mac")))
  {
    DeviceType = TEXT("Mac");
  }
  else if (Platform.Contains(TEXT("Linux")))
  {
    DeviceType = TEXT("Linux");
  }
  else
  {
    DeviceType = Platform;
  }
  
  // Get UE version
  UEVersion = FEngineVersion::Current().ToString();
  
  // Initialize metrics storage
  PerformanceMetrics = FMVSPerformanceMetrics();
  AssetLoadingMetrics = FMVSAssetLoadingMetrics();
  NetworkMetrics = FMVSNetworkMetrics();
  OfflineModeMetrics = FMVSOfflineModeMetrics();
  InteractionMetrics = FMVSInteractionMetrics();
  LLMMetrics = FMVSLLMMetrics();
  ErrorMetrics = FMVSErrorMetrics();
  
  // Start metrics collection timer
  if (bEnableMetricsCollection)
  {
    StartMetricsCollection();
  }
  
  UE_LOG(LogMVSMetrics, Log, TEXT("MVS Metrics Collector initialized for client %s"), *ClientId);
}

void UMVSMetricsCollector::StartMetricsCollection()
{
  // Register tick function
  if (!TickDelegateHandle.IsValid())
  {
    TickDelegateHandle = FTSTicker::GetCoreTicker().AddTicker(
      FTickerDelegate::CreateUObject(this, &UMVSMetricsCollector::Tick),
      CollectionInterval
    );
    
    UE_LOG(LogMVSMetrics, Log, TEXT("Metrics collection started with interval %.1f seconds"), CollectionInterval);
  }
}

void UMVSMetricsCollector::StopMetricsCollection()
{
  // Unregister tick function
  if (TickDelegateHandle.IsValid())
  {
    FTSTicker::GetCoreTicker().RemoveTicker(TickDelegateHandle);
    TickDelegateHandle.Reset();
    
    UE_LOG(LogMVSMetrics, Log, TEXT("Metrics collection stopped"));
  }
}

bool UMVSMetricsCollector::Tick(float DeltaTime)
{
  if (!bEnableMetricsCollection)
  {
    return true;
  }
  
  // Update collection time
  LastCollectionTime += DeltaTime;
  LastSubmissionTime += DeltaTime;
  
  // Collect metrics
  CollectMetrics();
  
  // Submit metrics if automatic submission is enabled
  if (bEnableAutomaticSubmission && LastSubmissionTime >= MetricsSubmissionInterval)
  {
    SubmitMetrics();
    LastSubmissionTime = 0.0f;
  }
  
  return true;
}

void UMVSMetricsCollector::CollectMetrics()
{
  // Collect performance metrics
  CollectPerformanceMetrics();
  
  // Collect asset loading metrics
  CollectAssetLoadingMetrics();
  
  // Collect network metrics
  CollectNetworkMetrics();
  
  // Collect offline mode metrics
  CollectOfflineModeMetrics();
  
  // Collect detailed metrics if enabled
  if (bEnableDetailedMetrics)
  {
    // These metrics are more expensive to collect, so only do it if detailed metrics are enabled
    CollectDetailedMetrics();
  }
  
  UE_LOG(LogMVSMetrics, Verbose, TEXT("Metrics collected"));
}

void UMVSMetricsCollector::CollectPerformanceMetrics()
{
  FScopeLock Lock(&MetricsLock);
  
  // Get frame rate
  PerformanceMetrics.FrameRate = 1.0f / FApp::GetDeltaTime();
  
  // Get draw calls
  PerformanceMetrics.DrawCalls = GNumDrawCallsRHI;
  
  // Get triangle count
  PerformanceMetrics.TriangleCount = GNumPrimitivesDrawnRHI;
  
  // Get GPU memory usage
  FTextureMemoryStats TextureMemoryStats;
  RHIGetTextureMemoryStats(TextureMemoryStats);
  PerformanceMetrics.GPUMemoryUsage = TextureMemoryStats.DedicatedVideoMemory / (1024.0f * 1024.0f); // Convert to MB
  
  // Get CPU usage
  PerformanceMetrics.CPUUsage = FPlatformTime::GetCPUTime().CPUTimePct * 100.0f;
  
  // Get memory usage
  FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
  PerformanceMetrics.MemoryUsage = MemoryStats.UsedPhysical / (1024.0f * 1024.0f); // Convert to MB
}

void UMVSMetricsCollector::CollectAssetLoadingMetrics()
{
  // This would be implemented to track asset loading times, cache hit rates, etc.
  // For now, we'll just use placeholder values
  
  FScopeLock Lock(&MetricsLock);
  
  // Example: Record asset load time for a texture
  AssetLoadingMetrics.LoadTimes.Add(TEXT("Texture"), 0.05f);
  
  // Example: Record asset load time for a mesh
  AssetLoadingMetrics.LoadTimes.Add(TEXT("Mesh"), 0.2f);
  
  // Example: Record cache hit rate
  AssetLoadingMetrics.CacheHitRates.Add(TEXT("Texture"), 85.0f);
  AssetLoadingMetrics.CacheHitRates.Add(TEXT("Mesh"), 70.0f);
  
  // Example: Record load counts
  FMVSAssetLoadCount TextureLoadCount;
  TextureLoadCount.Success = 10;
  TextureLoadCount.Failure = 1;
  AssetLoadingMetrics.LoadCounts.Add(TEXT("Texture"), TextureLoadCount);
  
  FMVSAssetLoadCount MeshLoadCount;
  MeshLoadCount.Success = 5;
  MeshLoadCount.Failure = 0;
  AssetLoadingMetrics.LoadCounts.Add(TEXT("Mesh"), MeshLoadCount);
}

void UMVSMetricsCollector::CollectNetworkMetrics()
{
  // This would be implemented to track network latency, bandwidth, etc.
  // For now, we'll just use placeholder values
  
  FScopeLock Lock(&MetricsLock);
  
  // Example: Record network latency for API endpoint
  NetworkMetrics.Latencies.Add(TEXT("api/assets"), 120.0f);
  NetworkMetrics.Latencies.Add(TEXT("api/scenes"), 85.0f);
  
  // Example: Record network bandwidth
  NetworkMetrics.Bandwidth.Download = 1500.0f; // kbps
  NetworkMetrics.Bandwidth.Upload = 500.0f;    // kbps
}

void UMVSMetricsCollector::CollectOfflineModeMetrics()
{
  // This would be implemented to track offline mode status, cache size, etc.
  // For now, we'll just use placeholder values
  
  FScopeLock Lock(&MetricsLock);
  
  // Example: Record offline mode status
  OfflineModeMetrics.Active = false;
  
  // Example: Record cache size
  OfflineModeMetrics.CacheSize = 250.0f; // MB
  
  // Example: Record network quality
  OfflineModeMetrics.NetworkQuality = 85.0f; // 0-100 scale
}

void UMVSMetricsCollector::CollectDetailedMetrics()
{
  FScopeLock Lock(&MetricsLock);
  
  // Collect interaction metrics
  CollectInteractionMetrics();
  
  // Collect LLM metrics
  CollectLLMMetrics();
  
  // Collect error metrics
  CollectErrorMetrics();
}

void UMVSMetricsCollector::CollectInteractionMetrics()
{
  // This would be implemented to track user interactions
  // For now, we'll just use placeholder values
  
  // Example: Record interaction counts
  InteractionMetrics.Counts.Add(TEXT("click"), 25);
  InteractionMetrics.Counts.Add(TEXT("drag"), 10);
  
  // Example: Record interaction latencies
  InteractionMetrics.Latencies.Add(TEXT("click"), 15.0f); // ms
  InteractionMetrics.Latencies.Add(TEXT("drag"), 25.0f);  // ms
}

void UMVSMetricsCollector::CollectLLMMetrics()
{
  // This would be implemented to track LLM usage
  // For now, we'll just use placeholder values
  
  // Example: Record LLM request counts
  FMVSLLMRequestCount GPT4Count;
  GPT4Count.Success = 5;
  GPT4Count.Failure = 1;
  LLMMetrics.Requests.Add(TEXT("gpt-4"), GPT4Count);
  
  FMVSLLMRequestCount LocalLLMCount;
  LocalLLMCount.Success = 10;
  LocalLLMCount.Failure = 0;
  LLMMetrics.Requests.Add(TEXT("local-llm"), LocalLLMCount);
  
  // Example: Record LLM response times
  LLMMetrics.ResponseTimes.Add(TEXT("gpt-4"), 2.5f); // seconds
  LLMMetrics.ResponseTimes.Add(TEXT("local-llm"), 0.8f); // seconds
  
  // Example: Record LLM token usage
  FMVSLLMTokenUsage GPT4Usage;
  GPT4Usage.Prompt = 500;
  GPT4Usage.Completion = 200;
  LLMMetrics.TokenUsage.Add(TEXT("gpt-4"), GPT4Usage);
  
  FMVSLLMTokenUsage LocalLLMUsage;
  LocalLLMUsage.Prompt = 300;
  LocalLLMUsage.Completion = 150;
  LLMMetrics.TokenUsage.Add(TEXT("local-llm"), LocalLLMUsage);
}

void UMVSMetricsCollector::CollectErrorMetrics()
{
  // This would be implemented to track errors
  // For now, we'll just use placeholder values
  
  // Example: Record error counts by type and component
  TMap<FString, int32> RenderingErrors;
  RenderingErrors.Add(TEXT("shader_compilation"), 2);
  RenderingErrors.Add(TEXT("texture_loading"), 1);
  ErrorMetrics.Errors.Add(TEXT("rendering"), RenderingErrors);
  
  TMap<FString, int32> NetworkErrors;
  NetworkErrors.Add(TEXT("timeout"), 3);
  NetworkErrors.Add(TEXT("connection_failed"), 1);
  ErrorMetrics.Errors.Add(TEXT("network"), NetworkErrors);
}

void UMVSMetricsCollector::SubmitMetrics()
{
  if (!Client || !Client->IsConnected())
  {
    UE_LOG(LogMVSMetrics, Warning, TEXT("Cannot submit metrics: Client is not connected"));
    return;
  }
  
  // Create metrics payload
  TSharedPtr<FJsonObject> MetricsPayload = CreateMetricsPayload();
  
  // Convert to JSON string
  FString JsonString;
  TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
  FJsonSerializer::Serialize(MetricsPayload.ToSharedRef(), Writer);
  
  // Create HTTP request
  TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
  HttpRequest->SetURL(Client->GetApiUrl() + TEXT("/api/metrics"));
  HttpRequest->SetVerb(TEXT("POST"));
  HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
  HttpRequest->SetHeader(TEXT("Authorization"), TEXT("Bearer ") + Client->GetAuthToken());
  HttpRequest->SetContentAsString(JsonString);
  
  // Set completion callback
  HttpRequest->OnProcessRequestComplete().BindUObject(this, &UMVSMetricsCollector::OnMetricsSubmitted);
  
  // Send request
  HttpRequest->ProcessRequest();
  
  UE_LOG(LogMVSMetrics, Log, TEXT("Metrics submitted"));
}

TSharedPtr<FJsonObject> UMVSMetricsCollector::CreateMetricsPayload()
{
  FScopeLock Lock(&MetricsLock);
  
  TSharedPtr<FJsonObject> Payload = MakeShared<FJsonObject>();
  
  // Add client information
  Payload->SetStringField(TEXT("clientId"), ClientId);
  Payload->SetStringField(TEXT("deviceType"), DeviceType);
  Payload->SetStringField(TEXT("ueVersion"), UEVersion);
  Payload->SetStringField(TEXT("timestamp"), FDateTime::UtcNow().ToString());
  
  // Create metrics object
  TSharedPtr<FJsonObject> MetricsObject = MakeShared<FJsonObject>();
  
  // Add performance metrics
  TSharedPtr<FJsonObject> PerformanceObject = MakeShared<FJsonObject>();
  PerformanceObject->SetNumberField(TEXT("frameRate"), PerformanceMetrics.FrameRate);
  PerformanceObject->SetNumberField(TEXT("drawCalls"), PerformanceMetrics.DrawCalls);
  PerformanceObject->SetNumberField(TEXT("triangleCount"), PerformanceMetrics.TriangleCount);
  PerformanceObject->SetNumberField(TEXT("gpuMemoryUsage"), PerformanceMetrics.GPUMemoryUsage);
  PerformanceObject->SetNumberField(TEXT("cpuUsage"), PerformanceMetrics.CPUUsage);
  PerformanceObject->SetNumberField(TEXT("memoryUsage"), PerformanceMetrics.MemoryUsage);
  MetricsObject->SetObjectField(TEXT("performance"), PerformanceObject);
  
  // Add asset loading metrics
  TSharedPtr<FJsonObject> AssetLoadingObject = MakeShared<FJsonObject>();
  
  // Add load times
  TSharedPtr<FJsonObject> LoadTimesObject = MakeShared<FJsonObject>();
  for (const auto& Pair : AssetLoadingMetrics.LoadTimes)
  {
    LoadTimesObject->SetNumberField(Pair.Key, Pair.Value);
  }
  AssetLoadingObject->SetObjectField(TEXT("loadTimes"), LoadTimesObject);
  
  // Add cache hit rates
  TSharedPtr<FJsonObject> CacheHitRatesObject = MakeShared<FJsonObject>();
  for (const auto& Pair : AssetLoadingMetrics.CacheHitRates)
  {
    CacheHitRatesObject->SetNumberField(Pair.Key, Pair.Value);
  }
  AssetLoadingObject->SetObjectField(TEXT("cacheHitRates"), CacheHitRatesObject);
  
  // Add load counts
  TSharedPtr<FJsonObject> LoadCountsObject = MakeShared<FJsonObject>();
  for (const auto& Pair : AssetLoadingMetrics.LoadCounts)
  {
    TSharedPtr<FJsonObject> CountObject = MakeShared<FJsonObject>();
    CountObject->SetNumberField(TEXT("success"), Pair.Value.Success);
    CountObject->SetNumberField(TEXT("failure"), Pair.Value.Failure);
    LoadCountsObject->SetObjectField(Pair.Key, CountObject);
  }
  AssetLoadingObject->SetObjectField(TEXT("loadCounts"), LoadCountsObject);
  
  MetricsObject->SetObjectField(TEXT("assetLoading"), AssetLoadingObject);
  
  // Add network metrics
  TSharedPtr<FJsonObject> NetworkObject = MakeShared<FJsonObject>();
  
  // Add latencies
  TSharedPtr<FJsonObject> LatenciesObject = MakeShared<FJsonObject>();
  for (const auto& Pair : NetworkMetrics.Latencies)
  {
    LatenciesObject->SetNumberField(Pair.Key, Pair.Value);
  }
  NetworkObject->SetObjectField(TEXT("latencies"), LatenciesObject);
  
  // Add bandwidth
  TSharedPtr<FJsonObject> BandwidthObject = MakeShared<FJsonObject>();
  BandwidthObject->SetNumberField(TEXT("download"), NetworkMetrics.Bandwidth.Download);
  BandwidthObject->SetNumberField(TEXT("upload"), NetworkMetrics.Bandwidth.Upload);
  NetworkObject->SetObjectField(TEXT("bandwidth"), BandwidthObject);
  
  MetricsObject->SetObjectField(TEXT("network"), NetworkObject);
  
  // Add offline mode metrics
  TSharedPtr<FJsonObject> OfflineModeObject = MakeShared<FJsonObject>();
  OfflineModeObject->SetBoolField(TEXT("active"), OfflineModeMetrics.Active);
  OfflineModeObject->SetNumberField(TEXT("cacheSize"), OfflineModeMetrics.CacheSize);
  OfflineModeObject->SetNumberField(TEXT("networkQuality"), OfflineModeMetrics.NetworkQuality);
  MetricsObject->SetObjectField(TEXT("offlineMode"), OfflineModeObject);
  
  // Add detailed metrics if enabled
  if (bEnableDetailedMetrics)
  {
    // Add interaction metrics
    TSharedPtr<FJsonObject> InteractionObject = MakeShared<FJsonObject>();
    
    // Add counts
    TSharedPtr<FJsonObject> CountsObject = MakeShared<FJsonObject>();
    for (const auto& Pair : InteractionMetrics.Counts)
    {
      CountsObject->SetNumberField(Pair.Key, Pair.Value);
    }
    InteractionObject->SetObjectField(TEXT("counts"), CountsObject);
    
    // Add latencies
    TSharedPtr<FJsonObject> InteractionLatenciesObject = MakeShared<FJsonObject>();
    for (const auto& Pair : InteractionMetrics.Latencies)
    {
      InteractionLatenciesObject->SetNumberField(Pair.Key, Pair.Value);
    }
    InteractionObject->SetObjectField(TEXT("latencies"), InteractionLatenciesObject);
    
    MetricsObject->SetObjectField(TEXT("interactions"), InteractionObject);
    
    // Add LLM metrics
    TSharedPtr<FJsonObject> LLMObject = MakeShared<FJsonObject>();
    
    // Add requests
    TSharedPtr<FJsonObject> RequestsObject = MakeShared<FJsonObject>();
    for (const auto& Pair : LLMMetrics.Requests)
    {
      TSharedPtr<FJsonObject> RequestCountObject = MakeShared<FJsonObject>();
      RequestCountObject->SetNumberField(TEXT("success"), Pair.Value.Success);
      RequestCountObject->SetNumberField(TEXT("failure"), Pair.Value.Failure);
      RequestsObject->SetObjectField(Pair.Key, RequestCountObject);
    }
    LLMObject->SetObjectField(TEXT("requests"), RequestsObject);
    
    // Add response times
    TSharedPtr<FJsonObject> ResponseTimesObject = MakeShared<FJsonObject>();
    for (const auto& Pair : LLMMetrics.ResponseTimes)
    {
      ResponseTimesObject->SetNumberField(Pair.Key, Pair.Value);
    }
    LLMObject->SetObjectField(TEXT("responseTimes"), ResponseTimesObject);
    
    // Add token usage
    TSharedPtr<FJsonObject> TokenUsageObject = MakeShared<FJsonObject>();
    for (const auto& Pair : LLMMetrics.TokenUsage)
    {
      TSharedPtr<FJsonObject> UsageObject = MakeShared<FJsonObject>();
      UsageObject->SetNumberField(TEXT("prompt"), Pair.Value.Prompt);
      UsageObject->SetNumberField(TEXT("completion"), Pair.Value.Completion);
      TokenUsageObject->SetObjectField(Pair.Key, UsageObject);
    }
    LLMObject->SetObjectField(TEXT("tokenUsage"), TokenUsageObject);
    
    MetricsObject->SetObjectField(TEXT("llm"), LLMObject);
    
    // Add error metrics
    TSharedPtr<FJsonObject> ErrorsObject = MakeShared<FJsonObject>();
    for (const auto& TypePair : ErrorMetrics.Errors)
    {
      TSharedPtr<FJsonObject> ComponentErrorsObject = MakeShared<FJsonObject>();
      for (const auto& ComponentPair : TypePair.Value)
      {
        ComponentErrorsObject->SetNumberField(ComponentPair.Key, ComponentPair.Value);
      }
      ErrorsObject->SetObjectField(TypePair.Key, ComponentErrorsObject);
    }
    MetricsObject->SetObjectField(TEXT("errors"), ErrorsObject);
  }
  
  // Add metrics object to payload
  Payload->SetObjectField(TEXT("metrics"), MetricsObject);
  
  return Payload;
}

void UMVSMetricsCollector::OnMetricsSubmitted(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
{
  if (!bSuccess || !Response.IsValid())
  {
    UE_LOG(LogMVSMetrics, Warning, TEXT("Failed to submit metrics: No response"));
    return;
  }
  
  int32 ResponseCode = Response->GetResponseCode();
  if (ResponseCode < 200 || ResponseCode >= 300)
  {
    UE_LOG(LogMVSMetrics, Warning, TEXT("Failed to submit metrics: HTTP %d - %s"), 
      ResponseCode, *Response->GetContentAsString());
    return;
  }
  
  UE_LOG(LogMVSMetrics, Log, TEXT("Metrics submitted successfully"));
  
  // Reset metrics after successful submission
  ResetMetrics();
}

void UMVSMetricsCollector::ResetMetrics()
{
  FScopeLock Lock(&MetricsLock);
  
  // Reset all metrics
  AssetLoadingMetrics.LoadTimes.Empty();
  AssetLoadingMetrics.CacheHitRates.Empty();
  AssetLoadingMetrics.LoadCounts.Empty();
  
  NetworkMetrics.Latencies.Empty();
  
  InteractionMetrics.Counts.Empty();
  InteractionMetrics.Latencies.Empty();
  
  LLMMetrics.Requests.Empty();
  LLMMetrics.ResponseTimes.Empty();
  LLMMetrics.TokenUsage.Empty();
  
  ErrorMetrics.Errors.Empty();
}

void UMVSMetricsCollector::SetEnableMetricsCollection(bool bEnable)
{
  bEnableMetricsCollection = bEnable;
  
  if (bEnableMetricsCollection)
  {
    StartMetricsCollection();
  }
  else
  {
    StopMetricsCollection();
  }
}

void UMVSMetricsCollector::SetEnableDetailedMetrics(bool bEnable)
{
  bEnableDetailedMetrics = bEnable;
}

void UMVSMetricsCollector::SetEnableAutomaticSubmission(bool bEnable)
{
  bEnableAutomaticSubmission = bEnable;
}

void UMVSMetricsCollector::SetCollectionInterval(float Interval)
{
  CollectionInterval = FMath::Max(1.0f, Interval);
  
  // Restart collection with new interval
  if (bEnableMetricsCollection)
  {
    StopMetricsCollection();
    StartMetricsCollection();
  }
}

void UMVSMetricsCollector::SetMetricsSubmissionInterval(float Interval)
{
  MetricsSubmissionInterval = FMath::Max(10.0f, Interval);
}

void UMVSMetricsCollector::RecordAssetLoadTime(const FString& AssetType, float LoadTimeSeconds)
{
  FScopeLock Lock(&MetricsLock);
  AssetLoadingMetrics.LoadTimes.Add(AssetType, LoadTimeSeconds);
}

void UMVSMetricsCollector::RecordAssetLoadResult(const FString& AssetType, bool bSuccess)
{
  FScopeLock Lock(&MetricsLock);
  
  if (!AssetLoadingMetrics.LoadCounts.Contains(AssetType))
  {
    FMVSAssetLoadCount NewCount;
    NewCount.Success = 0;
    NewCount.Failure = 0;
    AssetLoadingMetrics.LoadCounts.Add(AssetType, NewCount);
  }
  
  FMVSAssetLoadCount& Count = AssetLoadingMetrics.LoadCounts[AssetType];
  if (bSuccess)
  {
    Count.Success++;
  }
  else
  {
    Count.Failure++;
  }
}

void UMVSMetricsCollector::RecordNetworkLatency(const FString& Endpoint, float LatencyMs)
{
  FScopeLock Lock(&MetricsLock);
  NetworkMetrics.Latencies.Add(Endpoint, LatencyMs);
}

void UMVSMetricsCollector::RecordInteraction(const FString& InteractionType, float LatencyMs)
{
  FScopeLock Lock(&MetricsLock);
  
  // Increment interaction count
  if (!InteractionMetrics.Counts.Contains(InteractionType))
  {
    InteractionMetrics.Counts.Add(InteractionType, 0);
  }
  InteractionMetrics.Counts[InteractionType]++;
  
  // Record latency
  InteractionMetrics.Latencies.Add(InteractionType, LatencyMs);
}

void UMVSMetricsCollector::RecordLLMRequest(const FString& Model, bool bSuccess, float ResponseTimeSeconds, int32 PromptTokens, int32 CompletionTokens)
{
  FScopeLock Lock(&MetricsLock);
  
  // Record request count
  if (!LLMMetrics.Requests.Contains(Model))
  {
    FMVSLLMRequestCount NewCount;
    NewCount.Success = 0;
    NewCount.Failure = 0;
    LLMMetrics.Requests.Add(Model, NewCount);
  }
  
  FMVSLLMRequestCount& Count = LLMMetrics.Requests[Model];
  if (bSuccess)
  {
    Count.Success++;
  }
  else
  {
    Count.Failure++;
  }
  
  // Record response time
  LLMMetrics.ResponseTimes.Add(Model, ResponseTimeSeconds);
  
  // Record token usage
  if (!LLMMetrics.TokenUsage.Contains(Model))
  {
    FMVSLLMTokenUsage NewUsage;
    NewUsage.Prompt = 0;
    NewUsage.Completion = 0;
    LLMMetrics.TokenUsage.Add(Model, NewUsage);
  }
  
  FMVSLLMTokenUsage& Usage = LLMMetrics.TokenUsage[Model];
  Usage.Prompt += PromptTokens;
  Usage.Completion += CompletionTokens;
}

void UMVSMetricsCollector::RecordError(const FString& ErrorType, const FString& Component)
{
  FScopeLock Lock(&MetricsLock);
  
  if (!ErrorMetrics.Errors.Contains(ErrorType))
  {
    TMap<FString, int32> NewComponentMap;
    ErrorMetrics.Errors.Add(ErrorType, NewComponentMap);
  }
  
  TMap<FString, int32>& ComponentMap = ErrorMetrics.Errors[ErrorType];
  if (!ComponentMap.Contains(Component))
  {
    ComponentMap.Add(Component, 0);
  }
  
  ComponentMap[Component]++;
}

void UMVSMetricsCollector::BeginDestroy()
{
  // Stop metrics collection
  StopMetricsCollection();
  
  // Submit any remaining metrics
  if (bEnableMetricsCollection && bEnableAutomaticSubmission)
  {
    SubmitMetrics();
  }
  
  Super::BeginDestroy();
}
