{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "moduleResolution": "bundler", "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": "./", "paths": {"@/*": ["implementation/server/src/*"], "@directus/*": ["implementation/server/directus/extensions/*"], "@shared/*": ["implementation/server/shared/*"], "@services/*": ["implementation/server/services/*"], "@tests/*": ["implementation/server/tests/*"], "@setup/*": ["implementation/server/tests/setup/*"]}, "typeRoots": ["./node_modules/@types", "./implementation/server/types"], "types": ["node", "vitest/globals", "@testing-library/jest-dom", "@types/jest"]}, "include": ["implementation/server/**/*.ts", "implementation/server/**/*.d.ts", "implementation/server/**/*.tsx", "implementation/server/**/*.vue"], "exclude": ["node_modules", "dist", "build", ".deno"], "references": [{"path": "./implementation/server/tsconfig.json"}]}