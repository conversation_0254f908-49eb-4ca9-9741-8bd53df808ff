/**
 * Migration to create API key history table
 */

exports.up = function(knex) {
  return knex.schema
    // Add last_rotated_at column to api_keys table if it doesn't exist
    .hasColumn('api_keys', 'last_rotated_at')
    .then(exists => {
      if (!exists) {
        return knex.schema.alterTable('api_keys', table => {
          table.timestamp('last_rotated_at').nullable();
        });
      }
    })
    // Create api_key_history table
    .createTable('api_key_history', table => {
      table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      table.uuid('api_key_id').notNullable().references('id').inTable('api_keys').onDelete('CASCADE');
      table.string('key').notNullable();
      table.timestamp('created_at').notNullable();
      table.timestamp('expires_at').notNullable();
      table.timestamp('created_at_history').notNullable().defaultTo(knex.fn.now());
      
      // Indexes
      table.index('api_key_id');
      table.index('key');
      table.index('expires_at');
    });
};

exports.down = function(knex) {
  return knex.schema
    .dropTableIfExists('api_key_history')
    .alterTable('api_keys', table => {
      table.dropColumn('last_rotated_at');
    });
};
