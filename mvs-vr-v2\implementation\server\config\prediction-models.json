[{"id": "cpu-usage", "name": "CPU Usage Prediction", "description": "Predict CPU usage based on historical data", "metric": "cpu_usage", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 80, "critical": 90}, "groupBy": ["host"]}, {"id": "memory-usage", "name": "Memory Usage Prediction", "description": "Predict memory usage based on historical data", "metric": "memory_usage", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 80, "critical": 90}, "groupBy": ["host"]}, {"id": "disk-usage", "name": "Disk Usage Prediction", "description": "Predict disk usage based on historical data", "metric": "disk_usage", "algorithm": "linear-regression", "parameters": {"windowSize": 168}, "thresholds": {"warning": 80, "critical": 90}, "groupBy": ["host", "mount"]}, {"id": "network-traffic", "name": "Network Traffic Prediction", "description": "Predict network traffic based on historical data", "metric": "network_traffic", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 80, "critical": 90}, "groupBy": ["host", "interface"]}, {"id": "api-latency", "name": "API Latency Prediction", "description": "Predict API latency based on historical data", "metric": "api_latency", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 200, "critical": 500}, "groupBy": ["service", "endpoint"]}, {"id": "database-connections", "name": "Database Connections Prediction", "description": "Predict database connections based on historical data", "metric": "database_connections", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 80, "critical": 90}, "groupBy": ["database"]}, {"id": "error-rate", "name": "Error Rate Prediction", "description": "Predict error rate based on historical data", "metric": "error_rate", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 5, "critical": 10}, "groupBy": ["service"]}, {"id": "database-query-latency", "name": "Database Query Latency Prediction", "description": "Predict database query latency based on historical data", "metric": "db_query_latency", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 100, "critical": 300}, "groupBy": ["database", "query_type"]}, {"id": "api-error-rate", "name": "API Error Rate Prediction", "description": "Predict API error rate based on historical data", "metric": "api_error_rate", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 5, "critical": 10}, "groupBy": ["service", "endpoint"]}, {"id": "request-queue-depth", "name": "Request Queue Depth Prediction", "description": "Predict request queue depth based on historical data", "metric": "request_queue_depth", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 100, "critical": 500}, "groupBy": ["queue"]}, {"id": "connection-pool-usage", "name": "Connection Pool Usage Prediction", "description": "Predict connection pool usage based on historical data", "metric": "connection_pool_usage", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 80, "critical": 90}, "groupBy": ["pool_name"]}, {"id": "cache-hit-ratio", "name": "<PERSON><PERSON> Hit Ratio Prediction", "description": "Predict cache hit ratio based on historical data", "metric": "cache_hit_ratio", "algorithm": "exponential-smoothing", "parameters": {"alpha": 0.3, "beta": 0.1, "gamma": 0.1, "seasonality": 24}, "thresholds": {"warning": 50, "critical": 30}, "groupBy": ["cache_name"]}]