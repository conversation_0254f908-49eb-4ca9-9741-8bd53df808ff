<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1747907633802" clover="3.2.0">
  <project timestamp="1747907633802" name="All files">
    <metrics statements="6792" coveredstatements="18" conditionals="5023" coveredconditionals="2" methods="2280" coveredmethods="4" elements="14095" coveredelements="24" complexity="0" loc="6792" ncloc="6792" packages="12" files="70" classes="70"/>
    <package name="vendor-portal">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="babel.config.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\babel.config.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
      </file>
      <file name="jest.config.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\jest.config.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
      </file>
      <file name="vitest.config.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\vitest.config.js">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="vendor-portal.coverage.lcov-report">
      <metrics statements="120" coveredstatements="0" conditionals="329" coveredconditionals="0" methods="54" coveredmethods="0"/>
      <file name="block-navigation.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\coverage\lcov-report\block-navigation.js">
        <metrics statements="30" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
      </file>
      <file name="prettify.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\coverage\lcov-report\prettify.js">
        <metrics statements="1" coveredstatements="0" conditionals="286" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="2" count="0" type="cond" truecount="0" falsecount="286"/>
      </file>
      <file name="sorter.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\coverage\lcov-report\sorter.js">
        <metrics statements="89" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src">
      <metrics statements="93" coveredstatements="0" conditionals="82" coveredconditionals="0" methods="26" coveredmethods="0"/>
      <file name="App.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\App.vue">
        <metrics statements="9" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\index.js">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="interface.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\interface.vue">
        <metrics statements="84" coveredstatements="0" conditionals="80" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="282" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.components">
      <metrics statements="4678" coveredstatements="0" conditionals="3245" coveredconditionals="0" methods="1539" coveredmethods="0"/>
      <file name="ABTestingFramework.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ABTestingFramework.vue">
        <metrics statements="128" coveredstatements="0" conditionals="55" coveredconditionals="0" methods="50" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="607" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="608" count="0" type="stmt"/>
        <line num="636" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="637" count="0" type="stmt"/>
        <line num="662" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="663" count="0" type="stmt"/>
        <line num="687" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="688" count="0" type="stmt"/>
        <line num="720" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="759" count="0" type="stmt"/>
        <line num="796" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="797" count="0" type="stmt"/>
        <line num="829" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="830" count="0" type="stmt"/>
        <line num="867" count="0" type="stmt"/>
        <line num="874" count="0" type="stmt"/>
        <line num="912" count="0" type="stmt"/>
        <line num="938" count="0" type="stmt"/>
        <line num="939" count="0" type="stmt"/>
        <line num="940" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="941" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="942" count="0" type="stmt"/>
        <line num="943" count="0" type="stmt"/>
        <line num="944" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="945" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="952" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="962" count="0" type="stmt"/>
        <line num="986" count="0" type="stmt"/>
        <line num="1021" count="0" type="stmt"/>
        <line num="1052" count="0" type="stmt"/>
        <line num="1064" count="0" type="stmt"/>
        <line num="1066" count="0" type="stmt"/>
        <line num="1067" count="0" type="stmt"/>
        <line num="1068" count="0" type="stmt"/>
        <line num="1073" count="0" type="stmt"/>
        <line num="1075" count="0" type="stmt"/>
        <line num="1076" count="0" type="stmt"/>
      </file>
      <file name="ActivityTracking.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ActivityTracking.vue">
        <metrics statements="216" coveredstatements="0" conditionals="135" coveredconditionals="0" methods="59" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="358" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="374" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="394" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="395" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="407" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="421" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="422" count="0" type="stmt"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="438" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="444" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="496" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="497" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="597" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="598" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="599" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="601" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="602" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="605" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="611" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="657" count="0" type="stmt"/>
        <line num="658" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="659" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="660" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="662" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="663" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="666" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="672" count="0" type="stmt"/>
        <line num="710" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="711" count="0" type="stmt"/>
        <line num="733" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="734" count="0" type="stmt"/>
        <line num="771" count="0" type="stmt"/>
        <line num="779" count="0" type="stmt"/>
        <line num="808" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="809" count="0" type="stmt"/>
        <line num="842" count="0" type="stmt"/>
        <line num="857" count="0" type="stmt"/>
        <line num="940" count="0" type="stmt"/>
        <line num="1037" count="0" type="stmt"/>
        <line num="1060" count="0" type="stmt"/>
        <line num="1102" count="0" type="stmt"/>
        <line num="1121" count="0" type="stmt"/>
        <line num="1153" count="0" type="stmt"/>
        <line num="1169" count="0" type="stmt"/>
        <line num="1202" count="0" type="stmt"/>
        <line num="1203" count="0" type="stmt"/>
        <line num="1204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1206" count="0" type="stmt"/>
        <line num="1207" count="0" type="stmt"/>
        <line num="1208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1226" count="0" type="stmt"/>
        <line num="1249" count="0" type="stmt"/>
        <line num="1282" count="0" type="stmt"/>
        <line num="1283" count="0" type="stmt"/>
        <line num="1284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1285" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1286" count="0" type="stmt"/>
        <line num="1287" count="0" type="stmt"/>
        <line num="1288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1306" count="0" type="stmt"/>
        <line num="1330" count="0" type="stmt"/>
        <line num="1351" count="0" type="stmt"/>
        <line num="1352" count="0" type="stmt"/>
        <line num="1354" count="0" type="stmt"/>
        <line num="1355" count="0" type="stmt"/>
      </file>
      <file name="AnalyticsChart.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\AnalyticsChart.vue">
        <metrics statements="74" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="101" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="210" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
      </file>
      <file name="AnalyticsManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\AnalyticsManager.vue">
        <metrics statements="76" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="37" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="407" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="763" count="0" type="stmt"/>
        <line num="780" count="0" type="stmt"/>
        <line num="782" count="0" type="stmt"/>
        <line num="783" count="0" type="stmt"/>
        <line num="784" count="0" type="stmt"/>
        <line num="799" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="801" count="0" type="stmt"/>
        <line num="816" count="0" type="stmt"/>
        <line num="818" count="0" type="stmt"/>
        <line num="819" count="0" type="stmt"/>
      </file>
      <file name="BrandingManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\BrandingManager.vue">
        <metrics statements="81" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="340" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="341" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="398" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="399" count="0" type="stmt"/>
        <line num="422" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="423" count="0" type="stmt"/>
        <line num="456" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="457" count="0" type="stmt"/>
        <line num="480" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="481" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="524" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="525" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="618" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="619" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="716" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="717" count="0" type="stmt"/>
        <line num="895" count="0" type="stmt"/>
        <line num="896" count="0" type="stmt"/>
        <line num="898" count="0" type="stmt"/>
        <line num="899" count="0" type="stmt"/>
      </file>
      <file name="CategoryManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\CategoryManager.vue">
        <metrics statements="113" coveredstatements="0" conditionals="107" coveredconditionals="0" methods="44" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="283" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="677" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="678" count="0" type="stmt"/>
        <line num="706" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="707" count="0" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="739" count="0" type="stmt"/>
        <line num="742" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="743" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="761" count="0" type="stmt"/>
        <line num="805" count="0" type="stmt"/>
        <line num="807" count="0" type="stmt"/>
        <line num="810" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="811" count="0" type="stmt"/>
        <line num="813" count="0" type="stmt"/>
        <line num="857" count="0" type="stmt"/>
        <line num="859" count="0" type="stmt"/>
        <line num="862" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="863" count="0" type="stmt"/>
        <line num="865" count="0" type="stmt"/>
        <line num="944" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="945" count="0" type="stmt"/>
        <line num="997" count="0" type="stmt"/>
        <line num="1047" count="0" type="stmt"/>
        <line num="1068" count="0" type="stmt"/>
        <line num="1070" count="0" type="stmt"/>
        <line num="1071" count="0" type="stmt"/>
        <line num="1072" count="0" type="stmt"/>
        <line num="1083" count="0" type="stmt"/>
        <line num="1085" count="0" type="stmt"/>
        <line num="1086" count="0" type="stmt"/>
      </file>
      <file name="ClientAnalytics.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ClientAnalytics.vue">
        <metrics statements="95" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="283" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="368" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="629" count="0" type="stmt"/>
        <line num="705" count="0" type="stmt"/>
        <line num="772" count="0" type="stmt"/>
        <line num="774" count="0" type="stmt"/>
        <line num="775" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="781" count="0" type="stmt"/>
        <line num="782" count="0" type="stmt"/>
        <line num="783" count="0" type="stmt"/>
        <line num="788" count="0" type="stmt"/>
        <line num="789" count="0" type="stmt"/>
        <line num="790" count="0" type="stmt"/>
        <line num="795" count="0" type="stmt"/>
        <line num="796" count="0" type="stmt"/>
        <line num="797" count="0" type="stmt"/>
        <line num="802" count="0" type="stmt"/>
        <line num="803" count="0" type="stmt"/>
        <line num="804" count="0" type="stmt"/>
        <line num="821" count="0" type="stmt"/>
        <line num="823" count="0" type="stmt"/>
        <line num="824" count="0" type="stmt"/>
      </file>
      <file name="CollaborationFeatures.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\CollaborationFeatures.vue">
        <metrics statements="14" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
      </file>
      <file name="CommentingFeedbackSystem.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\CommentingFeedbackSystem.vue">
        <metrics statements="106" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="161" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="564" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="565" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="699" count="0" type="stmt"/>
        <line num="777" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="778" count="0" type="stmt"/>
        <line num="809" count="0" type="stmt"/>
        <line num="811" count="0" type="stmt"/>
        <line num="812" count="0" type="stmt"/>
        <line num="813" count="0" type="stmt"/>
        <line num="820" count="0" type="stmt"/>
        <line num="821" count="0" type="stmt"/>
        <line num="822" count="0" type="stmt"/>
        <line num="877" count="0" type="stmt"/>
        <line num="878" count="0" type="stmt"/>
        <line num="879" count="0" type="stmt"/>
        <line num="888" count="0" type="stmt"/>
        <line num="889" count="0" type="stmt"/>
        <line num="890" count="0" type="stmt"/>
        <line num="903" count="0" type="stmt"/>
        <line num="904" count="0" type="stmt"/>
        <line num="905" count="0" type="stmt"/>
        <line num="918" count="0" type="stmt"/>
        <line num="919" count="0" type="stmt"/>
        <line num="920" count="0" type="stmt"/>
        <line num="927" count="0" type="stmt"/>
        <line num="928" count="0" type="stmt"/>
        <line num="929" count="0" type="stmt"/>
        <line num="936" count="0" type="stmt"/>
        <line num="937" count="0" type="stmt"/>
        <line num="938" count="0" type="stmt"/>
        <line num="945" count="0" type="stmt"/>
        <line num="947" count="0" type="stmt"/>
        <line num="948" count="0" type="stmt"/>
      </file>
      <file name="ComparativeAnalytics.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ComparativeAnalytics.vue">
        <metrics statements="192" coveredstatements="0" conditionals="178" coveredconditionals="0" methods="50" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="164" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="318" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="375" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="409" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="478" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="479" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="484" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="490" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="581" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="582" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="584" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="585" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="590" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="596" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="651" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="652" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="654" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="655" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="660" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="666" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="744" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="745" count="0" type="stmt"/>
        <line num="768" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="769" count="0" type="stmt"/>
        <line num="796" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="797" count="0" type="stmt"/>
        <line num="820" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="821" count="0" type="stmt"/>
        <line num="845" count="0" type="stmt"/>
        <line num="847" count="0" type="stmt"/>
        <line num="850" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="851" count="0" type="stmt"/>
        <line num="853" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="868" count="0" type="stmt"/>
        <line num="895" count="0" type="stmt"/>
        <line num="921" count="0" type="stmt"/>
        <line num="922" count="0" type="stmt"/>
        <line num="923" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="924" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="925" count="0" type="stmt"/>
        <line num="926" count="0" type="stmt"/>
        <line num="927" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="928" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="933" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="939" count="0" type="stmt"/>
        <line num="971" count="0" type="stmt"/>
        <line num="1063" count="0" type="stmt"/>
        <line num="1064" count="0" type="stmt"/>
        <line num="1066" count="0" type="stmt"/>
        <line num="1067" count="0" type="stmt"/>
      </file>
      <file name="CustomReportBuilder.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\CustomReportBuilder.vue">
        <metrics statements="148" coveredstatements="0" conditionals="81" coveredconditionals="0" methods="42" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="250" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="276" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="330" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="409" count="0" type="stmt"/>
        <line num="434" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="435" count="0" type="stmt"/>
        <line num="460" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="461" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="493" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="511" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="512" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="673" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="674" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="688" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="699" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="700" count="0" type="stmt"/>
        <line num="707" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="708" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="719" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
        <line num="724" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="733" count="0" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="737" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="738" count="0" type="stmt"/>
        <line num="739" count="0" type="stmt"/>
        <line num="740" count="0" type="stmt"/>
        <line num="743" count="0" type="stmt"/>
        <line num="744" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="748" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="758" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="759" count="0" type="stmt"/>
        <line num="760" count="0" type="stmt"/>
        <line num="764" count="0" type="stmt"/>
        <line num="767" count="0" type="stmt"/>
        <line num="774" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="775" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="778" count="0" type="stmt"/>
        <line num="840" count="0" type="stmt"/>
        <line num="852" count="0" type="stmt"/>
        <line num="913" count="0" type="stmt"/>
        <line num="942" count="0" type="stmt"/>
        <line num="970" count="0" type="stmt"/>
        <line num="1000" count="0" type="stmt"/>
        <line num="1071" count="0" type="stmt"/>
        <line num="1072" count="0" type="stmt"/>
        <line num="1074" count="0" type="stmt"/>
        <line num="1075" count="0" type="stmt"/>
      </file>
      <file name="DashboardOverview.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\DashboardOverview.vue">
        <metrics statements="177" coveredstatements="0" conditionals="118" coveredconditionals="0" methods="56" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="386" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="445" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="451" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="459" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="462" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="463" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="464" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="465" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="466" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="468" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="475" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="484" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="486" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="501" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="502" count="0" type="stmt"/>
        <line num="506" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="507" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="512" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="566" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="567" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="599" count="0" type="stmt"/>
        <line num="600" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="601" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="607" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="610" count="0" type="stmt"/>
        <line num="611" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="612" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="624" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="630" count="0" type="stmt"/>
        <line num="631" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="633" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="636" count="0" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="657" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="674" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="675" count="0" type="stmt"/>
        <line num="676" count="0" type="stmt"/>
        <line num="677" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="700" count="0" type="stmt"/>
        <line num="703" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="704" count="0" type="stmt"/>
        <line num="706" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="782" count="0" type="stmt"/>
        <line num="790" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="901" count="0" type="stmt"/>
        <line num="956" count="0" type="stmt"/>
        <line num="1000" count="0" type="stmt"/>
        <line num="1060" count="0" type="stmt"/>
        <line num="1144" count="0" type="stmt"/>
        <line num="1271" count="0" type="stmt"/>
        <line num="1288" count="0" type="stmt"/>
        <line num="1378" count="0" type="stmt"/>
        <line num="1469" count="0" type="stmt"/>
        <line num="1471" count="0" type="stmt"/>
        <line num="1472" count="0" type="stmt"/>
        <line num="1473" count="0" type="stmt"/>
        <line num="1482" count="0" type="stmt"/>
        <line num="1483" count="0" type="stmt"/>
        <line num="1484" count="0" type="stmt"/>
        <line num="1489" count="0" type="stmt"/>
        <line num="1490" count="0" type="stmt"/>
        <line num="1491" count="0" type="stmt"/>
        <line num="1496" count="0" type="stmt"/>
        <line num="1497" count="0" type="stmt"/>
        <line num="1498" count="0" type="stmt"/>
        <line num="1503" count="0" type="stmt"/>
        <line num="1504" count="0" type="stmt"/>
        <line num="1505" count="0" type="stmt"/>
        <line num="1514" count="0" type="stmt"/>
        <line num="1515" count="0" type="stmt"/>
        <line num="1516" count="0" type="stmt"/>
        <line num="1521" count="0" type="stmt"/>
        <line num="1522" count="0" type="stmt"/>
        <line num="1523" count="0" type="stmt"/>
        <line num="1532" count="0" type="stmt"/>
        <line num="1534" count="0" type="stmt"/>
        <line num="1535" count="0" type="stmt"/>
      </file>
      <file name="DataSourceSelector.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\DataSourceSelector.vue">
        <metrics statements="90" coveredstatements="0" conditionals="63" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
      </file>
      <file name="DevicePreview.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\DevicePreview.vue">
        <metrics statements="94" coveredstatements="0" conditionals="46" coveredconditionals="0" methods="39" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="244" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="549" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="550" count="0" type="stmt"/>
        <line num="552" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="557" count="0" type="stmt"/>
        <line num="614" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="615" count="0" type="stmt"/>
        <line num="645" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="646" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="683" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="684" count="0" type="stmt"/>
        <line num="691" count="0" type="stmt"/>
        <line num="722" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="723" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="760" count="0" type="stmt"/>
        <line num="762" count="0" type="stmt"/>
        <line num="763" count="0" type="stmt"/>
        <line num="764" count="0" type="stmt"/>
        <line num="769" count="0" type="stmt"/>
        <line num="770" count="0" type="stmt"/>
        <line num="771" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="778" count="0" type="stmt"/>
        <line num="783" count="0" type="stmt"/>
        <line num="784" count="0" type="stmt"/>
        <line num="785" count="0" type="stmt"/>
        <line num="790" count="0" type="stmt"/>
        <line num="792" count="0" type="stmt"/>
        <line num="793" count="0" type="stmt"/>
      </file>
      <file name="ExportManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ExportManager.vue">
        <metrics statements="123" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="412" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="444" count="0" type="stmt"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="494" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
      </file>
      <file name="FilterBuilder.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\FilterBuilder.vue">
        <metrics statements="109" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="43" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="11"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="stmt"/>
        <line num="293" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="314" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="351" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
      </file>
      <file name="FilterCondition.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\FilterCondition.vue">
        <metrics statements="69" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="35" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="47" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="296" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="423" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="424" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
      </file>
      <file name="GroupingSelector.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GroupingSelector.vue">
        <metrics statements="243" coveredstatements="0" conditionals="257" coveredconditionals="0" methods="72" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="313" count="0" type="stmt"/>
        <line num="318" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="412" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="426" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="462" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="463" count="0" type="stmt"/>
        <line num="465" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="516" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="517" count="0" type="stmt"/>
        <line num="540" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="541" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="571" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="572" count="0" type="stmt"/>
        <line num="574" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="624" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="625" count="0" type="stmt"/>
        <line num="648" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="649" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
        <line num="681" count="0" type="stmt"/>
        <line num="684" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="685" count="0" type="stmt"/>
        <line num="687" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="696" count="0" type="stmt"/>
        <line num="729" count="0" type="stmt"/>
        <line num="731" count="0" type="stmt"/>
        <line num="734" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="735" count="0" type="stmt"/>
        <line num="737" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="778" count="0" type="stmt"/>
        <line num="780" count="0" type="stmt"/>
        <line num="783" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="784" count="0" type="stmt"/>
        <line num="786" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="824" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="825" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="860" count="0" type="stmt"/>
        <line num="861" count="0" type="stmt"/>
        <line num="862" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="863" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="864" count="0" type="stmt"/>
        <line num="865" count="0" type="stmt"/>
        <line num="866" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="867" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="870" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="876" count="0" type="stmt"/>
        <line num="908" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="909" count="0" type="stmt"/>
        <line num="912" count="0" type="stmt"/>
        <line num="942" count="0" type="stmt"/>
        <line num="944" count="0" type="stmt"/>
        <line num="947" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="948" count="0" type="stmt"/>
        <line num="950" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="959" count="0" type="stmt"/>
        <line num="992" count="0" type="stmt"/>
        <line num="994" count="0" type="stmt"/>
        <line num="997" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="998" count="0" type="stmt"/>
        <line num="1000" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1045" count="0" type="stmt"/>
        <line num="1047" count="0" type="stmt"/>
        <line num="1050" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1051" count="0" type="stmt"/>
        <line num="1053" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1062" count="0" type="stmt"/>
        <line num="1095" count="0" type="stmt"/>
        <line num="1097" count="0" type="stmt"/>
        <line num="1100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1101" count="0" type="stmt"/>
        <line num="1103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1144" count="0" type="stmt"/>
        <line num="1146" count="0" type="stmt"/>
        <line num="1149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1150" count="0" type="stmt"/>
        <line num="1152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1207" count="0" type="stmt"/>
        <line num="1223" count="0" type="stmt"/>
        <line num="1224" count="0" type="stmt"/>
        <line num="1226" count="0" type="stmt"/>
        <line num="1227" count="0" type="stmt"/>
      </file>
      <file name="InteractionHeatmap.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\InteractionHeatmap.vue">
        <metrics statements="84" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="24" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="325" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="358" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="488" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
      </file>
      <file name="LivePreview.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\LivePreview.vue">
        <metrics statements="33" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
      </file>
      <file name="MetricsSelector.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\MetricsSelector.vue">
        <metrics statements="103" coveredstatements="0" conditionals="78" coveredconditionals="0" methods="58" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="463" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="464" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="514" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="552" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="553" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="622" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="623" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="624" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="626" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="627" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="634" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="642" count="0" type="stmt"/>
        <line num="673" count="0" type="stmt"/>
        <line num="675" count="0" type="stmt"/>
        <line num="679" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="680" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="730" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="731" count="0" type="stmt"/>
        <line num="738" count="0" type="stmt"/>
        <line num="769" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="770" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="803" count="0" type="stmt"/>
        <line num="805" count="0" type="stmt"/>
        <line num="809" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="810" count="0" type="stmt"/>
        <line num="812" count="0" type="stmt"/>
        <line num="871" count="0" type="stmt"/>
        <line num="873" count="0" type="stmt"/>
        <line num="874" count="0" type="stmt"/>
        <line num="875" count="0" type="stmt"/>
        <line num="880" count="0" type="stmt"/>
        <line num="882" count="0" type="stmt"/>
        <line num="883" count="0" type="stmt"/>
      </file>
      <file name="PerformanceTestingTools.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\PerformanceTestingTools.vue">
        <metrics statements="91" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="424" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="429" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="430" count="0" type="stmt"/>
        <line num="432" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="477" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="478" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="523" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="524" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="527" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="530" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="536" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="636" count="0" type="stmt"/>
        <line num="673" count="0" type="stmt"/>
        <line num="696" count="0" type="stmt"/>
        <line num="735" count="0" type="stmt"/>
        <line num="774" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="778" count="0" type="stmt"/>
        <line num="789" count="0" type="stmt"/>
        <line num="791" count="0" type="stmt"/>
        <line num="792" count="0" type="stmt"/>
      </file>
      <file name="PreviewControls.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\PreviewControls.vue">
        <metrics statements="75" coveredstatements="0" conditionals="58" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
      </file>
      <file name="PreviewFrame.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\PreviewFrame.vue">
        <metrics statements="44" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
      </file>
      <file name="PreviewTestingTools.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\PreviewTestingTools.vue">
        <metrics statements="19" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
      </file>
      <file name="ProductManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ProductManager.vue">
        <metrics statements="331" coveredstatements="0" conditionals="279" coveredconditionals="0" methods="117" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="296" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="305" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="376" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="436" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="440" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="452" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="465" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="474" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="521" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="576" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="577" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="585" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="586" count="0" type="stmt"/>
        <line num="590" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="594" count="0" type="stmt"/>
        <line num="602" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="603" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="621" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="623" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="631" count="0" type="stmt"/>
        <line num="636" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="638" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="644" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="651" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="652" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="660" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="669" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="670" count="0" type="stmt"/>
        <line num="675" count="0" type="stmt"/>
        <line num="676" count="0" type="stmt"/>
        <line num="677" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="686" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="692" count="0" type="stmt"/>
        <line num="693" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="694" count="0" type="stmt"/>
        <line num="709" count="0" type="stmt"/>
        <line num="716" count="0" type="stmt"/>
        <line num="718" count="0" type="stmt"/>
        <line num="719" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="726" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="729" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="744" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="746" count="0" type="stmt"/>
        <line num="754" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="755" count="0" type="stmt"/>
        <line num="756" count="0" type="stmt"/>
        <line num="757" count="0" type="stmt"/>
        <line num="758" count="0" type="stmt"/>
        <line num="769" count="0" type="stmt"/>
        <line num="794" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="795" count="0" type="stmt"/>
        <line num="826" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="831" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="832" count="0" type="stmt"/>
        <line num="834" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="875" count="0" type="stmt"/>
        <line num="877" count="0" type="stmt"/>
        <line num="880" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="881" count="0" type="stmt"/>
        <line num="883" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="897" count="0" type="stmt"/>
        <line num="930" count="0" type="stmt"/>
        <line num="932" count="0" type="stmt"/>
        <line num="935" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="936" count="0" type="stmt"/>
        <line num="938" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="996" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="997" count="0" type="stmt"/>
        <line num="1026" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1027" count="0" type="stmt"/>
        <line num="1059" count="0" type="stmt"/>
        <line num="1061" count="0" type="stmt"/>
        <line num="1064" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1065" count="0" type="stmt"/>
        <line num="1067" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1116" count="0" type="stmt"/>
        <line num="1118" count="0" type="stmt"/>
        <line num="1121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1122" count="0" type="stmt"/>
        <line num="1124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1153" count="0" type="stmt"/>
        <line num="1171" count="0" type="stmt"/>
        <line num="1191" count="0" type="stmt"/>
        <line num="1199" count="0" type="stmt"/>
        <line num="1234" count="0" type="stmt"/>
        <line num="1256" count="0" type="stmt"/>
        <line num="1278" count="0" type="stmt"/>
        <line num="1288" count="0" type="stmt"/>
        <line num="1303" count="0" type="stmt"/>
        <line num="1358" count="0" type="stmt"/>
        <line num="1430" count="0" type="stmt"/>
        <line num="1489" count="0" type="stmt"/>
        <line num="1507" count="0" type="stmt"/>
        <line num="1525" count="0" type="stmt"/>
        <line num="1543" count="0" type="stmt"/>
        <line num="1567" count="0" type="stmt"/>
        <line num="1617" count="0" type="stmt"/>
        <line num="1697" count="0" type="stmt"/>
        <line num="1802" count="0" type="stmt"/>
        <line num="1820" count="0" type="stmt"/>
        <line num="1838" count="0" type="stmt"/>
        <line num="1856" count="0" type="stmt"/>
        <line num="1923" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1924" count="0" type="stmt"/>
        <line num="1952" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1953" count="0" type="stmt"/>
        <line num="1983" count="0" type="stmt"/>
        <line num="1985" count="0" type="stmt"/>
        <line num="1988" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1989" count="0" type="stmt"/>
        <line num="1991" count="0" type="stmt"/>
        <line num="2036" count="0" type="stmt"/>
        <line num="2038" count="0" type="stmt"/>
        <line num="2041" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2042" count="0" type="stmt"/>
        <line num="2044" count="0" type="stmt"/>
        <line num="2060" count="0" type="stmt"/>
        <line num="2088" count="0" type="stmt"/>
        <line num="2098" count="0" type="stmt"/>
        <line num="2160" count="0" type="stmt"/>
        <line num="2181" count="0" type="stmt"/>
        <line num="2212" count="0" type="stmt"/>
        <line num="2253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2254" count="0" type="stmt"/>
        <line num="2288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2289" count="0" type="stmt"/>
        <line num="2327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2328" count="0" type="stmt"/>
        <line num="2357" count="0" type="stmt"/>
        <line num="2358" count="0" type="stmt"/>
        <line num="2359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2361" count="0" type="stmt"/>
        <line num="2362" count="0" type="stmt"/>
        <line num="2363" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2379" count="0" type="stmt"/>
        <line num="2402" count="0" type="stmt"/>
        <line num="2459" count="0" type="stmt"/>
        <line num="2488" count="0" type="stmt"/>
        <line num="2520" count="0" type="stmt"/>
        <line num="2552" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2553" count="0" type="stmt"/>
        <line num="2578" count="0" type="stmt"/>
        <line num="2580" count="0" type="stmt"/>
        <line num="2583" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2584" count="0" type="stmt"/>
        <line num="2586" count="0" type="stmt"/>
        <line num="2667" count="0" type="stmt"/>
        <line num="2691" count="0" type="stmt"/>
        <line num="2693" count="0" type="stmt"/>
        <line num="2694" count="0" type="stmt"/>
        <line num="2695" count="0" type="stmt"/>
        <line num="2716" count="0" type="stmt"/>
        <line num="2717" count="0" type="stmt"/>
        <line num="2718" count="0" type="stmt"/>
        <line num="2723" count="0" type="stmt"/>
        <line num="2725" count="0" type="stmt"/>
        <line num="2726" count="0" type="stmt"/>
      </file>
      <file name="ProductVariantManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ProductVariantManager.vue">
        <metrics statements="61" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="36" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="421" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="422" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
      </file>
      <file name="RealtimeVisualization.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\RealtimeVisualization.vue">
        <metrics statements="77" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="394" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
      </file>
      <file name="ReportChartView.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ReportChartView.vue">
        <metrics statements="146" coveredstatements="0" conditionals="142" coveredconditionals="0" methods="46" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="243" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="433" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="434" count="0" type="stmt"/>
        <line num="457" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="458" count="0" type="stmt"/>
        <line num="485" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="486" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="518" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="573" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="574" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="630" count="0" type="stmt"/>
        <line num="631" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="632" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="633" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="635" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="636" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="643" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="651" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="684" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="685" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="686" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
        <line num="688" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="689" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="696" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="704" count="0" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="738" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="739" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="740" count="0" type="stmt"/>
        <line num="741" count="0" type="stmt"/>
        <line num="742" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="743" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="750" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="758" count="0" type="stmt"/>
        <line num="786" count="0" type="stmt"/>
        <line num="788" count="0" type="stmt"/>
        <line num="791" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="792" count="0" type="stmt"/>
        <line num="794" count="0" type="stmt"/>
        <line num="845" count="0" type="stmt"/>
        <line num="846" count="0" type="stmt"/>
        <line num="847" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="848" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="849" count="0" type="stmt"/>
        <line num="850" count="0" type="stmt"/>
        <line num="851" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="852" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="859" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="867" count="0" type="stmt"/>
        <line num="901" count="0" type="stmt"/>
        <line num="902" count="0" type="stmt"/>
        <line num="904" count="0" type="stmt"/>
        <line num="905" count="0" type="stmt"/>
      </file>
      <file name="ReportManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ReportManager.vue">
        <metrics statements="70" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
      </file>
      <file name="ReportPreview.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ReportPreview.vue">
        <metrics statements="162" coveredstatements="0" conditionals="105" coveredconditionals="0" methods="44" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="397" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="449" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="450" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="598" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="599" count="0" type="stmt"/>
        <line num="601" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="648" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="651" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="652" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="659" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="667" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="695" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="696" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="748" count="0" type="stmt"/>
        <line num="750" count="0" type="stmt"/>
        <line num="753" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="754" count="0" type="stmt"/>
        <line num="756" count="0" type="stmt"/>
        <line num="798" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="803" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="804" count="0" type="stmt"/>
        <line num="806" count="0" type="stmt"/>
        <line num="862" count="0" type="stmt"/>
        <line num="863" count="0" type="stmt"/>
        <line num="865" count="0" type="stmt"/>
        <line num="866" count="0" type="stmt"/>
      </file>
      <file name="ReportTableView.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ReportTableView.vue">
        <metrics statements="124" coveredstatements="0" conditionals="119" coveredconditionals="0" methods="45" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="554" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="555" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="556" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="558" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="559" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="562" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="570" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
      </file>
      <file name="ShowroomAnalytics.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ShowroomAnalytics.vue">
        <metrics statements="188" coveredstatements="0" conditionals="66" coveredconditionals="0" methods="44" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="429" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="439" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="444" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="451" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="452" count="0" type="stmt"/>
        <line num="456" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="457" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="458" count="0" type="stmt"/>
        <line num="462" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="463" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="464" count="0" type="stmt"/>
        <line num="468" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="507" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="508" count="0" type="stmt"/>
        <line num="510" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="559" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="560" count="0" type="stmt"/>
        <line num="583" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="584" count="0" type="stmt"/>
        <line num="766" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="970" count="0" type="stmt"/>
        <line num="972" count="0" type="stmt"/>
        <line num="975" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="976" count="0" type="stmt"/>
        <line num="978" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1027" count="0" type="stmt"/>
        <line num="1069" count="0" type="stmt"/>
        <line num="1071" count="0" type="stmt"/>
        <line num="1072" count="0" type="stmt"/>
        <line num="1073" count="0" type="stmt"/>
        <line num="1078" count="0" type="stmt"/>
        <line num="1079" count="0" type="stmt"/>
        <line num="1080" count="0" type="stmt"/>
        <line num="1085" count="0" type="stmt"/>
        <line num="1086" count="0" type="stmt"/>
        <line num="1087" count="0" type="stmt"/>
        <line num="1092" count="0" type="stmt"/>
        <line num="1093" count="0" type="stmt"/>
        <line num="1094" count="0" type="stmt"/>
        <line num="1099" count="0" type="stmt"/>
        <line num="1100" count="0" type="stmt"/>
        <line num="1101" count="0" type="stmt"/>
        <line num="1106" count="0" type="stmt"/>
        <line num="1107" count="0" type="stmt"/>
        <line num="1108" count="0" type="stmt"/>
        <line num="1113" count="0" type="stmt"/>
        <line num="1114" count="0" type="stmt"/>
        <line num="1115" count="0" type="stmt"/>
        <line num="1130" count="0" type="stmt"/>
        <line num="1132" count="0" type="stmt"/>
        <line num="1133" count="0" type="stmt"/>
      </file>
      <file name="ShowroomConfigurator.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ShowroomConfigurator.vue">
        <metrics statements="234" coveredstatements="0" conditionals="246" coveredconditionals="0" methods="72" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="451" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="617" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="618" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="671" count="0" type="stmt"/>
        <line num="674" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="675" count="0" type="stmt"/>
        <line num="677" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="755" count="0" type="stmt"/>
        <line num="783" count="0" type="stmt"/>
        <line num="818" count="0" type="stmt"/>
        <line num="820" count="0" type="stmt"/>
        <line num="823" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="824" count="0" type="stmt"/>
        <line num="826" count="0" type="stmt"/>
        <line num="880" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="881" count="0" type="stmt"/>
        <line num="907" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="908" count="0" type="stmt"/>
        <line num="934" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="935" count="0" type="stmt"/>
        <line num="966" count="0" type="stmt"/>
        <line num="996" count="0" type="stmt"/>
        <line num="1018" count="0" type="stmt"/>
        <line num="1026" count="0" type="stmt"/>
        <line num="1076" count="0" type="stmt"/>
        <line num="1092" count="0" type="stmt"/>
        <line num="1109" count="0" type="stmt"/>
        <line num="1137" count="0" type="stmt"/>
        <line num="1138" count="0" type="stmt"/>
        <line num="1139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1141" count="0" type="stmt"/>
        <line num="1142" count="0" type="stmt"/>
        <line num="1143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1161" count="0" type="stmt"/>
        <line num="1196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1197" count="0" type="stmt"/>
        <line num="1211" count="0" type="stmt"/>
        <line num="1239" count="0" type="stmt"/>
        <line num="1240" count="0" type="stmt"/>
        <line num="1241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1243" count="0" type="stmt"/>
        <line num="1244" count="0" type="stmt"/>
        <line num="1245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1263" count="0" type="stmt"/>
        <line num="1371" count="0" type="stmt"/>
        <line num="1372" count="0" type="stmt"/>
        <line num="1373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1374" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1375" count="0" type="stmt"/>
        <line num="1376" count="0" type="stmt"/>
        <line num="1377" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1385" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1393" count="0" type="stmt"/>
        <line num="1431" count="0" type="stmt"/>
        <line num="1432" count="0" type="stmt"/>
        <line num="1433" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1434" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1435" count="0" type="stmt"/>
        <line num="1436" count="0" type="stmt"/>
        <line num="1437" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1438" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1445" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1453" count="0" type="stmt"/>
        <line num="1490" count="0" type="stmt"/>
        <line num="1491" count="0" type="stmt"/>
        <line num="1492" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1493" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1494" count="0" type="stmt"/>
        <line num="1495" count="0" type="stmt"/>
        <line num="1496" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1497" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1504" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1512" count="0" type="stmt"/>
        <line num="1546" count="0" type="stmt"/>
        <line num="1547" count="0" type="stmt"/>
        <line num="1548" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1549" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1550" count="0" type="stmt"/>
        <line num="1551" count="0" type="stmt"/>
        <line num="1552" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1553" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1560" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1568" count="0" type="stmt"/>
        <line num="1604" count="0" type="stmt"/>
        <line num="1605" count="0" type="stmt"/>
        <line num="1606" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1607" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1608" count="0" type="stmt"/>
        <line num="1609" count="0" type="stmt"/>
        <line num="1610" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1611" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1618" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1626" count="0" type="stmt"/>
        <line num="1661" count="0" type="stmt"/>
        <line num="1662" count="0" type="stmt"/>
        <line num="1663" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1664" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1665" count="0" type="stmt"/>
        <line num="1666" count="0" type="stmt"/>
        <line num="1667" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1668" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1675" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1683" count="0" type="stmt"/>
        <line num="1714" count="0" type="stmt"/>
        <line num="1715" count="0" type="stmt"/>
        <line num="1716" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1717" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1718" count="0" type="stmt"/>
        <line num="1719" count="0" type="stmt"/>
        <line num="1720" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1721" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1728" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1736" count="0" type="stmt"/>
        <line num="1768" count="0" type="stmt"/>
        <line num="1769" count="0" type="stmt"/>
        <line num="1770" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1771" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1772" count="0" type="stmt"/>
        <line num="1773" count="0" type="stmt"/>
        <line num="1774" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1775" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1782" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1790" count="0" type="stmt"/>
        <line num="1828" count="0" type="stmt"/>
        <line num="1852" count="0" type="stmt"/>
        <line num="1876" count="0" type="stmt"/>
        <line num="1909" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1910" count="0" type="stmt"/>
        <line num="1939" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1940" count="0" type="stmt"/>
        <line num="1964" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1965" count="0" type="stmt"/>
        <line num="1993" count="0" type="stmt"/>
        <line num="1994" count="0" type="stmt"/>
        <line num="1995" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1996" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1997" count="0" type="stmt"/>
        <line num="1998" count="0" type="stmt"/>
        <line num="1999" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2000" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2007" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2015" count="0" type="stmt"/>
        <line num="2042" count="0" type="stmt"/>
        <line num="2043" count="0" type="stmt"/>
        <line num="2044" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2045" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2046" count="0" type="stmt"/>
        <line num="2047" count="0" type="stmt"/>
        <line num="2048" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2049" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2056" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2064" count="0" type="stmt"/>
        <line num="2091" count="0" type="stmt"/>
        <line num="2092" count="0" type="stmt"/>
        <line num="2093" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2094" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2095" count="0" type="stmt"/>
        <line num="2096" count="0" type="stmt"/>
        <line num="2097" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2098" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2113" count="0" type="stmt"/>
        <line num="2135" count="0" type="stmt"/>
        <line num="2178" count="0" type="stmt"/>
        <line num="2179" count="0" type="stmt"/>
        <line num="2181" count="0" type="stmt"/>
        <line num="2182" count="0" type="stmt"/>
      </file>
      <file name="ShowroomManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\ShowroomManager.vue">
        <metrics statements="176" coveredstatements="0" conditionals="109" coveredconditionals="0" methods="77" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="283" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="284" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="409" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="428" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="461" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="462" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="497" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="498" count="0" type="stmt"/>
        <line num="500" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="541" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="546" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="547" count="0" type="stmt"/>
        <line num="549" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="590" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="595" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="596" count="0" type="stmt"/>
        <line num="598" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="622" count="0" type="stmt"/>
        <line num="636" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
        <line num="766" count="0" type="stmt"/>
        <line num="783" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="818" count="0" type="stmt"/>
        <line num="834" count="0" type="stmt"/>
        <line num="851" count="0" type="stmt"/>
        <line num="874" count="0" type="stmt"/>
        <line num="942" count="0" type="stmt"/>
        <line num="959" count="0" type="stmt"/>
        <line num="976" count="0" type="stmt"/>
        <line num="994" count="0" type="stmt"/>
        <line num="1010" count="0" type="stmt"/>
        <line num="1027" count="0" type="stmt"/>
        <line num="1086" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1087" count="0" type="stmt"/>
        <line num="1115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1116" count="0" type="stmt"/>
        <line num="1179" count="0" type="stmt"/>
        <line num="1181" count="0" type="stmt"/>
        <line num="1184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1185" count="0" type="stmt"/>
        <line num="1187" count="0" type="stmt"/>
        <line num="1216" count="0" type="stmt"/>
        <line num="1226" count="0" type="stmt"/>
        <line num="1331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1332" count="0" type="stmt"/>
        <line num="1360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1361" count="0" type="stmt"/>
        <line num="1424" count="0" type="stmt"/>
        <line num="1426" count="0" type="stmt"/>
        <line num="1429" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1430" count="0" type="stmt"/>
        <line num="1432" count="0" type="stmt"/>
        <line num="1495" count="0" type="stmt"/>
        <line num="1524" count="0" type="stmt"/>
        <line num="1537" count="0" type="stmt"/>
        <line num="1604" count="0" type="stmt"/>
        <line num="1606" count="0" type="stmt"/>
        <line num="1607" count="0" type="stmt"/>
        <line num="1608" count="0" type="stmt"/>
        <line num="1627" count="0" type="stmt"/>
        <line num="1629" count="0" type="stmt"/>
        <line num="1630" count="0" type="stmt"/>
      </file>
      <file name="SubscriptionManager.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\SubscriptionManager.vue">
        <metrics statements="318" coveredstatements="0" conditionals="204" coveredconditionals="0" methods="89" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="395" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="397" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="465" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="466" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="467" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="476" count="0" type="stmt"/>
        <line num="478" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="479" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="486" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="490" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="499" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="504" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="508" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="518" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="519" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="520" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="525" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="532" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="537" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="538" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="574" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="575" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="631" count="0" type="stmt"/>
        <line num="636" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="645" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="647" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="657" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="659" count="0" type="stmt"/>
        <line num="660" count="0" type="stmt"/>
        <line num="662" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="671" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="673" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="674" count="0" type="stmt"/>
        <line num="676" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="681" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
        <line num="688" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="693" count="0" type="stmt"/>
        <line num="696" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="701" count="0" type="stmt"/>
        <line num="704" count="0" type="stmt"/>
        <line num="707" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
        <line num="709" count="0" type="stmt"/>
        <line num="710" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="755" count="0" type="stmt"/>
        <line num="759" count="0" type="stmt"/>
        <line num="764" count="0" type="stmt"/>
        <line num="765" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="768" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="769" count="0" type="stmt"/>
        <line num="773" count="0" type="stmt"/>
        <line num="815" count="0" type="stmt"/>
        <line num="825" count="0" type="stmt"/>
        <line num="826" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="829" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="830" count="0" type="stmt"/>
        <line num="834" count="0" type="stmt"/>
        <line num="867" count="0" type="stmt"/>
        <line num="889" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="890" count="0" type="stmt"/>
        <line num="891" count="0" type="stmt"/>
        <line num="892" count="0" type="stmt"/>
        <line num="893" count="0" type="stmt"/>
        <line num="954" count="0" type="stmt"/>
        <line num="976" count="0" type="stmt"/>
        <line num="989" count="0" type="stmt"/>
        <line num="1020" count="0" type="stmt"/>
        <line num="1022" count="0" type="stmt"/>
        <line num="1025" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1026" count="0" type="stmt"/>
        <line num="1028" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1062" count="0" type="stmt"/>
        <line num="1079" count="0" type="stmt"/>
        <line num="1089" count="0" type="stmt"/>
        <line num="1155" count="0" type="stmt"/>
        <line num="1184" count="0" type="stmt"/>
        <line num="1284" count="0" type="stmt"/>
        <line num="1324" count="0" type="stmt"/>
        <line num="1359" count="0" type="stmt"/>
        <line num="1377" count="0" type="stmt"/>
        <line num="1418" count="0" type="stmt"/>
        <line num="1420" count="0" type="stmt"/>
        <line num="1423" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1424" count="0" type="stmt"/>
        <line num="1426" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1456" count="0" type="stmt"/>
        <line num="1666" count="0" type="stmt"/>
        <line num="1714" count="0" type="stmt"/>
        <line num="1774" count="0" type="stmt"/>
        <line num="1794" count="0" type="stmt"/>
        <line num="1818" count="0" type="stmt"/>
        <line num="1844" count="0" type="stmt"/>
        <line num="1857" count="0" type="stmt"/>
        <line num="1868" count="0" type="stmt"/>
        <line num="1901" count="0" type="stmt"/>
        <line num="1929" count="0" type="stmt"/>
        <line num="1966" count="0" type="stmt"/>
        <line num="2006" count="0" type="stmt"/>
        <line num="2008" count="0" type="stmt"/>
        <line num="2011" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2012" count="0" type="stmt"/>
        <line num="2014" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2071" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2072" count="0" type="stmt"/>
        <line num="2087" count="0" type="stmt"/>
        <line num="2119" count="0" type="stmt"/>
        <line num="2152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2153" count="0" type="stmt"/>
        <line num="2190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2191" count="0" type="stmt"/>
        <line num="2224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2225" count="0" type="stmt"/>
        <line num="2258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2259" count="0" type="stmt"/>
        <line num="2288" count="0" type="stmt"/>
        <line num="2289" count="0" type="stmt"/>
        <line num="2290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2292" count="0" type="stmt"/>
        <line num="2293" count="0" type="stmt"/>
        <line num="2294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="2310" count="0" type="stmt"/>
        <line num="2330" count="0" type="stmt"/>
        <line num="2365" count="0" type="stmt"/>
        <line num="2426" count="0" type="stmt"/>
        <line num="2447" count="0" type="stmt"/>
        <line num="2449" count="0" type="stmt"/>
        <line num="2450" count="0" type="stmt"/>
        <line num="2451" count="0" type="stmt"/>
        <line num="2456" count="0" type="stmt"/>
        <line num="2457" count="0" type="stmt"/>
        <line num="2458" count="0" type="stmt"/>
        <line num="2473" count="0" type="stmt"/>
        <line num="2474" count="0" type="stmt"/>
        <line num="2475" count="0" type="stmt"/>
        <line num="2486" count="0" type="stmt"/>
        <line num="2488" count="0" type="stmt"/>
        <line num="2489" count="0" type="stmt"/>
      </file>
      <file name="TeamMemberManagement.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\TeamMemberManagement.vue">
        <metrics statements="194" coveredstatements="0" conditionals="123" coveredconditionals="0" methods="65" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="438" count="0" type="stmt"/>
        <line num="440" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="483" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="488" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="489" count="0" type="stmt"/>
        <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="523" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="735" count="0" type="stmt"/>
        <line num="750" count="0" type="stmt"/>
        <line num="758" count="0" type="stmt"/>
        <line num="775" count="0" type="stmt"/>
        <line num="793" count="0" type="stmt"/>
        <line num="818" count="0" type="stmt"/>
        <line num="820" count="0" type="stmt"/>
        <line num="823" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="824" count="0" type="stmt"/>
        <line num="826" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="860" count="0" type="stmt"/>
        <line num="891" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="892" count="0" type="stmt"/>
        <line num="921" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="922" count="0" type="stmt"/>
        <line num="954" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="955" count="0" type="stmt"/>
        <line num="985" count="0" type="stmt"/>
        <line num="987" count="0" type="stmt"/>
        <line num="990" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="991" count="0" type="stmt"/>
        <line num="993" count="0" type="stmt"/>
        <line num="1054" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1055" count="0" type="stmt"/>
        <line num="1069" count="0" type="stmt"/>
        <line num="1102" count="0" type="stmt"/>
        <line num="1140" count="0" type="stmt"/>
        <line num="1161" count="0" type="stmt"/>
        <line num="1163" count="0" type="stmt"/>
        <line num="1164" count="0" type="stmt"/>
        <line num="1165" count="0" type="stmt"/>
        <line num="1182" count="0" type="stmt"/>
        <line num="1183" count="0" type="stmt"/>
        <line num="1184" count="0" type="stmt"/>
        <line num="1193" count="0" type="stmt"/>
        <line num="1195" count="0" type="stmt"/>
        <line num="1196" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.components.GuidedSetupWizard">
      <metrics statements="251" coveredstatements="0" conditionals="241" coveredconditionals="0" methods="73" coveredmethods="0"/>
      <file name="GuidedSetupWizard.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\GuidedSetupWizard.vue">
        <metrics statements="45" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="69" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
      </file>
      <file name="WizardContainer.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\WizardContainer.vue">
        <metrics statements="122" coveredstatements="0" conditionals="148" coveredconditionals="0" methods="42" coveredmethods="0"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="131" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="17"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="590" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
      </file>
      <file name="WizardStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\WizardStep.vue">
        <metrics statements="63" coveredstatements="0" conditionals="77" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="61" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\index.js">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="run-tests.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\run-tests.js">
        <metrics statements="21" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.components.GuidedSetupWizard.components">
      <metrics statements="185" coveredstatements="0" conditionals="139" coveredconditionals="0" methods="73" coveredmethods="0"/>
      <file name="DocumentationReference.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\components\DocumentationReference.vue">
        <metrics statements="54" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
      </file>
      <file name="HelpTooltip.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\components\HelpTooltip.vue">
        <metrics statements="54" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="24" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
      </file>
      <file name="VideoTutorial.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\components\VideoTutorial.vue">
        <metrics statements="77" coveredstatements="0" conditionals="68" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="84" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.components.GuidedSetupWizard.steps">
      <metrics statements="457" coveredstatements="0" conditionals="323" coveredconditionals="0" methods="191" coveredmethods="0"/>
      <file name="BrandingSetupStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\steps\BrandingSetupStep.vue">
        <metrics statements="55" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="33" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="340" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="425" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="426" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="518" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="684" count="0" type="stmt"/>
        <line num="686" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
      </file>
      <file name="CompanyProfileStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\steps\CompanyProfileStep.vue">
        <metrics statements="55" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="280" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="324" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="397" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="429" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="430" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
      </file>
      <file name="CompletionStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\steps\CompletionStep.vue">
        <metrics statements="16" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
      </file>
      <file name="ProductUploadStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\steps\ProductUploadStep.vue">
        <metrics statements="97" coveredstatements="0" conditionals="75" coveredconditionals="0" methods="41" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="587" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="588" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="618" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="619" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="679" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="680" count="0" type="stmt"/>
        <line num="715" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="716" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="833" count="0" type="stmt"/>
        <line num="835" count="0" type="stmt"/>
        <line num="836" count="0" type="stmt"/>
      </file>
      <file name="ShowroomConfigStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\steps\ShowroomConfigStep.vue">
        <metrics statements="168" coveredstatements="0" conditionals="146" coveredconditionals="0" methods="54" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="438" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="439" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="507" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="508" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="575" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="576" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="577" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="579" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="580" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="587" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="595" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="648" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="651" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="652" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="659" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="667" count="0" type="stmt"/>
        <line num="675" count="0" type="stmt"/>
        <line num="710" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="712" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="713" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="714" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="716" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="717" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="724" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="732" count="0" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="772" count="0" type="stmt"/>
        <line num="773" count="0" type="stmt"/>
        <line num="774" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="775" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="776" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="778" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="779" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="786" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="794" count="0" type="stmt"/>
        <line num="802" count="0" type="stmt"/>
        <line num="835" count="0" type="stmt"/>
        <line num="836" count="0" type="stmt"/>
        <line num="837" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="838" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="839" count="0" type="stmt"/>
        <line num="840" count="0" type="stmt"/>
        <line num="841" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="842" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="849" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="857" count="0" type="stmt"/>
        <line num="861" count="0" type="stmt"/>
        <line num="896" count="0" type="stmt"/>
        <line num="897" count="0" type="stmt"/>
        <line num="898" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="899" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="900" count="0" type="stmt"/>
        <line num="901" count="0" type="stmt"/>
        <line num="902" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="903" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="910" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="918" count="0" type="stmt"/>
        <line num="926" count="0" type="stmt"/>
        <line num="968" count="0" type="stmt"/>
        <line num="970" count="0" type="stmt"/>
        <line num="973" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="974" count="0" type="stmt"/>
        <line num="976" count="0" type="stmt"/>
        <line num="985" count="0" type="stmt"/>
        <line num="1055" count="0" type="stmt"/>
        <line num="1057" count="0" type="stmt"/>
        <line num="1058" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1059" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1060" count="0" type="stmt"/>
        <line num="1061" count="0" type="stmt"/>
        <line num="1062" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1063" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1070" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1078" count="0" type="stmt"/>
        <line num="1086" count="0" type="stmt"/>
        <line num="1130" count="0" type="stmt"/>
        <line num="1132" count="0" type="stmt"/>
        <line num="1133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1135" count="0" type="stmt"/>
        <line num="1136" count="0" type="stmt"/>
        <line num="1137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1153" count="0" type="stmt"/>
        <line num="1161" count="0" type="stmt"/>
        <line num="1202" count="0" type="stmt"/>
        <line num="1203" count="0" type="stmt"/>
        <line num="1204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1206" count="0" type="stmt"/>
        <line num="1207" count="0" type="stmt"/>
        <line num="1208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1224" count="0" type="stmt"/>
        <line num="1232" count="0" type="stmt"/>
        <line num="1268" count="0" type="stmt"/>
        <line num="1269" count="0" type="stmt"/>
        <line num="1270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1272" count="0" type="stmt"/>
        <line num="1273" count="0" type="stmt"/>
        <line num="1274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1290" count="0" type="stmt"/>
        <line num="1298" count="0" type="stmt"/>
        <line num="1318" count="0" type="stmt"/>
        <line num="1319" count="0" type="stmt"/>
        <line num="1321" count="0" type="stmt"/>
        <line num="1322" count="0" type="stmt"/>
      </file>
      <file name="UserAccountStep.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\steps\UserAccountStep.vue">
        <metrics statements="66" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="482" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="483" count="0" type="stmt"/>
        <line num="515" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="516" count="0" type="stmt"/>
        <line num="544" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="545" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="575" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="576" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="633" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="636" count="0" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.components.GuidedSetupWizard.utils">
      <metrics statements="93" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="StepDependencyManager.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\GuidedSetupWizard\utils\StepDependencyManager.js">
        <metrics statements="93" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="12"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.components.VisualEditors">
      <metrics statements="733" coveredstatements="0" conditionals="463" coveredconditionals="0" methods="283" coveredmethods="0"/>
      <file name="AnimationEditor.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\VisualEditors\AnimationEditor.vue">
        <metrics statements="228" coveredstatements="0" conditionals="154" coveredconditionals="0" methods="83" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="425" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="433" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="447" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="450" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="462" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="463" count="0" type="stmt"/>
        <line num="467" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="468" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="733" count="0" type="stmt"/>
        <line num="747" count="0" type="stmt"/>
        <line num="748" count="0" type="stmt"/>
        <line num="820" count="0" type="stmt"/>
        <line num="837" count="0" type="stmt"/>
        <line num="873" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="874" count="0" type="stmt"/>
        <line num="915" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="916" count="0" type="stmt"/>
        <line num="925" count="0" type="stmt"/>
        <line num="961" count="0" type="stmt"/>
        <line num="962" count="0" type="stmt"/>
        <line num="963" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="964" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="965" count="0" type="stmt"/>
        <line num="966" count="0" type="stmt"/>
        <line num="967" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="968" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="975" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="985" count="0" type="stmt"/>
        <line num="1036" count="0" type="stmt"/>
        <line num="1041" count="0" type="stmt"/>
        <line num="1090" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1091" count="0" type="stmt"/>
        <line num="1100" count="0" type="stmt"/>
        <line num="1126" count="0" type="stmt"/>
        <line num="1130" count="0" type="stmt"/>
        <line num="1135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1136" count="0" type="stmt"/>
        <line num="1138" count="0" type="stmt"/>
        <line num="1218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1219" count="0" type="stmt"/>
        <line num="1220" count="0" type="stmt"/>
        <line num="1230" count="0" type="stmt"/>
        <line num="1265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1266" count="0" type="stmt"/>
        <line num="1267" count="0" type="stmt"/>
        <line num="1277" count="0" type="stmt"/>
        <line num="1312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1313" count="0" type="stmt"/>
        <line num="1314" count="0" type="stmt"/>
        <line num="1324" count="0" type="stmt"/>
        <line num="1365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1366" count="0" type="stmt"/>
        <line num="1367" count="0" type="stmt"/>
        <line num="1377" count="0" type="stmt"/>
        <line num="1412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1413" count="0" type="stmt"/>
        <line num="1414" count="0" type="stmt"/>
        <line num="1424" count="0" type="stmt"/>
        <line num="1459" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1460" count="0" type="stmt"/>
        <line num="1461" count="0" type="stmt"/>
        <line num="1471" count="0" type="stmt"/>
        <line num="1515" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1516" count="0" type="stmt"/>
        <line num="1517" count="0" type="stmt"/>
        <line num="1527" count="0" type="stmt"/>
        <line num="1565" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1566" count="0" type="stmt"/>
        <line num="1567" count="0" type="stmt"/>
        <line num="1577" count="0" type="stmt"/>
        <line num="1615" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1616" count="0" type="stmt"/>
        <line num="1617" count="0" type="stmt"/>
        <line num="1627" count="0" type="stmt"/>
        <line num="1684" count="0" type="stmt"/>
        <line num="1686" count="0" type="stmt"/>
        <line num="1687" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1688" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1689" count="0" type="stmt"/>
        <line num="1690" count="0" type="stmt"/>
        <line num="1691" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1692" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1699" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1709" count="0" type="stmt"/>
        <line num="1764" count="0" type="stmt"/>
        <line num="1771" count="0" type="stmt"/>
        <line num="1804" count="0" type="stmt"/>
        <line num="1806" count="0" type="stmt"/>
        <line num="1807" count="0" type="stmt"/>
        <line num="1808" count="0" type="stmt"/>
        <line num="1817" count="0" type="stmt"/>
        <line num="1818" count="0" type="stmt"/>
        <line num="1819" count="0" type="stmt"/>
        <line num="1824" count="0" type="stmt"/>
        <line num="1826" count="0" type="stmt"/>
        <line num="1827" count="0" type="stmt"/>
      </file>
      <file name="LightingEditor.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\VisualEditors\LightingEditor.vue">
        <metrics statements="136" coveredstatements="0" conditionals="88" coveredconditionals="0" methods="52" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="451" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="486" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="487" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="490" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="498" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="508" count="0" type="stmt"/>
        <line num="539" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="540" count="0" type="stmt"/>
        <line num="563" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="564" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="700" count="0" type="stmt"/>
        <line num="707" count="0" type="stmt"/>
        <line num="756" count="0" type="stmt"/>
        <line num="763" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="807" count="0" type="stmt"/>
        <line num="846" count="0" type="stmt"/>
        <line num="853" count="0" type="stmt"/>
        <line num="892" count="0" type="stmt"/>
        <line num="899" count="0" type="stmt"/>
        <line num="953" count="0" type="stmt"/>
        <line num="960" count="0" type="stmt"/>
        <line num="1001" count="0" type="stmt"/>
        <line num="1008" count="0" type="stmt"/>
        <line num="1049" count="0" type="stmt"/>
        <line num="1056" count="0" type="stmt"/>
        <line num="1108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1109" count="0" type="stmt"/>
        <line num="1134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1135" count="0" type="stmt"/>
        <line num="1156" count="0" type="stmt"/>
        <line num="1158" count="0" type="stmt"/>
        <line num="1159" count="0" type="stmt"/>
        <line num="1160" count="0" type="stmt"/>
        <line num="1169" count="0" type="stmt"/>
        <line num="1170" count="0" type="stmt"/>
        <line num="1171" count="0" type="stmt"/>
        <line num="1190" count="0" type="stmt"/>
        <line num="1192" count="0" type="stmt"/>
        <line num="1193" count="0" type="stmt"/>
      </file>
      <file name="MaterialTextureEditor.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\VisualEditors\MaterialTextureEditor.vue">
        <metrics statements="122" coveredstatements="0" conditionals="75" coveredconditionals="0" methods="54" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="460" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="461" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="571" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="625" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="626" count="0" type="stmt"/>
        <line num="661" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="662" count="0" type="stmt"/>
        <line num="687" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="688" count="0" type="stmt"/>
        <line num="725" count="0" type="stmt"/>
        <line num="732" count="0" type="stmt"/>
        <line num="775" count="0" type="stmt"/>
        <line num="782" count="0" type="stmt"/>
        <line num="825" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="858" count="0" type="stmt"/>
        <line num="875" count="0" type="stmt"/>
        <line num="878" count="0" type="stmt"/>
        <line num="881" count="0" type="stmt"/>
        <line num="920" count="0" type="stmt"/>
        <line num="921" count="0" type="stmt"/>
        <line num="968" count="0" type="stmt"/>
        <line num="985" count="0" type="stmt"/>
        <line num="1002" count="0" type="stmt"/>
        <line num="1020" count="0" type="stmt"/>
        <line num="1022" count="0" type="stmt"/>
        <line num="1023" count="0" type="stmt"/>
        <line num="1024" count="0" type="stmt"/>
        <line num="1035" count="0" type="stmt"/>
        <line num="1037" count="0" type="stmt"/>
        <line num="1038" count="0" type="stmt"/>
      </file>
      <file name="ProductConfigurator.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\VisualEditors\ProductConfigurator.vue">
        <metrics statements="107" coveredstatements="0" conditionals="72" coveredconditionals="0" methods="39" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="461" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="462" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="751" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="755" count="0" type="stmt"/>
        <line num="766" count="0" type="stmt"/>
        <line num="768" count="0" type="stmt"/>
        <line num="769" count="0" type="stmt"/>
      </file>
      <file name="ShowroomLayoutEditor.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\VisualEditors\ShowroomLayoutEditor.vue">
        <metrics statements="122" coveredstatements="0" conditionals="62" coveredconditionals="0" methods="45" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="377" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="667" count="0" type="stmt"/>
      </file>
      <file name="VisualEditors.vue" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\components\VisualEditors\VisualEditors.vue">
        <metrics statements="18" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.contexts">
      <metrics statements="80" coveredstatements="0" conditionals="58" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="PreviewContext.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\contexts\PreviewContext.js">
        <metrics statements="80" coveredstatements="0" conditionals="58" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.services">
      <metrics statements="54" coveredstatements="18" conditionals="10" coveredconditionals="2" methods="10" coveredmethods="4"/>
      <file name="GuidedSetupService.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\services\GuidedSetupService.js">
        <metrics statements="54" coveredstatements="18" conditionals="10" coveredconditionals="2" methods="10" coveredmethods="4"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="28" count="3" type="stmt"/>
        <line num="29" count="3" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="3" type="stmt"/>
        <line num="33" count="3" type="stmt"/>
        <line num="45" count="3" type="stmt"/>
        <line num="46" count="3" type="stmt"/>
        <line num="48" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="50" count="1" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="3" type="stmt"/>
        <line num="69" count="3" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="218" count="2" type="stmt"/>
        <line num="219" count="2" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="2" type="stmt"/>
        <line num="230" count="2" type="stmt"/>
      </file>
    </package>
    <package name="vendor-portal.src.utils">
      <metrics statements="46" coveredstatements="0" conditionals="60" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="directus.js" path="C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\implementation\server\directus\extensions\interfaces\vendor-portal\src\utils\directus.js">
        <metrics statements="46" coveredstatements="0" conditionals="60" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
