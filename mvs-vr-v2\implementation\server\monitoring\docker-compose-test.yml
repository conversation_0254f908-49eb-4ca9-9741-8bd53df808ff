version: '3.8'

services:
  # Prometheus for metrics storage
  prometheus:
    image: prom/prometheus:latest
    container_name: mvs-vr-prometheus
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - monitoring-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: mvs-vr-grafana
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/etc/grafana/dashboards
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Alert Manager for alert handling
  alertmanager:
    image: prom/alertmanager:latest
    container_name: mvs-vr-alertmanager
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    ports:
      - "9093:9093"
    restart: unless-stopped
    networks:
      - monitoring-network

  # Node Exporter for host metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: mvs-vr-node-exporter
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9101:9100"
    restart: unless-stopped
    networks:
      - monitoring-network

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: mvs-vr-cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    restart: unless-stopped
    networks:
      - monitoring-network

  # Alert Manager Service
  alert-manager-service:
    image: prom/alertmanager:latest
    container_name: mvs-vr-alert-manager-service
    environment:
      - NODE_ENV=production
      - ALERT_MANAGER_PORT=9096
      - SLACK_WEBHOOK=${SLACK_WEBHOOK:-}
      - EMAIL_RECIPIENTS=${EMAIL_RECIPIENTS:-}
    ports:
      - "9096:9096"
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - alertmanager

  # Real-time Anomaly Detector
  real-time-anomaly-detector:
    image: mvs-vr/real-time-anomaly-detector:latest
    build:
      context: .
      dockerfile: Dockerfile.real-time-anomaly-detector
    container_name: mvs-vr-real-time-anomaly-detector
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - REAL_TIME_ANOMALY_DETECTOR_PORT=9112
      - METRICS_ENDPOINT=http://prometheus:9090/api/v1/query
      - ALERT_MANAGER_ENDPOINT=http://alert-manager-service:9096/api/alerts
      - KAFKA_BROKERS=kafka:9092
    ports:
      - "9112:9112"
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - alert-manager-service

  # Predictive Scaling Service
  predictive-scaling-service:
    image: mvs-vr/predictive-scaling-service:latest
    build:
      context: .
      dockerfile: Dockerfile.predictive-scaling-service
    container_name: mvs-vr-predictive-scaling-service
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - PREDICTIVE_SCALING_SERVICE_PORT=9113
      - METRICS_ENDPOINT=http://prometheus:9090/api/v1/query
      - KUBERNETES_API_ENDPOINT=http://kubernetes-api:8080
      - KUBERNETES_NAMESPACE=mvs-vr
      - ENABLE_AUTO_SCALING=false
    ports:
      - "9113:9113"
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - prometheus

  # Incident Management Integration
  incident-management-integration:
    image: mvs-vr/incident-management-integration:latest
    build:
      context: .
      dockerfile: Dockerfile.incident-management-integration
    container_name: mvs-vr-incident-management-integration
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - INCIDENT_MANAGEMENT_INTEGRATION_PORT=9114
      - ALERT_MANAGER_ENDPOINT=http://alert-manager-service:9096/api/alerts
      - PAGERDUTY_ENABLED=false
      - OPSGENIE_ENABLED=false
      - SERVICENOW_ENABLED=false
      - JIRA_ENABLED=false
    ports:
      - "9114:9114"
    restart: unless-stopped
    networks:
      - monitoring-network
    depends_on:
      - alert-manager-service

networks:
  monitoring-network:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:
