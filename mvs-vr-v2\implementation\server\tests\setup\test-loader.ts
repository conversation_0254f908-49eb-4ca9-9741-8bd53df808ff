import { fileURLToPath, pathToFileURL } from 'url';
import { dirname, resolve } from 'path';
import * as dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const ROOT_DIR = resolve(__dirname, '../..');

// Load environment variables
dotenv.config({ path: resolve(ROOT_DIR, '.env.test') });

// Configure module loader for testing environment
const moduleLoader = {
  async load(specifier: string, context: any = {}) {
    // Handle npm packages
    if (specifier.startsWith('npm:')) {
      const packageName = specifier.slice(4);
      return import(resolve(ROOT_DIR, 'node_modules', packageName));
    }

    // Handle Node.js built-in modules
    if (specifier.startsWith('node:')) {
      return import(specifier);
    }

    // Handle local aliases
    const aliases: Record<string, string> = {
      '@': resolve(ROOT_DIR, 'src'),
      '@directus': resolve(ROOT_DIR, 'directus/extensions'),
      '@shared': resolve(ROOT_DIR, 'shared'),
      '@services': resolve(ROOT_DIR, 'services'),
      '@tests': resolve(ROOT_DIR, 'tests'),
      '@setup': resolve(ROOT_DIR, 'tests/setup'),
    };

    for (const [alias, path] of Object.entries(aliases)) {
      if (specifier.startsWith(alias)) {
        const resolvedPath = specifier.replace(alias, path);
        return import(pathToFileURL(resolvedPath).href);
      }
    }

    // Handle relative imports
    if (specifier.startsWith('.')) {
      const resolvedPath = resolve(dirname(context.parentURL || __filename), specifier);
      return import(pathToFileURL(resolvedPath).href);
    }

    // Default to node_modules for other imports
    return import(resolve(ROOT_DIR, 'node_modules', specifier));
  },
};

// Register loader
if (typeof Deno !== 'undefined') {
  // @ts-ignore: Deno specific
  Deno.customLoaders = Deno.customLoaders || [];
  // @ts-ignore: Deno specific
  Deno.customLoaders.push(moduleLoader);
}

export { moduleLoader };
