/**
 * Deployment script for vendor-portal extensions
 * This script builds and deploys the vendor-portal extensions to production
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { logger } = require('../shared/utils/logger');

// Configuration
const config = {
  // Source directory (current directory)
  sourceDir: __dirname,
  
  // Build directory
  buildDir: path.join(__dirname, 'dist'),
  
  // Deployment directory
  deployDir: path.join(__dirname, 'deploy'),
  
  // Files to include in deployment
  includeFiles: [
    'dist/**/*',
    'package.json',
    'README.md'
  ],
  
  // Files to exclude from deployment
  excludeFiles: [
    'node_modules/**/*',
    'src/**/*',
    'tests/**/*',
    '*.test.js',
    '*.spec.js',
    'deploy.js'
  ]
};

/**
 * Clean the build directory
 */
function cleanBuildDir() {
  logger.info('Cleaning build directory...');
  
  if (fs.existsSync(config.buildDir)) {
    fs.rmSync(config.buildDir, { recursive: true, force: true });
  }
  
  fs.mkdirSync(config.buildDir, { recursive: true });
  logger.info('Build directory cleaned.');
}

/**
 * Build the project
 */
function buildProject() {
  logger.info('Building project...');
  
  try {
    // Run the build command
    execSync('npm run build', { stdio: 'inherit' });
    logger.info('Build completed successfully.');
  } catch (error) {
    console.error('Build failed:', error.message);
    process.exit(1);
  }
}

/**
 * Prepare deployment directory
 */
function prepareDeployDir() {
  logger.info('Preparing deployment directory...');
  
  if (fs.existsSync(config.deployDir)) {
    fs.rmSync(config.deployDir, { recursive: true, force: true });
  }
  
  fs.mkdirSync(config.deployDir, { recursive: true });
  logger.info('Deployment directory prepared.');
}

/**
 * Copy files to deployment directory
 */
function copyFilesToDeploy() {
  logger.info('Copying files to deployment directory...');
  
  // Copy dist directory
  fs.cpSync(config.buildDir, path.join(config.deployDir, 'dist'), { recursive: true });
  
  // Copy package.json
  fs.copyFileSync(
    path.join(config.sourceDir, 'package.json'),
    path.join(config.deployDir, 'package.json')
  );
  
  // Copy README.md if it exists
  const readmePath = path.join(config.sourceDir, 'README.md');
  if (fs.existsSync(readmePath)) {
    fs.copyFileSync(readmePath, path.join(config.deployDir, 'README.md'));
  }
  
  logger.info('Files copied to deployment directory.');
}

/**
 * Deploy to production
 */
function deployToProduction() {
  logger.info('Deploying to production...');
  
  try {
    // Create a deployment package
    const deployPackage = path.join(config.sourceDir, 'vendor-portal-extension.zip');
    
    // Create zip file
    execSync(`cd "${config.deployDir}" && zip -r "${deployPackage}" .`, { stdio: 'inherit' });
    
    logger.info(`Deployment package created at: ${deployPackage}`);
    
    // In a real environment, you would upload this package to your server
    // For example:

    logger.info('Deployment completed successfully.');
  } catch (error) {
    console.error('Deployment failed:', error.message);
    process.exit(1);
  }
}

/**
 * Run the deployment process
 */
function runDeployment() {
  logger.info('Starting deployment process...');
  
  // Clean build directory
  cleanBuildDir();
  
  // Build the project
  buildProject();
  
  // Prepare deployment directory
  prepareDeployDir();
  
  // Copy files to deployment directory
  copyFilesToDeploy();
  
  // Deploy to production
  deployToProduction();
  
  logger.info('Deployment process completed.');
}

// Run the deployment
runDeployment();
