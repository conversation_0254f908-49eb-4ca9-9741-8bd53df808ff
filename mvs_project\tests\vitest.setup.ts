import { supabase } from '../utils/supabase';
import { beforeAll, afterAll, expect, vi } from 'vitest';
import dotenv from 'dotenv';
import type { User } from '@supabase/supabase-js';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_ANON_KEY || ''
);

// Set up global beforeAll and afterAll hooks
beforeAll(async () => {
  // Extend timeout for integration tests
  vi.setConfig({ testTimeout: 30000 });

  // Verify Supabase connection
  const { data, error } = await supabase.auth.getSession();
  if (error) {
    throw new Error(`Failed to connect to Supabase: ${error.message}`);
  }

  // Verify required environment variables
  const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY', 'SUPABASE_SERVICE_ROLE_KEY'];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }
});

afterAll(async () => {
  // Close Supabase connection
  await supabase.auth.signOut();
});

// Global error handler for unhandled promises
process.on('unhandledRejection', error => {
  console.error('Unhandled Promise Rejection:', error);
  process.exit(1);
});

// Custom matchers for Supabase testing
expect.extend({
  toHaveValidUUID(received: string) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    return {
      message: () => `expected ${received} to be${pass ? ' not' : ''} a valid UUID`,
      pass,
    };
  },

  toHaveValidTimestamp(received: string) {
    const timestamp = new Date(received).getTime();
    const pass = !isNaN(timestamp);
    return {
      message: () => `expected ${received} to be${pass ? ' not' : ''} a valid timestamp`,
      pass,
    };
  },

  toHaveRequiredKeys(received: Record<string, unknown>, keys: string[]) {
    const missingKeys = keys.filter(key => !(key in received));
    const pass = missingKeys.length === 0;
    return {
      message: () =>
        pass
          ? `expected object to not have all required keys`
          : `expected object to have keys: ${missingKeys.join(', ')}`,
      pass,
    };
  },
});

// Test data types
export interface TestUser extends User {
  name: string;
}

export interface TestVendor {
  id: string;
  email: string;
  name: string;
  user?: TestUser;
}

export interface TestAsset {
  id: string;
  name: string;
  vendor_id: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

// Mock data for tests
export const TEST_VENDOR: TestVendor = {
  id: '00000000-0000-0000-0000-000000000001',
  email: '<EMAIL>',
  name: 'Test Vendor',
};

export const TEST_ASSET: TestAsset = {
  id: '00000000-0000-0000-0000-000000000002',
  name: 'Test Asset',
  vendor_id: TEST_VENDOR.id,
  status: 'draft',
};

// Test environment helper functions
export const generateTestId = (): string => {
  return Math.random().toString(36).substring(7);
};

export const waitForCondition = async (
  condition: () => Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<boolean> => {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }

  return false;
};
