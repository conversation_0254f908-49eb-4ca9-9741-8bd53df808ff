/**
 * Rate Limit Monitoring Service Tests
 * 
 * This file contains tests for the rate limit monitoring service.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import Redis from 'ioredis';

// Mock dependencies
vi.mock('ioredis', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
      del: vi.fn(),
      incr: vi.fn(),
      expire: vi.fn(),
      hincrby: vi.fn(),
      hset: vi.fn(),
      hget: vi.fn(),
      hgetall: vi.fn(),
      lpush: vi.fn(),
      ltrim: vi.fn(),
      keys: vi.fn(),
    })),
  };
});

// Mock logger
const mockLogger = {
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock auth-middleware
vi.mock('../../api/middleware/auth-middleware', () => ({
  logger: mockLogger,
  redis: new Redis(),
}));

// Import the module under test
import {
  trackRateLimitEvent,
  getMetrics,
  getTopRateLimitedIPs,
  rateLimitEvents,
} from '../../services/monitoring/rate-limit-monitor.js';

describe('Rate Limit Monitoring Service', () => {
  // Define type for Redis mock
  type MockRedis = {
    get: ReturnType<typeof vi.fn>;
    set: ReturnType<typeof vi.fn>;
    del: ReturnType<typeof vi.fn>;
    incr: ReturnType<typeof vi.fn>;
    expire: ReturnType<typeof vi.fn>;
    hincrby: ReturnType<typeof vi.fn>;
    hset: ReturnType<typeof vi.fn>;
    hget: ReturnType<typeof vi.fn>;
    hgetall: ReturnType<typeof vi.fn>;
    lpush: ReturnType<typeof vi.fn>;
    ltrim: ReturnType<typeof vi.fn>;
    keys: ReturnType<typeof vi.fn>;
  };
  
  let mockRedis: MockRedis;

  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();

    // Get Redis mock from auth-middleware
    mockRedis = (await import('../../api/middleware/auth-middleware.js')).redis;
  });

  describe('trackRateLimitEvent', () => {
    it('should track a rate limit event', async () => {
      const eventType = 'RATE_LIMIT_EXCEEDED';
      const eventData = {
        ip: '127.0.0.1',
        path: '/api/test',
        method: 'GET',
        userId: 'user-id',
        apiKeyId: 'key-id',
      };

      await trackRateLimitEvent(eventType, eventData);

      // Check Redis operations
      expect(mockRedis.lpush).toHaveBeenCalledWith(
        'rate-limit:events:RATE_LIMIT_EXCEEDED',
        expect.any(String)
      );
      expect(mockRedis.ltrim).toHaveBeenCalledWith(
        'rate-limit:events:RATE_LIMIT_EXCEEDED',
        0,
        999
      );

      // Check metrics updates
      expect(mockRedis.hincrby).toHaveBeenCalled();
      expect(mockRedis.expire).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      mockRedis.lpush.mockRejectedValue(new Error('Redis error'));

      const eventType = 'RATE_LIMIT_EXCEEDED';
      const eventData = {
        ip: '127.0.0.1',
        path: '/api/test',
      };

      await trackRateLimitEvent(eventType, eventData);

      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getMetrics', () => {
    it('should get metrics for a specific timeframe', async () => {
      const mockMetrics = {
        count: '10',
        avg_response_time: '150',
      };

      mockRedis.hgetall.mockResolvedValue(mockMetrics);

      const result = await getMetrics('RATE_LIMIT_EXCEEDED', 'minute', 123);

      expect(mockRedis.hgetall).toHaveBeenCalledWith(
        'rate-limit:metrics:RATE_LIMIT_EXCEEDED:minute:123'
      );
      expect(result).toEqual(mockMetrics);
    });

    it('should handle errors gracefully', async () => {
      mockRedis.hgetall.mockRejectedValue(new Error('Redis error'));

      const result = await getMetrics('RATE_LIMIT_EXCEEDED', 'minute', 123);

      expect(mockLogger.error).toHaveBeenCalled();
      expect(result).toEqual({});
    });
  });

  describe('getTopRateLimitedIPs', () => {
    it('should get top rate limited IPs', async () => {
      mockRedis.keys.mockResolvedValue([
        'rate-limit:metrics:RATE_LIMIT_EXCEEDED:ip:127.0.0.1',
        'rate-limit:metrics:RATE_LIMIT_EXCEEDED:ip:***********',
      ]);

      mockRedis.hget
        .mockResolvedValueOnce('10') // 127.0.0.1 count
        .mockResolvedValueOnce('5'); // *********** count

      const result = await getTopRateLimitedIPs('RATE_LIMIT_EXCEEDED', 2);

      expect(mockRedis.keys).toHaveBeenCalledWith(
        'rate-limit:metrics:RATE_LIMIT_EXCEEDED:ip:*'
      );
      expect(mockRedis.hget).toHaveBeenCalledTimes(2);
      expect(result).toEqual([
        { ip: '127.0.0.1', count: 10 },
        { ip: '***********', count: 5 },
      ]);
    });

    it('should handle errors gracefully', async () => {
      mockRedis.keys.mockRejectedValue(new Error('Redis error'));

      const result = await getTopRateLimitedIPs('RATE_LIMIT_EXCEEDED');

      expect(mockLogger.error).toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe('rateLimitEvents', () => {
    it('should be an EventEmitter', () => {
      expect(rateLimitEvents.on).toBeDefined();
      expect(rateLimitEvents.emit).toBeDefined();
    });
  });
});
