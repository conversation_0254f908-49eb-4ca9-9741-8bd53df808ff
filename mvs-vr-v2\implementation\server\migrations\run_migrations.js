/**
 * Run Migrations
 * 
 * This script runs all migrations in the migrations directory.
 */

const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

async function run() {
  try {
    logger.info('Starting migrations');
    
    // Get all migration files
    const migrationFiles = fs.readdirSync(__dirname)
      .filter(file => file.endsWith('.js') && file !== 'run_migrations.js')
      .sort();
    
    logger.info(`Found ${migrationFiles.length} migrations`);
    
    // Run each migration
    for (const file of migrationFiles) {
      logger.info(`Running migration: ${file}`);
      
      const migration = require(path.join(__dirname, file));
      
      if (typeof migration.run === 'function') {
        await migration.run();
      } else {
        logger.warn(`Migration ${file} does not export a run function`);
      }
    }
    
    logger.info('All migrations completed');
  } catch (error) {
    logger.error('Error running migrations', { error: error.message });
  }
}

// Run migrations if called directly
if (require.main === module) {
  run().then(() => {
    process.exit(0);
  }).catch(error => {
    logger.error('Migrations failed', { error: error.message });
    process.exit(1);
  });
}

module.exports = { run };
