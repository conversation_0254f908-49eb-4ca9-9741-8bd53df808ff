/**
 * Client Capability Detection Middleware
 *
 * This middleware detects client capabilities from request headers and adds them to the request object.
 */

const logger = require('../../utils/logger').getLogger('client-capability-detection');
const { detectClientCapabilities } = require('../../services/assets/adaptive-compression');

/**
 * Create client capability detection middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function createClientCapabilityDetectionMiddleware(options = {}) {
  const {
    enableClientHints = true,
    enableUserAgentParsing = true,
    enableNetworkInformation = true,
    enableSaveToSession = true,
  } = options;

  return async (req, res, next) => {
    try {
      // Detect client capabilities from headers
      const clientCapabilities = detectClientCapabilities(req.headers);

      // Add client capabilities to request object
      req.clientCapabilities = clientCapabilities;

      // Add client hints headers to response if enabled
      if (enableClientHints) {
        res.set(
          'Accept-CH',
          'Device-Memory, Viewport-Width, Width, DPR, Downlink, ECT, RTT, Save-Data',
        );
        res.set('Accept-CH-Lifetime', '86400'); // 24 hours
        res.set('Critical-CH', 'Device-Memory, ECT');
      }

      // Save client capabilities to session if enabled
      if (enableSaveToSession && req.session) {
        req.session.clientCapabilities = clientCapabilities;
      }

      // Log client capabilities
      logger.debug('Detected client capabilities', { clientCapabilities });

      next();
    } catch (error) {
      logger.error('Error detecting client capabilities', { error });

      // Continue even if detection fails
      next();
    }
  };
}

module.exports = createClientCapabilityDetectionMiddleware;
