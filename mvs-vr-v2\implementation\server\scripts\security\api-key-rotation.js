/**
 * API Key Rotation System
 * 
 * This script implements an API key rotation system with grace period and
 * email notifications.
 */

const crypto = require('crypto');
const { Pool } = require('pg');
const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { logger } = require('../shared/utils/logger');
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

// Configuration
const config = {
  database: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DB || 'postgres'
  },
  email: {
    host: process.env.SMTP_HOST || 'smtp.example.com',
    port: process.env.SMTP_PORT || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '<EMAIL>',
      pass: process.env.SMTP_PASSWORD || 'password'
    },
    from: process.env.SMTP_FROM || '<EMAIL>'
  },
  keyRotation: {
    gracePeriodDays: parseInt(process.env.API_KEY_GRACE_PERIOD_DAYS || '7', 10),
    notificationDays: parseInt(process.env.API_KEY_NOTIFICATION_DAYS || '14', 10),
    keyLength: parseInt(process.env.API_KEY_LENGTH || '32', 10),
    logPath: path.join(__dirname, '../../logs/api-key-rotation.json')
  }
};

// Create database pool
const pool = new Pool(config.database);

// Create email transporter
const transporter = nodemailer.createTransport(config.email);

/**
 * Load rotation log
 * @returns {Object} Rotation log
 */
async function loadRotationLog() {
  try {
    if (fs.existsSync(config.keyRotation.logPath)) {
      const data = await readFileAsync(config.keyRotation.logPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading rotation log:', error);
  }
  
  return {
    lastRun: null,
    rotations: []
  };
}

/**
 * Save rotation log
 * @param {Object} log - Rotation log
 */
async function saveRotationLog(log) {
  try {
    await writeFileAsync(
      config.keyRotation.logPath,
      JSON.stringify(log, null, 2),
      'utf8'
    );
  } catch (error) {
    console.error('Error saving rotation log:', error);
  }
}

/**
 * Generate a new API key
 * @returns {string} New API key
 */
function generateApiKey() {
  return crypto.randomBytes(config.keyRotation.keyLength).toString('hex');
}

/**
 * Get API keys due for rotation
 * @returns {Array} API keys due for rotation
 */
async function getKeysForRotation() {
  const client = await pool.connect();
  
  try {
    // Get keys that haven't been rotated in the last 90 days
    const result = await client.query(`
      SELECT 
        k.id, 
        k.name, 
        k.key, 
        k.created_at, 
        k.last_rotated_at,
        u.email as owner_email,
        u.id as owner_id
      FROM api_keys k
      JOIN users u ON k.owner_id = u.id
      WHERE k.last_rotated_at IS NULL OR k.last_rotated_at < NOW() - INTERVAL '90 days'
      ORDER BY k.last_rotated_at ASC NULLS FIRST
    `);
    
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * Rotate an API key
 * @param {Object} key - API key object
 * @returns {Object} Rotation result
 */
async function rotateApiKey(key) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // Generate new key
    const newKey = generateApiKey();
    const now = new Date();
    
    // Insert new key into history
    await client.query(`
      INSERT INTO api_key_history (api_key_id, key, created_at, expires_at)
      VALUES ($1, $2, $3, $4)
    `, [key.id, key.key, key.created_at, new Date(now.getTime() + config.keyRotation.gracePeriodDays * 24 * 60 * 60 * 1000)]);
    
    // Update key
    await client.query(`
      UPDATE api_keys
      SET key = $1, last_rotated_at = $2
      WHERE id = $3
    `, [newKey, now, key.id]);
    
    await client.query('COMMIT');
    
    return {
      id: key.id,
      name: key.name,
      oldKey: key.key,
      newKey,
      rotatedAt: now.toISOString(),
      gracePeriodDays: config.keyRotation.gracePeriodDays
    };
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Send email notification about key rotation
 * @param {Object} key - API key object
 * @param {Object} rotationResult - Rotation result
 * @returns {boolean} Whether the email was sent successfully
 */
async function sendRotationNotification(key, rotationResult) {
  try {
    const expirationDate = new Date(new Date(rotationResult.rotatedAt).getTime() + config.keyRotation.gracePeriodDays * 24 * 60 * 60 * 1000);
    
    const mailOptions = {
      from: config.email.from,
      to: key.owner_email,
      subject: `API Key Rotation: ${key.name}`,
      text: `
Dear API Key Owner,

Your API key "${key.name}" has been rotated as part of our security policy.

New API Key: ${rotationResult.newKey}

Your previous API key will continue to work until ${expirationDate.toISOString().split('T')[0]}, after which it will be deactivated.

Please update your applications to use the new API key before this date.

If you have any questions or need assistance, please contact our support team.

Best regards,
MVS-VR Team
      `,
      html: `
<p>Dear API Key Owner,</p>

<p>Your API key "<strong>${key.name}</strong>" has been rotated as part of our security policy.</p>

<p><strong>New API Key:</strong> ${rotationResult.newKey}</p>

<p>Your previous API key will continue to work until <strong>${expirationDate.toISOString().split('T')[0]}</strong>, after which it will be deactivated.</p>

<p>Please update your applications to use the new API key before this date.</p>

<p>If you have any questions or need assistance, please contact our support team.</p>

<p>Best regards,<br>
MVS-VR Team</p>
      `
    };
    
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error(`Error sending rotation notification for key ${key.id}:`, error);
    return false;
  }
}

/**
 * Get API keys due for notification
 * @returns {Array} API keys due for notification
 */
async function getKeysForNotification() {
  const client = await pool.connect();
  
  try {
    // Get keys that will be rotated in the next N days
    const result = await client.query(`
      SELECT 
        k.id, 
        k.name, 
        k.key, 
        k.created_at, 
        k.last_rotated_at,
        u.email as owner_email,
        u.id as owner_id
      FROM api_keys k
      JOIN users u ON k.owner_id = u.id
      WHERE 
        (k.last_rotated_at IS NULL AND k.created_at < NOW() - INTERVAL '90 days' + INTERVAL '${config.keyRotation.notificationDays} days')
        OR 
        (k.last_rotated_at < NOW() - INTERVAL '90 days' + INTERVAL '${config.keyRotation.notificationDays} days')
      ORDER BY k.last_rotated_at ASC NULLS FIRST
    `);
    
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * Send upcoming rotation notification
 * @param {Object} key - API key object
 * @returns {boolean} Whether the email was sent successfully
 */
async function sendUpcomingRotationNotification(key) {
  try {
    const rotationDate = new Date();
    
    if (key.last_rotated_at) {
      rotationDate.setTime(new Date(key.last_rotated_at).getTime() + 90 * 24 * 60 * 60 * 1000);
    } else {
      rotationDate.setTime(new Date(key.created_at).getTime() + 90 * 24 * 60 * 60 * 1000);
    }
    
    const mailOptions = {
      from: config.email.from,
      to: key.owner_email,
      subject: `Upcoming API Key Rotation: ${key.name}`,
      text: `
Dear API Key Owner,

Your API key "${key.name}" is scheduled for rotation on ${rotationDate.toISOString().split('T')[0]} as part of our security policy.

After rotation, your current API key will continue to work for ${config.keyRotation.gracePeriodDays} days, after which it will be deactivated.

You will receive another email with your new API key when the rotation occurs.

If you have any questions or need assistance, please contact our support team.

Best regards,
MVS-VR Team
      `,
      html: `
<p>Dear API Key Owner,</p>

<p>Your API key "<strong>${key.name}</strong>" is scheduled for rotation on <strong>${rotationDate.toISOString().split('T')[0]}</strong> as part of our security policy.</p>

<p>After rotation, your current API key will continue to work for ${config.keyRotation.gracePeriodDays} days, after which it will be deactivated.</p>

<p>You will receive another email with your new API key when the rotation occurs.</p>

<p>If you have any questions or need assistance, please contact our support team.</p>

<p>Best regards,<br>
MVS-VR Team</p>
      `
    };
    
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error(`Error sending upcoming rotation notification for key ${key.id}:`, error);
    return false;
  }
}

/**
 * Clean up expired API keys
 * @returns {Object} Cleanup results
 */
async function cleanupExpiredKeys() {
  const client = await pool.connect();
  
  try {
    // Get expired keys
    const result = await client.query(`
      SELECT id, api_key_id, key, created_at, expires_at
      FROM api_key_history
      WHERE expires_at < NOW()
    `);
    
    const expiredKeys = result.rows;
    logger.info(`Found ${expiredKeys.length} expired API keys to clean up`);
    
    // Delete expired keys
    if (expiredKeys.length > 0) {
      const ids = expiredKeys.map(key => key.id);
      await client.query(`
        DELETE FROM api_key_history
        WHERE id = ANY($1)
      `, [ids]);
    }
    
    return {
      cleanedKeys: expiredKeys.length,
      timestamp: new Date().toISOString()
    };
  } finally {
    client.release();
  }
}

/**
 * Run API key rotation
 * @returns {Object} Rotation results
 */
async function runKeyRotation() {
  const log = await loadRotationLog();
  const startTime = new Date();
  
  logger.info(`Starting API key rotation at ${startTime.toISOString();}`);
  
  const rotationResults = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    rotatedKeys: 0,
    notifiedKeys: 0,
    cleanedKeys: 0,
    details: {
      rotations: [],
      notifications: [],
      cleanup: null
    }
  };
  
  try {
    // Get keys for rotation
    const keysForRotation = await getKeysForRotation();
    logger.info(`Found ${keysForRotation.length} API keys for rotation`);
    
    // Rotate keys
    for (const key of keysForRotation) {
      try {
        const rotationResult = await rotateApiKey(key);
        const notificationSent = await sendRotationNotification(key, rotationResult);
        
        rotationResults.rotatedKeys++;
        rotationResults.details.rotations.push({
          id: key.id,
          name: key.name,
          rotatedAt: rotationResult.rotatedAt,
          notificationSent
        });
        
        logger.info(`Rotated API key ${key.id} (${key.name});`);
      } catch (error) {
        console.error(`Error rotating API key ${key.id}:`, error);
        rotationResults.details.rotations.push({
          id: key.id,
          name: key.name,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    // Get keys for notification
    const keysForNotification = await getKeysForNotification();
    logger.info(`Found ${keysForNotification.length} API keys for notification`);
    
    // Send notifications
    for (const key of keysForNotification) {
      try {
        const notificationSent = await sendUpcomingRotationNotification(key);
        
        if (notificationSent) {
          rotationResults.notifiedKeys++;
        }
        
        rotationResults.details.notifications.push({
          id: key.id,
          name: key.name,
          notificationSent,
          timestamp: new Date().toISOString()
        });
        
        logger.info(`Sent notification for API key ${key.id} (${key.name});`);
      } catch (error) {
        console.error(`Error sending notification for API key ${key.id}:`, error);
        rotationResults.details.notifications.push({
          id: key.id,
          name: key.name,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    // Clean up expired keys
    const cleanupResults = await cleanupExpiredKeys();
    rotationResults.cleanedKeys = cleanupResults.cleanedKeys;
    rotationResults.details.cleanup = cleanupResults;
    
    const endTime = new Date();
    const durationMs = endTime - startTime;
    
    rotationResults.endTime = endTime.toISOString();
    rotationResults.duration = durationMs / 1000; // Convert to seconds
    
    // Update log
    log.lastRun = endTime.toISOString();
    log.rotations.push(rotationResults);
    
    // Keep only the last 100 rotations
    if (log.rotations.length > 100) {
      log.rotations = log.rotations.slice(-100);
    }
    
    await saveRotationLog(log);
    
    logger.info(`Completed API key rotation in ${rotationResults.duration} seconds`);
    logger.info(`Rotated ${rotationResults.rotatedKeys} keys, notified ${rotationResults.notifiedKeys} keys, cleaned up ${rotationResults.cleanedKeys} keys`);
    
    return rotationResults;
  } catch (error) {
    console.error('Error during API key rotation:', error);
    
    const endTime = new Date();
    const durationMs = endTime - startTime;
    
    rotationResults.endTime = endTime.toISOString();
    rotationResults.duration = durationMs / 1000;
    rotationResults.error = error.message;
    
    // Update log
    log.lastRun = endTime.toISOString();
    log.rotations.push(rotationResults);
    
    await saveRotationLog(log);
    
    throw error;
  }
}

// If script is run directly, run key rotation
if (require.main === module) {
  runKeyRotation()
    .then(results => {
      logger.info('Rotation completed:');
      logger.info(JSON.stringify(results, null, 2););
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runKeyRotation,
  rotateApiKey,
  sendRotationNotification,
  sendUpcomingRotationNotification,
  cleanupExpiredKeys
};
