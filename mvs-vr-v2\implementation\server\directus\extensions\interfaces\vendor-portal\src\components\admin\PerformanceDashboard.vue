<template>
  <div class="performance-dashboard">
    <h1 class="dashboard-title">Performance Monitoring Dashboard</h1>
    
    <div class="dashboard-filters">
      <div class="filter-group">
        <label for="component-filter">Component:</label>
        <select id="component-filter" v-model="selectedComponent">
          <option value="all">All Components</option>
          <option v-for="component in components" :key="component" :value="component">
            {{ component }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label for="period-filter">Time Period:</label>
        <select id="period-filter" v-model="selectedPeriod">
          <option value="day">Last 24 Hours</option>
          <option value="week">Last 7 Days</option>
          <option value="month">Last 30 Days</option>
        </select>
      </div>
      
      <button class="refresh-button" @click="fetchData">
        <i class="material-icons">refresh</i> Refresh
      </button>
    </div>
    
    <div class="dashboard-summary">
      <div class="summary-card">
        <h3>Performance Improvement</h3>
        <div class="summary-value">{{ formatNumber(summary.performanceImprovement) }}x</div>
        <div class="summary-description">Faster with caching</div>
      </div>
      
      <div class="summary-card">
        <h3>Cache Hit Ratio</h3>
        <div class="summary-value">{{ formatPercent(summary.cacheHitRatio) }}</div>
        <div class="summary-description">Cache efficiency</div>
      </div>
      
      <div class="summary-card">
        <h3>Avg. Render Time</h3>
        <div class="summary-value">{{ formatNumber(summary.renderTime) }}ms</div>
        <div class="summary-description">Per component render</div>
      </div>
      
      <div class="summary-card">
        <h3>Memory Usage</h3>
        <div class="summary-value">{{ formatPercent(summary.memoryUsageRatio) }}</div>
        <div class="summary-description">Of allocated memory</div>
      </div>
    </div>
    
    <div class="dashboard-charts">
      <div class="chart-container">
        <h3>Performance Over Time</h3>
        <canvas ref="performanceChart"></canvas>
      </div>
      
      <div class="chart-container">
        <h3>Cache Efficiency</h3>
        <canvas ref="cacheChart"></canvas>
      </div>
    </div>
    
    <div class="dashboard-tables">
      <div class="table-container">
        <h3>Component Performance</h3>
        <table class="performance-table">
          <thead>
            <tr>
              <th>Component</th>
              <th>Render Time</th>
              <th>API Load Time</th>
              <th>Cache Load Time</th>
              <th>Performance Gain</th>
              <th>Cache Hit Ratio</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in componentStats" :key="index">
              <td>{{ item.component_name }}</td>
              <td>{{ formatNumber(item.avg_render_time) }}ms</td>
              <td>{{ formatNumber(item.avg_api_load_time) }}ms</td>
              <td>{{ formatNumber(item.avg_cache_load_time) }}ms</td>
              <td>{{ formatNumber(item.avg_performance_improvement) }}x</td>
              <td>{{ formatPercent(item.avg_cache_hit_ratio) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="table-container">
        <h3>Recent Errors</h3>
        <table class="error-table">
          <thead>
            <tr>
              <th>Component</th>
              <th>Error</th>
              <th>Time</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(error, index) in recentErrors" :key="index">
              <td>{{ error.component_name }}</td>
              <td>{{ error.message }}</td>
              <td>{{ formatDate(error.timestamp) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch } from 'vue';
import Chart from 'chart.js/auto';

export default {
  name: 'PerformanceDashboard',
  
  setup() {
    // State
    const selectedComponent = ref('all');
    const selectedPeriod = ref('week');
    const components = ref([]);
    const telemetryData = ref([]);
    const errorData = ref([]);
    const performanceChart = ref(null);
    const cacheChart = ref(null);
    const isLoading = ref(false);
    
    // Computed properties
    const componentStats = computed(() => {
      if (selectedComponent.value === 'all') {
        return telemetryData.value;
      } else {
        return telemetryData.value.filter(item => item.component_name === selectedComponent.value);
      }
    });
    
    const recentErrors = computed(() => {
      if (selectedComponent.value === 'all') {
        return errorData.value.slice(0, 10);
      } else {
        return errorData.value
          .filter(item => item.component_name === selectedComponent.value)
          .slice(0, 10);
      }
    });
    
    const summary = computed(() => {
      if (componentStats.value.length === 0) {
        return {
          renderTime: 0,
          cacheHitRatio: 0,
          performanceImprovement: 0,
          memoryUsageRatio: 0
        };
      }
      
      const totalRenderTime = componentStats.value.reduce((sum, item) => sum + item.avg_render_time, 0);
      const totalCacheHitRatio = componentStats.value.reduce((sum, item) => sum + item.avg_cache_hit_ratio, 0);
      const totalPerformanceImprovement = componentStats.value.reduce((sum, item) => sum + item.avg_performance_improvement, 0);
      const totalMemoryUsageRatio = componentStats.value.reduce((sum, item) => sum + (item.avg_memory_usage_ratio || 0), 0);
      
      return {
        renderTime: totalRenderTime / componentStats.value.length,
        cacheHitRatio: totalCacheHitRatio / componentStats.value.length,
        performanceImprovement: totalPerformanceImprovement / componentStats.value.length,
        memoryUsageRatio: totalMemoryUsageRatio / componentStats.value.length
      };
    });
    
    // Methods
    const fetchData = async () => {
      isLoading.value = true;
      
      try {
        // Fetch component stats
        const statsResponse = await fetch(`/api/telemetry/stats`);
        const statsData = await statsResponse.json();
        telemetryData.value = statsData;
        
        // Extract unique component names
        components.value = [...new Set(statsData.map(item => item.component_name))];
        
        // Fetch trend data for charts
        const trendsResponse = await fetch(`/api/telemetry/trends?period=${selectedPeriod.value}&component=${selectedComponent.value === 'all' ? '' : selectedComponent.value}`);
        const trendsData = await trendsResponse.json();
        
        // Fetch recent errors
        const errorsResponse = await fetch(`/api/telemetry/errors?limit=10&component=${selectedComponent.value === 'all' ? '' : selectedComponent.value}`);
        const errorsData = await errorsResponse.json();
        errorData.value = errorsData;
        
        // Update charts
        updateCharts(trendsData);
      } catch (error) {
        console.error('Error fetching telemetry data:', error);
      } finally {
        isLoading.value = false;
      }
    };
    
    const updateCharts = (trendsData) => {
      // Destroy existing charts
      if (performanceChart.value) {
        performanceChart.value.destroy();
      }
      
      if (cacheChart.value) {
        cacheChart.value.destroy();
      }
      
      // Prepare data for performance chart
      const labels = trendsData.map(item => item.time_period);
      const renderTimes = trendsData.map(item => item.avg_render_time);
      const apiLoadTimes = trendsData.map(item => item.avg_api_load_time);
      const cacheLoadTimes = trendsData.map(item => item.avg_cache_load_time);
      
      // Create performance chart
      const performanceCtx = document.getElementById('performanceChart').getContext('2d');
      performanceChart.value = new Chart(performanceCtx, {
        type: 'line',
        data: {
          labels,
          datasets: [
            {
              label: 'Render Time (ms)',
              data: renderTimes,
              borderColor: '#4CAF50',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              tension: 0.4
            },
            {
              label: 'API Load Time (ms)',
              data: apiLoadTimes,
              borderColor: '#2196F3',
              backgroundColor: 'rgba(33, 150, 243, 0.1)',
              tension: 0.4
            },
            {
              label: 'Cache Load Time (ms)',
              data: cacheLoadTimes,
              borderColor: '#FF9800',
              backgroundColor: 'rgba(255, 152, 0, 0.1)',
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Time (ms)'
              }
            }
          }
        }
      });
      
      // Prepare data for cache chart
      const cacheHitRatios = trendsData.map(item => item.avg_cache_hit_ratio * 100);
      const performanceImprovements = trendsData.map(item => item.avg_performance_improvement);
      
      // Create cache chart
      const cacheCtx = document.getElementById('cacheChart').getContext('2d');
      cacheChart.value = new Chart(cacheCtx, {
        type: 'bar',
        data: {
          labels,
          datasets: [
            {
              label: 'Cache Hit Ratio (%)',
              data: cacheHitRatios,
              backgroundColor: 'rgba(156, 39, 176, 0.7)',
              yAxisID: 'y'
            },
            {
              label: 'Performance Improvement (x)',
              data: performanceImprovements,
              backgroundColor: 'rgba(233, 30, 99, 0.7)',
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              position: 'left',
              title: {
                display: true,
                text: 'Cache Hit Ratio (%)'
              },
              max: 100
            },
            y1: {
              beginAtZero: true,
              position: 'right',
              title: {
                display: true,
                text: 'Performance Improvement (x)'
              },
              grid: {
                drawOnChartArea: false
              }
            }
          }
        }
      });
    };
    
    // Formatting helpers
    const formatNumber = (value) => {
      if (value === undefined || value === null) return '0';
      return value.toFixed(2);
    };
    
    const formatPercent = (value) => {
      if (value === undefined || value === null) return '0%';
      return `${(value * 100).toFixed(1)}%`;
    };
    
    const formatDate = (timestamp) => {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString();
    };
    
    // Watch for filter changes
    watch([selectedComponent, selectedPeriod], () => {
      fetchData();
    });
    
    // Initialize
    onMounted(() => {
      fetchData();
    });
    
    return {
      selectedComponent,
      selectedPeriod,
      components,
      componentStats,
      recentErrors,
      summary,
      isLoading,
      fetchData,
      formatNumber,
      formatPercent,
      formatDate
    };
  }
};
</script>

<style scoped>
.performance-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-title {
  margin-bottom: 20px;
  color: #333;
}

.dashboard-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
}

.filter-group select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-button:hover {
  background-color: #1976D2;
}

.dashboard-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.summary-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #555;
  font-size: 16px;
}

.summary-value {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.summary-description {
  color: #777;
  font-size: 14px;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
}

.dashboard-tables {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.table-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
}

.performance-table, .error-table {
  width: 100%;
  border-collapse: collapse;
}

.performance-table th, .performance-table td,
.error-table th, .error-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.performance-table th, .error-table th {
  font-weight: 500;
  color: #555;
  background-color: #f5f5f5;
}

.performance-table tr:hover, .error-table tr:hover {
  background-color: #f9f9f9;
}
</style>
