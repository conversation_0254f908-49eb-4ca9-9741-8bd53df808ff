import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { createClientSupabaseClient } from '@supabase/auth-helpers-nextjs';

/**
 * API Client for vendor portal
 * 
 * This client handles API requests with proper error handling,
 * authentication, and request/response interceptors.
 */
class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Set up request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      async (config) => {
        // Get session token from Supabase
        const supabase = createClientSupabaseClient();
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.access_token) {
          config.headers.Authorization = `Bearer ${session.access_token}`;
        }
        
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
        
        // Handle 401 Unauthorized - Token expired
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            // Refresh token
            const supabase = createClientSupabaseClient();
            const { data, error: refreshError } = await supabase.auth.refreshSession();
            
            if (refreshError) throw refreshError;
            
            if (data.session?.access_token) {
              // Retry the original request with new token
              this.client.defaults.headers.common.Authorization = `Bearer ${data.session.access_token}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            
            // Redirect to login page
            window.location.href = '/vendor/login';
            return Promise.reject(refreshError);
          }
        }
        
        // Handle other errors
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Handle API errors
   */
  private handleApiError(error: AxiosError): void {
    const { response } = error;
    
    // Log error details
    console.error('API Error:', {
      status: response?.status,
      statusText: response?.statusText,
      url: response?.config?.url,
      data: response?.data,
    });
    
    // Handle specific error codes
    switch (response?.status) {
      case 400:
        console.error('Bad Request:', response.data);
        break;
      case 403:
        console.error('Forbidden:', response.data);
        break;
      case 404:
        console.error('Not Found:', response.data);
        break;
      case 500:
        console.error('Server Error:', response.data);
        break;
      default:
        console.error('Unexpected Error:', response?.data || error.message);
    }
  }

  /**
   * Make a GET request
   */
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.get(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Make a POST request
   */
  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.post(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Make a PUT request
   */
  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.put(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Make a DELETE request
   */
  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Make a PATCH request
   */
  public async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upload a file
   */
  public async uploadFile<T>(
    url: string,
    file: File,
    onProgress?: (percentage: number) => void,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);
    
    const uploadConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentage);
        }
      },
    };
    
    try {
      const response: AxiosResponse<T> = await this.client.post(url, formData, uploadConfig);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;
