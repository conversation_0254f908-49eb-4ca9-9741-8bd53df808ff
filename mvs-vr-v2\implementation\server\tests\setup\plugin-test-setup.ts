import { SpyInstance } from 'npm:vitest';
import { JSDOM } from 'npm:jsdom';

interface PluginMock {
  sendMessage: SpyInstance;
  receiveMessage: SpyInstance;
  connect: SpyInstance;
  disconnect: SpyInstance;
  isConnected: boolean;
}

interface WebSocketMock {
  send: SpyInstance;
  close: SpyInstance;
  onmessage: (event: MessageEvent) => void;
  onclose: () => void;
}

/**
 * Creates a mock UE plugin environment for testing
 */
export function createPluginTestEnvironment() {
  // Set up DOM environment
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    url: 'http://localhost:3000',
    runScripts: 'dangerously',
  });

  // Mock WebSocket
  const mockWS: WebSocketMock = {
    send: vi.fn(),
    close: vi.fn(),
    onmessage: () => {},
    onclose: () => {},
  };

  // Mock plugin interface
  const mockPlugin: PluginMock = {
    sendMessage: vi.fn(),
    receiveMessage: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn(),
    isConnected: false,
  };

  // Add mocks to global
  global.WebSocket = vi.fn().mockImplementation(() => mockWS);
  (global as any).UE = {
    plugin: mockPlugin,
  };

  // Set up message event simulation
  function simulatePluginMessage(data: any) {
    const event = new MessageEvent('message', {
      data: JSON.stringify(data),
    });
    mockWS.onmessage(event);
  }

  // Set up connection event simulation
  function simulateConnection() {
    mockPlugin.isConnected = true;
    mockWS.onmessage(new MessageEvent('open'));
  }

  function simulateDisconnection() {
    mockPlugin.isConnected = false;
    mockWS.onclose();
  }

  return {
    // DOM elements
    document: dom.window.document,
    window: dom.window,

    // Mocks
    websocket: mockWS,
    plugin: mockPlugin,

    // Event simulators
    simulatePluginMessage,
    simulateConnection,
    simulateDisconnection,

    // Cleanup
    cleanup: () => {
      vi.clearAllMocks();
      mockPlugin.isConnected = false;
      dom.window.document.body.innerHTML = '';
    },
  };
}

/**
 * Test utilities for plugin-specific assertions
 */
export const pluginAssertions = {
  toHaveReceivedMessage: (mock: PluginMock, expected: any) => {
    const calls = mock.sendMessage.mock.calls;
    return {
      pass: calls.some(call => {
        const message = JSON.parse(call[0]);
        return expect.objectContaining(expected).asymmetricMatch(message);
      }),
      message: () =>
        `Expected plugin to have received message matching ${JSON.stringify(expected)}`,
    };
  },

  toBeConnected: (mock: PluginMock) => ({
    pass: mock.isConnected,
    message: () => 'Expected plugin to be connected',
  }),
};

// Add custom matchers
expect.extend(pluginAssertions);

// Type augmentation
declare global {
  namespace Vi {
    interface Assertion {
      toHaveReceivedMessage(message: any): void;
      toBeConnected(): void;
    }
  }
}
