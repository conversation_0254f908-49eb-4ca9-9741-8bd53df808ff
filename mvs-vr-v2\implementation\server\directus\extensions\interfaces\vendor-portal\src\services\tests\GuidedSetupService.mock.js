/**
 * Mock implementation of GuidedSetupService for testing
 */

import axios from 'axios';

class GuidedSetupService {
  constructor() {
    this.api = axios.create({
      baseURL: globalThis.directus.url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${globalThis.directus.auth.token}`
      }
    });
  }

  async getOnboardingStatus(vendorId) {
    try {
      const response = await this.api.get(`/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`);
      return response.data.data.length > 0 ? response.data.data[0] : null;
    } catch (error) {
      throw error;
    }
  }

  async createOnboardingStatus(vendorId) {
    try {
      const data = {
        vendor_id: vendorId,
        is_completed: false,
        progress_data: JSON.stringify({})
      };
      const response = await this.api.post('/items/vendor_onboarding', data);
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  async updateOnboardingStatus(id, data) {
    try {
      const response = await this.api.patch(`/items/vendor_onboarding/${id}`, data);
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
}

export default GuidedSetupService;
