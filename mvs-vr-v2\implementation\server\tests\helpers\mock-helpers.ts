/**
 * Mock Helpers for Vitest
 * 
 * This file provides helper functions to standardize the conversion of Jest mocks to Vitest mocks.
 */
import { vi } from 'vitest';

/**
 * Creates a mock function with implementation
 * 
 * @param implementation The implementation function
 * @returns A mock function with the provided implementation
 */
export function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): ReturnType<typeof vi.fn> {
  return implementation ? vi.fn().mockImplementation(implementation) : vi.fn();
}

/**
 * Creates a mock object with the specified methods
 * 
 * @param methods An object containing method names and their implementations
 * @returns A mock object with the specified methods
 */
export function createMockObject<T extends Record<string, any>>(methods: T): T {
  const mockObject: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(methods)) {
    if (typeof value === 'function') {
      mockObject[key] = createMockFn(value);
    } else {
      mockObject[key] = value;
    }
  }
  
  return mockObject as T;
}

/**
 * Creates a mock class with the specified methods
 * 
 * @param methods An object containing method names and their implementations
 * @returns A mock class constructor function
 */
export function createMockClass<T extends Record<string, any>>(methods: T): ReturnType<typeof vi.fn> {
  return vi.fn().mockImplementation(() => createMockObject(methods));
}

/**
 * Creates a mock for a module with default export
 * 
 * @param defaultExport The mock for the default export
 * @param namedExports An object containing named exports
 * @returns A mock module object
 */
export function createMockModule<T extends Record<string, any>>(
  defaultExport: any,
  namedExports: T = {} as T
): { default: any } & T {
  return {
    default: defaultExport,
    ...namedExports,
  };
}

/**
 * Creates a mock for Supabase client
 * 
 * @returns A mock Supabase client
 */
export function createMockSupabaseClient() {
  return {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn(),
        download: vi.fn(),
        getPublicUrl: vi.fn(),
        remove: vi.fn(),
        list: vi.fn(),
      }),
    },
    auth: {
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      getUser: vi.fn(),
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(),
    },
  };
}

/**
 * Creates a mock for Express request
 * 
 * @param overrides Properties to override in the mock request
 * @returns A mock Express request
 */
export function createMockExpressRequest(overrides: Record<string, any> = {}) {
  return {
    headers: {},
    params: {},
    query: {},
    body: {},
    cookies: {},
    ip: '127.0.0.1',
    method: 'GET',
    path: '/',
    ...overrides,
  };
}

/**
 * Creates a mock for Express response
 * 
 * @returns A mock Express response
 */
export function createMockExpressResponse() {
  const res: Record<string, any> = {
    statusCode: 200,
    headers: {},
  };
  
  res.status = vi.fn().mockImplementation((code) => {
    res.statusCode = code;
    return res;
  });
  
  res.json = vi.fn().mockImplementation((data) => {
    res.body = data;
    return res;
  });
  
  res.send = vi.fn().mockImplementation((data) => {
    res.body = data;
    return res;
  });
  
  res.set = vi.fn().mockImplementation((key, value) => {
    if (typeof key === 'object') {
      res.headers = { ...res.headers, ...key };
    } else {
      res.headers[key] = value;
    }
    return res;
  });
  
  res.cookie = vi.fn().mockReturnThis();
  res.clearCookie = vi.fn().mockReturnThis();
  res.end = vi.fn().mockReturnThis();
  
  return res;
}

/**
 * Creates a mock for Express next function
 * 
 * @returns A mock Express next function
 */
export function createMockExpressNext() {
  return vi.fn();
}
