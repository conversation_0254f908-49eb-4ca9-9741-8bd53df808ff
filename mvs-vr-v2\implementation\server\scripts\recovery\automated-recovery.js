/**
 * Automated Recovery Procedures
 * 
 * This script automates recovery procedures for various system components.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync, exec } = require('child_process');
const { S3Client, GetObjectCommand, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const { Pool } = require('pg');
const axios = require('axios');
const rtoMeasurement = require('./rto-measurement');
const { Logger } = require('../../services/integration/logger');
const { logger } = require('../shared/utils/logger');

// Promisify functions
const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);

// Create logger
const logger = new Logger();

// Configuration
const config = {
  primaryRegion: process.env.PRIMARY_REGION || 'us-east-1',
  secondaryRegion: process.env.SECONDARY_REGION || 'us-west-2',
  database: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DB || 'postgres'
  },
  buckets: {
    database: {
      primary: process.env.PRIMARY_DB_BUCKET || 'mvs-vr-db-backups',
      secondary: process.env.SECONDARY_DB_BUCKET || 'mvs-vr-db-backups-dr'
    },
    files: {
      primary: process.env.PRIMARY_FILES_BUCKET || 'mvs-vr-file-backups',
      secondary: process.env.SECONDARY_FILES_BUCKET || 'mvs-vr-file-backups-dr'
    },
    config: {
      primary: process.env.PRIMARY_CONFIG_BUCKET || 'mvs-vr-config-backups',
      secondary: process.env.SECONDARY_CONFIG_BUCKET || 'mvs-vr-config-backups-dr'
    }
  },
  services: [
    {
      name: 'api',
      url: process.env.API_URL || 'http://localhost:3000',
      healthEndpoint: '/health',
      restartCommand: 'docker-compose restart api'
    },
    {
      name: 'admin',
      url: process.env.ADMIN_URL || 'http://localhost:3001',
      healthEndpoint: '/health',
      restartCommand: 'docker-compose restart admin'
    },
    {
      name: 'vendor',
      url: process.env.VENDOR_URL || 'http://localhost:3002',
      healthEndpoint: '/health',
      restartCommand: 'docker-compose restart vendor'
    }
  ],
  recoveryLogPath: path.join(__dirname, '../../logs/recovery'),
  tempDir: path.join(__dirname, '../../temp/recovery')
};

/**
 * Create S3 client for a specific region
 * @param {string} region - AWS region
 * @returns {S3Client} S3 client
 */
function createS3Client(region) {
  return new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
}

/**
 * Create database pool
 * @param {Object} options - Database options
 * @returns {Pool} Database pool
 */
function createDatabasePool(options = {}) {
  return new Pool({
    host: options.host || config.database.host,
    port: options.port || config.database.port,
    user: options.user || config.database.user,
    password: options.password || config.database.password,
    database: options.database || config.database.database
  });
}

/**
 * Get latest backup from S3
 * @param {string} bucketType - Bucket type (database, files, config)
 * @param {string} region - AWS region
 * @returns {Promise<Object>} Latest backup
 */
async function getLatestBackup(bucketType, region = config.secondaryRegion) {
  const s3Client = createS3Client(region);
  const bucketName = config.buckets[bucketType][region === config.primaryRegion ? 'primary' : 'secondary'];
  
  const command = new ListObjectsV2Command({
    Bucket: bucketName,
    MaxKeys: 100
  });
  
  const response = await s3Client.send(command);
  const backups = response.Contents || [];
  
  if (backups.length === 0) {
    throw new Error(`No backups found in ${bucketType} bucket`);
  }
  
  // Sort by last modified (newest first)
  backups.sort((a, b) => b.LastModified - a.LastModified);
  
  return {
    key: backups[0].Key,
    lastModified: backups[0].LastModified,
    size: backups[0].Size,
    bucket: bucketName,
    region
  };
}

/**
 * Download backup from S3
 * @param {Object} backup - Backup object
 * @param {string} outputPath - Output path
 * @returns {Promise<string>} Output path
 */
async function downloadBackup(backup, outputPath) {
  const s3Client = createS3Client(backup.region);
  
  const command = new GetObjectCommand({
    Bucket: backup.bucket,
    Key: backup.key
  });
  
  const response = await s3Client.send(command);
  
  // Create directory if it doesn't exist
  const dir = path.dirname(outputPath);
  await mkdirAsync(dir, { recursive: true });
  
  // Write file
  const writeStream = fs.createWriteStream(outputPath);
  response.Body.pipe(writeStream);
  
  return new Promise((resolve, reject) => {
    writeStream.on('finish', () => resolve(outputPath));
    writeStream.on('error', reject);
  });
}

/**
 * Recover database from backup
 * @param {string} backupPath - Path to backup file
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverDatabase(backupPath, options = {}) {
  const {
    dropDatabase = true,
    createDatabase = true,
    database = config.database.database,
    host = config.database.host,
    port = config.database.port,
    user = config.database.user,
    password = config.database.password,
    incidentId = `db-recovery-${Date.now()}`
  } = options;
  
  const recoveryId = rtoMeasurement.startRecovery(
    'database.main',
    incidentId,
    `Database recovery from ${path.basename(backupPath)}`
  );
  
  logger.info(`Starting database recovery from ${backupPath}`);
  
  try {
    // Drop database if requested
    if (dropDatabase) {
      try {
        await execAsync(`PGPASSWORD=${password} dropdb -h ${host} -p ${port} -U ${user} ${database}`);
        logger.info(`Dropped database ${database}`);
      } catch (error) {
        logger.warn(`Error dropping database: ${error.message}`);
      }
    }
    
    // Create database if requested
    if (createDatabase) {
      try {
        await execAsync(`PGPASSWORD=${password} createdb -h ${host} -p ${port} -U ${user} ${database}`);
        logger.info(`Created database ${database}`);
      } catch (error) {
        logger.error(`Error creating database: ${error.message}`);
        throw error;
      }
    }
    
    // Restore database
    await execAsync(`PGPASSWORD=${password} pg_restore -h ${host} -p ${port} -U ${user} -d ${database} ${backupPath}`);
    logger.info(`Restored database from ${backupPath}`);
    
    // Verify database
    const pool = createDatabasePool({ database });
    
    try {
      // Check if tables exist
      const tablesResult = await pool.query(`
        SELECT count(*) as table_count
        FROM information_schema.tables
        WHERE table_schema = 'public'
      `);
      
      const tableCount = parseInt(tablesResult.rows[0].table_count, 10);
      
      if (tableCount === 0) {
        throw new Error('No tables found in restored database');
      }
      
      logger.info(`Verified database: ${tableCount} tables found`);
      
      // End recovery measurement
      const recovery = await rtoMeasurement.endRecovery(
        recoveryId,
        'success',
        `Database recovery completed successfully. Restored ${tableCount} tables.`
      );
      
      return {
        success: true,
        recoveryId,
        recovery,
        details: {
          tableCount
        }
      };
    } finally {
      // Close pool
      await pool.end();
    }
  } catch (error) {
    logger.error(`Database recovery failed: ${error.message}`, { error });
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );
    
    return {
      success: false,
      recoveryId,
      recovery,
      error: error.message
    };
  }
}

/**
 * Recover file storage from backup
 * @param {string} backupPath - Path to backup file
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverFileStorage(backupPath, options = {}) {
  const {
    extractDir = path.join(config.tempDir, 'extract'),
    targetDir = process.env.FILE_STORAGE_DIR || path.join(__dirname, '../../storage'),
    incidentId = `file-recovery-${Date.now()}`
  } = options;
  
  const recoveryId = rtoMeasurement.startRecovery(
    'storage.assets',
    incidentId,
    `File storage recovery from ${path.basename(backupPath)}`
  );
  
  logger.info(`Starting file storage recovery from ${backupPath}`);
  
  try {
    // Create extract directory
    await mkdirAsync(extractDir, { recursive: true });
    
    // Create target directory
    await mkdirAsync(targetDir, { recursive: true });
    
    // Extract backup
    if (backupPath.endsWith('.zip')) {
      await execAsync(`unzip -o ${backupPath} -d ${extractDir}`);
    } else if (backupPath.endsWith('.tar.gz') || backupPath.endsWith('.tgz')) {
      await execAsync(`tar -xzf ${backupPath} -C ${extractDir}`);
    } else if (backupPath.endsWith('.tar')) {
      await execAsync(`tar -xf ${backupPath} -C ${extractDir}`);
    } else {
      throw new Error(`Unsupported backup format: ${path.extname(backupPath)}`);
    }
    
    logger.info(`Extracted backup to ${extractDir}`);
    
    // Copy files to target directory
    await execAsync(`rsync -av ${extractDir}/ ${targetDir}/`);
    
    logger.info(`Copied files to ${targetDir}`);
    
    // Verify files
    const fileCount = parseInt(execSync(`find ${targetDir} -type f | wc -l`).toString().trim(), 10);
    
    if (fileCount === 0) {
      throw new Error('No files found in target directory');
    }
    
    logger.info(`Verified files: ${fileCount} files found`);
    
    // End recovery measurement
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'success',
      `File storage recovery completed successfully. Restored ${fileCount} files.`
    );
    
    return {
      success: true,
      recoveryId,
      recovery,
      details: {
        fileCount
      }
    };
  } catch (error) {
    logger.error(`File storage recovery failed: ${error.message}`, { error });
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );
    
    return {
      success: false,
      recoveryId,
      recovery,
      error: error.message
    };
  }
}

/**
 * Recover service
 * @param {Object} service - Service configuration
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverService(service, options = {}) {
  const {
    restart = true,
    maxRetries = 5,
    retryDelay = 5000,
    incidentId = `service-recovery-${service.name}-${Date.now()}`
  } = options;
  
  const recoveryId = rtoMeasurement.startRecovery(
    `application.${service.name}`,
    incidentId,
    `${service.name} service recovery`
  );
  
  logger.info(`Starting ${service.name} service recovery`);
  
  try {
    // Check if service is already available
    try {
      const response = await axios.get(`${service.url}${service.healthEndpoint}`, {
        timeout: 5000,
        validateStatus: () => true
      });
      
      if (response.status === 200) {
        logger.info(`Service ${service.name} is already available`);
        
        // End recovery measurement
        const recovery = await rtoMeasurement.endRecovery(
          recoveryId,
          'success',
          `Service was already available.`
        );
        
        return {
          success: true,
          recoveryId,
          recovery,
          details: {
            status: response.status,
            data: response.data,
            restarted: false
          }
        };
      }
    } catch (error) {
      logger.info(`Service ${service.name} is not available: ${error.message}`);
    }
    
    // Restart service if requested
    if (restart) {
      logger.info(`Restarting service ${service.name}`);
      await execAsync(service.restartCommand);
      logger.info(`Service ${service.name} restarted`);
    }
    
    // Wait for service to become available
    let retries = 0;
    let available = false;
    let lastError = null;
    let lastResponse = null;
    
    while (retries < maxRetries && !available) {
      try {
        logger.info(`Checking if service ${service.name} is available (attempt ${retries + 1}/${maxRetries})`);
        
        const response = await axios.get(`${service.url}${service.healthEndpoint}`, {
          timeout: 5000,
          validateStatus: () => true
        });
        
        lastResponse = response;
        
        if (response.status === 200) {
          available = true;
          logger.info(`Service ${service.name} is available`);
        } else {
          logger.warn(`Service ${service.name} returned status ${response.status}`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          retries++;
        }
      } catch (error) {
        lastError = error;
        logger.warn(`Error checking service ${service.name}: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retries++;
      }
    }
    
    if (!available) {
      throw new Error(`Service ${service.name} is not available after ${maxRetries} retries`);
    }
    
    // End recovery measurement
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'success',
      `Service recovery completed successfully. Service is available.`
    );
    
    return {
      success: true,
      recoveryId,
      recovery,
      details: {
        status: lastResponse.status,
        data: lastResponse.data,
        restarted: restart,
        retries
      }
    };
  } catch (error) {
    logger.error(`Service recovery failed: ${error.message}`, { error });
    
    // End recovery measurement with failure status
    const recovery = await rtoMeasurement.endRecovery(
      recoveryId,
      'failed',
      `Error: ${error.message}`
    );
    
    return {
      success: false,
      recoveryId,
      recovery,
      error: error.message
    };
  }
}

/**
 * Run automated recovery
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery results
 */
async function runAutomatedRecovery(options = {}) {
  const {
    components = ['database', 'files', 'services'],
    incidentId = `recovery-${Date.now()}`
  } = options;
  
  logger.info('Starting automated recovery');
  
  // Create recovery log directory
  await mkdirAsync(config.recoveryLogPath, { recursive: true });
  
  // Create temp directory
  await mkdirAsync(config.tempDir, { recursive: true });
  
  const startTime = new Date();
  const results = {
    timestamp: startTime.toISOString(),
    incidentId,
    duration: null,
    components: {}
  };
  
  try {
    // Recover database
    if (components.includes('database')) {
      logger.info('Recovering database');
      
      // Get latest backup
      const backup = await getLatestBackup('database');
      logger.info(`Found latest database backup: ${backup.key}`);
      
      // Download backup
      const backupPath = path.join(config.tempDir, path.basename(backup.key));
      await downloadBackup(backup, backupPath);
      logger.info(`Downloaded database backup to ${backupPath}`);
      
      // Recover database
      results.components.database = await recoverDatabase(backupPath, { incidentId });
    }
    
    // Recover file storage
    if (components.includes('files')) {
      logger.info('Recovering file storage');
      
      // Get latest backup
      const backup = await getLatestBackup('files');
      logger.info(`Found latest file storage backup: ${backup.key}`);
      
      // Download backup
      const backupPath = path.join(config.tempDir, path.basename(backup.key));
      await downloadBackup(backup, backupPath);
      logger.info(`Downloaded file storage backup to ${backupPath}`);
      
      // Recover file storage
      results.components.files = await recoverFileStorage(backupPath, { incidentId });
    }
    
    // Recover services
    if (components.includes('services')) {
      logger.info('Recovering services');
      
      results.components.services = {};
      
      for (const service of config.services) {
        logger.info(`Recovering service ${service.name}`);
        results.components.services[service.name] = await recoverService(service, { incidentId });
      }
    }
    
    // Calculate duration
    const endTime = new Date();
    results.duration = (endTime - startTime) / 1000; // in seconds
    
    // Generate RTO compliance report
    const rtoReport = rtoMeasurement.generateRtoReport(incidentId);
    results.rtoReport = rtoReport;
    
    // Save results
    const reportPath = path.join(config.recoveryLogPath, `recovery-${startTime.toISOString().replace(/:/g, '-')}.json`);
    await writeFileAsync(reportPath, JSON.stringify(results, null, 2));
    
    logger.info(`Automated recovery completed in ${results.duration} seconds`);
    logger.info(`Report saved to ${reportPath}`);
    
    return results;
  } catch (error) {
    logger.error(`Automated recovery failed: ${error.message}`, { error });
    
    // Calculate duration
    const endTime = new Date();
    results.duration = (endTime - startTime) / 1000; // in seconds
    results.error = error.message;
    
    // Save results
    const reportPath = path.join(config.recoveryLogPath, `recovery-${startTime.toISOString().replace(/:/g, '-')}.json`);
    await writeFileAsync(reportPath, JSON.stringify(results, null, 2));
    
    logger.info(`Automated recovery failed in ${results.duration} seconds`);
    logger.info(`Report saved to ${reportPath}`);
    
    throw error;
  }
}

// If script is run directly, run automated recovery
if (require.main === module) {
  const args = process.argv.slice(2);
  const components = args.length > 0 ? args : ['database', 'files', 'services'];
  
  runAutomatedRecovery({ components })
    .then(results => {
      logger.info('Automated recovery completed:');
      logger.info(`Duration: ${results.duration} seconds`);
      
      if (results.components.database) {
        logger.info(`Database recovery: ${results.components.database.success ? 'Success' : 'Failed'}`);
      }
      
      if (results.components.files) {
        logger.info(`File storage recovery: ${results.components.files.success ? 'Success' : 'Failed'}`);
      }
      
      if (results.components.services) {
        for (const [serviceName, serviceResult] of Object.entries(results.components.services)) {
          logger.info(`${serviceName} service recovery: ${serviceResult.success ? 'Success' : 'Failed'}`);
        }
      }
      
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runAutomatedRecovery,
  recoverDatabase,
  recoverFileStorage,
  recoverService,
  getLatestBackup,
  downloadBackup
};
