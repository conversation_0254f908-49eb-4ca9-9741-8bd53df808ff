/**
 * Vitest setup file for server tests
 */
import { beforeAll, afterAll, expect, vi } from 'vitest';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Set up global beforeAll and afterAll hooks
beforeAll(async () => {
  // Extend timeout for integration tests
  vi.setConfig({ testTimeout: 30000 });

  // Verify required environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.warn(`Warning: Missing environment variable: ${envVar}`);
    }
  }
});

afterAll(async () => {
  // Cleanup resources
});

// Mock implementations
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    reload: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    query: {},
    pathname: '/',
    asPath: '/',
    isFallback: false,
  }),
}));

// Custom matchers
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    return {
      message: () =>
        `expected ${received} to ${pass ? 'not ' : ''}be within range ${floor} - ${ceiling}`,
      pass,
    };
  },
});
