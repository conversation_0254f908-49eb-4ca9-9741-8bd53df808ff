import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createWrapper } from '../src/test-utils/component-test-utils.js';
import PreviewFrame from '../src/components/PreviewFrame.vue';
import PreviewControls from '../src/components/PreviewControls.vue';

// Create a mock LivePreview component
const LivePreviewMock = {
  name: 'LivePreview',
  components: {
    PreviewFrame,
    PreviewControls,
  },
  props: {
    initialMode: {
      type: String,
      default: 'edit',
    },
    initialDevice: {
      type: String,
      default: 'desktop',
    },
    initialOrientation: {
      type: String,
      default: 'portrait',
    },
    initialScale: {
      type: Number,
      default: 1,
    },
    initialUrl: {
      type: String,
      required: true,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    debounceMs: {
      type: Number,
      default: 300,
    },
  },
  data() {
    return {
      mode: this.initialMode,
      device: this.initialDevice,
      orientation: this.initialOrientation,
      scale: this.initialScale,
      url: this.initialUrl,
      data: this.initialData,
    };
  },
  methods: {
    handlePreviewChange(changeData) {
      this.mode = changeData.mode || this.mode;
      this.url = changeData.url || this.url;
      this.data = changeData.data || this.data;

      this.$emit('change', {
        mode: this.mode,
        url: this.url,
        data: this.data,
      });

      if (this.onChange) {
        this.onChange({
          mode: this.mode,
          url: this.url,
          data: this.data,
        });
      }
    },
  },
  template: `
    <div class="live-preview">
      <div class="preview-frame-container">
        <PreviewFrame :src="url" />
      </div>
      <div class="preview-controls-container">
        <PreviewControls />
      </div>
      <div class="editor-container">
        <slot name="editor"></slot>
      </div>
    </div>
  `,
};

// Mock the child components
vi.mock('../src/components/PreviewFrame.vue', () => ({
  default: {
    name: 'PreviewFrame',
    render: h => h('div', { class: 'mock-preview-frame' }),
    props: ['src'],
  },
}));

vi.mock('../src/components/PreviewControls.vue', () => ({
  default: {
    name: 'PreviewControls',
    render: h => h('div', { class: 'mock-preview-controls' }),
  },
}));

// Mock the PreviewContext
vi.mock('../src/contexts/PreviewContext', () => {
  const previewMode = vi.fn();
  const previewUrl = vi.fn();
  const isEditMode = vi.fn();
  const isPreviewMode = vi.fn();
  const isSplitMode = vi.fn();

  return {
    providePreviewContext: vi.fn(() => ({
      previewMode,
      previewUrl,
      isEditMode,
      isPreviewMode,
      isSplitMode,
    })),
    usePreviewContext: vi.fn(() => ({
      previewMode,
      previewUrl,
      isEditMode,
      isPreviewMode,
      isSplitMode,
    })),
  };
});

describe('LivePreview', () => {
  let wrapper;
  const mockProps = {
    initialMode: 'edit',
    initialDevice: 'desktop',
    initialOrientation: 'portrait',
    initialScale: 1,
    initialUrl: 'https://example.com',
    initialData: { test: 'data' },
    onChange: vi.fn(),
    debounceMs: 0,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    wrapper = mount(LivePreview, {
      props: mockProps,
      slots: {
        editor: '<div class="mock-editor">Editor Content</div>',
      },
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.live-preview').exists()).toBe(true);
  });

  it('renders the PreviewFrame component', () => {
    expect(wrapper.findComponent(PreviewFrame).exists()).toBe(true);
  });

  it('renders the PreviewControls component', () => {
    expect(wrapper.findComponent(PreviewControls).exists()).toBe(true);
  });

  it('passes the correct props to PreviewFrame', () => {
    const previewFrame = wrapper.findComponent(PreviewFrame);
    expect(previewFrame.props('src')).toBe('https://example.com');
  });

  it('renders the editor slot content', () => {
    expect(wrapper.find('.mock-editor').exists()).toBe(true);
    expect(wrapper.find('.mock-editor').text()).toBe('Editor Content');
  });

  it('emits change event when preview data changes', async () => {
    // Simulate a change in the preview data
    await wrapper.vm.handlePreviewChange({
      mode: 'preview',
      data: { updated: 'data' },
      url: 'https://updated.example.com',
    });

    // Check that the component emits the change event
    expect(wrapper.emitted('change')).toBeTruthy();
    expect(wrapper.emitted('change')[0][0]).toEqual({
      mode: 'preview',
      data: { updated: 'data' },
      url: 'https://updated.example.com',
    });

    // Check that the onChange prop function is called
    expect(mockProps.onChange).toHaveBeenCalledWith({
      mode: 'preview',
      data: { updated: 'data' },
      url: 'https://updated.example.com',
    });
  });

  it('provides the PreviewContext with the correct initial values', () => {
    const { providePreviewContext } = require('../src/contexts/PreviewContext');

    expect(providePreviewContext).toHaveBeenCalledWith({
      initialMode: 'edit',
      initialDevice: 'desktop',
      initialOrientation: 'portrait',
      initialScale: 1,
      initialUrl: 'https://example.com',
      initialData: { test: 'data' },
    });
  });
});
