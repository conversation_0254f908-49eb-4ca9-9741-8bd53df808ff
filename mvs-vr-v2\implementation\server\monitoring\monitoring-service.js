/**
 * Monitoring Service
 *
 * This service integrates all monitoring components and provides a unified API for the dashboard.
 */

const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config');

// Import monitoring components
const { getAlertConfig, triggerAlert, resolveAlert } = require('./alert-manager');
const { identifyUserSegments } = require('./user-segment-analyzer');
const { analyzeTestPerformance } = require('./ab-test-monitor');
const { runPredictiveAnalysis } = require('./predictive-analyzer');
const { processAuthEvent, updateAuthMetrics } = require('./auth-monitor');
const { processAssetDeliveryEvent, updateAssetMetrics } = require('./asset-delivery-monitor');

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Create Express app
const app = express();
app.use(cors());
app.use(express.json());

// Configuration
const COMPONENT_PORTS = {
  METRICS_COLLECTOR: process.env.METRICS_COLLECTOR_PORT || 9090,
  ALERT_MANAGER: process.env.ALERT_MANAGER_PORT || 9096,
  USER_SEGMENT_ANALYZER: process.env.USER_SEGMENT_ANALYZER_PORT || 9095,
  AB_TEST_MONITOR: process.env.AB_TEST_MONITOR_PORT || 9097,
  PREDICTIVE_ANALYZER: process.env.PREDICTIVE_ANALYZER_PORT || 9098,
  UE_PLUGIN_MONITOR: process.env.UE_PLUGIN_MONITOR_PORT || 9100,
  MOBILE_APP_MONITOR: process.env.MOBILE_APP_MONITOR_PORT || 9101,
  ML_ALERT_ANALYZER: process.env.ML_ALERT_ANALYZER_PORT || 9102,
  USER_BEHAVIOR_PREDICTOR: process.env.USER_BEHAVIOR_PREDICTOR_PORT || 9103,
  CHURN_PREDICTOR: process.env.CHURN_PREDICTOR_PORT || 9104,
  PERSONALIZATION_IMPACT_ANALYZER: process.env.PERSONALIZATION_IMPACT_ANALYZER_PORT || 9105,
  AUTOMATED_TEST_CREATOR: process.env.AUTOMATED_TEST_CREATOR_PORT || 9106,
  MULTI_VARIANT_TEST_MANAGER: process.env.MULTI_VARIANT_TEST_MANAGER_PORT || 9107,
  LONG_TERM_IMPACT_ANALYZER: process.env.LONG_TERM_IMPACT_ANALYZER_PORT || 9108,
  RESOURCE_OPTIMIZER: process.env.RESOURCE_OPTIMIZER_PORT || 9109,
  PROACTIVE_SCALER: process.env.PROACTIVE_SCALER_PORT || 9110,
  BUSINESS_IMPACT_PREDICTOR: process.env.BUSINESS_IMPACT_PREDICTOR_PORT || 9111,
  AUTH_MONITOR: process.env.AUTH_MONITOR_PORT || 9101,
  ASSET_DELIVERY_MONITOR: process.env.ASSET_DELIVERY_MONITOR_PORT || 9102,
};

/**
 * Get metrics from a component
 *
 * @param {string} component - Component name
 * @returns {string} - Prometheus metrics
 */
async function getComponentMetrics(component) {
  try {
    const port = COMPONENT_PORTS[component];
    if (!port) {
      logger.error(`Unknown component: ${component}`);
      return '';
    }

    const response = await axios.get(`http://localhost:${port}/metrics`);
    return response.data;
  } catch (error) {
    logger.error(`Error getting metrics from ${component}`, { error: error.message });
    return '';
  }
}

/**
 * Get all metrics
 *
 * @returns {string} - Combined Prometheus metrics
 */
async function getAllMetrics() {
  try {
    const metricsPromises = Object.keys(COMPONENT_PORTS).map(component =>
      getComponentMetrics(component),
    );

    const metricsResults = await Promise.all(metricsPromises);
    return metricsResults.join('\n');
  } catch (error) {
    logger.error('Error getting all metrics', { error: error.message });
    return '';
  }
}

/**
 * Get dashboard data
 *
 * @returns {Object} - Dashboard data
 */
async function getDashboardData() {
  try {
    // Get active alerts
    const { data: alerts, error: alertsError } = await supabase
      .from('alerts')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (alertsError) {
      logger.error('Error fetching alerts for dashboard', { error: alertsError.message });
    }

    // Get user segments
    const { data: segments, error: segmentsError } = await supabase
      .from('user_segments')
      .select('segment, count')
      .order('count', { ascending: false });

    if (segmentsError) {
      logger.error('Error fetching user segments for dashboard', { error: segmentsError.message });
    }

    // Get active A/B tests
    const { data: abTests, error: abTestsError } = await supabase
      .from('ab_tests')
      .select(
        `
        id,
        name,
        description,
        start_date,
        end_date,
        traffic_percentage,
        variants (
          id,
          name,
          description,
          traffic_weight
        )
      `,
      )
      .eq('status', 'active');

    if (abTestsError) {
      logger.error('Error fetching A/B tests for dashboard', { error: abTestsError.message });
    }

    // Get scaling recommendations
    let scalingRecommendations = [];
    try {
      const response = await axios.get(
        `http://localhost:${COMPONENT_PORTS.PREDICTIVE_ANALYZER}/api/scaling-recommendations`,
      );
      scalingRecommendations = response.data.recommendations || [];
    } catch (error) {
      logger.error('Error fetching scaling recommendations', { error: error.message });
    }

    // Get system health
    const { data: healthData, error: healthError } = await supabase
      .from('system_health')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(1);

    if (healthError) {
      logger.error('Error fetching system health for dashboard', { error: healthError.message });
    }

    // Get recent errors
    const { data: recentErrors, error: errorsError } = await supabase
      .from('error_logs')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(10);

    if (errorsError) {
      logger.error('Error fetching recent errors for dashboard', { error: errorsError.message });
    }

    return {
      alerts: alerts || [],
      userSegments: segments || [],
      abTests: abTests || [],
      scalingRecommendations,
      systemHealth: healthData?.[0] || {},
      recentErrors: recentErrors || [],
    };
  } catch (error) {
    logger.error('Error getting dashboard data', { error: error.message });
    return {
      alerts: [],
      userSegments: [],
      abTests: [],
      scalingRecommendations: [],
      systemHealth: {},
      recentErrors: [],
    };
  }
}

/**
 * Get component health status
 *
 * @returns {Object} - Component health status
 */
async function getComponentHealth() {
  const health = {};

  for (const [component, port] of Object.entries(COMPONENT_PORTS)) {
    try {
      await axios.get(`http://localhost:${port}/metrics`, { timeout: 2000 });
      health[component] = { status: 'healthy' };
    } catch (error) {
      health[component] = {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  return health;
}

// API endpoints
app.get('/api/dashboard', async (req, res) => {
  try {
    const dashboardData = await getDashboardData();
    res.json(dashboardData);
  } catch (error) {
    logger.error('Error in GET /api/dashboard', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/health', async (req, res) => {
  try {
    const health = await getComponentHealth();

    // Check if all components are healthy
    const allHealthy = Object.values(health).every(h => h.status === 'healthy');

    res.json({
      status: allHealthy ? 'healthy' : 'degraded',
      components: health,
    });
  } catch (error) {
    logger.error('Error in GET /api/health', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/metrics', async (req, res) => {
  try {
    const metrics = await getAllMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(metrics);
  } catch (error) {
    logger.error('Error in GET /metrics', { error: error.message });
    res.status(500).send('Error collecting metrics');
  }
});

// User segment endpoints
app.get('/api/user-segments', async (req, res) => {
  try {
    // Trigger user segmentation
    await identifyUserSegments();

    // Get user segments
    const { data, error } = await supabase
      .from('user_segments')
      .select('*')
      .order('count', { ascending: false });

    if (error) {
      logger.error('Error fetching user segments', { error: error.message });
      return res.status(500).json({ error: 'Error fetching user segments' });
    }

    res.json({ segments: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/user-segments', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/user-segments/:segment/metrics', async (req, res) => {
  try {
    const { segment } = req.params;

    // Get segment metrics
    const { data, error } = await supabase
      .from('segment_metrics')
      .select('*')
      .eq('segment', segment);

    if (error) {
      logger.error('Error fetching segment metrics', { error: error.message });
      return res.status(500).json({ error: 'Error fetching segment metrics' });
    }

    res.json({ metrics: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/user-segments/:segment/metrics', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// A/B test endpoints
app.get('/api/ab-tests', async (req, res) => {
  try {
    // Get A/B tests
    const { data, error } = await supabase
      .from('ab_tests')
      .select(
        `
        id,
        name,
        description,
        start_date,
        end_date,
        traffic_percentage,
        status,
        variants (
          id,
          name,
          description,
          traffic_weight
        )
      `,
      )
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching A/B tests', { error: error.message });
      return res.status(500).json({ error: 'Error fetching A/B tests' });
    }

    res.json({ tests: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/ab-tests', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/ab-tests/:id/metrics', async (req, res) => {
  try {
    const { id } = req.params;

    // Trigger test performance analysis
    await analyzeTestPerformance();

    // Get test metrics
    const { data, error } = await supabase.from('ab_test_metrics').select('*').eq('test_id', id);

    if (error) {
      logger.error('Error fetching A/B test metrics', { error: error.message });
      return res.status(500).json({ error: 'Error fetching A/B test metrics' });
    }

    res.json({ metrics: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/ab-tests/:id/metrics', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Predictive analysis endpoints
app.get('/api/predictions', async (req, res) => {
  try {
    // Trigger predictive analysis
    await runPredictiveAnalysis();

    // Get predictions
    const { data, error } = await supabase
      .from('predictions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100);

    if (error) {
      logger.error('Error fetching predictions', { error: error.message });
      return res.status(500).json({ error: 'Error fetching predictions' });
    }

    res.json({ predictions: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/predictions', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/anomalies', async (req, res) => {
  try {
    // Get anomalies
    const { data, error } = await supabase
      .from('anomalies')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100);

    if (error) {
      logger.error('Error fetching anomalies', { error: error.message });
      return res.status(500).json({ error: 'Error fetching anomalies' });
    }

    res.json({ anomalies: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/anomalies', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Authentication monitoring endpoints
app.get('/api/auth-metrics', async (req, res) => {
  try {
    // Get authentication metrics
    const { data, error } = await supabase
      .from('auth_metrics')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(100);

    if (error) {
      logger.error('Error fetching auth metrics', { error: error.message });
      return res.status(500).json({ error: 'Error fetching auth metrics' });
    }

    res.json({ metrics: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/auth-metrics', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Asset delivery monitoring endpoints
app.get('/api/asset-delivery-metrics', async (req, res) => {
  try {
    // Get asset delivery metrics
    const { data, error } = await supabase
      .from('asset_delivery_metrics')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(100);

    if (error) {
      logger.error('Error fetching asset delivery metrics', { error: error.message });
      return res.status(500).json({ error: 'Error fetching asset delivery metrics' });
    }

    res.json({ metrics: data || [] });
  } catch (error) {
    logger.error('Error in GET /api/asset-delivery-metrics', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.MONITORING_SERVICE_PORT || 9099;
app.listen(PORT, () => {
  logger.info(`Monitoring Service listening on port ${PORT}`);
});

module.exports = {
  getDashboardData,
  getComponentHealth,
  getAllMetrics,
};
