/**
 * Apply Validation to High-Risk Endpoints
 * 
 * This script applies validation to high-risk endpoints identified in the audit.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// Configuration
const config = {
  auditPath: path.join(__dirname, '../../logs/api-validation-audit.json'),
  validationMiddlewarePath: path.join(__dirname, '../../middleware/query-validation.js'),
  highRiskMethods: ['POST', 'PUT', 'PATCH'],
  maxEndpointsToFix: 10 // Limit the number of endpoints to fix in one run
};

/**
 * Load audit results
 * @returns {Promise<Object>} Audit results
 */
async function loadAuditResults() {
  const data = await readFileAsync(config.auditPath, 'utf8');
  return JSON.parse(data);
}

/**
 * Generate validation schema for a route
 * @param {string} method - HTTP method
 * @param {string} route - Route path
 * @returns {string} Validation schema code
 */
function generateValidationSchema(method, route) {
  // Extract parameter names from route
  const paramNames = [];
  const paramRegex = /:([a-zA-Z0-9_]+)/g;
  let match;
  
  while ((match = paramRegex.exec(route)) !== null) {
    paramNames.push(match[1]);
  }
  
  // Generate schema based on method
  switch (method) {
    case 'GET':
      return `

const ${method.toLowerCase()}${route.replace(/\//g, '_').replace(/:/g, '').replace(/-/g, '_')}Schema = z.object({
  ${paramNames.map(param => `${param}: z.string()`).join(',\n  ')}
  // Add more validation rules as needed
});`;
    
    case 'POST':
    case 'PUT':
    case 'PATCH':
      return `

const ${method.toLowerCase()}${route.replace(/\//g, '_').replace(/:/g, '').replace(/-/g, '_')}Schema = z.object({
  ${paramNames.map(param => `${param}: z.string()`).join(',\n  ')}
  // Add more validation rules as needed
});`;
    
    case 'DELETE':
      return `

const ${method.toLowerCase()}${route.replace(/\//g, '_').replace(/:/g, '').replace(/-/g, '_')}Schema = z.object({
  ${paramNames.map(param => `${param}: z.string()`).join(',\n  ')}
  // Add more validation rules as needed
});`;
    
    default:
      return `

const ${method.toLowerCase()}${route.replace(/\//g, '_').replace(/:/g, '').replace(/-/g, '_')}Schema = z.object({
  // Add validation rules as needed
});`;
  }
}

/**
 * Apply validation to a file
 * @param {string} filePath - Path to file
 * @param {Array<Object>} routes - Routes to apply validation to
 * @returns {Promise<Object>} Result of applying validation
 */
async function applyValidationToFile(filePath, routes) {
  const content = await readFileAsync(filePath, 'utf8');
  const fullPath = path.resolve(filePath);
  
  // Parse the file
  const ast = parse(content, {
    sourceType: 'module',
    plugins: ['jsx', 'typescript']
  });
  
  // Check if file already imports validation middleware
  let hasValidationImport = false;
  let hasZodImport = false;
  
  traverse(ast, {
    ImportDeclaration(path) {
      const source = path.node.source.value;
      
      if (source.includes('query-validation')) {
        hasValidationImport = true;
      }
      
      if (source === 'zod') {
        hasZodImport = true;
      }
    }
  });
  
  // Add imports if needed
  if (!hasValidationImport) {
    const importDeclaration = t.importDeclaration(
      [
        t.importSpecifier(t.identifier('validateQuery'), t.identifier('validateQuery')),
        t.importSpecifier(t.identifier('commonSchemas'), t.identifier('commonSchemas')),
        t.importSpecifier(t.identifier('combineSchemas'), t.identifier('combineSchemas'))
      ],
      t.stringLiteral('../../middleware/query-validation')
    );
    
    ast.program.body.unshift(importDeclaration);
  }
  
  if (!hasZodImport) {
    const importDeclaration = t.importDeclaration(
      [t.importSpecifier(t.identifier('z'), t.identifier('z'))],
      t.stringLiteral('zod')
    );
    
    ast.program.body.unshift(importDeclaration);
  }
  
  // Generate validation schemas
  const schemas = routes.map(route => generateValidationSchema(route.method, route.route));
  
  // Find the last import declaration
import { logger } from '../shared/utils/logger';
  let lastImportIndex = -1;
  
  for (let i = 0; i < ast.program.body.length; i++) {
    if (ast.program.body[i].type === 'ImportDeclaration') {
      lastImportIndex = i;
    }
  }
  
  // Insert schemas after imports
  const schemasCode = schemas.join('\n');
  const schemasAst = parse(schemasCode, {
    sourceType: 'module',
    plugins: ['jsx', 'typescript']
  });
  
  ast.program.body.splice(lastImportIndex + 1, 0, ...schemasAst.program.body);
  
  // Apply validation middleware to routes
  const routeMap = new Map(routes.map(route => [`${route.method}:${route.route}`, route]));
  let fixedRoutes = 0;
  
  traverse(ast, {
    CallExpression(path) {
      const callee = path.node.callee;
      const args = path.node.arguments;
      
      // Check for router.METHOD calls (e.g., router.get, router.post)
      if (
        callee.type === 'MemberExpression' &&
        (callee.object.name === 'router' || callee.object.name === 'app') &&
        ['get', 'post', 'put', 'patch', 'delete'].includes(callee.property.name) &&
        args.length >= 2
      ) {
        const method = callee.property.name.toUpperCase();
        let route = '';
        
        // Get route path
        if (args[0].type === 'StringLiteral') {
          route = args[0].value;
        }
        
        const key = `${method}:${route}`;
        
        if (routeMap.has(key)) {
          const routeInfo = routeMap.get(key);
          
          // Check if this route needs validation
          if (!routeInfo.hasValidation) {
            // Generate validation middleware call
            const schemaName = `${method.toLowerCase()}${route.replace(/\//g, '_').replace(/:/g, '').replace(/-/g, '_')}Schema`;
            const validationCall = parse(`validateQuery(${schemaName})`, { sourceType: 'module' }).program.body[0].expression;
            
            // Insert validation middleware before the route handler
            args.splice(1, 0, validationCall);
            
            fixedRoutes++;
            logger.info(`Applied validation to ${method} ${route}`);
          }
        }
      }
    }
  });
  
  // Generate modified code
  const output = generate(ast, {}, content);
  
  // Write modified code back to file
  await writeFileAsync(filePath, output.code);
  
  return {
    file: filePath,
    fixedRoutes
  };
}

/**
 * Apply validation to high-risk endpoints
 * @returns {Promise<Object>} Result of applying validation
 */
async function applyValidation() {
  logger.info('Applying validation to high-risk endpoints...');
  
  // Load audit results
  const auditResults = await loadAuditResults();
  
  // Get high-risk routes without validation
  const highRiskRoutes = auditResults.routesWithoutValidation.filter(route => 
    config.highRiskMethods.includes(route.method)
  );
  
  logger.info(`Found ${highRiskRoutes.length} high-risk routes without validation`);
  
  // Limit the number of endpoints to fix
  const routesToFix = highRiskRoutes.slice(0, config.maxEndpointsToFix);
  
  logger.info(`Fixing ${routesToFix.length} endpoints in this run`);
  
  // Group routes by file
  const routesByFile = {};
  
  for (const route of routesToFix) {
    const filePath = path.join(__dirname, '../..', route.file);
    
    if (!routesByFile[filePath]) {
      routesByFile[filePath] = [];
    }
    
    routesByFile[filePath].push(route);
  }
  
  // Apply validation to each file
  const results = {
    timestamp: new Date().toISOString(),
    totalFilesModified: 0,
    totalRoutesFixed: 0,
    details: []
  };
  
  for (const [filePath, routes] of Object.entries(routesByFile)) {
    try {
      const result = await applyValidationToFile(filePath, routes);
      
      results.totalFilesModified++;
      results.totalRoutesFixed += result.fixedRoutes;
      results.details.push(result);
    } catch (error) {
      console.error(`Error applying validation to ${filePath}:`, error);
      
      results.details.push({
        file: filePath,
        error: error.message
      });
    }
  }
  
  logger.info(`Applied validation to ${results.totalRoutesFixed} routes in ${results.totalFilesModified} files`);
  
  return results;
}

// If script is run directly, apply validation
if (require.main === module) {
  applyValidation()
    .then(results => {
      logger.info('Validation application completed:');
      logger.info(`Modified ${results.totalFilesModified} files`);
      logger.info(`Fixed ${results.totalRoutesFixed} routes`);
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  applyValidation
};
