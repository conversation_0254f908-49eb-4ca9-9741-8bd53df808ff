FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY utils ./utils
COPY config ./config
COPY monitoring/ue-plugin-monitor.js ./monitoring/

# Set environment variables
ENV NODE_ENV=production
ENV UE_PLUGIN_MONITOR_PORT=9100

# Expose port
EXPOSE 9100

# Start the service
CMD ["node", "monitoring/ue-plugin-monitor.js"]
