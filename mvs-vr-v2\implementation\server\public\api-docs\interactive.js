import { logger } from '../shared/utils/logger';

/**
 * Interactive API Documentation Enhancement
 * 
 * This script enhances the interactive API documentation with additional features:
 * - Code snippet generation
 * - Authentication flow visualization
 * - Response visualization
 * - Dark mode toggle
 */

document.addEventListener('DOMContentLoaded', () => {
  // Initialize the documentation enhancements
  initializeDocumentation();
});

/**
 * Initialize the documentation enhancements
 */
function initializeDocumentation() {
  // Add dark mode toggle
  addDarkModeToggle();
  
  // Add language selector for code snippets
  addLanguageSelector();
  
  // Add authentication flow visualization
  addAuthFlowVisualization();
  
  // Add event listeners for API operations
  addOperationListeners();
}

/**
 * Add dark mode toggle
 */
function addDarkModeToggle() {
  const header = document.querySelector('.api-docs-header');
  if (!header) return;
  
  const darkModeToggle = document.createElement('button');
  darkModeToggle.className = 'dark-mode-toggle';
  darkModeToggle.innerHTML = '🌙';
  darkModeToggle.title = 'Toggle dark mode';
  
  darkModeToggle.addEventListener('click', () => {
    document.body.classList.toggle('dark-mode');
    darkModeToggle.innerHTML = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    
    // Save preference
    localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
  });
  
  // Check saved preference
  if (localStorage.getItem('darkMode') === 'true') {
    document.body.classList.add('dark-mode');
    darkModeToggle.innerHTML = '☀️';
  }
  
  header.appendChild(darkModeToggle);
}

/**
 * Add language selector for code snippets
 */
function addLanguageSelector() {
  const header = document.querySelector('.api-docs-header');
  if (!header) return;
  
  const languageSelector = document.createElement('select');
  languageSelector.className = 'language-selector';
  languageSelector.title = 'Select code snippet language';
  
  const languages = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'python', label: 'Python' },
    { value: 'curl', label: 'cURL' },
    { value: 'csharp', label: 'C#' },
  ];
  
  languages.forEach(language => {
    const option = document.createElement('option');
    option.value = language.value;
    option.textContent = language.label;
    languageSelector.appendChild(option);
  });
  
  languageSelector.addEventListener('change', () => {
    // Save preference
    localStorage.setItem('preferredLanguage', languageSelector.value);
    
    // Update code snippets
    updateCodeSnippets(languageSelector.value);
  });
  
  // Check saved preference
  const preferredLanguage = localStorage.getItem('preferredLanguage');
  if (preferredLanguage) {
    languageSelector.value = preferredLanguage;
  }
  
  const languageContainer = document.createElement('div');
  languageContainer.className = 'language-container';
  languageContainer.appendChild(document.createTextNode('Code snippets: '));
  languageContainer.appendChild(languageSelector);
  
  header.appendChild(languageContainer);
}

/**
 * Update code snippets based on selected language
 * @param {string} language - Selected language
 */
function updateCodeSnippets(language) {
  // This will be implemented when the Stoplight Elements are fully loaded
  // and we have access to the API operations
  logger.info(`Updating code snippets to ${language}`);
  
  // Find all code snippet containers and update them
  const codeSnippets = document.querySelectorAll('.code-snippet-container');
  codeSnippets.forEach(container => {
    const snippets = container.querySelectorAll('.code-snippet');
    snippets.forEach(snippet => {
      snippet.style.display = snippet.dataset.language === language ? 'block' : 'none';
    });
  });
}

/**
 * Add authentication flow visualization
 */
function addAuthFlowVisualization() {
  // This will be implemented when we have the authentication endpoints
  // and can visualize the flow
  logger.info('Adding authentication flow visualization');
}

/**
 * Add event listeners for API operations
 */
function addOperationListeners() {
  // This will be implemented when the Stoplight Elements are fully loaded
  // and we have access to the API operations
  logger.info('Adding operation listeners');
  
  // We need to wait for the Stoplight Elements to be fully loaded
  // before we can add event listeners to the API operations
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        const operations = document.querySelectorAll('.operation');
        if (operations.length) {
          operations.forEach(operation => {
            operation.addEventListener('click', () => {
              // When an operation is clicked, we can enhance it with
              // additional features like code snippets
              enhanceOperation(operation);
            });
          });
          
          // Once we've added the listeners, we can disconnect the observer
          observer.disconnect();
        }
      }
    });
  });
  
  // Start observing the document
  observer.observe(document.body, { childList: true, subtree: true });
}

/**
 * Enhance an API operation with additional features
 * @param {HTMLElement} operation - API operation element
 */
function enhanceOperation(operation) {
  // This will be implemented when we have the API operations
  // and can enhance them with additional features
  logger.info('Enhancing operation', operation);
}
