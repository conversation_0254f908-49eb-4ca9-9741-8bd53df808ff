# Testing Documentation

## Overview

This project uses Vitest as the main testing framework, supporting both Node.js and Deno environments. The test setup is configured to work seamlessly across platforms while maintaining type safety and proper module resolution.

## Setup

### Prerequisites

- Node.js (v18+)
- Deno (v1.35+)
- npm/pnpm

### Initial Setup

1. Run the bootstrap script:

   ```bash
   chmod +x scripts/test-bootstrap.sh
   ./scripts/test-bootstrap.sh
   ```

2. Copy environment configuration:

   ```bash
   cp .env.example .env.test
   ```

## Directory Structure

```
tests/
├── integration/    # Integration tests
├── services/      # Service-level tests
├── setup/         # Test setup and configuration
└── utils/         # Shared test utilities
```

## Writing Tests

### File Naming Convention

- Unit tests: `*.test.ts`
- Integration tests: `*.integration.test.ts`
- Component tests: `*.spec.ts`
- Vitest-specific tests: `*.vitest.ts`

### Import Convention

```typescript
// For vitest imports
import { describe, it, expect } from 'npm:vitest';

// For local modules
import { MyService } from '../../services/my-service.ts';

// For test utilities
import { createMockService } from '../utils/test-utils.ts';
```

### Test Structure

```typescript
describe('MyService', () => {
  // Setup
  beforeEach(() => {
    // Setup code
  });

  // Test cases
  it('should perform expected behavior', () => {
    // Test code
  });

  // Edge cases
  it('should handle edge case', () => {
    // Edge case test
  });

  // Error cases
  it('should handle errors', () => {
    // Error test
  });
});
```

## Configuration Files

- `vitest.config.js`: Main Vitest configuration
- `tsconfig.json`: TypeScript configuration for source code
- `tests/tsconfig.json`: TypeScript configuration for tests
- `deno.json`: Deno-specific configuration
- `.env.test`: Test environment variables

## Test Types

### Unit Tests

- Test individual functions and classes
- Isolated from external dependencies
- Use mocks for dependencies

### Integration Tests

- Test service interactions
- May use real dependencies
- Focus on service boundaries

### E2E Tests

- Test complete workflows
- Use real services when possible
- Focus on user scenarios

## Mocking

### Service Mocks

```typescript
import { createMockService } from '../utils/test-utils';

const mockService = createMockService('ServiceName', {
  dependencies: ['OtherService'],
  methods: {
    myMethod: vi.fn().mockResolvedValue(result)
  }
});
```

### Console Mocks

```typescript
const mockConsole = testUtils.createMockConsole();
```

## Assertions

### Basic Assertions

```typescript
expect(value).toBe(expected);
expect(promise).resolves.toBe(expected);
expect(fn).toThrow(error);
```

### Custom Matchers

```typescript
expect(element).toBeInTheDocument();
expect(element).toBeVisible();
expect(element).toHaveAttribute('data-test', 'value');
```

## Running Tests

### All Tests

```bash
npm test
```

### Watch Mode

```bash
npm run test:watch
```

### Coverage

```bash
npm run test:coverage
```

### Specific Tests

```bash
npm test -- tests/integration/my-test.test.ts
```

## Debug

### VSCode Launch Configuration

Launch configurations are provided for debugging tests in both Node.js and Deno environments.

### Environment Variables

Set `DEBUG=true` in `.env.test` for additional logging during test execution.

## Common Issues

### Module Resolution

If experiencing module resolution issues:

1. Check import paths
2. Verify tsconfig.json paths
3. Run `./scripts/test-bootstrap.sh` to reset environment

### Type Errors

1. Ensure correct type imports
2. Check for missing type definitions
3. Verify TypeScript configuration

## Best Practices

1. **Isolation**: Each test should be independent
2. **Cleanup**: Use `afterEach` to clean up resources
3. **Naming**: Use descriptive test names
4. **Organization**: Group related tests using `describe`
5. **Mocking**: Prefer dependency injection for easier mocking
6. **Coverage**: Aim for high coverage but focus on critical paths
7. **Documentation**: Document complex test setups

## Contributing

1. Follow existing patterns
2. Add tests for new features
3. Update documentation
4. Run full test suite before committing
