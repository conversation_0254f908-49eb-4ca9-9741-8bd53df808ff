/**
 * Predictive Scaling Service
 * 
 * This service predicts resource needs based on historical patterns and current trends,
 * and automatically scales resources to meet anticipated demand.
 */

const express = require('express');
const promClient = require('prom-client');
const { createLogger, format, transports } = require('winston');
const axios = require('axios');
const { EventEmitter } = require('events');

// Configuration
const config = {
  port: parseInt(process.env.PREDICTIVE_SCALING_SERVICE_PORT || '9113', 10),
  logLevel: process.env.LOG_LEVEL || 'info',
  metricsEndpoint: process.env.METRICS_ENDPOINT || 'http://prometheus:9090/api/v1/query',
  kubernetesApiEndpoint: process.env.KUBERNETES_API_ENDPOINT || 'http://kubernetes-api:8080',
  kubernetesNamespace: process.env.KUBERNETES_NAMESPACE || 'mvs-vr',
  updateInterval: parseInt(process.env.UPDATE_INTERVAL || '60000', 10), // 1 minute
  predictionHorizon: parseInt(process.env.PREDICTION_HORIZON || '30', 10), // 30 minutes
  scalingThresholds: {
    cpu: {
      scaleUp: parseFloat(process.env.CPU_SCALE_UP_THRESHOLD || '70.0'),
      scaleDown: parseFloat(process.env.CPU_SCALE_DOWN_THRESHOLD || '30.0')
    },
    memory: {
      scaleUp: parseFloat(process.env.MEMORY_SCALE_UP_THRESHOLD || '70.0'),
      scaleDown: parseFloat(process.env.MEMORY_SCALE_DOWN_THRESHOLD || '30.0')
    },
    requests: {
      scaleUp: parseFloat(process.env.REQUESTS_SCALE_UP_THRESHOLD || '70.0'),
      scaleDown: parseFloat(process.env.REQUESTS_SCALE_DOWN_THRESHOLD || '30.0')
    }
  },
  scalingLimits: {
    minReplicas: parseInt(process.env.MIN_REPLICAS || '1', 10),
    maxReplicas: parseInt(process.env.MAX_REPLICAS || '10', 10),
    cooldownPeriod: parseInt(process.env.COOLDOWN_PERIOD || '300000', 10) // 5 minutes
  },
  services: (process.env.SERVICES || 'api-server,asset-processor,authentication-service').split(','),
  enableAutoScaling: process.env.ENABLE_AUTO_SCALING === 'true'
};

// Setup logger
const logger = createLogger({
  level: config.logLevel,
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  defaultMeta: { service: 'predictive-scaling-service' },
  transports: [
    new transports.Console()
  ]
});

// Initialize Prometheus metrics
const register = new promClient.Registry();
promClient.collectDefaultMetrics({ register });

// Custom metrics
const metrics = {
  predictedLoad: new promClient.Gauge({
    name: 'mvs_vr_predicted_load',
    help: 'Predicted load for a service',
    labelNames: ['service', 'resource_type', 'prediction_window']
  }),
  scalingRecommendations: new promClient.Gauge({
    name: 'mvs_vr_scaling_recommendations',
    help: 'Scaling recommendations for a service',
    labelNames: ['service', 'direction', 'reason']
  }),
  scalingActions: new promClient.Counter({
    name: 'mvs_vr_scaling_actions_total',
    help: 'Total number of scaling actions',
    labelNames: ['service', 'direction', 'result']
  }),
  predictionAccuracy: new promClient.Gauge({
    name: 'mvs_vr_prediction_accuracy',
    help: 'Accuracy of load predictions',
    labelNames: ['service', 'resource_type', 'prediction_window']
  })
};

// Register custom metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Predictive Scaling Service
 */
class PredictiveScalingService extends EventEmitter {
  constructor() {
    super();
    this.app = express();
    this.metricHistory = new Map();
    this.predictions = new Map();
    this.scalingHistory = new Map();
    this.lastScalingTime = new Map();
    this.setupExpress();
    this.setupIntervals();
  }

  /**
   * Setup Express server
   */
  setupExpress() {
    this.app.get('/metrics', async (req, res) => {
      res.set('Content-Type', register.contentType);
      res.end(await register.metrics());
    });

    this.app.get('/api/predictions', (req, res) => {
      const predictions = Array.from(this.predictions.entries()).map(([key, value]) => ({
        service: key,
        ...value
      }));
      res.json(predictions);
    });

    this.app.get('/api/scaling-history', (req, res) => {
      const history = Array.from(this.scalingHistory.entries()).map(([key, value]) => ({
        service: key,
        actions: value
      }));
      res.json(history);
    });

    this.app.post('/api/scale', async (req, res) => {
      const { service, replicas } = req.body;
      
      if (!service || !replicas) {
        return res.status(400).json({ error: 'Missing required parameters' });
      }

      try {
        const result = await this.scaleService(service, replicas);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/health', (req, res) => {
      res.json({ status: 'ok' });
    });
  }

  /**
   * Setup intervals for periodic tasks
   */
  setupIntervals() {
    // Fetch metrics from Prometheus periodically
    setInterval(() => this.fetchMetricsFromPrometheus(), config.updateInterval);

    // Generate predictions periodically
    setInterval(() => this.generatePredictions(), config.updateInterval);

    // Generate scaling recommendations periodically
    setInterval(() => this.generateScalingRecommendations(), config.updateInterval);

    // Apply scaling if auto-scaling is enabled
    if (config.enableAutoScaling) {
      setInterval(() => this.applyScaling(), config.updateInterval);
    }
  }

  /**
   * Fetch metrics from Prometheus
   */
  async fetchMetricsFromPrometheus() {
    try {
      for (const service of config.services) {
        // Fetch CPU usage
        await this.fetchMetric(
          `avg(container_cpu_usage_seconds_total{namespace="${config.kubernetesNamespace}",container="${service}"})`,
          service,
          'cpu'
        );

        // Fetch memory usage
        await this.fetchMetric(
          `avg(container_memory_usage_bytes{namespace="${config.kubernetesNamespace}",container="${service}"})`,
          service,
          'memory'
        );

        // Fetch request rate
        await this.fetchMetric(
          `sum(rate(mvs_vr_http_requests_total{service="${service}"}[5m]))`,
          service,
          'requests'
        );
      }
    } catch (error) {
      logger.error('Error fetching metrics from Prometheus', { error: error.message });
    }
  }

  /**
   * Fetch a specific metric from Prometheus
   * @param {string} query - Prometheus query
   * @param {string} service - Service name
   * @param {string} metricType - Metric type
   */
  async fetchMetric(query, service, metricType) {
    try {
      const response = await axios.get(config.metricsEndpoint, {
        params: { query }
      });

      if (response.data && response.data.data && response.data.data.result && response.data.data.result.length > 0) {
        const value = parseFloat(response.data.data.result[0].value[1]);
        const timestamp = response.data.data.result[0].value[0] * 1000;

        const key = `${service}_${metricType}`;
        if (!this.metricHistory.has(key)) {
          this.metricHistory.set(key, []);
        }

        const history = this.metricHistory.get(key);
        history.push({ value, timestamp });

        // Keep only the last 24 hours of data
        const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
        while (history.length > 0 && history[0].timestamp < oneDayAgo) {
          history.shift();
        }
      }
    } catch (error) {
      logger.error('Error fetching metric', { query, error: error.message });
    }
  }

  /**
   * Generate predictions for all services
   */
  generatePredictions() {
    for (const service of config.services) {
      this.generateServicePredictions(service);
    }
  }

  /**
   * Generate predictions for a specific service
   * @param {string} service - Service name
   */
  generateServicePredictions(service) {
    const predictionWindows = [5, 15, 30]; // 5, 15, and 30 minutes
    const resourceTypes = ['cpu', 'memory', 'requests'];
    
    const predictions = {
      timestamp: Date.now(),
      resources: {}
    };

    for (const resourceType of resourceTypes) {
      const key = `${service}_${resourceType}`;
      const history = this.metricHistory.get(key) || [];
      
      if (history.length < 10) {
        continue; // Not enough data
      }

      predictions.resources[resourceType] = {};

      for (const window of predictionWindows) {
        const prediction = this.predictValue(history, window);
        predictions.resources[resourceType][window] = prediction;
        
        // Update metrics
        metrics.predictedLoad.set(
          { service, resource_type: resourceType, prediction_window: `${window}m` },
          prediction.value
        );
      }
    }

    this.predictions.set(service, predictions);
  }

  /**
   * Predict a value based on historical data
   * @param {Array} history - Historical data
   * @param {number} window - Prediction window in minutes
   * @returns {Object} - Prediction result
   */
  predictValue(history, window) {
    // Simple linear regression for prediction
    // In a real implementation, this would use more sophisticated algorithms
    const recentHistory = history.slice(-60); // Last hour
    
    if (recentHistory.length < 10) {
      return { value: history[history.length - 1].value, confidence: 0 };
    }

    // Calculate trend
    const xValues = recentHistory.map((_, i) => i);
    const yValues = recentHistory.map(h => h.value);
    
    const n = xValues.length;
    const sumX = xValues.reduce((a, b) => a + b, 0);
    const sumY = yValues.reduce((a, b) => a + b, 0);
    const sumXY = xValues.reduce((a, b, i) => a + b * yValues[i], 0);
    const sumXX = xValues.reduce((a, b) => a + b * b, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    // Predict future value
    const futureX = n + (window / 60) * 60; // Convert minutes to data points
    const predictedValue = slope * futureX + intercept;
    
    // Calculate confidence (simplified)
    const predictions = xValues.map(x => slope * x + intercept);
    const errors = predictions.map((p, i) => Math.abs(p - yValues[i]));
    const meanError = errors.reduce((a, b) => a + b, 0) / n;
    const confidence = Math.max(0, 1 - meanError / (sumY / n));
    
    return {
      value: Math.max(0, predictedValue), // Ensure non-negative
      confidence
    };
  }

  /**
   * Generate scaling recommendations for all services
   */
  generateScalingRecommendations() {
    for (const service of config.services) {
      this.generateServiceScalingRecommendations(service);
    }
  }

  /**
   * Generate scaling recommendations for a specific service
   * @param {string} service - Service name
   */
  async generateServiceScalingRecommendations(service) {
    const predictions = this.predictions.get(service);
    
    if (!predictions || !predictions.resources) {
      return;
    }

    // Get current replicas
    const currentReplicas = await this.getCurrentReplicas(service);
    
    if (currentReplicas === null) {
      return;
    }

    // Check if we're in cooldown period
    const lastScalingTime = this.lastScalingTime.get(service) || 0;
    const now = Date.now();
    
    if (now - lastScalingTime < config.scalingLimits.cooldownPeriod) {
      return;
    }

    // Check if we need to scale based on predictions
    let scaleDirection = null;
    let scaleReason = null;
    let targetReplicas = currentReplicas;

    // Check CPU predictions
    if (predictions.resources.cpu && predictions.resources.cpu[30]) {
      const cpuPrediction = predictions.resources.cpu[30].value;
      
      if (cpuPrediction > config.scalingThresholds.cpu.scaleUp) {
        scaleDirection = 'up';
        scaleReason = 'cpu';
        targetReplicas = Math.min(
          config.scalingLimits.maxReplicas,
          Math.ceil(currentReplicas * 1.5)
        );
      } else if (cpuPrediction < config.scalingThresholds.cpu.scaleDown) {
        scaleDirection = 'down';
        scaleReason = 'cpu';
        targetReplicas = Math.max(
          config.scalingLimits.minReplicas,
          Math.floor(currentReplicas * 0.7)
        );
      }
    }

    // Check request rate predictions (if no CPU-based scaling)
    if (!scaleDirection && predictions.resources.requests && predictions.resources.requests[30]) {
      const requestsPrediction = predictions.resources.requests[30].value;
      const requestsPerReplica = requestsPrediction / currentReplicas;
      
      if (requestsPerReplica > config.scalingThresholds.requests.scaleUp) {
        scaleDirection = 'up';
        scaleReason = 'requests';
        targetReplicas = Math.min(
          config.scalingLimits.maxReplicas,
          Math.ceil(requestsPrediction / (config.scalingThresholds.requests.scaleUp * 0.8))
        );
      } else if (requestsPerReplica < config.scalingThresholds.requests.scaleDown) {
        scaleDirection = 'down';
        scaleReason = 'requests';
        targetReplicas = Math.max(
          config.scalingLimits.minReplicas,
          Math.floor(requestsPrediction / (config.scalingThresholds.requests.scaleDown * 1.2))
        );
      }
    }

    // Update metrics
    if (scaleDirection) {
      metrics.scalingRecommendations.set(
        { service, direction: scaleDirection, reason: scaleReason },
        targetReplicas
      );
    } else {
      metrics.scalingRecommendations.set(
        { service, direction: 'none', reason: 'stable' },
        currentReplicas
      );
    }

    return {
      service,
      currentReplicas,
      targetReplicas,
      direction: scaleDirection,
      reason: scaleReason
    };
  }

  /**
   * Apply scaling recommendations
   */
  async applyScaling() {
    for (const service of config.services) {
      try {
        const recommendation = await this.generateServiceScalingRecommendations(service);
        
        if (recommendation && recommendation.direction) {
          await this.scaleService(service, recommendation.targetReplicas);
        }
      } catch (error) {
        logger.error('Error applying scaling', { service, error: error.message });
      }
    }
  }

  /**
   * Scale a service
   * @param {string} service - Service name
   * @param {number} replicas - Target replicas
   * @returns {Object} - Scaling result
   */
  async scaleService(service, replicas) {
    try {
      // Get current replicas
      const currentReplicas = await this.getCurrentReplicas(service);
      
      if (currentReplicas === null) {
        throw new Error('Failed to get current replicas');
      }

      // No change needed
      if (currentReplicas === replicas) {
        return { service, success: true, message: 'No scaling needed' };
      }

      // Apply scaling
      const direction = replicas > currentReplicas ? 'up' : 'down';
      
      // In a real implementation, this would call the Kubernetes API
      // For now, we'll just log the action
      logger.info('Scaling service', { service, from: currentReplicas, to: replicas, direction });
      
      // Update scaling history
      if (!this.scalingHistory.has(service)) {
        this.scalingHistory.set(service, []);
      }
      
      const history = this.scalingHistory.get(service);
      history.push({
        timestamp: Date.now(),
        from: currentReplicas,
        to: replicas,
        direction,
        reason: 'prediction'
      });
      
      // Update last scaling time
      this.lastScalingTime.set(service, Date.now());
      
      // Update metrics
      metrics.scalingActions.inc({ service, direction, result: 'success' });
      
      return {
        service,
        success: true,
        message: `Scaled ${service} from ${currentReplicas} to ${replicas} replicas`,
        from: currentReplicas,
        to: replicas,
        direction
      };
    } catch (error) {
      logger.error('Error scaling service', { service, replicas, error: error.message });
      
      // Update metrics
      metrics.scalingActions.inc({ service, direction: 'unknown', result: 'failure' });
      
      throw error;
    }
  }

  /**
   * Get current replicas for a service
   * @param {string} service - Service name
   * @returns {number|null} - Current replicas or null if error
   */
  async getCurrentReplicas(service) {
    try {
      // In a real implementation, this would call the Kubernetes API
      // For now, we'll return a mock value
      return 2;
    } catch (error) {
      logger.error('Error getting current replicas', { service, error: error.message });
      return null;
    }
  }

  /**
   * Start the service
   */
  start() {
    this.app.listen(config.port, () => {
      logger.info(`Predictive Scaling Service listening on port ${config.port}`);
    });
  }
}

// Create and start the service
const service = new PredictiveScalingService();
service.start();

module.exports = PredictiveScalingService;
