import { resolve } from 'node:path';
import { existsSync } from 'node:fs';

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

interface ConfigFile {
  path: string;
  required: boolean;
  validate?: (content: string) => string[];
}

const requiredFiles: ConfigFile[] = [
  {
    path: 'tsconfig.json',
    required: true,
    validate: content => {
      const config = JSON.parse(content);
      const errors = [];
      if (!config.compilerOptions) errors.push('Missing compilerOptions');
      if (!config.include) errors.push('Missing include array');
      return errors;
    },
  },
  {
    path: 'tsconfig.base.json',
    required: true,
  },
  {
    path: 'tsconfig.node.json',
    required: true,
  },
  {
    path: 'tests/tsconfig.json',
    required: true,
  },
  {
    path: '.env.test',
    required: true,
  },
  {
    path: 'vitest.config.js',
    required: true,
  },
  {
    path: 'deno.json',
    required: true,
  },
  {
    path: 'import_map.json',
    required: true,
  },
];

const requiredDirs = ['.deno', 'tests/integration', 'tests/services', 'tests/setup', 'tests/utils'];

async function validateEnvironment(): Promise<ValidationResult> {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: [],
  };

  // Check Node.js version
  const nodeVersion = process.version.slice(1).split('.').map(Number);
  if (nodeVersion[0] < 18) {
    result.errors.push(`Node.js version must be 18 or higher (found ${process.version})`);
  }

  // Check Deno version
  try {
    const denoVersion = (await Deno.version).split('.').map(Number);
    if (denoVersion[0] < 1 || (denoVersion[0] === 1 && denoVersion[1] < 35)) {
      result.errors.push(`Deno version must be 1.35 or higher (found ${denoVersion.join('.')})`);
    }
  } catch (error) {
    result.errors.push('Deno is not installed or not accessible');
  }

  // Check required directories
  for (const dir of requiredDirs) {
    const fullPath = resolve(process.cwd(), dir);
    if (!existsSync(fullPath)) {
      result.errors.push(`Missing required directory: ${dir}`);
    }
  }

  // Check configuration files
  for (const file of requiredFiles) {
    const fullPath = resolve(process.cwd(), file.path);
    if (!existsSync(fullPath)) {
      if (file.required) {
        result.errors.push(`Missing required file: ${file.path}`);
      } else {
        result.warnings.push(`Missing optional file: ${file.path}`);
      }
      continue;
    }

    if (file.validate) {
      try {
        const content = await Deno.readTextFile(fullPath);
        const validationErrors = file.validate(content);
        if (validationErrors.length > 0) {
          result.errors.push(
            `Validation errors in ${file.path}:`,
            ...validationErrors.map(err => `  - ${err}`),
          );
        }
      } catch (error) {
        result.errors.push(`Error reading/validating ${file.path}: ${error.message}`);
      }
    }
  }

  // Check package.json dependencies
  try {
    const packageJson = JSON.parse(await Deno.readTextFile('package.json'));
    const requiredDeps = ['vitest', '@testing-library/jest-dom', '@types/node'];

    const missingDeps = requiredDeps.filter(
      dep =>
        !(
          (packageJson.dependencies && packageJson.dependencies[dep]) ||
          (packageJson.devDependencies && packageJson.devDependencies[dep])
        ),
    );

    if (missingDeps.length > 0) {
      result.errors.push('Missing required dependencies:', ...missingDeps.map(dep => `  - ${dep}`));
    }
  } catch (error) {
    result.errors.push(`Error reading package.json: ${error.message}`);
  }

  result.valid = result.errors.length === 0;
  return result;
}

if (import.meta.main) {
  const result = await validateEnvironment();

  if (result.warnings.length > 0) {
    console.warn('\nWarnings:');
    result.warnings.forEach(warning => console.warn(`- ${warning}`));
  }

  if (result.errors.length > 0) {
    console.error('\nErrors:');
    result.errors.forEach(error => console.error(`- ${error}`));
    Deno.exit(1);
  }

  if (result.valid) {
    console.log('\n✅ Test environment validation passed');
    Deno.exit(0);
  }
}

export { validateEnvironment, type ValidationResult };
