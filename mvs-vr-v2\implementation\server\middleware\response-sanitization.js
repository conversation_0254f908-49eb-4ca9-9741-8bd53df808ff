/**
 * Response Sanitization Middleware
 *
 * This middleware sanitizes response data to prevent sensitive information disclosure.
 * It detects and redacts sensitive information in API responses to prevent
 * accidental exposure of confidential data.
 */

// Import logger
const logger = console;

/**
 * Sensitive field patterns to sanitize
 */
const SENSITIVE_FIELD_PATTERNS = [
  // Authentication related
  /password/i,
  /secret/i,
  /token/i,
  /key/i,
  /auth/i,
  /credential/i,
  /private/i,

  // Personal identifiable information
  /ssn/i,
  /social.*security/i,
  /birth.*date/i,
  /dob/i,
  /address/i,
  /phone/i,
  /email/i,

  // Financial information
  /credit.*card/i,
  /card.*number/i,
  /cvv/i,
  /cvc/i,
  /pin/i,
  /account.*number/i,
  /routing.*number/i,
  /tax.*id/i,

  // Internal system information
  /internal/i,
  /system/i,
  /config/i,
  /env/i,
  /environment/i,
  /connection.*string/i,
  /database.*url/i,
];

/**
 * Fields that should never be sanitized
 */
const NEVER_SANITIZE_FIELDS = [
  // Identifiers
  'id',
  'uuid',
  'guid',
  '_id',

  // Public keys and tokens
  'public_key',
  'public_token',
  'token_type',
  'token_expires_at',

  // API key metadata (but not the key itself)
  'api_key_id',
  'api_key_name',
  'api_key_scopes',

  // Public user information
  'username',
  'display_name',
  'first_name',
  'last_name',

  // Public metadata
  'created_at',
  'updated_at',
  'status',
  'type',
];

/**
 * Check if a field name matches sensitive patterns
 * @param {string} fieldName - Field name to check
 * @returns {boolean} Whether the field is sensitive
 */
function isSensitiveField(fieldName) {
  // Never sanitize these fields
  if (NEVER_SANITIZE_FIELDS.includes(fieldName)) {
    return false;
  }

  // Check against sensitive patterns
  return SENSITIVE_FIELD_PATTERNS.some(pattern => pattern.test(fieldName));
}

/**
 * Sanitize an object recursively
 * @param {any} data - Data to sanitize
 * @param {Array<string>} path - Current path in the object
 * @param {Set<any>} visited - Set of visited objects to prevent circular references
 * @returns {any} Sanitized data
 */
function sanitizeData(data, path = [], visited = new Set()) {
  // Handle null/undefined
  if (data == null) {
    return data;
  }

  // Handle primitive types
  if (typeof data !== 'object') {
    return data;
  }

  // Prevent circular references
  if (visited.has(data)) {
    return '[Circular]';
  }

  visited.add(data);

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map((item, index) => sanitizeData(item, [...path, index], visited));
  }

  // Handle objects
  const result = {};

  for (const [key, value] of Object.entries(data)) {
    const currentPath = [...path, key];
    const fieldPath = currentPath.join('.');

    if (isSensitiveField(key) || isSensitiveField(fieldPath)) {
      // Sanitize sensitive field
      if (typeof value === 'string') {
        result[key] = '[REDACTED]';
      } else if (typeof value === 'number') {
        result[key] = 0;
      } else if (typeof value === 'boolean') {
        result[key] = false;
      } else if (value == null) {
        result[key] = null;
      } else {
        result[key] = '[REDACTED]';
      }
    } else {
      // Recursively sanitize non-sensitive field
      result[key] = sanitizeData(value, currentPath, visited);
    }
  }

  return result;
}

/**
 * Find sensitive fields in an object
 * @param {Object} obj - Object to search
 * @param {Array<string>} path - Current path in the object
 * @returns {Array<string>} Array of sensitive field paths
 */
function findSensitiveFields(obj, path = []) {
  const sensitiveFields = [];

  if (obj == null || typeof obj !== 'object') {
    return sensitiveFields;
  }

  for (const [key, value] of Object.entries(obj)) {
    const currentPath = [...path, key];
    const fieldPath = currentPath.join('.');

    if (isSensitiveField(key) || isSensitiveField(fieldPath)) {
      sensitiveFields.push(fieldPath);
    }

    if (value != null && typeof value === 'object') {
      sensitiveFields.push(...findSensitiveFields(value, currentPath));
    }
  }

  return sensitiveFields;
}

/**
 * Check if request is authorized to bypass sanitization
 * @param {Object} req - Express request object
 * @returns {boolean} Whether the request is authorized to bypass sanitization
 */
function isAuthorizedForBypass(req) {
  // Check for admin role
  if (req.user && req.user.role === 'admin') {
    return true;
  }

  // Check for specific bypass header with correct value
  const bypassHeader = req.headers['x-sanitization-bypass'];
  const bypassSecret = process.env.SANITIZATION_BYPASS_SECRET;

  if (bypassHeader && bypassSecret && bypassHeader === bypassSecret) {
    return true;
  }

  // Check for specific endpoint bypass
  const bypassEndpoints = ['/api/admin/users', '/api/admin/system', '/api/admin/logs'];

  if (req.path && bypassEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
    // Still require admin role for endpoint bypass
    return req.user && req.user.role === 'admin';
  }

  return false;
}

/**
 * Response sanitization middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function responseSanitization(options = {}) {
  const {
    enabled = true,
    logSanitization = false,
    additionalSensitiveFields = [],
    allowBypass = true,
    maskingChar = '[REDACTED]',
    sensitiveDataPatterns = [],
  } = options;

  // Add additional sensitive fields
  for (const field of additionalSensitiveFields) {
    if (typeof field === 'string') {
      SENSITIVE_FIELD_PATTERNS.push(new RegExp(field, 'i'));
    } else if (field instanceof RegExp) {
      SENSITIVE_FIELD_PATTERNS.push(field);
    }
  }

  // Add sensitive data patterns (for content-based detection)
  const contentPatterns = [
    // Credit card numbers
    /\b(?:\d[ -]*?){13,16}\b/g,
    // Social Security Numbers
    /\b\d{3}[-]?\d{2}[-]?\d{4}\b/g,
    // Email addresses
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    // IP addresses
    /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
    // Phone numbers
    /\b(?:\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b/g,
    // API keys (common formats)
    /\b[A-Za-z0-9_]{32,}\b/g,
    ...sensitiveDataPatterns,
  ];

  return (req, res, next) => {
    if (!enabled) {
      return next();
    }

    // Store original res.json method
    const originalJson = res.json;

    // Override res.json method
    res.json = function (data) {
      // Skip sanitization for error responses or if authorized to bypass
      if ((data && data.error) || (allowBypass && isAuthorizedForBypass(req))) {
        if (allowBypass && isAuthorizedForBypass(req) && logSanitization) {
          logger.info(
            `[SANITIZATION] Bypassed for ${req.method} ${req.path} by ${req.user?.id || 'unknown'}`,
          );
        }
        return originalJson.call(this, data);
      }

      // Sanitize response data
      const sanitized = sanitizeData(data);

      // Content-based sanitization for strings
      if (typeof data === 'string') {
        let sanitizedContent = data;
        for (const pattern of contentPatterns) {
          sanitizedContent = sanitizedContent.replace(pattern, maskingChar);
        }
        data = sanitizedContent;
      }

      if (logSanitization) {
        const sensitiveFields = findSensitiveFields(data);

        if (sensitiveFields.length > 0) {
          logger.info(
            `[SANITIZATION] Sanitized ${sensitiveFields.length} sensitive fields in response: ${sensitiveFields.join(', ')}`,
          );

          // Log access to sensitive data for audit purposes
          if (req.user) {
            logger.info(
              `[AUDIT] User ${req.user.id} accessed sensitive data in ${req.method} ${req.path}`,
            );
          }
        }
      }

      // Call original json method with sanitized data
      return originalJson.call(this, sanitized);
    };

    next();
  };
}

module.exports = responseSanitization;
