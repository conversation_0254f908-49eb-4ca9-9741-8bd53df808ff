/**
 * Cross-Region Backup Replication
 * 
 * This script implements cross-region replication for database, file storage,
 * and configuration backups.
 */

const { S3Client, CopyObjectCommand, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { logger } = require('../shared/utils/logger');
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

// Configuration
const config = {
  primaryRegion: process.env.PRIMARY_REGION || 'us-east-1',
  secondaryRegion: process.env.SECONDARY_REGION || 'us-west-2',
  buckets: {
    database: {
      primary: process.env.PRIMARY_DB_BUCKET || 'mvs-vr-db-backups',
      secondary: process.env.SECONDARY_DB_BUCKET || 'mvs-vr-db-backups-dr'
    },
    files: {
      primary: process.env.PRIMARY_FILES_BUCKET || 'mvs-vr-file-backups',
      secondary: process.env.SECONDARY_FILES_BUCKET || 'mvs-vr-file-backups-dr'
    },
    config: {
      primary: process.env.PRIMARY_CONFIG_BUCKET || 'mvs-vr-config-backups',
      secondary: process.env.SECONDARY_CONFIG_BUCKET || 'mvs-vr-config-backups-dr'
    }
  },
  replicationLogPath: path.join(__dirname, '../../logs/replication.json'),
  maxReplicationLag: 15 * 60 * 1000, // 15 minutes in milliseconds
};

/**
 * Create S3 client for a specific region
 * @param {string} region - AWS region
 * @returns {S3Client} S3 client
 */
function createS3Client(region) {
  return new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
}

/**
 * Load replication log
 * @returns {Object} Replication log
 */
async function loadReplicationLog() {
  try {
    if (fs.existsSync(config.replicationLogPath)) {
      const data = await readFileAsync(config.replicationLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading replication log:', error);
  }
  
  return {
    lastRun: null,
    replications: []
  };
}

/**
 * Save replication log
 * @param {Object} log - Replication log
 */
async function saveReplicationLog(log) {
  try {
    await writeFileAsync(
      config.replicationLogPath,
      JSON.stringify(log, null, 2),
      'utf8'
    );
  } catch (error) {
    console.error('Error saving replication log:', error);
  }
}

/**
 * List objects in a bucket
 * @param {S3Client} client - S3 client
 * @param {string} bucket - Bucket name
 * @returns {Array} List of objects
 */
async function listObjects(client, bucket) {
  const command = new ListObjectsV2Command({
    Bucket: bucket
  });

  const response = await client.send(command);
  return response.Contents || [];
}

/**
 * Replicate objects from primary to secondary region
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @returns {Object} Replication results
 */
async function replicateBucket(bucketType) {
  const primaryClient = createS3Client(config.primaryRegion);
  const secondaryClient = createS3Client(config.secondaryRegion);
  
  const primaryBucket = config.buckets[bucketType].primary;
  const secondaryBucket = config.buckets[bucketType].secondary;
  
  logger.info(`Replicating ${bucketType} backups from ${primaryBucket} to ${secondaryBucket}...`);
  
  // List objects in primary bucket
  const primaryObjects = await listObjects(primaryClient, primaryBucket);
  logger.info(`Found ${primaryObjects.length} objects in primary bucket`);
  
  // List objects in secondary bucket
  const secondaryObjects = await listObjects(secondaryClient, secondaryBucket);
  const secondaryKeys = new Set(secondaryObjects.map(obj => obj.Key));
  
  // Find objects that need to be replicated
  const objectsToReplicate = primaryObjects.filter(obj => {
    return !secondaryKeys.has(obj.Key) || 
           !secondaryObjects.find(sObj => 
             sObj.Key === obj.Key && sObj.LastModified >= obj.LastModified
           );
  });
  
  logger.info(`Found ${objectsToReplicate.length} objects to replicate`);
  
  // Replicate objects
  const results = {
    bucketType,
    totalObjects: primaryObjects.length,
    replicatedObjects: 0,
    failedObjects: 0,
    details: []
  };
  
  for (const obj of objectsToReplicate) {
    try {
      const copyCommand = new CopyObjectCommand({
        Bucket: secondaryBucket,
        CopySource: `${primaryBucket}/${obj.Key}`,
        Key: obj.Key
      });
      
      await secondaryClient.send(copyCommand);
      
      results.replicatedObjects++;
      results.details.push({
        key: obj.Key,
        status: 'success',
        timestamp: new Date().toISOString()
      });
      
      logger.info(`Replicated: ${obj.Key}`);
    } catch (error) {
      console.error(`Error replicating ${obj.Key}:`, error);
      
      results.failedObjects++;
      results.details.push({
        key: obj.Key,
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  return results;
}

/**
 * Verify replication by comparing objects in primary and secondary buckets
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @returns {Object} Verification results
 */
async function verifyReplication(bucketType) {
  const primaryClient = createS3Client(config.primaryRegion);
  const secondaryClient = createS3Client(config.secondaryRegion);
  
  const primaryBucket = config.buckets[bucketType].primary;
  const secondaryBucket = config.buckets[bucketType].secondary;
  
  logger.info(`Verifying ${bucketType} backup replication...`);
  
  // List objects in primary bucket
  const primaryObjects = await listObjects(primaryClient, primaryBucket);
  const primaryMap = new Map(primaryObjects.map(obj => [obj.Key, obj]));
  
  // List objects in secondary bucket
  const secondaryObjects = await listObjects(secondaryClient, secondaryBucket);
  const secondaryMap = new Map(secondaryObjects.map(obj => [obj.Key, obj]));
  
  // Verify replication
  const results = {
    bucketType,
    totalObjects: primaryObjects.length,
    replicatedObjects: 0,
    missingObjects: 0,
    outdatedObjects: 0,
    replicationLag: 0,
    details: []
  };
  
  for (const [key, primaryObj] of primaryMap.entries()) {
    const secondaryObj = secondaryMap.get(key);
    
    if (!secondaryObj) {
      results.missingObjects++;
      results.details.push({
        key,
        status: 'missing',
        timestamp: new Date().toISOString()
      });
    } else {
      const primaryDate = new Date(primaryObj.LastModified);
      const secondaryDate = new Date(secondaryObj.LastModified);
      const lagMs = Math.max(0, primaryDate - secondaryDate);
      
      if (lagMs > config.maxReplicationLag) {
        results.outdatedObjects++;
        results.details.push({
          key,
          status: 'outdated',
          lag: lagMs / 1000, // Convert to seconds
          timestamp: new Date().toISOString()
        });
      } else {
        results.replicatedObjects++;
        results.replicationLag = Math.max(results.replicationLag, lagMs);
      }
    }
  }
  
  // Convert lag to seconds
  results.replicationLag = results.replicationLag / 1000;
  
  return results;
}

/**
 * Run cross-region replication for all bucket types
 * @returns {Object} Replication results
 */
async function runReplication() {
  const log = await loadReplicationLog();
  const startTime = new Date();
  
  logger.info(`Starting cross-region replication at ${startTime.toISOString();}`);
  
  const replicationResults = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    buckets: {}
  };
  
  // Replicate each bucket type
  for (const bucketType of Object.keys(config.buckets)) {
    try {
      replicationResults.buckets[bucketType] = await replicateBucket(bucketType);
    } catch (error) {
      console.error(`Error replicating ${bucketType} backups:`, error);
      replicationResults.buckets[bucketType] = {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  const endTime = new Date();
  const durationMs = endTime - startTime;
  
  replicationResults.endTime = endTime.toISOString();
  replicationResults.duration = durationMs / 1000; // Convert to seconds
  
  // Update log
  log.lastRun = endTime.toISOString();
  log.replications.push(replicationResults);
  
  // Keep only the last 100 replications
  if (log.replications.length > 100) {
    log.replications = log.replications.slice(-100);
  }
  
  await saveReplicationLog(log);
  
  logger.info(`Completed cross-region replication in ${replicationResults.duration} seconds`);
  
  return replicationResults;
}

/**
 * Verify cross-region replication for all bucket types
 * @returns {Object} Verification results
 */
async function verifyAllReplications() {
  const startTime = new Date();
  
  logger.info(`Starting replication verification at ${startTime.toISOString();}`);
  
  const verificationResults = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    buckets: {}
  };
  
  // Verify each bucket type
  for (const bucketType of Object.keys(config.buckets)) {
    try {
      verificationResults.buckets[bucketType] = await verifyReplication(bucketType);
    } catch (error) {
      console.error(`Error verifying ${bucketType} backup replication:`, error);
      verificationResults.buckets[bucketType] = {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  const endTime = new Date();
  const durationMs = endTime - startTime;
  
  verificationResults.endTime = endTime.toISOString();
  verificationResults.duration = durationMs / 1000; // Convert to seconds
  
  logger.info(`Completed replication verification in ${verificationResults.duration} seconds`);
  
  return verificationResults;
}

// If script is run directly, run replication
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'replicate';
  
  if (command === 'replicate') {
    runReplication()
      .then(results => {
        logger.info('Replication completed:');
        logger.info(JSON.stringify(results, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  } else if (command === 'verify') {
    verifyAllReplications()
      .then(results => {
        logger.info('Verification completed:');
        logger.info(JSON.stringify(results, null, 2););
      })
      .catch(error => {
        console.error('Error:', error);
        process.exit(1);
      });
  } else {
    console.error(`Unknown command: ${command}`);
    console.error('Usage: node cross-region-replication.js [replicate|verify]');
    process.exit(1);
  }
}

module.exports = {
  runReplication,
  verifyAllReplications,
  replicateBucket,
  verifyReplication
};
