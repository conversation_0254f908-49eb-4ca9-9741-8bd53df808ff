/**
 * Tests for response sanitization middleware
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import responseSanitization from '../../middleware/response-sanitization.js';
import process from 'node:process';

describe('Response Sanitization Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      user: { id: 'test-user', role: 'user' },
      method: 'GET',
      path: '/api/test',
      headers: {},
    };

    res = {
      json: vi.fn().mockReturnThis(),
    };

    next = vi.fn();
  });

  test('should sanitize sensitive fields in response', () => {
    // Setup middleware
    const middleware = responseSanitization({ logSanitization: false });
    middleware(req, res, next);

    // Verify next was called
    expect(next).toHaveBeenCalled();

    // Create test data with sensitive fields
    const testData = {
      id: 123,
      username: 'testuser',
      password: 'secret123',
      email: '<EMAIL>',
      creditCard: '4111-1111-1111-1111',
      address: {
        street: '123 Main St',
        city: 'Anytown',
        zip: '12345',
      },
      apiKey: 'sk_test_abcdefghijklmnopqrstuvwxyz',
      settings: {
        privateKey: 'private-key-value',
        publicKey: 'public-key-value',
      },
    };

    // Call the overridden json method
    res.json(testData);

    // Verify json was called with sanitized data
    expect(res.json).toHaveBeenCalled();

    // Get the sanitized data
    const sanitizedData = res.json.mock.calls[0][0];

    // Verify sensitive fields are redacted
    expect(sanitizedData.id).toBe(123);
    expect(sanitizedData.username).toBe('testuser');
    expect(sanitizedData.password).toBe('[REDACTED]');
    expect(sanitizedData.email).toBe('[REDACTED]');
    expect(sanitizedData.creditCard).toBe('[REDACTED]');
    expect(sanitizedData.address.street).toBe('[REDACTED]');
    expect(sanitizedData.address.city).toBe('Anytown');
    expect(sanitizedData.address.zip).toBe('12345');
    expect(sanitizedData.apiKey).toBe('[REDACTED]');
    expect(sanitizedData.settings.privateKey).toBe('[REDACTED]');
    expect(sanitizedData.settings.publicKey).toBe('public-key-value');
  });

  test('should bypass sanitization for admin users', () => {
    // Setup admin user
    req.user.role = 'admin';

    // Setup middleware
    const middleware = responseSanitization({ logSanitization: false, allowBypass: true });
    middleware(req, res, next);

    // Create test data with sensitive fields
    const testData = {
      id: 123,
      username: 'testuser',
      password: 'secret123',
      email: '<EMAIL>',
    };

    // Call the overridden json method
    res.json(testData);

    // Verify json was called with original data
    expect(res.json).toHaveBeenCalledWith(testData);
  });

  test('should bypass sanitization with correct bypass header', () => {
    // Setup bypass header
    process.env.SANITIZATION_BYPASS_SECRET = 'test-secret';
    req.headers['x-sanitization-bypass'] = 'test-secret';

    // Setup middleware
    const middleware = responseSanitization({ logSanitization: false, allowBypass: true });
    middleware(req, res, next);

    // Create test data with sensitive fields
    const testData = {
      id: 123,
      username: 'testuser',
      password: 'secret123',
    };

    // Call the overridden json method
    res.json(testData);

    // Verify json was called with original data
    expect(res.json).toHaveBeenCalledWith(testData);

    // Clean up
    delete process.env.SANITIZATION_BYPASS_SECRET;
  });

  test('should not sanitize error responses', () => {
    // Setup middleware
    const middleware = responseSanitization({ logSanitization: false });
    middleware(req, res, next);

    // Create error response
    const errorData = {
      error: 'Something went wrong',
      details: {
        password: 'Invalid password',
        token: 'Invalid token',
      },
    };

    // Call the overridden json method
    res.json(errorData);

    // Verify json was called with original error data
    expect(res.json).toHaveBeenCalledWith(errorData);
  });

  test('should detect and sanitize sensitive patterns in string content', () => {
    // Setup middleware
    const middleware = responseSanitization({
      logSanitization: false,
      maskingChar: '[REDACTED]',
    });
    middleware(req, res, next);

    // Create test data with sensitive content in strings
    const testData = {
      message: 'Your credit card 4111-1111-1111-1111 has been charged',
      details: 'Please contact <NAME_EMAIL> or call 555-123-4567',
      note: 'SSN: *********** is required for verification',
    };

    // Call the overridden json method
    res.json(testData);

    // Get the sanitized data
    const sanitizedData = res.json.mock.calls[0][0];

    // Verify sensitive patterns are redacted
    expect(sanitizedData.message).toContain('[REDACTED]');
    expect(sanitizedData.message).not.toContain('4111-1111-1111-1111');
    expect(sanitizedData.details).toContain('[REDACTED]');
    expect(sanitizedData.details).not.toContain('<EMAIL>');
    expect(sanitizedData.details).not.toContain('555-123-4567');
    expect(sanitizedData.note).toContain('[REDACTED]');
    expect(sanitizedData.note).not.toContain('***********');
  });
});
