/**
 * Migration to create telemetry tables
 */
module.exports = {
  async up(knex) {
    // Create telemetry table
    await knex.schema.createTable('telemetry', (table) => {
      table.increments('id').primary();
      table.string('component_name').notNullable().index();
      table.string('session_id').notNullable().index();
      table.integer('session_duration').notNullable();
      table.timestamp('timestamp').notNullable().defaultTo(knex.fn.now()).index();
      table.uuid('user_id').notNullable().index();
      table.string('ip_address');
      table.string('user_agent');
      table.float('average_render_time').notNullable();
      table.float('average_api_load_time').notNullable();
      table.float('average_cache_load_time').notNullable();
      table.float('cache_hit_ratio').notNullable();
      table.float('average_memory_usage').notNullable();
      table.float('average_memory_usage_ratio').notNullable();
      table.integer('total_errors').notNullable().defaultTo(0);
      table.float('performance_improvement').notNullable();
    });
    
    // Create telemetry_errors table
    await knex.schema.createTable('telemetry_errors', (table) => {
      table.increments('id').primary();
      table.string('session_id').notNullable().index();
      table.string('component_name').notNullable().index();
      table.uuid('user_id').notNullable().index();
      table.string('message').notNullable();
      table.text('details');
      table.timestamp('timestamp').notNullable().index();
    });
    
    // Create Directus collections
    await knex('directus_collections').insert([
      {
        collection: 'telemetry',
        icon: 'analytics',
        note: 'Performance telemetry data',
        display_template: '{{component_name}} - {{session_id}}',
        hidden: false,
        singleton: false,
        translations: JSON.stringify([
          { language: 'en-US', translation: 'Performance Telemetry' }
        ]),
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: 'all',
        color: '#2ECDA7',
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: 'open'
      },
      {
        collection: 'telemetry_errors',
        icon: 'bug_report',
        note: 'Telemetry error logs',
        display_template: '{{component_name}} - {{message}}',
        hidden: false,
        singleton: false,
        translations: JSON.stringify([
          { language: 'en-US', translation: 'Telemetry Errors' }
        ]),
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: 'all',
        color: '#F44336',
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: 'open'
      }
    ]);
    
    // Create Directus fields for telemetry
    const telemetryFields = [
      { field: 'id', type: 'integer', meta: { hidden: true, interface: 'input', readonly: true } },
      { field: 'component_name', type: 'string', meta: { interface: 'input', options: { placeholder: 'Component Name' } } },
      { field: 'session_id', type: 'string', meta: { interface: 'input', options: { placeholder: 'Session ID' } } },
      { field: 'session_duration', type: 'integer', meta: { interface: 'input', options: { placeholder: 'Session Duration (ms)' } } },
      { field: 'timestamp', type: 'timestamp', meta: { interface: 'datetime', options: { includeSeconds: true } } },
      { field: 'user_id', type: 'uuid', meta: { interface: 'select-dropdown-m2o', options: { template: '{{first_name}} {{last_name}}' } } },
      { field: 'ip_address', type: 'string', meta: { interface: 'input', options: { placeholder: 'IP Address' } } },
      { field: 'user_agent', type: 'string', meta: { interface: 'input', options: { placeholder: 'User Agent' } } },
      { field: 'average_render_time', type: 'float', meta: { interface: 'input', options: { placeholder: 'Average Render Time (ms)' } } },
      { field: 'average_api_load_time', type: 'float', meta: { interface: 'input', options: { placeholder: 'Average API Load Time (ms)' } } },
      { field: 'average_cache_load_time', type: 'float', meta: { interface: 'input', options: { placeholder: 'Average Cache Load Time (ms)' } } },
      { field: 'cache_hit_ratio', type: 'float', meta: { interface: 'input', options: { placeholder: 'Cache Hit Ratio (0-1)' } } },
      { field: 'average_memory_usage', type: 'float', meta: { interface: 'input', options: { placeholder: 'Average Memory Usage (bytes)' } } },
      { field: 'average_memory_usage_ratio', type: 'float', meta: { interface: 'input', options: { placeholder: 'Average Memory Usage Ratio (0-1)' } } },
      { field: 'total_errors', type: 'integer', meta: { interface: 'input', options: { placeholder: 'Total Errors' } } },
      { field: 'performance_improvement', type: 'float', meta: { interface: 'input', options: { placeholder: 'Performance Improvement Factor' } } }
    ];
    
    // Create Directus fields for telemetry_errors
    const telemetryErrorFields = [
      { field: 'id', type: 'integer', meta: { hidden: true, interface: 'input', readonly: true } },
      { field: 'session_id', type: 'string', meta: { interface: 'input', options: { placeholder: 'Session ID' } } },
      { field: 'component_name', type: 'string', meta: { interface: 'input', options: { placeholder: 'Component Name' } } },
      { field: 'user_id', type: 'uuid', meta: { interface: 'select-dropdown-m2o', options: { template: '{{first_name}} {{last_name}}' } } },
      { field: 'message', type: 'string', meta: { interface: 'input', options: { placeholder: 'Error Message' } } },
      { field: 'details', type: 'text', meta: { interface: 'input-multiline', options: { placeholder: 'Error Details' } } },
      { field: 'timestamp', type: 'timestamp', meta: { interface: 'datetime', options: { includeSeconds: true } } }
    ];
    
    // Insert fields into directus_fields
    for (const field of telemetryFields) {
      await knex('directus_fields').insert({
        collection: 'telemetry',
        field: field.field,
        type: field.type,
        meta: field.meta ? JSON.stringify(field.meta) : null,
        schema: JSON.stringify({
          name: field.field,
          table: 'telemetry',
          data_type: field.type,
          default_value: null,
          max_length: null,
          is_nullable: field.field === 'id' ? false : true,
          is_primary_key: field.field === 'id' ? true : false,
          has_auto_increment: field.field === 'id' ? true : false,
          foreign_key_column: null,
          foreign_key_table: null
        })
      });
    }
    
    for (const field of telemetryErrorFields) {
      await knex('directus_fields').insert({
        collection: 'telemetry_errors',
        field: field.field,
        type: field.type,
        meta: field.meta ? JSON.stringify(field.meta) : null,
        schema: JSON.stringify({
          name: field.field,
          table: 'telemetry_errors',
          data_type: field.type,
          default_value: null,
          max_length: null,
          is_nullable: field.field === 'id' ? false : true,
          is_primary_key: field.field === 'id' ? true : false,
          has_auto_increment: field.field === 'id' ? true : false,
          foreign_key_column: null,
          foreign_key_table: null
        })
      });
    }
  },
  
  async down(knex) {
    // Remove Directus fields
    await knex('directus_fields').where('collection', 'telemetry').delete();
    await knex('directus_fields').where('collection', 'telemetry_errors').delete();
    
    // Remove Directus collections
    await knex('directus_collections').where('collection', 'telemetry').delete();
    await knex('directus_collections').where('collection', 'telemetry_errors').delete();
    
    // Drop tables
    await knex.schema.dropTableIfExists('telemetry_errors');
    await knex.schema.dropTableIfExists('telemetry');
  }
};
