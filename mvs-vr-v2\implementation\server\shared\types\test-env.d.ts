/// <reference types="npm:vitest/globals" />
/// <reference types="npm:@testing-library/jest-dom" />

// DOM Types
interface Window {
  IS_VITEST: boolean;
  getComputedStyle(element: Element): CSSStyleDeclaration;
}

interface Document {
  body: Element;
  querySelector(selectors: string): Element | null;
  querySelectorAll<E extends Element = Element>(selectors: string): NodeListOf<E>;
}

interface Element {
  hasAttribute(name: string): boolean;
  getAttribute(name: string): string | null;
  hasChildNodes(): boolean;
}

// Vitest Types
declare module 'npm:vitest' {
  export interface SpyInstance<T extends (...args: any[]) => any> {
    getMockName(): string;
    mock: {
      calls: Parameters<T>[];
      results: Array<{ type: string; value: ReturnType<T> }>;
      instances: any[];
      contexts: any[];
      lastCall: Parameters<T>;
    };
    mockClear(): this;
    mockReset(): this;
    mockRestore(): void;
    mockImplementation(fn: T): this;
    mockImplementationOnce(fn: T): this;
    mockName(name: string): this;
    mockReturnThis(): this;
    mockReturnValue(value: ReturnType<T>): this;
    mockReturnValueOnce(value: ReturnType<T>): this;
    mockResolvedValue<U extends ReturnType<T>>(value: Awaited<U>): this;
    mockResolvedValueOnce<U extends ReturnType<T>>(value: Awaited<U>): this;
    mockRejectedValue(value: unknown): this;
    mockRejectedValueOnce(value: unknown): this;
  }
}

// Test Environment Types
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'test' | 'production';
    VITEST_SEGFAULT_RETRY: string;
    VITEST_MAX_THREADS: string;
    VITEST_MIN_THREADS: string;
    VITEST_POOL_OPTIONS: string;
    TEST_MODE: 'unit' | 'integration' | 'e2e';
    TEST_LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
    TEST_TIMEOUT: string;
    DEBUG: string;
  }
}

// Custom Test Types
declare namespace TestUtils {
  interface ValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
  }

  interface MockService {
    initialize(): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
    getConfig(): {
      name: string;
      version: string;
      dependencies?: string[];
    };
    getStatus(): string;
    getDependencies(): string[];
  }

  interface CustomMatchers<R = unknown> {
    toBeInTheDocument(): R;
    toBeVisible(): R;
    toBeEmpty(): R;
    toHaveAttribute(attr: string, value?: string): R;
    toHaveStyle(style: Record<string, any>): R;
    toHaveClass(...classNames: string[]): R;
  }
}

// Extend global types
declare global {
  var IS_VITEST: boolean;
  var document: Document;
  var window: Window;

  namespace Vi {
    interface Assertion extends TestUtils.CustomMatchers {}
  }
}
