import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';

// Validation schema for login request
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

/**
 * Vendor login endpoint
 *
 * This endpoint handles vendor authentication via email and password.
 * It validates the credentials, checks if the user has vendor role,
 * and returns a JWT token if authentication is successful.
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validationResult = loginSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors,
      });
    }

    const { email, password } = validationResult.data;

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Attempt to sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      logger.error('Vendor login failed', { email, error: error.message });
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    if (!data.user || !data.session) {
      logger.error('Vendor login failed - no user or session', { email });
      return res.status(401).json({ error: 'Authentication failed' });
    }

    // Check if user has vendor role
    const { data: vendorData, error: vendorError } = await supabase
      .from('vendors')
      .select('id, name, status')
      .eq('user_id', data.user.id)
      .single();

    if (vendorError || !vendorData) {
      logger.error('Vendor login failed - not a vendor', {
        email,
        userId: data.user.id,
        error: vendorError?.message,
      });

      // Sign out the user since they don't have vendor role
      await supabase.auth.signOut();

      return res.status(403).json({ error: 'User is not a vendor' });
    }

    // Check if vendor is active
    if (vendorData.status !== 'active') {
      logger.warn('Vendor login attempt with inactive account', {
        email,
        vendorId: vendorData.id,
        status: vendorData.status,
      });

      // Sign out the user since their account is not active
      await supabase.auth.signOut();

      return res.status(403).json({ error: 'Vendor account is not active' });
    }

    // Log successful login
    logger.info('Vendor login successful', {
      email,
      vendorId: vendorData.id,
      vendorName: vendorData.name,
    });

    // Return session and vendor data
    return res.status(200).json({
      session: data.session,
      vendor: {
        id: vendorData.id,
        name: vendorData.name,
      },
    });
  } catch (error) {
    logger.error('Unexpected error in vendor login', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
