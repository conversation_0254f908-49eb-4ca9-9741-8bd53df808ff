/**
 * Scene Validator Tests
 * 
 * This file contains tests for the scene validator service.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SceneValidatorService } from '../../services/scene/scene-validator';
import { BlueprintValidatorService } from '../../services/blueprint/blueprint-validator';

// Mock dependencies
vi.mock('../../shared/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

// Mock Supabase client
const mockSupabase = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  in: vi.fn().mockReturnThis(),
  single: vi.fn(),
};

// Mock BlueprintValidatorService
vi.mock('../../services/blueprint/blueprint-validator');
const MockBlueprintValidatorService = BlueprintValidatorService as unknown as ReturnType<typeof vi.fn>;

describe('SceneValidatorService', () => {
  let sceneValidator: SceneValidatorService;
  
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create instance
    sceneValidator = new SceneValidatorService(mockSupabase as any);
  });
  
  describe('validateSceneData', () => {
    it('should validate valid scene data', () => {
      // Arrange
      const validData = {
        objects: [],
        settings: {},
      };
      
      // Act
      const errors = sceneValidator.validateSceneData(validData);
      
      // Assert
      expect(errors).toHaveLength(0);
    });
    
    it('should return errors for invalid scene data', () => {
      // Arrange
      const invalidData = {
        // Missing objects and settings
      };
      
      // Act
      const errors = sceneValidator.validateSceneData(invalidData);
      
      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(e => e.code === 'MISSING_OBJECTS')).toBeTruthy();
      expect(errors.some(e => e.code === 'MISSING_SETTINGS')).toBeTruthy();
    });
    
    it('should validate objects array', () => {
      // Arrange
      const invalidData = {
        objects: 'not an array', // Invalid type
        settings: {},
      };
      
      // Act
      const errors = sceneValidator.validateSceneData(invalidData as any);
      
      // Assert
      expect(errors.some(e => e.code === 'INVALID_OBJECTS')).toBeTruthy();
    });
    
    it('should validate settings object', () => {
      // Arrange
      const invalidData = {
        objects: [],
        settings: 'not an object', // Invalid type
      };
      
      // Act
      const errors = sceneValidator.validateSceneData(invalidData as any);
      
      // Assert
      expect(errors.some(e => e.code === 'INVALID_SETTINGS')).toBeTruthy();
    });
  });
  
  describe('validateScene', () => {
    it('should validate a scene successfully', async () => {
      // Arrange
      const mockScene = {
        id: 'scene-123',
        name: 'Test Scene',
        data: {
          objects: [],
          settings: {},
        },
      };
      
      mockSupabase.single.mockResolvedValueOnce({
        data: mockScene,
        error: null,
      });
      
      // Mock blueprint instances validation
      const mockValidateBlueprint = vi.fn().mockResolvedValue({
        errors: [],
        warnings: [],
      });
      
      MockBlueprintValidatorService.prototype.validateBlueprintInstance = mockValidateBlueprint;
      
      // Act
      const result = await sceneValidator.validateScene('scene-123');
      
      // Assert
      expect(result.valid).toBeTruthy();
      expect(result.errors).toHaveLength(0);
      expect(mockSupabase.from).toHaveBeenCalledWith('scenes');
      expect(mockSupabase.select).toHaveBeenCalled();
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'scene-123');
    });
    
    it('should return errors for a scene with invalid data', async () => {
      // Arrange
      const mockScene = {
        id: 'scene-123',
        name: 'Test Scene',
        data: {
          // Missing objects and settings
        },
      };
      
      mockSupabase.single.mockResolvedValueOnce({
        data: mockScene,
        error: null,
      });
      
      // Act
      const result = await sceneValidator.validateScene('scene-123');
      
      // Assert
      expect(result.valid).toBeFalsy();
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.code === 'MISSING_OBJECTS')).toBeTruthy();
      expect(result.errors.some(e => e.code === 'MISSING_SETTINGS')).toBeTruthy();
    });
    
    it('should handle scene not found', async () => {
      // Arrange
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Scene not found' },
      });
      
      // Act
      const result = await sceneValidator.validateScene('non-existent-id');
      
      // Assert
      expect(result.valid).toBeFalsy();
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe('SCENE_NOT_FOUND');
    });
  });
});
