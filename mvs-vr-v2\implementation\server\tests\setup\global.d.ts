/// <reference types="npm:vitest/globals" />
/// <reference types="npm:@testing-library/jest-dom" />
/// <reference types="npm:vite/client" />

import type { SpyInstance as VitestSpy } from 'npm:vitest';

declare global {
  // Extend the Console interface
  interface Console {
    log: VitestSpy<typeof console.log>;
    error: VitestSpy<typeof console.error>;
    warn: VitestSpy<typeof console.warn>;
    info: VitestSpy<typeof console.info>;
    debug: VitestSpy<typeof console.debug>;
  }

  // Extend expect matchers
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toBeVisible(): R;
      toBeEmpty(): R;
      toHaveAttribute(attr: string, value?: string): R;
      toHaveStyle(style: Record<string, any>): R;
      toHaveClass(...classNames: string[]): R;
    }
  }

  // Define test environment
  const describe: typeof import('npm:vitest').describe;
  const it: typeof import('npm:vitest').it;
  const test: typeof import('npm:vitest').test;
  const expect: typeof import('npm:vitest').expect;
  const beforeAll: typeof import('npm:vitest').beforeAll;
  const afterAll: typeof import('npm:vitest').afterAll;
  const beforeEach: typeof import('npm:vitest').beforeEach;
  const afterEach: typeof import('npm:vitest').afterEach;
  const vi: typeof import('npm:vitest').vi;

  // Add Vitest specific interfaces
  interface VitestAssertion<T = any> {
    toBe(expected: T): void;
    toBeCloseTo(expected: number, precision?: number): void;
    toBeDefined(): void;
    toBeFalsy(): void;
    toBeGreaterThan(expected: number): void;
    toBeGreaterThanOrEqual(expected: number): void;
    toBeLessThan(expected: number): void;
    toBeLessThanOrEqual(expected: number): void;
    toBeInstanceOf(expected: any): void;
    toBeNull(): void;
    toBeTruthy(): void;
    toBeUndefined(): void;
    toContain(item: any): void;
    toEqual(expected: any): void;
    toHaveLength(length: number): void;
    toHaveProperty(keyPath: string | string[], value?: any): void;
    toMatch(pattern: string | RegExp): void;
    toMatchObject(object: Record<string, any>): void;
    toStrictEqual(expected: any): void;
    toThrow(error?: string | RegExp | Error): void;
    toThrowError(error?: string | RegExp | Error): void;
  }

  // Extend Window interface for testing
  interface Window {
    IS_VITEST: boolean;
  }
}
