/**
 * ML Alert Analyzer
 * 
 * This service uses machine learning to analyze alert patterns, reduce noise,
 * add context to alerts, and correlate related alerts.
 */

const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');
const { getAlertConfig, triggerAlert, resolveAlert } = require('./alert-manager');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json());

// Alert correlation window in milliseconds (15 minutes)
const CORRELATION_WINDOW_MS = 15 * 60 * 1000;

// Minimum confidence for ML predictions
const MIN_CONFIDENCE = 0.7;

// Maximum alerts to process in a batch
const MAX_BATCH_SIZE = 1000;

// Alert severity levels (imported from alert-manager)
const SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  CRITICAL: 'critical'
};

// Alert categories (imported from alert-manager)
const CATEGORY = {
  PERFORMANCE: 'performance',
  SECURITY: 'security',
  BUSINESS: 'business',
  SYSTEM: 'system'
};

/**
 * Get historical alerts
 * 
 * @param {number} days - Number of days of history to retrieve
 * @returns {Array} - Historical alerts
 */
async function getHistoricalAlerts(days = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('alerts')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });
      
    if (error) {
      logger.error('Error fetching historical alerts', { error: error.message });
      return [];
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error in getHistoricalAlerts', { error: error.message });
    return [];
  }
}

/**
 * Get alert patterns
 * 
 * @returns {Object} - Alert patterns
 */
async function getAlertPatterns() {
  try {
    // Get historical alerts
    const alerts = await getHistoricalAlerts();
    
    if (alerts.length === 0) {
      return {};
    }
    
    // Group alerts by alertId
    const alertGroups = {};
    
    alerts.forEach(alert => {
      if (!alertGroups[alert.alertId]) {
        alertGroups[alert.alertId] = [];
      }
      
      alertGroups[alert.alertId].push(alert);
    });
    
    // Calculate patterns for each alert type
    const patterns = {};
    
    Object.entries(alertGroups).forEach(([alertId, alertList]) => {
      // Skip if not enough data
      if (alertList.length < 10) {
        return;
      }
      
      // Calculate frequency
      const timeSpanMs = new Date(alertList[alertList.length - 1].created_at) - new Date(alertList[0].created_at);
      const frequency = alertList.length / (timeSpanMs / (24 * 60 * 60 * 1000)); // alerts per day
      
      // Calculate time of day distribution
      const hourDistribution = Array(24).fill(0);
      
      alertList.forEach(alert => {
        const hour = new Date(alert.created_at).getHours();
        hourDistribution[hour]++;
      });
      
      // Normalize hour distribution
      const totalAlerts = alertList.length;
      const normalizedHourDistribution = hourDistribution.map(count => count / totalAlerts);
      
      // Calculate day of week distribution
      const dayDistribution = Array(7).fill(0);
      
      alertList.forEach(alert => {
        const day = new Date(alert.created_at).getDay();
        dayDistribution[day]++;
      });
      
      // Normalize day distribution
      const normalizedDayDistribution = dayDistribution.map(count => count / totalAlerts);
      
      // Calculate resolution time statistics
      const resolutionTimes = alertList
        .filter(alert => alert.resolved_at)
        .map(alert => new Date(alert.resolved_at) - new Date(alert.created_at));
      
      let avgResolutionTime = 0;
      let medianResolutionTime = 0;
      
      if (resolutionTimes.length > 0) {
        avgResolutionTime = resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length;
        
        // Calculate median
        const sortedTimes = [...resolutionTimes].sort((a, b) => a - b);
        const mid = Math.floor(sortedTimes.length / 2);
        
        medianResolutionTime = sortedTimes.length % 2 === 0
          ? (sortedTimes[mid - 1] + sortedTimes[mid]) / 2
          : sortedTimes[mid];
      }
      
      // Store patterns
      patterns[alertId] = {
        frequency,
        hourDistribution: normalizedHourDistribution,
        dayDistribution: normalizedDayDistribution,
        avgResolutionTime,
        medianResolutionTime,
        totalAlerts: alertList.length,
        falsePositives: 0, // Will be calculated later
        actionability: 1.0 // Will be calculated later
      };
    });
    
    // Calculate false positives and actionability
    await calculateActionability(patterns, alertGroups);
    
    return patterns;
  } catch (error) {
    logger.error('Error in getAlertPatterns', { error: error.message });
    return {};
  }
}

/**
 * Calculate actionability scores for alert patterns
 * 
 * @param {Object} patterns - Alert patterns
 * @param {Object} alertGroups - Alert groups
 */
async function calculateActionability(patterns, alertGroups) {
  try {
    // Get alert actions
    const { data: actions, error } = await supabase
      .from('alert_actions')
      .select('*');
      
    if (error) {
      logger.error('Error fetching alert actions', { error: error.message });
      return;
    }
    
    // Group actions by alertId
    const actionGroups = {};
    
    (actions || []).forEach(action => {
      if (!actionGroups[action.alert_id]) {
        actionGroups[action.alert_id] = [];
      }
      
      actionGroups[action.alert_id].push(action);
    });
    
    // Calculate actionability for each alert type
    Object.entries(patterns).forEach(([alertId, pattern]) => {
      const alertList = alertGroups[alertId] || [];
      const actionList = actionList = [];
      
      // Count alerts with actions
      let alertsWithActions = 0;
      
      alertList.forEach(alert => {
        const alertActions = actionGroups[alert.id] || [];
        
        if (alertActions.length > 0) {
          alertsWithActions++;
        }
      });
      
      // Calculate actionability
      const actionability = alertList.length > 0 ? alertsWithActions / alertList.length : 0;
      
      // Calculate false positives (alerts with no actions and quick auto-resolution)
      let falsePositives = 0;
      
      alertList.forEach(alert => {
        const alertActions = actionGroups[alert.id] || [];
        
        if (alertActions.length === 0 && alert.resolved_at) {
          const resolutionTime = new Date(alert.resolved_at) - new Date(alert.created_at);
          
          // If resolved in less than 5 minutes with no actions, likely a false positive
          if (resolutionTime < 5 * 60 * 1000) {
            falsePositives++;
          }
        }
      });
      
      // Update pattern
      pattern.actionability = actionability;
      pattern.falsePositives = falsePositives;
    });
  } catch (error) {
    logger.error('Error in calculateActionability', { error: error.message });
  }
}

/**
 * Predict alert importance
 * 
 * @param {Object} alert - Alert object
 * @param {Object} patterns - Alert patterns
 * @returns {Object} - Prediction
 */
function predictAlertImportance(alert, patterns) {
  try {
    const pattern = patterns[alert.alertId];
    
    if (!pattern) {
      // No pattern data, default to medium importance
      return {
        importance: 0.5,
        confidence: 0.3,
        reasons: ['No historical data for this alert type']
      };
    }
    
    // Calculate importance based on multiple factors
    let importance = 0.5; // Default to medium importance
    const reasons = [];
    
    // Factor 1: Actionability
    const actionabilityWeight = 0.4;
    importance += (pattern.actionability - 0.5) * actionabilityWeight;
    
    if (pattern.actionability > 0.7) {
      reasons.push(`High actionability (${Math.round(pattern.actionability * 100)}%)`);
    } else if (pattern.actionability < 0.3) {
      reasons.push(`Low actionability (${Math.round(pattern.actionability * 100)}%)`);
    }
    
    // Factor 2: False positive rate
    const falsePositiveRate = pattern.falsePositives / pattern.totalAlerts;
    const falsePositiveWeight = 0.3;
    importance -= falsePositiveRate * falsePositiveWeight;
    
    if (falsePositiveRate > 0.5) {
      reasons.push(`High false positive rate (${Math.round(falsePositiveRate * 100)}%)`);
    } else if (falsePositiveRate < 0.1) {
      reasons.push(`Low false positive rate (${Math.round(falsePositiveRate * 100)}%)`);
    }
    
    // Factor 3: Frequency
    const frequencyWeight = 0.2;
    
    if (pattern.frequency > 10) {
      // Very frequent alerts are less important
      importance -= 0.1 * frequencyWeight;
      reasons.push(`Very frequent alert (${Math.round(pattern.frequency)} per day)`);
    } else if (pattern.frequency < 1) {
      // Rare alerts are more important
      importance += 0.1 * frequencyWeight;
      reasons.push(`Rare alert (${Math.round(pattern.frequency * 100) / 100} per day)`);
    }
    
    // Factor 4: Time of day anomaly
    const hour = new Date(alert.created_at).getHours();
    const hourProbability = pattern.hourDistribution[hour];
    const timeAnomalyWeight = 0.1;
    
    if (hourProbability < 0.02) { // Less than 2% probability
      importance += 0.1 * timeAnomalyWeight;
      reasons.push(`Unusual time of day (${hour}:00)`);
    }
    
    // Clamp importance between 0 and 1
    importance = Math.max(0, Math.min(1, importance));
    
    // Calculate confidence based on amount of data
    let confidence = Math.min(1, pattern.totalAlerts / 100);
    
    return {
      importance,
      confidence,
      reasons
    };
  } catch (error) {
    logger.error('Error in predictAlertImportance', { error: error.message });
    
    return {
      importance: 0.5,
      confidence: 0.3,
      reasons: ['Error calculating importance']
    };
  }
}

/**
 * Find correlated alerts
 * 
 * @param {Object} alert - Alert object
 * @returns {Array} - Correlated alerts
 */
async function findCorrelatedAlerts(alert) {
  try {
    // Get recent alerts
    const startTime = new Date(alert.created_at);
    startTime.setTime(startTime.getTime() - CORRELATION_WINDOW_MS);
    
    const { data: recentAlerts, error } = await supabase
      .from('alerts')
      .select('*')
      .gte('created_at', startTime.toISOString())
      .lt('created_at', alert.created_at)
      .order('created_at', { ascending: false });
      
    if (error) {
      logger.error('Error fetching recent alerts', { error: error.message });
      return [];
    }
    
    if (!recentAlerts || recentAlerts.length === 0) {
      return [];
    }
    
    // Find correlated alerts
    const correlatedAlerts = [];
    
    // Correlation rules
    
    // Rule 1: Same component alerts
    if (alert.data && alert.data.component) {
      const sameComponentAlerts = recentAlerts.filter(a => 
        a.data && a.data.component === alert.data.component
      );
      
      sameComponentAlerts.forEach(a => {
        correlatedAlerts.push({
          alert: a,
          correlation: 0.8,
          reason: `Same component (${alert.data.component})`
        });
      });
    }
    
    // Rule 2: Same service alerts
    if (alert.data && alert.data.service) {
      const sameServiceAlerts = recentAlerts.filter(a => 
        a.data && a.data.service === alert.data.service && 
        (!a.data.component || a.data.component !== alert.data.component)
      );
      
      sameServiceAlerts.forEach(a => {
        correlatedAlerts.push({
          alert: a,
          correlation: 0.7,
          reason: `Same service (${alert.data.service})`
        });
      });
    }
    
    // Rule 3: Resource exhaustion cascade
    if (alert.alertId === 'highCpuUsage' || alert.alertId === 'highMemoryUsage' || alert.alertId === 'highDiskUsage') {
      const resourceAlerts = recentAlerts.filter(a => 
        a.alertId === 'highCpuUsage' || a.alertId === 'highMemoryUsage' || a.alertId === 'highDiskUsage'
      );
      
      resourceAlerts.forEach(a => {
        correlatedAlerts.push({
          alert: a,
          correlation: 0.9,
          reason: 'Resource exhaustion cascade'
        });
      });
    }
    
    // Rule 4: Network issues cascade
    if (alert.alertId === 'networkLatency' || alert.alertId === 'networkErrors') {
      const networkAlerts = recentAlerts.filter(a => 
        a.alertId === 'apiLatency' || a.alertId === 'apiErrors' || a.alertId === 'databaseLatency'
      );
      
      networkAlerts.forEach(a => {
        correlatedAlerts.push({
          alert: a,
          correlation: 0.8,
          reason: 'Network issues cascade'
        });
      });
    }
    
    // Rule 5: Database issues cascade
    if (alert.alertId === 'databaseLatency' || alert.alertId === 'databaseErrors') {
      const dbAlerts = recentAlerts.filter(a => 
        a.alertId === 'apiLatency' || a.alertId === 'apiErrors'
      );
      
      dbAlerts.forEach(a => {
        correlatedAlerts.push({
          alert: a,
          correlation: 0.8,
          reason: 'Database issues cascade'
        });
      });
    }
    
    // Remove duplicates
    const uniqueAlerts = [];
    const alertIds = new Set();
    
    correlatedAlerts.forEach(ca => {
      if (!alertIds.has(ca.alert.id)) {
        uniqueAlerts.push(ca);
        alertIds.add(ca.alert.id);
      }
    });
    
    // Sort by correlation
    uniqueAlerts.sort((a, b) => b.correlation - a.correlation);
    
    return uniqueAlerts;
  } catch (error) {
    logger.error('Error in findCorrelatedAlerts', { error: error.message });
    return [];
  }
}

/**
 * Add context to alert
 * 
 * @param {Object} alert - Alert object
 * @returns {Object} - Alert with context
 */
async function addAlertContext(alert) {
  try {
    // Get alert patterns
    const patterns = await getAlertPatterns();
    
    // Predict importance
    const prediction = predictAlertImportance(alert, patterns);
    
    // Find correlated alerts
    const correlatedAlerts = await findCorrelatedAlerts(alert);
    
    // Get system state
    const systemState = await getSystemState(alert);
    
    // Add context to alert
    const alertWithContext = {
      ...alert,
      context: {
        importance: prediction.importance,
        confidence: prediction.confidence,
        reasons: prediction.reasons,
        correlatedAlerts,
        systemState
      }
    };
    
    return alertWithContext;
  } catch (error) {
    logger.error('Error in addAlertContext', { error: error.message });
    return alert;
  }
}

/**
 * Get system state
 * 
 * @param {Object} alert - Alert object
 * @returns {Object} - System state
 */
async function getSystemState(alert) {
  try {
    // Get system metrics
    const { data: metrics, error } = await supabase
      .from('system_metrics')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(1);
      
    if (error) {
      logger.error('Error fetching system metrics', { error: error.message });
      return {};
    }
    
    if (!metrics || metrics.length === 0) {
      return {};
    }
    
    // Get active user count
    const { data: userCount, error: userError } = await supabase
      .from('active_users')
      .select('count')
      .order('timestamp', { ascending: false })
      .limit(1);
      
    if (userError) {
      logger.error('Error fetching active user count', { error: userError.message });
    }
    
    // Get deployment status
    const { data: deployments, error: deploymentError } = await supabase
      .from('deployments')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(1);
      
    if (deploymentError) {
      logger.error('Error fetching deployment status', { error: deploymentError.message });
    }
    
    // Get maintenance status
    const { data: maintenance, error: maintenanceError } = await supabase
      .from('maintenance')
      .select('*')
      .eq('active', true)
      .limit(1);
      
    if (maintenanceError) {
      logger.error('Error fetching maintenance status', { error: maintenanceError.message });
    }
    
    // Return system state
    return {
      metrics: metrics[0],
      activeUsers: userCount && userCount.length > 0 ? userCount[0].count : null,
      recentDeployment: deployments && deployments.length > 0 ? deployments[0] : null,
      maintenanceMode: maintenance && maintenance.length > 0
    };
  } catch (error) {
    logger.error('Error in getSystemState', { error: error.message });
    return {};
  }
}

/**
 * Process alerts
 */
async function processAlerts() {
  try {
    // Get unprocessed alerts
    const { data: alerts, error } = await supabase
      .from('alerts')
      .select('*')
      .is('ml_processed', null)
      .order('created_at', { ascending: true })
      .limit(MAX_BATCH_SIZE);
      
    if (error) {
      logger.error('Error fetching unprocessed alerts', { error: error.message });
      return;
    }
    
    if (!alerts || alerts.length === 0) {
      return;
    }
    
    logger.info(`Processing ${alerts.length} alerts`);
    
    // Process each alert
    for (const alert of alerts) {
      // Add context to alert
      const alertWithContext = await addAlertContext(alert);
      
      // Update alert in database
      const { error: updateError } = await supabase
        .from('alerts')
        .update({
          ml_processed: true,
          ml_importance: alertWithContext.context.importance,
          ml_confidence: alertWithContext.context.confidence,
          ml_reasons: alertWithContext.context.reasons,
          ml_correlated_alerts: alertWithContext.context.correlatedAlerts.map(ca => ca.alert.id),
          ml_system_state: alertWithContext.context.systemState,
          updated_at: new Date().toISOString()
        })
        .eq('id', alert.id);
        
      if (updateError) {
        logger.error('Error updating alert', { error: updateError.message, alertId: alert.id });
      }
    }
    
    logger.info(`Processed ${alerts.length} alerts`);
  } catch (error) {
    logger.error('Error in processAlerts', { error: error.message });
  }
}

// API endpoints
app.get('/api/alert-patterns', async (req, res) => {
  try {
    const patterns = await getAlertPatterns();
    res.json({ patterns });
  } catch (error) {
    logger.error('Error in GET /api/alert-patterns', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/alert-context/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get alert
    const { data: alerts, error } = await supabase
      .from('alerts')
      .select('*')
      .eq('id', id)
      .limit(1);
      
    if (error) {
      logger.error('Error fetching alert', { error: error.message });
      return res.status(500).json({ error: 'Error fetching alert' });
    }
    
    if (!alerts || alerts.length === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }
    
    // Add context to alert
    const alertWithContext = await addAlertContext(alerts[0]);
    
    res.json({ alert: alertWithContext });
  } catch (error) {
    logger.error('Error in GET /api/alert-context/:id', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/process-alerts', async (req, res) => {
  try {
    await processAlerts();
    res.json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/process-alerts', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
const PORT = process.env.ML_ALERT_ANALYZER_PORT || 9102;
app.listen(PORT, () => {
  logger.info(`ML Alert Analyzer listening on port ${PORT}`);
  
  // Process alerts periodically
  setInterval(processAlerts, 60000); // Every minute
});

module.exports = {
  addAlertContext,
  findCorrelatedAlerts,
  getAlertPatterns,
  predictAlertImportance,
  processAlerts
};
