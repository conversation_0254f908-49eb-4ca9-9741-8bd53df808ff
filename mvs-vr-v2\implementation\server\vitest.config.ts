import { defineConfig } from 'npm:vitest/config';
import { resolve } from 'node:path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/vitest.setup.ts'],
    include: ['**/*.{test,spec,vitest}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.deno', 'coverage'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['src/**/*', 'services/**/*', 'shared/**/*'],
      exclude: [
        'node_modules/**',
        'dist/**',
        '.deno/**',
        'coverage/**',
        '**/*.d.ts',
        '**/*.test.*',
        '**/*.spec.*',
        '**/*.config.*',
      ],
    },
    browser: {
      enabled: false,
      headless: true,
      name: 'chrome',
    },
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@directus': resolve(__dirname, './directus/extensions'),
      '@shared': resolve(__dirname, './shared'),
      '@services': resolve(__dirname, './services'),
      '@tests': resolve(__dirname, './tests'),
      '@setup': resolve(__dirname, './tests/setup'),
    },
  },
  deps: {
    interopDefault: true,
  },
  esbuild: {
    target: 'node18',
  },
});
