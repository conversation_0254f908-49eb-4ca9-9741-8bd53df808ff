/**
 * Tests for business continuity service
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const {
  BusinessContinuityService,
  IMPACT_LEVELS,
  SERVICE_STATUS
} = require('../../services/continuity/business-continuity');

// Mock fs functions
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  existsSync: jest.fn(),
  readFile: jest.fn(),
  writeFile: jest.fn(),
  mkdir: jest.fn()
}));

// Promisify mocked functions
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);

describe('Business Continuity Service', () => {
  let service;
  let mockConfig;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock existsSync to return true
    fs.existsSync.mockReturnValue(true);
    
    // Create mock configuration
    mockConfig = {
      services: [
        {
          id: 'api-gateway',
          name: 'API Gateway',
          priority: 5,
          businessImpact: {
            degraded: 3,
            outage: 5,
            maintenance: 2
          },
          dependencies: []
        },
        {
          id: 'authentication',
          name: 'Authentication Service',
          priority: 4,
          businessImpact: {
            degraded: 3,
            outage: 4,
            maintenance: 2
          },
          dependencies: ['api-gateway']
        },
        {
          id: 'storage',
          name: 'Storage Service',
          priority: 3,
          businessImpact: {
            degraded: 2,
            outage: 3,
            maintenance: 1
          },
          dependencies: ['api-gateway']
        }
      ]
    };
    
    // Mock readFile to return mock configuration
    readFileAsync.mockResolvedValue(JSON.stringify(mockConfig));
    
    // Mock mkdir to succeed
    mkdirAsync.mockResolvedValue(undefined);
    
    // Mock writeFile to succeed
    writeFileAsync.mockResolvedValue(undefined);
    
    // Create service with test options
    service = new BusinessContinuityService({
      configPath: '/test/config.json',
      reportPath: '/test/reports',
      checkIntervalMs: 1000
    });
    
    // Mock checkServiceStatus to return predictable results
    service.checkServiceStatus = jest.fn().mockImplementation((service) => {
      if (service.id === 'api-gateway') {
        return Promise.resolve(SERVICE_STATUS.OPERATIONAL);
      } else if (service.id === 'authentication') {
        return Promise.resolve(SERVICE_STATUS.DEGRADED);
      } else {
        return Promise.resolve(SERVICE_STATUS.OUTAGE);
      }
    });
  });
  
  afterEach(() => {
    // Stop service
    service.stop();
  });
  
  test('should initialize correctly', async () => {
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify config was loaded
    expect(fs.existsSync).toHaveBeenCalledWith('/test/config.json');
    expect(readFileAsync).toHaveBeenCalledWith('/test/config.json', 'utf8');
    
    // Verify directory was created
    expect(mkdirAsync).toHaveBeenCalledWith('/test/reports', { recursive: true });
    
    // Verify services were loaded
    expect(service.services).toHaveLength(3);
    expect(service.services[0].id).toBe('api-gateway');
    expect(service.services[1].id).toBe('authentication');
    expect(service.services[2].id).toBe('storage');
  });
  
  test('should build dependency map correctly', async () => {
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify dependency map
    expect(service.dependencies.get('authentication')).toEqual(['api-gateway']);
    expect(service.dependencies.get('storage')).toEqual(['api-gateway']);
    
    // Verify reverse dependencies
    expect(service.dependencies.get('reverse:api-gateway')).toEqual(['authentication', 'storage']);
  });
  
  test('should check service health', async () => {
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Check service health
    await service.checkServiceHealth();
    
    // Verify service status was updated
    expect(service.getServiceStatus('api-gateway')).toBe(SERVICE_STATUS.OPERATIONAL);
    expect(service.getServiceStatus('authentication')).toBe(SERVICE_STATUS.DEGRADED);
    expect(service.getServiceStatus('storage')).toBe(SERVICE_STATUS.OUTAGE);
    
    // Verify status report was generated
    expect(writeFileAsync).toHaveBeenCalled();
    const writeCall = writeFileAsync.mock.calls[0];
    expect(writeCall[0]).toMatch(/\/test\/reports\/status-report-.*\.json/);
    
    // Parse report data
    const reportData = JSON.parse(writeCall[1]);
    
    // Verify report contains status changes
    expect(reportData.statusChanges).toHaveLength(3);
    expect(reportData.statusChanges[0].serviceId).toBe('api-gateway');
    expect(reportData.statusChanges[0].newStatus).toBe(SERVICE_STATUS.OPERATIONAL);
    expect(reportData.statusChanges[1].serviceId).toBe('authentication');
    expect(reportData.statusChanges[1].newStatus).toBe(SERVICE_STATUS.DEGRADED);
    expect(reportData.statusChanges[2].serviceId).toBe('storage');
    expect(reportData.statusChanges[2].newStatus).toBe(SERVICE_STATUS.OUTAGE);
  });
  
  test('should calculate business impact correctly', () => {
    const apiGateway = mockConfig.services[0];
    const authentication = mockConfig.services[1];
    
    // Calculate impact for operational status
    const operationalImpact = service.calculateBusinessImpact(apiGateway, SERVICE_STATUS.OPERATIONAL);
    expect(operationalImpact.level).toBe(IMPACT_LEVELS.MINIMAL);
    expect(operationalImpact.score).toBe(IMPACT_LEVELS.MINIMAL * apiGateway.priority);
    expect(operationalImpact.severity).toBe('info');
    
    // Calculate impact for degraded status
    const degradedImpact = service.calculateBusinessImpact(apiGateway, SERVICE_STATUS.DEGRADED);
    expect(degradedImpact.level).toBe(apiGateway.businessImpact.degraded);
    expect(degradedImpact.score).toBe(apiGateway.businessImpact.degraded * apiGateway.priority);
    expect(degradedImpact.severity).toBe('warning');
    
    // Calculate impact for outage status
    const outageImpact = service.calculateBusinessImpact(authentication, SERVICE_STATUS.OUTAGE);
    expect(outageImpact.level).toBe(authentication.businessImpact.outage);
    expect(outageImpact.score).toBe(authentication.businessImpact.outage * authentication.priority);
    expect(outageImpact.severity).toBe('error');
  });
  
  test('should update business metrics', () => {
    // Update business metrics
    service.updateBusinessMetric('active-users', 1000, { region: 'us-east' });
    service.updateBusinessMetric('session-duration', 300, { region: 'us-east' });
    
    // Get business metrics
    const metrics = service.getBusinessMetrics();
    
    // Verify metrics
    expect(metrics['active-users:{"region":"us-east"}']).toBeDefined();
    expect(metrics['active-users:{"region":"us-east"}'].value).toBe(1000);
    expect(metrics['session-duration:{"region":"us-east"}']).toBeDefined();
    expect(metrics['session-duration:{"region":"us-east"}'].value).toBe(300);
  });
  
  test('should emit events on service status change', async () => {
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Set up event listener
    const statusChangeHandler = jest.fn();
    service.on('serviceStatusChange', statusChangeHandler);
    
    // Check service health
    await service.checkServiceHealth();
    
    // Verify event was emitted
    expect(statusChangeHandler).toHaveBeenCalledTimes(3);
    
    // Verify event data
    const apiGatewayEvent = statusChangeHandler.mock.calls.find(
      call => call[0].service.id === 'api-gateway'
    )[0];
    
    expect(apiGatewayEvent.previousStatus).toBe(SERVICE_STATUS.UNKNOWN);
    expect(apiGatewayEvent.newStatus).toBe(SERVICE_STATUS.OPERATIONAL);
    expect(apiGatewayEvent.impact.severity).toBe('info');
  });
});
