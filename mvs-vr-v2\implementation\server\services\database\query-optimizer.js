/**
 * Database Query Optimizer
 * 
 * This module provides tools for optimizing and monitoring database queries.
 */

const { Pool } = require('pg');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const { Logger } = require('../integration/logger');
const { Histogram, Counter } = require('prom-client');

// Create logger
const logger = new Logger();

// Create metrics
const queryDuration = new Histogram({
  name: 'db_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['query_name', 'query_type', 'status']
});

const queryCount = new Counter({
  name: 'db_query_count',
  help: 'Number of database queries',
  labelNames: ['query_name', 'query_type', 'status']
});

const queryRowCount = new Histogram({
  name: 'db_query_row_count',
  help: 'Number of rows returned by database queries',
  labelNames: ['query_name', 'query_type'],
  buckets: [0, 1, 10, 100, 1000, 10000, 100000]
});

const slowQueryCount = new Counter({
  name: 'db_slow_query_count',
  help: 'Number of slow database queries',
  labelNames: ['query_name', 'query_type']
});

/**
 * Configuration
 */
const config = {
  slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000', 10), // 1 second in ms
  logSlowQueries: process.env.LOG_SLOW_QUERIES !== 'false',
  logAllQueries: process.env.LOG_ALL_QUERIES === 'true',
  explainSlowQueries: process.env.EXPLAIN_SLOW_QUERIES !== 'false',
  queryLogPath: process.env.QUERY_LOG_PATH || path.join(__dirname, '../../../logs/queries.log'),
  slowQueryLogPath: process.env.SLOW_QUERY_LOG_PATH || path.join(__dirname, '../../../logs/slow-queries.log')
};

/**
 * Query optimizer class
 */
class QueryOptimizer {
  /**
   * Constructor
   * @param {Pool} pool - Database connection pool
   * @param {Object} options - Optimizer options
   */
  constructor(pool, options = {}) {
    this.pool = pool;
    this.options = {
      slowQueryThreshold: options.slowQueryThreshold || config.slowQueryThreshold,
      logSlowQueries: options.logSlowQueries !== undefined ? options.logSlowQueries : config.logSlowQueries,
      logAllQueries: options.logAllQueries !== undefined ? options.logAllQueries : config.logAllQueries,
      explainSlowQueries: options.explainSlowQueries !== undefined ? options.explainSlowQueries : config.explainSlowQueries,
      queryLogPath: options.queryLogPath || config.queryLogPath,
      slowQueryLogPath: options.slowQueryLogPath || config.slowQueryLogPath
    };

    // Create log directories if they don't exist
    const queryLogDir = path.dirname(this.options.queryLogPath);
    const slowQueryLogDir = path.dirname(this.options.slowQueryLogPath);

    if (!fs.existsSync(queryLogDir)) {
      fs.mkdirSync(queryLogDir, { recursive: true });
    }

    if (!fs.existsSync(slowQueryLogDir)) {
      fs.mkdirSync(slowQueryLogDir, { recursive: true });
    }

    // Create log streams
    this.queryLogStream = fs.createWriteStream(this.options.queryLogPath, { flags: 'a' });
    this.slowQueryLogStream = fs.createWriteStream(this.options.slowQueryLogPath, { flags: 'a' });

    logger.info('Query optimizer initialized', {
      slowQueryThreshold: this.options.slowQueryThreshold,
      logSlowQueries: this.options.logSlowQueries,
      logAllQueries: this.options.logAllQueries,
      explainSlowQueries: this.options.explainSlowQueries,
      queryLogPath: this.options.queryLogPath,
      slowQueryLogPath: this.options.slowQueryLogPath
    });
  }

  /**
   * Execute a query with optimization and monitoring
   * @param {string} text - Query text
   * @param {Array} params - Query parameters
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Query result
   */
  async query(text, params = [], options = {}) {
    const {
      name = 'unnamed',
      type = this.getQueryType(text),
      timeout = 30000,
      explain = false
    } = options;

    const startTime = process.hrtime();
    const startMs = Date.now();

    try {
      // Execute query
      const result = await this.pool.query({
        text,
        values: params,
        name: name !== 'unnamed' ? name : undefined,
        timeout
      });

      // Calculate duration
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const duration = seconds + nanoseconds / 1e9;
      const durationMs = Date.now() - startMs;

      // Track metrics
      queryDuration.observe(
        { query_name: name, query_type: type, status: 'success' },
        duration
      );
      queryCount.inc({ query_name: name, query_type: type, status: 'success' });
      queryRowCount.observe(
        { query_name: name, query_type: type },
        result.rowCount || 0
      );

      // Check if slow query
      const isSlowQuery = durationMs >= this.options.slowQueryThreshold;

      if (isSlowQuery) {
        slowQueryCount.inc({ query_name: name, query_type: type });

        // Log slow query
        if (this.options.logSlowQueries) {
          this.logSlowQuery(text, params, name, type, durationMs, result.rowCount);
        }

        // Explain slow query
        if (this.options.explainSlowQueries && !explain) {
          this.explainQuery(text, params, name, type);
        }
      }

      // Log all queries
      if (this.options.logAllQueries) {
        this.logQuery(text, params, name, type, durationMs, result.rowCount);
      }

      return result;
    } catch (error) {
      // Calculate duration
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const duration = seconds + nanoseconds / 1e9;
      const durationMs = Date.now() - startMs;

      // Track metrics
      queryDuration.observe(
        { query_name: name, query_type: type, status: 'error' },
        duration
      );
      queryCount.inc({ query_name: name, query_type: type, status: 'error' });

      // Log error
      logger.error(`Query error: ${error.message}`, {
        query: text,
        params,
        name,
        type,
        duration: durationMs,
        error
      });

      throw error;
    }
  }

  /**
   * Execute an EXPLAIN query
   * @param {string} text - Query text
   * @param {Array} params - Query parameters
   * @param {string} name - Query name
   * @param {string} type - Query type
   * @returns {Promise<Object>} Explain result
   */
  async explainQuery(text, params = [], name = 'unnamed', type = 'unknown') {
    try {
      // Skip EXPLAIN for non-SELECT queries in production
      if (process.env.NODE_ENV === 'production' && type !== 'SELECT') {
        return null;
      }

      // Execute EXPLAIN query
      const explainText = `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${text}`;
      const explainResult = await this.pool.query({
        text: explainText,
        values: params,
        timeout: 10000
      });

      // Get explain plan
      const plan = explainResult.rows[0]['QUERY PLAN'][0];

      // Log explain plan
      this.logSlowQuery(
        text,
        params,
        name,
        type,
        plan['Execution Time'],
        plan['Plan']['Plan Rows'],
        plan
      );

      return plan;
    } catch (error) {
      logger.error(`Explain error: ${error.message}`, {
        query: text,
        params,
        name,
        type,
        error
      });

      return null;
    }
  }

  /**
   * Log a query
   * @param {string} text - Query text
   * @param {Array} params - Query parameters
   * @param {string} name - Query name
   * @param {string} type - Query type
   * @param {number} duration - Query duration in ms
   * @param {number} rowCount - Number of rows returned
   */
  logQuery(text, params = [], name = 'unnamed', type = 'unknown', duration = 0, rowCount = 0) {
    const timestamp = new Date().toISOString();
    const logEntry = JSON.stringify({
      timestamp,
      name,
      type,
      duration,
      rowCount,
      query: text,
      params
    }) + '\n';

    this.queryLogStream.write(logEntry);
  }

  /**
   * Log a slow query
   * @param {string} text - Query text
   * @param {Array} params - Query parameters
   * @param {string} name - Query name
   * @param {string} type - Query type
   * @param {number} duration - Query duration in ms
   * @param {number} rowCount - Number of rows returned
   * @param {Object} explain - Explain plan
   */
  logSlowQuery(text, params = [], name = 'unnamed', type = 'unknown', duration = 0, rowCount = 0, explain = null) {
    const timestamp = new Date().toISOString();
    const logEntry = JSON.stringify({
      timestamp,
      name,
      type,
      duration,
      rowCount,
      query: text,
      params,
      explain
    }) + '\n';

    this.slowQueryLogStream.write(logEntry);

    logger.warn(`Slow query detected: ${name} (${duration}ms)`, {
      query: text,
      params,
      name,
      type,
      duration,
      rowCount
    });
  }

  /**
   * Get query type from query text
   * @param {string} text - Query text
   * @returns {string} Query type
   */
  getQueryType(text) {
    const trimmedText = text.trim().toUpperCase();

    if (trimmedText.startsWith('SELECT')) {
      return 'SELECT';
    } else if (trimmedText.startsWith('INSERT')) {
      return 'INSERT';
    } else if (trimmedText.startsWith('UPDATE')) {
      return 'UPDATE';
    } else if (trimmedText.startsWith('DELETE')) {
      return 'DELETE';
    } else if (trimmedText.startsWith('CREATE')) {
      return 'CREATE';
    } else if (trimmedText.startsWith('ALTER')) {
      return 'ALTER';
    } else if (trimmedText.startsWith('DROP')) {
      return 'DROP';
    } else if (trimmedText.startsWith('TRUNCATE')) {
      return 'TRUNCATE';
    } else if (trimmedText.startsWith('BEGIN')) {
      return 'BEGIN';
    } else if (trimmedText.startsWith('COMMIT')) {
      return 'COMMIT';
    } else if (trimmedText.startsWith('ROLLBACK')) {
      return 'ROLLBACK';
    } else {
      return 'UNKNOWN';
    }
  }

  /**
   * Close the optimizer
   */
  close() {
    this.queryLogStream.end();
    this.slowQueryLogStream.end();
  }
}

/**
 * Create a query optimizer
 * @param {Pool} pool - Database connection pool
 * @param {Object} options - Optimizer options
 * @returns {QueryOptimizer} Query optimizer
 */
function createQueryOptimizer(pool, options = {}) {
  return new QueryOptimizer(pool, options);
}

module.exports = {
  QueryOptimizer,
  createQueryOptimizer
};
