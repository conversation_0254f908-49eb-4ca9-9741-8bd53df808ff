# Test Migration and Setup Guide

## Architecture Overview

Based on the project structure, we have three main testing areas:

1. **Vue Component Tests** (Directus Extensions)
   - Location: `directus/extensions/interfaces/vendor-portal/tests/`
   - Framework: Vitest + Vue Test Utils
   - Purpose: Test vendor portal UI components

2. **Server API Tests**
   - Location: `tests/services/`
   - Framework: Deno Test + Vitest
   - Purpose: Test backend services and API endpoints

3. **Integration Tests**
   - Location: `tests/integration/`
   - Framework: Deno Test + Vitest
   - Purpose: Test communication between services

## Environment Setup

### 1. Component Testing Environment

```bash
# Vue component specific dependencies
npm install --save-dev @vitejs/plugin-vue @vue/test-utils
npm install --save-dev @testing-library/vue @testing-library/jest-dom
```

### 2. Server Testing Environment

```bash
# Deno dependencies
deno add npm:vitest
deno add npm:@types/node
deno cache deps.ts
```

### 3. Integration Testing Environment

```bash
# Install all test dependencies
./install-deps.sh
```

## Test Organization

### Component Tests

- Use `.vitest.js` extension
- Keep in same directory as component
- Focus on component isolation
- Mock external dependencies

### Service Tests

- Use `.test.ts` extension
- Mirror service directory structure
- Test pure business logic
- Mock external services

### Integration Tests

- Use `.integration.test.ts` extension
- Focus on service interactions
- Test real service communication
- Minimal mocking

## Configuration Files

1. `vitest.config.ts` - Main test configuration
2. `deno.json` - Deno specific settings
3. `tsconfig.node.json` - Node.js TypeScript config
4. `import_map.json` - Module resolution

## Migration Steps

1. Update imports:

```typescript
// Old
import { describe, it, expect } from 'vitest';

// New
import { describe, it, expect } from 'npm:vitest';
```

2. Update file extensions:

```bash
# Add .ts extension to imports
import { MyService } from './my-service.ts';
```

3. Update test helpers:

```typescript
// Use new test utils
import { createTestUtils } from '@tests/utils/test-utils.ts';
```

## Common Issues & Solutions

### Module Resolution

Problem: `Cannot find module 'vitest'`
Solution: Add to import_map.json:

```json
{
  "imports": {
    "vitest": "npm:vitest"
  }
}
```

### Type Errors

Problem: `Cannot find name 'describe'`
Solution: Add to tsconfig.json:

```json
{
  "types": ["vitest/globals"]
}
```

### Browser APIs

Problem: `document is not defined`
Solution: Add JSDOM setup:

```typescript
import { beforeAll } from 'npm:vitest';
import { JSDOM } from 'npm:jsdom';

beforeAll(() => {
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
  global.document = dom.window.document;
});
```

## Best Practices

1. **Isolation**: Each test should be independent
2. **Mocking**: Mock external dependencies
3. **Naming**: Use descriptive test names
4. **Structure**: Follow AAA pattern (Arrange, Act, Assert)
5. **Coverage**: Aim for high coverage, but focus on critical paths

## Migration Checklist

- [ ] Update all import statements
- [ ] Add file extensions
- [ ] Update test utilities
- [ ] Verify JSDOM setup
- [ ] Run test suite
- [ ] Check coverage
- [ ] Update CI/CD pipeline

## Next Steps

1. Complete environment setup
2. Migrate Vue component tests
3. Update service tests
4. Add integration tests
5. Configure CI/CD pipeline
