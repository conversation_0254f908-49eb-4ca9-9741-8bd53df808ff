/**
 * Adaptive Compression Tests
 * 
 * This file contains tests for the adaptive compression service.
 */

const { expect } = require('chai');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { 
  compressFile, 
  detectClientCapabilities, 
  determineCompressionSettings 
} = require('../services/assets/adaptive-compression');

const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const unlinkAsync = promisify(fs.unlink);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

describe('Adaptive Compression', () => {
  const testDir = path.join(__dirname, 'temp');
  const testFile = path.join(testDir, 'test-file.txt');
  const compressedFile = path.join(testDir, 'test-file.txt.gz');
  
  before(async () => {
    // Create test directory if it doesn't exist
    if (!await existsAsync(testDir)) {
      await mkdirAsync(testDir, { recursive: true });
    }
    
    // Create test file with random content
    const content = Array(1000).fill(0).map(() => Math.random().toString(36).substring(2)).join('');
    await writeFileAsync(testFile, content, 'utf8');
  });
  
  after(async () => {
    // Clean up test files
    if (await existsAsync(testFile)) {
      await unlinkAsync(testFile);
    }
    
    if (await existsAsync(compressedFile)) {
      await unlinkAsync(compressedFile);
    }
  });
  
  describe('detectClientCapabilities', () => {
    it('should detect client capabilities from headers', () => {
      const headers = {
        'accept-encoding': 'gzip, deflate, br',
        'device-memory': '8',
        'ect': '4g',
        'cpu-class': 'high'
      };
      
      const capabilities = detectClientCapabilities(headers);
      
      expect(capabilities).to.be.an('object');
      expect(capabilities.level).to.equal('high');
      expect(capabilities.supportedAlgorithms).to.include('gzip');
      expect(capabilities.supportedAlgorithms).to.include('deflate');
      expect(capabilities.supportedAlgorithms).to.include('brotli');
      expect(capabilities.preferredAlgorithm).to.equal('brotli');
      expect(capabilities.cpuPower).to.equal('high');
      expect(capabilities.memoryLimit).to.equal('high');
      expect(capabilities.networkQuality).to.equal('high');
    });
    
    it('should handle missing headers', () => {
      const capabilities = detectClientCapabilities(null);
      
      expect(capabilities).to.be.an('object');
      expect(capabilities.level).to.equal('medium');
      expect(capabilities.supportedAlgorithms).to.include('gzip');
      expect(capabilities.supportedAlgorithms).to.include('deflate');
      expect(capabilities.preferredAlgorithm).to.equal('gzip');
      expect(capabilities.cpuPower).to.equal('medium');
      expect(capabilities.memoryLimit).to.equal('medium');
      expect(capabilities.networkQuality).to.equal('medium');
    });
    
    it('should handle partial headers', () => {
      const headers = {
        'accept-encoding': 'gzip, deflate'
      };
      
      const capabilities = detectClientCapabilities(headers);
      
      expect(capabilities).to.be.an('object');
      expect(capabilities.level).to.equal('medium');
      expect(capabilities.supportedAlgorithms).to.include('gzip');
      expect(capabilities.supportedAlgorithms).to.include('deflate');
      expect(capabilities.preferredAlgorithm).to.equal('gzip');
      expect(capabilities.cpuPower).to.equal('medium');
      expect(capabilities.memoryLimit).to.equal('medium');
      expect(capabilities.networkQuality).to.equal('medium');
    });
    
    it('should detect low capabilities', () => {
      const headers = {
        'accept-encoding': 'gzip',
        'device-memory': '1',
        'ect': '2g',
        'cpu-class': 'low'
      };
      
      const capabilities = detectClientCapabilities(headers);
      
      expect(capabilities).to.be.an('object');
      expect(capabilities.level).to.equal('low');
      expect(capabilities.supportedAlgorithms).to.include('gzip');
      expect(capabilities.preferredAlgorithm).to.equal('gzip');
      expect(capabilities.cpuPower).to.equal('low');
      expect(capabilities.memoryLimit).to.equal('low');
      expect(capabilities.networkQuality).to.equal('low');
    });
  });
  
  describe('determineCompressionSettings', () => {
    it('should determine compression settings based on MIME type', () => {
      const mimeType = 'application/json';
      const clientCapabilities = {
        level: 'high',
        supportedAlgorithms: ['gzip', 'brotli', 'deflate'],
        preferredAlgorithm: 'brotli'
      };
      
      const settings = determineCompressionSettings(mimeType, clientCapabilities, 1024);
      
      expect(settings).to.be.an('object');
      expect(settings.algorithm).to.equal('brotli');
      expect(settings.level).to.be.a('number');
    });
    
    it('should fall back to supported algorithm if preferred is not supported', () => {
      const mimeType = 'application/json';
      const clientCapabilities = {
        level: 'high',
        supportedAlgorithms: ['gzip', 'deflate'],
        preferredAlgorithm: 'gzip'
      };
      
      const settings = determineCompressionSettings(mimeType, clientCapabilities, 1024);
      
      expect(settings).to.be.an('object');
      expect(settings.algorithm).to.equal('gzip');
      expect(settings.level).to.be.a('number');
    });
    
    it('should adjust compression level based on file size', () => {
      const mimeType = 'application/json';
      const clientCapabilities = {
        level: 'high',
        supportedAlgorithms: ['gzip', 'brotli', 'deflate'],
        preferredAlgorithm: 'brotli'
      };
      
      // Small file
      const smallSettings = determineCompressionSettings(mimeType, clientCapabilities, 500);
      
      // Large file
      const largeSettings = determineCompressionSettings(mimeType, clientCapabilities, 200 * 1024 * 1024);
      
      expect(smallSettings.level).to.be.at.most(3);
      expect(largeSettings.level).to.be.at.most(4);
    });
  });
  
  describe('compressFile', () => {
    it('should compress a file with adaptive compression', async () => {
      const result = await compressFile(testFile, compressedFile, {
        mimeType: 'text/plain',
        clientCapabilities: {
          level: 'high',
          supportedAlgorithms: ['gzip', 'brotli', 'deflate'],
          preferredAlgorithm: 'brotli'
        }
      });
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.inputPath).to.equal(testFile);
      expect(result.outputPath).to.equal(compressedFile);
      expect(result.fileSize).to.be.a('number');
      expect(result.compressedSize).to.be.a('number');
      expect(result.compressionRatio).to.be.a('number');
      expect(result.duration).to.be.a('number');
      expect(result.algorithm).to.be.a('string');
      expect(result.level).to.be.a('number');
      
      // Compressed file should be smaller than original
      expect(result.compressedSize).to.be.lessThan(result.fileSize);
    });
    
    it('should fall back to gzip if adaptive compression fails', async () => {
      // Mock adaptive compression to fail
      const originalCompressFile = compressFile;
      const mockCompressFile = async (inputPath, outputPath, options) => {
        // Force fallback by throwing an error
        throw new Error('Mock error');
      };
      
      // Replace compressFile with mock
      compressFile.compressFile = mockCompressFile;
      
      try {
        const result = await compressFile(testFile, compressedFile, {
          mimeType: 'text/plain',
          clientCapabilities: {
            level: 'high',
            supportedAlgorithms: ['gzip', 'brotli', 'deflate'],
            preferredAlgorithm: 'brotli'
          }
        });
        
        expect(result).to.be.an('object');
        expect(result.success).to.be.true;
        expect(result.algorithm).to.equal('gzip');
      } finally {
        // Restore original compressFile
        compressFile.compressFile = originalCompressFile;
      }
    });
  });
});
