'use strict';
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
  var exports = {};
  exports.id = 'pages/_error';
  exports.ids = ['pages/_error'];
  exports.modules = {
    /***/ './node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js':
      /*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js ***!
  \***********************************************************************************/
      /***/ (__unused_webpack_module, exports) => {
        eval(
          '/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\nObject.defineProperty(exports, "hoist", ({\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n}));\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it\'s a promise and\n    // return a promise that resolves to the name.\n    if ("then" in module && typeof module.then === "function") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we\'re trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === "function" && name === "default") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9oZWxwZXJzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQWlCO0FBQ2pCLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHlDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL212cy12ci1zZXJ2ZXIvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9oZWxwZXJzLmpzPzk5ZmQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBIb2lzdHMgYSBuYW1lIGZyb20gYSBtb2R1bGUgb3IgcHJvbWlzZWQgbW9kdWxlLlxuICpcbiAqIEBwYXJhbSBtb2R1bGUgdGhlIG1vZHVsZSB0byBob2lzdCB0aGUgbmFtZSBmcm9tXG4gKiBAcGFyYW0gbmFtZSB0aGUgbmFtZSB0byBob2lzdFxuICogQHJldHVybnMgdGhlIHZhbHVlIG9uIHRoZSBtb2R1bGUgKG9yIHByb21pc2VkIG1vZHVsZSlcbiAqLyBcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImhvaXN0XCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBob2lzdDtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGhvaXN0KG1vZHVsZSwgbmFtZSkge1xuICAgIC8vIElmIHRoZSBuYW1lIGlzIGF2YWlsYWJsZSBpbiB0aGUgbW9kdWxlLCByZXR1cm4gaXQuXG4gICAgaWYgKG5hbWUgaW4gbW9kdWxlKSB7XG4gICAgICAgIHJldHVybiBtb2R1bGVbbmFtZV07XG4gICAgfVxuICAgIC8vIElmIGEgcHJvcGVydHkgY2FsbGVkIGB0aGVuYCBleGlzdHMsIGFzc3VtZSBpdCdzIGEgcHJvbWlzZSBhbmRcbiAgICAvLyByZXR1cm4gYSBwcm9taXNlIHRoYXQgcmVzb2x2ZXMgdG8gdGhlIG5hbWUuXG4gICAgaWYgKFwidGhlblwiIGluIG1vZHVsZSAmJiB0eXBlb2YgbW9kdWxlLnRoZW4gPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICByZXR1cm4gbW9kdWxlLnRoZW4oKG1vZCk9PmhvaXN0KG1vZCwgbmFtZSkpO1xuICAgIH1cbiAgICAvLyBJZiB3ZSdyZSB0cnlpbmcgdG8gaG9pc2UgdGhlIGRlZmF1bHQgZXhwb3J0LCBhbmQgdGhlIG1vZHVsZSBpcyBhIGZ1bmN0aW9uLFxuICAgIC8vIHJldHVybiB0aGUgbW9kdWxlIGl0c2VsZi5cbiAgICBpZiAodHlwZW9mIG1vZHVsZSA9PT0gXCJmdW5jdGlvblwiICYmIG5hbWUgPT09IFwiZGVmYXVsdFwiKSB7XG4gICAgICAgIHJldHVybiBtb2R1bGU7XG4gICAgfVxuICAgIC8vIE90aGVyd2lzZSwgcmV0dXJuIHVuZGVmaW5lZC5cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWxwZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!':
      /*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
      /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
        eval(
          '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module */ "./node_modules/next/dist/server/future/route-modules/pages/module.js");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ "./node_modules/next/dist/server/future/route-kind.js");\n/* harmony import */ var next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-route-loader/helpers */ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/helpers.js");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ "./node_modules/next/dist/pages/_document.js");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ "./node_modules/next/dist/pages/_app.js");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ "./node_modules/next/dist/pages/_error.js");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\n// Import the app and document modules.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst PagesRouteModule = next_dist_server_future_route_modules_pages_module__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule;\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "default"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "getStaticProps");\nconst getStaticPaths = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "getStaticPaths");\nconst getServerSideProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "getServerSideProps");\nconst config = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "config");\nconst reportWebVitals = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "reportWebVitals");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "unstable_getStaticProps");\nconst unstable_getStaticPaths = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "unstable_getStaticPaths");\nconst unstable_getStaticParams = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "unstable_getStaticParams");\nconst unstable_getServerProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "unstable_getServerProps");\nconst unstable_getServerSideProps = (0,next_dist_build_webpack_loaders_next_route_loader_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, "unstable_getServerSideProps");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: "/_error",\n        pathname: "/_error",\n        // The following aren\'t used in production.\n        bundlePath: "",\n        filename: ""\n    },\n    components: {\n        App: (private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default()),\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/pages/_app.js':
      /*!**********************************************!*\
  !*** ./node_modules/next/dist/pages/_app.js ***!
  \**********************************************/
      /***/ (module, exports, __webpack_require__) => {
        eval(
          '\nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\nObject.defineProperty(exports, "default", ({\n    enumerable: true,\n    get: function() {\n        return App;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ "./node_modules/@swc/helpers/cjs/_interop_require_default.cjs");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ "react"));\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ "../shared/lib/utils");\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */ async function appGetInitialProps(param) {\n    let { Component, ctx } = param;\n    const pageProps = await (0, _utils.loadGetInitialProps)(Component, ctx);\n    return {\n        pageProps\n    };\n}\nclass App extends _react.default.Component {\n    render() {\n        const { Component, pageProps } = this.props;\n        return /*#__PURE__*/ _react.default.createElement(Component, pageProps);\n    }\n}\n(()=>{\n    App.origGetInitialProps = appGetInitialProps;\n})();\n(()=>{\n    App.getInitialProps = appGetInitialProps;\n})();\nif ((typeof exports.default === "function" || typeof exports.default === "object" && exports.default !== null) && typeof exports.default.__esModule === "undefined") {\n    Object.defineProperty(exports.default, "__esModule", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_app.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_app.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/pages/_document.js':
      /*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
      /***/ (__unused_webpack_module, exports, __webpack_require__) => {
        eval(
          '\nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ "react"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ "../shared/lib/constants");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ "../server/get-page-files");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ "../server/htmlescape");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ "./node_modules/next/dist/lib/is-error.js"));\nconst _htmlcontext = __webpack_require__(/*! ../shared/lib/html-context */ "../shared/lib/html-context");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, "/_app");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix, buildManifest, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(".js") && !polyfill.endsWith(".module.js")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement("script", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${assetQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property \'props\' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props, _el_props_dangerouslySetInnerHTML;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property \'props\' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement("style", {\n        "amp-custom": "",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join("").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, "").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, "")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports, assetPrefix, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(".js") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement("script", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix, buildManifest, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(".js"));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith(".js"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement("script", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || "nodejs" === "edge") return null;\n    try {\n        let { partytownSnippet } = require("@builder.io/partytown/integration");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props, _child_props_dangerouslySetInnerHTML;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && "data-partytown-config" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement("script", {\n            "data-partytown-config": "",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: "${assetPrefix}/_next/static/~partytown/"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement("script", {\n            "data-partytown": "",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy, src, children: scriptChildren, dangerouslySetInnerHTML, ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === "string" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join("") : ""\n                };\n            } else {\n                throw new Error("Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script");\n            }\n            return /*#__PURE__*/ _react.default.createElement("script", {\n                ...srcProps,\n                ...scriptProps,\n                type: "text/partytown",\n                key: src || index,\n                nonce: props.nonce,\n                "data-nscript": "worker",\n                crossOrigin: props.crossOrigin || crossOrigin\n            });\n        }));\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== "MODULE_NOT_FOUND") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader, disableOptimizedLoading, crossOrigin } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy, ...scriptProps } = file;\n        return /*#__PURE__*/ _react.default.createElement("script", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            "data-nscript": "beforeInteractive",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin, nonce, ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes("?") ? "&" : "?"}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = "") {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages["/_app"];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = [\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ];\n    // If no font files should preload but there\'s an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ _react.default.createElement("link", {\n            "data-next-font": nextFontManifest.pagesUsingSizeAdjust ? "size-adjust" : "",\n            rel: "preconnect",\n            href: "/",\n            crossOrigin: "anonymous"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ _react.default.createElement("link", {\n                key: fontFile,\n                rel: "preload",\n                href: `${assetPrefix}/_next/${encodeURI(fontFile)}`,\n                as: "font",\n                type: `font/${ext}`,\n                crossOrigin: "anonymous",\n                "data-next-font": fontFile.includes("-s") ? "size-adjust" : ""\n            });\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = (()=>{\n        this.contextType = _htmlcontext.HtmlContext;\n    })();\n    getCssLinks(files) {\n        const { assetPrefix, assetQueryString, dynamicImports, crossOrigin, optimizeCss, optimizeFonts } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(".css"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(".css"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement("link", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: "preload",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: "style",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement("link", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: "stylesheet",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                "data-n-g": isUnmanagedFile ? undefined : isSharedFile ? "" : undefined,\n                "data-n-p": isUnmanagedFile ? undefined : isSharedFile ? undefined : ""\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(".js")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement("link", {\n                rel: "preload",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                as: "script",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(".js");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement("link", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: "preload",\n                    href: file.src,\n                    as: "script",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement("link", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: "preload",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: "script",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader } = this.context;\n        const { nonce, crossOrigin } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy, children, dangerouslySetInnerHTML, src, ...scriptProps } = file;\n            let html = "";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === "string" ? children : Array.isArray(children) ? children.join("") : "";\n            }\n            return /*#__PURE__*/ _react.default.createElement("script", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                "data-nscript": "beforeInteractive",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var _c_props, _c_props1;\n            if ((c == null ? void 0 : c.type) === "link" && (c == null ? void 0 : (_c_props = c.props) == null ? void 0 : _c_props.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url })=>{\n                var _c_props, _c_props_href;\n                return c == null ? void 0 : (_c_props = c.props) == null ? void 0 : (_c_props_href = _c_props.href) == null ? void 0 : _c_props_href.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    "data-href": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (_c_props1 = c.props) == null ? void 0 : _c_props1.children) {\n                const newProps = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n            return c;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles, ampPath, inAmpMode, hybridAmp, canonicalBase, __NEXT_DATA__, dangerousAsPath, headTags, unstable_runtimeJS, unstable_JsPreload, disableOptimizedLoading, optimizeCss, optimizeFonts, assetPrefix, nextFontManifest } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                let metaTag;\n                if (this.context.strictNextHead) {\n                    metaTag = /*#__PURE__*/ _react.default.createElement("meta", {\n                        name: "next-head",\n                        content: "1"\n                    });\n                }\n                if (c && c.type === "link" && c.props["rel"] === "preload" && c.props["as"] === "style") {\n                    metaTag && cssPreloads.push(metaTag);\n                    cssPreloads.push(c);\n                } else {\n                    if (c) {\n                        if (metaTag && (c.type !== "meta" || !c.props["charSet"])) {\n                            otherHeadElements.push(metaTag);\n                        }\n                        otherHeadElements.push(c);\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props["data-react-helmet"];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === "title") {\n                        console.warn("Warning: <title> should not be used in _document.js\'s <Head>. https://nextjs.org/docs/messages/no-document-title");\n                    } else if ((child == null ? void 0 : child.type) === "meta" && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === "viewport") {\n                        console.warn("Warning: viewport meta tags should not be used in _document.js\'s <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn("Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type, props } = child;\n            if ( true && inAmpMode) {\n                let badProp = "";\n                if (type === "meta" && props.name === "viewport") {\n                    badProp = \'name="viewport"\';\n                } else if (type === "link" && props.rel === "canonical") {\n                    hasCanonicalRel = true;\n                } else if (type === "script") {\n                    // only block if\n                    // 1. it has a src and isn\'t pointing to ampproject\'s CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf("ampproject") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === "text/javascript")) {\n                        badProp = "<script";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}="${props[prop]}"`;\n                        });\n                        badProp += "/>";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag "${child.type}" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === "link" && props.rel === "amphtml") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n        return /*#__PURE__*/ _react.default.createElement("head", getHeadHTMLProps(this.props), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement("style", {\n            "data-next-hide-fouc": true,\n            "data-ampdevmode":  true && inAmpMode ? "true" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement("noscript", {\n            "data-next-hide-fouc": true,\n            "data-ampdevmode":  true && inAmpMode ? "true" : undefined\n        }, /*#__PURE__*/ _react.default.createElement("style", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, this.context.strictNextHead ? null : /*#__PURE__*/ _react.default.createElement("meta", {\n            name: "next-head-count",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement("meta", {\n            name: "next-font-preconnect"\n        }), nextFontLinkTags.preconnect, nextFontLinkTags.preload,  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement("meta", {\n            name: "viewport",\n            content: "width=device-width,minimum-scale=1,initial-scale=1"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement("link", {\n            rel: "canonical",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ "../server/utils").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement("link", {\n            rel: "preload",\n            as: "script",\n            href: "https://cdn.ampproject.org/v0.js"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement("style", {\n            "amp-boilerplate": "",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement("noscript", null, /*#__PURE__*/ _react.default.createElement("style", {\n            "amp-boilerplate": "",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement("script", {\n            async: true,\n            src: "https://cdn.ampproject.org/v0.js"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement("link", {\n            rel: "amphtml",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement("noscript", {\n            "data-n-css": this.props.nonce ?? ""\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement("noscript", {\n            "data-n-css": this.props.nonce ?? ""\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement("noscript", {\n            id: "__next_css__DO_NOT_USE__"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find, _children_find_props, _children_find1, _children_find_props1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === "body")) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === "beforeInteractive") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                "lazyOnload",\n                "afterInteractive",\n                "worker"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = (()=>{\n        this.contextType = _htmlcontext.HtmlContext;\n    })();\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__, largePageDataBytes } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ "./node_modules/next/dist/lib/pretty-bytes.js")["default"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page "${__NEXT_DATA__.page}"${__NEXT_DATA__.page === context.dangerousAsPath ? "" : ` (path "${context.dangerousAsPath}")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf("circular structure") !== -1) {\n                throw new Error(`Circular structure in "getInitialProps" result of page "${__NEXT_DATA__.page}". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix, inAmpMode, buildManifest, unstable_runtimeJS, docComponentsRendered, assetQueryString, disableOptimizedLoading, crossOrigin } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement("script", {\n                id: "__NEXT_DATA__",\n                type: "application/json",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                "data-ampdevmode": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement("script", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${assetQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    "data-ampdevmode": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn("Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement("script", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement("script", {\n            id: "__NEXT_DATA__",\n            type: "application/json",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nfunction Html(props) {\n    const { inAmpMode, docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } = (0, _htmlcontext.useHtmlContext)();\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement("html", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? "" : undefined,\n        "data-ampdevmode":  true && inAmpMode && "development" !== "production" ? "" : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered } = (0, _htmlcontext.useHtmlContext)();\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement("next-js-internal-body-render-target", null);\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement("body", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement("body", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/pages/_error.js':
      /*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
      /***/ (module, exports, __webpack_require__) => {
        eval(
          '\nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\nObject.defineProperty(exports, "default", ({\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ "./node_modules/@swc/helpers/cjs/_interop_require_default.cjs");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ "react"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ "./node_modules/next/dist/shared/lib/head.js"));\nconst statusCodes = {\n    400: "Bad Request",\n    404: "This page could not be found",\n    405: "Method Not Allowed",\n    500: "Internal Server Error"\n};\nfunction _getInitialProps(param) {\n    let { res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: \'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"\',\n        height: "100vh",\n        textAlign: "center",\n        display: "flex",\n        flexDirection: "column",\n        alignItems: "center",\n        justifyContent: "center"\n    },\n    desc: {\n        lineHeight: "48px"\n    },\n    h1: {\n        display: "inline-block",\n        margin: "0 20px 0 0",\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: "top"\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: "28px"\n    },\n    wrap: {\n        display: "inline-block"\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || "An unexpected error has occurred";\n        return /*#__PURE__*/ _react.default.createElement("div", {\n            style: styles.error\n        }, /*#__PURE__*/ _react.default.createElement(_head.default, null, /*#__PURE__*/ _react.default.createElement("title", null, statusCode ? statusCode + ": " + title : "Application error: a client-side exception has occurred")), /*#__PURE__*/ _react.default.createElement("div", {\n            style: styles.desc\n        }, /*#__PURE__*/ _react.default.createElement("style", {\n            dangerouslySetInnerHTML: {\n                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : \'\'\n                }\n               */ __html: "body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}" + (withDarkMode ? "@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}" : "")\n            }\n        }), statusCode ? /*#__PURE__*/ _react.default.createElement("h1", {\n            className: "next-error-h1",\n            style: styles.h1\n        }, statusCode) : null, /*#__PURE__*/ _react.default.createElement("div", {\n            style: styles.wrap\n        }, /*#__PURE__*/ _react.default.createElement("h2", {\n            style: styles.h2\n        }, this.props.title || statusCode ? title : /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, "Application error: a client-side exception has occurred (see the browser console for more information)"), "."))));\n    }\n}\n(()=>{\n    Error.displayName = "ErrorPage";\n})();\n(()=>{\n    Error.getInitialProps = _getInitialProps;\n})();\n(()=>{\n    Error.origGetInitialProps = _getInitialProps;\n})();\nif ((typeof exports.default === "function" || typeof exports.default === "object" && exports.default !== null) && typeof exports.default.__esModule === "undefined") {\n    Object.defineProperty(exports.default, "__esModule", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_error.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/shared/lib/head.js':
      /*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
      /***/ (module, exports, __webpack_require__) => {
        eval(
          '/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    defaultHead: function() {\n        return defaultHead;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ "./node_modules/@swc/helpers/cjs/_interop_require_default.cjs");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ "./node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ "react"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ "./side-effect"));\nconst _ampcontext = __webpack_require__(/*! ./amp-context */ "./amp-context");\nconst _headmanagercontext = __webpack_require__(/*! ./head-manager-context */ "./head-manager-context");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ "./amp-mode");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ "./utils/warn-once");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ _react.default.createElement("meta", {\n            charSet: "utf-8"\n        })\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ _react.default.createElement("meta", {\n            name: "viewport",\n            content: "width=device-width"\n        }));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be "string" or "number" in this case we ignore them for backwards compat\n    if (typeof child === "string" || typeof child === "number") {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === "string" || typeof fragmentChild === "number") {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    "name",\n    "httpEquiv",\n    "charSet",\n    "itemProp"\n];\n/*\n returns a function for filtering head child elements\n which shouldn\'t be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== "number" && h.key.indexOf("$") > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf("$") + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case "title":\n            case "base":\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case "meta":\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === "charSet") {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== "name" || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === "script" && c.props["type"] !== "application/ld+json") {\n                const srcMessage = c.props["src"] ? \'<script> tag with src="\' + c.props["src"] + \'"\' : "inline <script>";\n                (0, _warnonce.warnOnce)("Do not add <script> tags using next/head (see " + srcMessage + "). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component");\n            } else if (c.type === "link" && c.props["rel"] === "stylesheet") {\n                (0, _warnonce.warnOnce)(\'Do not add stylesheets using next/head (see <link rel="stylesheet"> tag with href="\' + c.props["href"] + \'"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component\');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontext.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontext.HeadManagerContext);\n    return /*#__PURE__*/ _react.default.createElement(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState)\n    }, children);\n}\nconst _default = Head;\nif ((typeof exports.default === "function" || typeof exports.default === "object" && exports.default !== null) && typeof exports.default.__esModule === "undefined") {\n    Object.defineProperty(exports.default, "__esModule", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/head.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/lib/is-error.js':
      /*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
      /***/ (__unused_webpack_module, exports, __webpack_require__) => {
        eval(
          '\nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = __webpack_require__(/*! ../shared/lib/is-plain-object */ "../shared/lib/is-plain-object");\nfunction isError(err) {\n    return typeof err === "object" && err !== null && "name" in err && "message" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === "undefined") {\n            return new Error("An undefined error was thrown, " + "see here for more info: https://nextjs.org/docs/messages/threw-undefined");\n        }\n        if (err === null) {\n            return new Error("A null error was thrown, " + "see here for more info: https://nextjs.org/docs/messages/threw-undefined");\n        }\n    }\n    return new Error((0, _isplainobject.isPlainObject)(err) ? JSON.stringify(err) : err + "");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/lib/pretty-bytes.js':
      /*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
      /***/ (__unused_webpack_module, exports) => {
        eval(
          '/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\nObject.defineProperty(exports, "default", ({\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n}));\nconst UNITS = [\n    "B",\n    "kB",\n    "MB",\n    "GB",\n    "TB",\n    "PB",\n    "EB",\n    "ZB",\n    "YB"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === "string") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return " 0 B";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? "-" : options.signed ? "+" : "";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + " B";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + " " + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/server/future/route-kind.js':
      /*!************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-kind.js ***!
  \************************************************************/
      /***/ (__unused_webpack_module, exports) => {
        eval(
          '\nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\nObject.defineProperty(exports, "RouteKind", ({\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n}));\nvar RouteKind;\n(function(RouteKind) {\n    RouteKind[/**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ "PAGES"] = "PAGES";\n    RouteKind[/**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ "PAGES_API"] = "PAGES_API";\n    RouteKind[/**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ "APP_PAGE"] = "APP_PAGE";\n    RouteKind[/**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ "APP_ROUTE"] = "APP_ROUTE";\n})(RouteKind || (RouteKind = {}));\n\n//# sourceMappingURL=route-kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDZDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLElBQUksRUFBRSxHQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixJQUFJLEVBQUUsR0FBRztBQUNsQztBQUNBLENBQUMsOEJBQThCOztBQUUvQiIsInNvdXJjZXMiOlsid2VicGFjazovL212cy12ci1zZXJ2ZXIvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcz8zOTc5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiUm91dGVLaW5kXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBSb3V0ZUtpbmQ7XG4gICAgfVxufSk7XG52YXIgUm91dGVLaW5kO1xuKGZ1bmN0aW9uKFJvdXRlS2luZCkge1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYFBBR0VTYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYHBhZ2VzL2AuXG4gICAqLyBcIlBBR0VTXCJdID0gXCJQQUdFU1wiO1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYFBBR0VTX0FQSWAgcmVwcmVzZW50cyBhbGwgdGhlIEFQSSByb3V0ZXMgdW5kZXIgYHBhZ2VzL2FwaS9gLlxuICAgKi8gXCJQQUdFU19BUElcIl0gPSBcIlBBR0VTX0FQSVwiO1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYEFQUF9QQUdFYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGBwYWdlLntqLHR9c3sseH1gLlxuICAgKi8gXCJBUFBfUEFHRVwiXSA9IFwiQVBQX1BBR0VcIjtcbiAgICBSb3V0ZUtpbmRbLyoqXG4gICAqIGBBUFBfUk9VVEVgIHJlcHJlc2VudHMgYWxsIHRoZSBBUEkgcm91dGVzIGFuZCBtZXRhZGF0YSByb3V0ZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGByb3V0ZS57aix0fXN7LHh9YC5cbiAgICovIFwiQVBQX1JPVVRFXCJdID0gXCJBUFBfUk9VVEVcIjtcbn0pKFJvdXRlS2luZCB8fCAoUm91dGVLaW5kID0ge30pKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cm91dGUta2luZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-kind.js\n',
        );

        /***/
      },

    /***/ './node_modules/next/dist/server/future/route-modules/pages/module.js':
      /*!****************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/module.js ***!
  \****************************************************************************/
      /***/ (__unused_webpack_module, exports, __webpack_require__) => {
        eval(
          '\nObject.defineProperty(exports, "__esModule", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PagesRouteModule: function() {\n        return PagesRouteModule;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _routemodule = __webpack_require__(/*! ../route-module */ "../route-module");\nconst _render = __webpack_require__(/*! ../../../render */ "../../../render");\nclass PagesRouteModule extends _routemodule.RouteModule {\n    constructor(options){\n        super(options);\n        this.components = options.components;\n    }\n    render(req, res, context) {\n        return (0, _render.renderToHTMLImpl)(req, res, context.page, context.query, context.renderOpts, {\n            App: this.components.App,\n            Document: this.components.Document\n        });\n    }\n}\nconst _default = PagesRouteModule;\n\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/module.js\n',
        );

        /***/
      },

    /***/ '../route-module':
      /*!************************************************************************!*\
  !*** external "next/dist/server/future/route-modules/route-module.js" ***!
  \************************************************************************/
      /***/ module => {
        module.exports = require('next/dist/server/future/route-modules/route-module.js');

        /***/
      },

    /***/ '../server/get-page-files':
      /*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
      /***/ module => {
        module.exports = require('next/dist/server/get-page-files.js');

        /***/
      },

    /***/ '../server/htmlescape':
      /*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
      /***/ module => {
        module.exports = require('next/dist/server/htmlescape.js');

        /***/
      },

    /***/ '../../../render':
      /*!*********************************************!*\
  !*** external "next/dist/server/render.js" ***!
  \*********************************************/
      /***/ module => {
        module.exports = require('next/dist/server/render.js');

        /***/
      },

    /***/ '../server/utils':
      /*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
      /***/ module => {
        module.exports = require('next/dist/server/utils.js');

        /***/
      },

    /***/ './amp-context':
      /*!******************************************************!*\
  !*** external "next/dist/shared/lib/amp-context.js" ***!
  \******************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/amp-context.js');

        /***/
      },

    /***/ './amp-mode':
      /*!***************************************************!*\
  !*** external "next/dist/shared/lib/amp-mode.js" ***!
  \***************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/amp-mode.js');

        /***/
      },

    /***/ '../shared/lib/constants':
      /*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/constants.js');

        /***/
      },

    /***/ './head-manager-context':
      /*!***************************************************************!*\
  !*** external "next/dist/shared/lib/head-manager-context.js" ***!
  \***************************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/head-manager-context.js');

        /***/
      },

    /***/ '../shared/lib/html-context':
      /*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/html-context.js');

        /***/
      },

    /***/ '../shared/lib/is-plain-object':
      /*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/is-plain-object.js');

        /***/
      },

    /***/ './side-effect':
      /*!******************************************************!*\
  !*** external "next/dist/shared/lib/side-effect.js" ***!
  \******************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/side-effect.js');

        /***/
      },

    /***/ '../shared/lib/utils':
      /*!************************************************!*\
  !*** external "next/dist/shared/lib/utils.js" ***!
  \************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/utils.js');

        /***/
      },

    /***/ './utils/warn-once':
      /*!**********************************************************!*\
  !*** external "next/dist/shared/lib/utils/warn-once.js" ***!
  \**********************************************************/
      /***/ module => {
        module.exports = require('next/dist/shared/lib/utils/warn-once.js');

        /***/
      },

    /***/ react:
      /*!************************!*\
  !*** external "react" ***!
  \************************/
      /***/ module => {
        module.exports = require('react');

        /***/
      },

    /***/ './node_modules/@swc/helpers/cjs/_interop_require_default.cjs':
      /*!********************************************************************!*\
  !*** ./node_modules/@swc/helpers/cjs/_interop_require_default.cjs ***!
  \********************************************************************/
      /***/ (__unused_webpack_module, exports) => {
        eval(
          '\n\nexports._ = exports._interop_require_default = _interop_require_default;\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2Nqcy9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQuY2pzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFNBQVMsR0FBRyxnQ0FBZ0M7QUFDNUM7QUFDQSwyQ0FBMkM7QUFDM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tdnMtdnItc2VydmVyLy4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9janMvX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0LmNqcz9kMjAxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLl8gPSBleHBvcnRzLl9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdCA9IF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdDtcbmZ1bmN0aW9uIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdChvYmopIHtcbiAgICByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDogeyBkZWZhdWx0OiBvYmogfTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/cjs/_interop_require_default.cjs\n',
        );

        /***/
      },

    /***/ './node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs':
      /*!*********************************************************************!*\
  !*** ./node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs ***!
  \*********************************************************************/
      /***/ (__unused_webpack_module, exports) => {
        eval(
          '\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== "function") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nexports._ = exports._interop_require_wildcard = _interop_require_wildcard;\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== "object" && typeof obj !== "function") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs\n',
        );

        /***/
      },
  };
  // load runtime
  var __webpack_require__ = require('../webpack-runtime.js');
  __webpack_require__.C(exports);
  var __webpack_exec__ = moduleId => __webpack_require__((__webpack_require__.s = moduleId));
  var __webpack_exports__ = __webpack_exec__(
    './node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!',
  );
  module.exports = __webpack_exports__;
})();
