#!/bin/bash

# Set up test environment for both Deno and Node.js/Vue components

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Setting up test environment...${NC}"

# Function to check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check required tools
for cmd in node npm deno; do
  if ! command_exists "$cmd"; then
    echo -e "${RED}Error: ${cmd} is required but not installed${NC}"
    exit 1
  fi
done

# Create test directories
mkdir -p tests/{integration,services,utils,setup}
mkdir -p .deno/types
mkdir -p coverage

# Install Node.js dependencies
echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
npm install

# Cache Deno dependencies
echo -e "${YELLOW}Caching Deno dependencies...${NC}"
deno cache deps.ts

# Generate base test environment
cat > .env.test << EOF
NODE_ENV=test
DENO_ENV=test
TEST_MODE=unit

# Test configuration
VITEST_SEGFAULT_RETRY=3
VITEST_MAX_THREADS=4
VITEST_MIN_THREADS=1
VITEST_POOL_OPTIONS={"threads":{"singleThread":true}}

# Coverage settings
COVERAGE_PROVIDER=v8
COVERAGE_DIRECTORY=coverage

# Test timeouts
TEST_TIMEOUT=5000
TEST_SETUP_TIMEOUT=10000

# Debug settings
DEBUG_PORT=9229
DEBUG_BREAK_ON_FAILURE=false
EOF

# Update tsconfig for tests
cat > tests/tsconfig.json << EOF
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "types": ["vitest/globals", "@testing-library/jest-dom"],
    "paths": {
      "@/*": ["../src/*"],
      "@tests/*": ["./*"],
      "@shared/*": ["../shared/*"],
      "@services/*": ["../services/*"]
    }
  },
  "include": [
    "./**/*.ts",
    "../src/types/*.d.ts"
  ]
}
EOF

# Create test utility module
cat > tests/utils/test-utils.ts << EOF
import { SpyInstance } from 'npm:vitest';
import { JSDOM } from 'npm:jsdom';

// Mock document for component tests
function setupDom() {
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
  globalThis.document = dom.window.document;
  globalThis.window = dom.window;
}

// Mock console for cleaner test output
function createMockConsole() {
  return {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
  };
}

// Test utility factory
export function createTestUtils() {
  setupDom();
  
  return {
    dom: globalThis.document,
    console: createMockConsole(),
    cleanup: () => {
      vi.clearAllMocks();
      document.body.innerHTML = '';
    }
  };
}
EOF

# Set file permissions
chmod +x scripts/*.sh
chmod +x scripts/*.js

echo -e "${GREEN}Test environment setup complete!${NC}"
echo -e "${YELLOW}Run tests with:${NC}"
echo -e "  ${GREEN}npm test${NC} - Run all tests"
echo -e "  ${GREEN}npm run test:watch${NC} - Run tests in watch mode"
echo -e "  ${GREEN}npm run test:coverage${NC} - Run tests with coverage"