# Animation Editor Developer Guide

## Overview

The Animation Editor is a Vue.js component that provides a comprehensive interface for creating and managing animations for 3D objects in the MVS-VR platform. This guide provides detailed information for developers who need to understand, maintain, or extend the Animation Editor component.

## Table of Contents

1. [Component Structure](#component-structure)
2. [Props and Events](#props-and-events)
3. [Data Model](#data-model)
4. [Key Features](#key-features)
5. [API Integration](#api-integration)
6. [Performance Optimization](#performance-optimization)
7. [Extending the Component](#extending-the-component)
8. [Collaborative Editing](#collaborative-editing)

## Component Structure

The Animation Editor is located at:

```plaintext
mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/AnimationEditor.vue
```

The component follows a standard Vue.js single-file component structure with template, script, and style sections.

### Main Sections

1. **Editor Header**: Contains the title, save/reset buttons, and blend animations button
2. **Editor Sidebar**: Lists available animations with create/delete functionality
3. **Timeline**: Displays animation tracks and keyframes with playback controls
4. **Properties Panel**: Provides editing interfaces for animation and keyframe properties
5. **Blend Dialog**: Modal for creating new animations by blending existing ones

## Props and Events

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `vendorId` | String | Required | The ID of the vendor whose animations are being edited |
| `useVirtualScrolling` | Boolean | `true` | Whether to use virtual scrolling for large animation lists |
| `timelineScale` | Number | `100` | Pixels per second in the timeline |
| `initialAnimations` | Array | `[]` | Initial animations data to populate the editor |

### Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update` | `Array` | Emitted when animations are updated (saved) |
| `error` | `String` | Emitted when an error occurs |
| `preview-update` | `Object` | Emitted during animation playback for preview updates |

## Data Model

### Animation Object

```javascript
{
  id: String,              // Unique identifier
  name: String,            // Animation name
  duration: Number,        // Duration in seconds
  loop: Boolean,           // Whether animation should loop
  vendor_id: String,       // Associated vendor ID
  tracks: Array            // Array of track objects
}
```

### Track Object

```javascript
{
  id: String,              // Unique identifier
  name: String,            // Track name
  type: String,            // Track type (transform, visibility, material)
  targetId: String,        // ID of the target object
  keyframes: Array         // Array of keyframe objects
}
```

### Keyframe Object

```javascript
{
  id: String,              // Unique identifier
  time: Number,            // Time position in seconds
  easing: String,          // Easing function name
  curve: Object,           // Custom bezier curve control points (for custom easing)
  value: Object            // Keyframe value (depends on track type)
}
```

#### Transform Keyframe Value

```javascript
{
  position: { x: Number, y: Number, z: Number },
  rotation: { x: Number, y: Number, z: Number },
  scale: { x: Number, y: Number, z: Number }
}
```

#### Visibility Keyframe Value

```javascript
{
  visible: Boolean,
  opacity: Number
}
```

#### Material Keyframe Value

```javascript
{
  color: String,
  metalness: Number,
  roughness: Number,
  // Other material properties
}
```

## Key Features

### Animation Management

- **Create Animation**: Creates a new animation with default properties
- **Delete Animation**: Removes an animation with confirmation
- **Save Animations**: Saves all animations to the server
- **Reset Animations**: Resets to the last saved state

### Timeline Editing

- **Add Track**: Creates a new track with default keyframes
- **Add Keyframe**: Adds a keyframe at the current time position
- **Select Keyframe**: Selects a keyframe for editing
- **Move Keyframe**: Changes the time position of a keyframe
- **Delete Keyframe**: Removes a keyframe from a track

### Playback Controls

- **Play/Pause**: Controls animation playback
- **Stop**: Stops playback and resets to the beginning
- **Scrubbing**: Allows dragging the timeline scrubber to a specific time
- **Playback Speed**: Adjusts the playback speed

### Animation Blending

- **Blend Dialog**: Interface for creating new animations by blending existing ones
- **Blend Factor**: Controls the contribution of each source animation
- **Preview**: Shows a preview of the blended animation

### Easing and Interpolation

- **Easing Presets**: Predefined easing functions (linear, ease-in, ease-out, etc.)
- **Custom Curves**: Interface for creating custom bezier curves for interpolation
- **Curve Preview**: Visual preview of the easing curve

## API Integration

The Animation Editor uses the `AnimationService` to interact with the server API:

### Loading Animations

```javascript
async loadAnimations() {
  this.isLoading = true;
  try {
    const animations = await AnimationService.getAnimations(this.$api, this.vendorId);
    this.animations = animations;
    this.originalAnimations = JSON.parse(JSON.stringify(animations));
  } catch (error) {
    this.error = 'Failed to load animations';
  } finally {
    this.isLoading = false;
  }
}
```

### Saving Animations

```javascript
async saveAnimations() {
  this.isLoading = true;
  try {
    const savedAnimations = await AnimationService.saveAnimations(
      this.$api,
      this.animations,
      this.vendorId
    );
    this.animations = savedAnimations;
    this.originalAnimations = JSON.parse(JSON.stringify(savedAnimations));
    this.$emit('update', this.animations);
  } catch (error) {
    this.error = 'Failed to save animations';
  } finally {
    this.isLoading = false;
  }
}
```

## Performance Optimization

The Animation Editor includes several performance optimizations to handle large numbers of animations and keyframes efficiently:

### Virtual Scrolling

For large animation lists, virtual scrolling renders only the visible items, significantly reducing DOM nodes and improving performance:

```javascript
// In the template
<div v-if="useVirtualScrolling" class="virtual-scroll-container"
     :style="virtualListRenderer?.getVisibleItems().containerStyle">
  <div v-for="animation in visibleAnimations"
       :key="animation.id"
       :style="animation.style">
    <!-- Animation item content -->
  </div>
</div>
```

The VirtualListRenderer class handles the calculation of which items should be visible:

```javascript
class VirtualListRenderer {
  constructor(items, itemHeight, containerHeight, buffer, options) {
    this.items = items;
    this.itemHeight = itemHeight;
    this.containerHeight = containerHeight;
    this.buffer = buffer;
    this.scrollTop = 0;

    // Lazy loading options
    this.lazyLoad = options.lazyLoad || false;
    this.loadMoreItems = options.loadMoreItems || null;
    this.loadThreshold = options.loadThreshold || 0.8;
    this.pageSize = options.pageSize || 20;
    this.isLoading = false;
    this.hasMoreItems = true;
  }

  getVisibleItems() {
    const totalHeight = this.items.length * this.itemHeight;
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.buffer * 2;

    // Calculate start and end indices
    const startIndex = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.buffer);
    const endIndex = Math.min(this.items.length, startIndex + visibleCount);

    // Get visible items with absolute positioning
    const visibleItems = this.items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      style: {
        position: 'absolute',
        top: `${(startIndex + index) * this.itemHeight}px`,
        height: `${this.itemHeight}px`,
        width: '100%'
      }
    }));

    return {
      visibleItems,
      containerStyle: {
        position: 'relative',
        height: `${totalHeight}px`,
        overflow: 'hidden'
      }
    };
  }
}
```

### Advanced Loading Optimizations

The Animation Editor implements several advanced loading optimizations to handle large datasets efficiently:

#### 1. Lazy Loading

Animations are loaded on demand to reduce initial load time and memory usage. This is especially important for vendors with hundreds or thousands of animations:

```javascript
async loadAnimationsWithPagination(page = 1, limit = 20) {
  // Check cache first
  const cacheKey = `animations_${this.vendorId}_page${page}_limit${limit}`;
  const cachedData = this.cache.get(cacheKey);

  if (cachedData) {
    // Track cache performance
    const loadTime = performance.now() - startTime;
    this.performanceMetrics.cacheHits++;
    this.performanceMetrics.cacheTimes.push(loadTime);

    return cachedData;
  }

  // Fetch animations from the API with pagination
  const response = await this.$api.get(
    `/items/animations?filter[vendor_id][_eq]=${this.vendorId}&limit=${limit}&page=${page}`
  );

  const data = response.data.data || [];

  // Calculate approximate size for memory management
  const dataSize = this.approximateDataSize(data);

  // Cache the result with size information
  this.cache.set(cacheKey, data, {
    size: dataSize,
    ttl: 10 * 60 * 1000 // 10 minutes TTL
  });

  return data;
}
```

#### 2. Prefetching

The VirtualListRenderer proactively loads the next page of data before the user reaches the end of the current page:

```javascript
// In the VirtualListRenderer constructor
this.prefetch = options.prefetch || false;
this.prefetchThreshold = options.prefetchThreshold || 0.5; // Start prefetching at 50% of the current page

// In the updateScroll method
updateScroll(scrollTop) {
  this.scrollTop = scrollTop;

  // Check if we need to load more items
  if (this.lazyLoad && this.loadMoreItems && this.hasMoreItems && !this.isLoading) {
    const scrollRatio = scrollTop / (this.items.length * this.itemHeight - this.containerHeight);

    if (scrollRatio > this.loadThreshold) {
      this.loadMore();
    } else if (this.prefetch && !this.isPrefetching && scrollRatio > this.prefetchThreshold) {
      // Start prefetching the next page when we're halfway through the current page
      this.prefetchNextPage();
    }
  }
}

// Prefetch the next page
prefetchNextPage() {
  if (this.isPrefetching || !this.hasMoreItems || !this.loadMoreItems) return;

  this.isPrefetching = true;
  const nextPage = this.currentPage + 1;

  // Use main thread for prefetching
  this.loadMoreItems(nextPage, this.pageSize)
    .then(data => {
      this.prefetchedData = data;
      this.isPrefetching = false;
    })
    .catch(error => {
      console.error('Error prefetching data:', error);
      this.isPrefetching = false;
    });
}
```

#### 3. Background Loading with Web Workers

For browsers that support it, the VirtualListRenderer can use Web Workers to load data in the background without blocking the main thread:

```javascript
// In the VirtualListRenderer constructor
this.useWorker = options.useWorker || false;
this.worker = null;

if (this.useWorker) {
  this.initWorker();
}

// Initialize web worker
initWorker() {
  try {
    const workerCode = `
      self.onmessage = function(e) {
        const { action, page, pageSize, loadFn } = e.data;

        if (action === 'load') {
          // In a real implementation, we would call the load function
          // For now, we'll simulate a response
          setTimeout(() => {
            self.postMessage({
              action: 'loaded',
              page: page,
              data: Array.from({ length: pageSize }, (_, i) => ({
                id: \`worker-item-\${(page - 1) * pageSize + i}\`,
                name: \`Worker Item \${(page - 1) * pageSize + i}\`,
                value: (page - 1) * pageSize + i
              }))
            });
          }, 200);
        }
      };
    `;

    const blob = new Blob([workerCode], { type: 'application/javascript' });
    this.worker = new Worker(URL.createObjectURL(blob));

    this.worker.onmessage = (e) => {
      const { action, page, data } = e.data;

      if (action === 'loaded') {
        this.handleWorkerData(page, data);
      }
    };
  } catch (error) {
    console.error('Failed to initialize web worker:', error);
    this.useWorker = false;
  }
}
```

### Performance Metrics

The Animation Editor tracks performance metrics to help identify bottlenecks:

```javascript
// Initialize performance metrics
data() {
  return {
    // ...other data properties
    performanceMetrics: {
      loadTime: 0,
      renderTime: 0,
      keyframeCount: 0,
      trackCount: 0
    },
    lastRenderTime: 0,
    renderCount: 0
  };
}

// Measure render performance
measureRenderPerformance() {
  const startTime = performance.now();

  // Force a re-render by accessing the DOM
  if (this.$refs.animationList) {
    const dummy = this.$refs.animationList.offsetHeight;
  }

  // Calculate render time
  const renderTime = performance.now() - startTime;

  // Update performance metrics
  this.performanceMetrics.renderTime = renderTime;
  this.renderCount++;

  // Log performance metrics periodically
  if (this.renderCount % 10 === 0) {
    console.log('Performance metrics:', {
      ...this.performanceMetrics,
      renderCount: this.renderCount,
      animationCount: this.animations.length,
      visibleAnimationCount: this.visibleAnimations.length
    });
  }
}
```

### Caching

The Animation Editor uses a caching system to avoid redundant API calls and calculations:

```javascript
// Initialize cache
data() {
  return {
    // ...other data properties
    cache: new PerformanceOptimizer(50, 10 * 60 * 1000) // 50 items, 10 minutes TTL
  };
}

// Use cache when loading data
async loadAnimationsWithPagination(page = 1, limit = 20) {
  // Check cache first
  const cacheKey = `animations_${this.vendorId}_page${page}_limit${limit}`;
  const cachedData = this.cache.get(cacheKey);

  if (cachedData) {
    // Use cached data
    console.log(`Using cached animations data for page ${page}`);
    return cachedData;
  }

  // Fetch from API if not in cache
  const response = await this.$api.get(
    `/items/animations?filter[vendor_id][_eq]=${this.vendorId}&limit=${limit}&page=${page}`
  );

  const data = response.data.data || [];

  // Cache the result
  this.cache.set(cacheKey, data);

  return data;
}
```

The PerformanceOptimizer class implements an advanced LRU (Least Recently Used) cache with memory management, time-to-live (TTL), and eviction policies:

```javascript
/**
 * Advanced performance optimizer with memory-aware LRU caching
 */
class PerformanceOptimizer {
  /**
   * Create a new cache instance
   * @param {Number} maxSize - Maximum number of items to cache
   * @param {Number} ttl - Time to live in milliseconds
   * @param {Object} options - Additional options
   * @param {Number} options.maxMemorySize - Maximum memory size in bytes (approximate)
   * @param {Boolean} options.trackHitRate - Whether to track cache hit rate
   * @param {Number} options.evictionThreshold - Memory threshold for eviction (0-1)
   */
  constructor(maxSize = 100, ttl = 5 * 60 * 1000, options = {}) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.hits = 0;
    this.misses = 0;

    // Advanced options
    this.maxMemorySize = options.maxMemorySize || 50 * 1024 * 1024; // Default 50MB
    this.evictionThreshold = options.evictionThreshold || 0.9; // Start evicting at 90% capacity

    // Cache statistics
    this.evictions = 0;
    this.expirations = 0;
    this.totalMemoryUsed = 0;

    // Set up automatic cleanup
    this.setupAutoCleanup();
  }

  /**
   * Set up automatic cleanup of expired items
   * @private
   */
  setupAutoCleanup() {
    // Clean up expired items every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredItems();
    }, 60 * 1000);
  }

  /**
   * Approximate the size of a value in bytes
   * @param {*} value - Value to measure
   * @returns {Number} - Approximate size in bytes
   * @private
   */
  approximateSize(value) {
    if (value === null || value === undefined) return 8;

    const type = typeof value;

    if (type === 'boolean') return 4;
    if (type === 'number') return 8;
    if (type === 'string') return value.length * 2;

    if (type === 'object') {
      if (Array.isArray(value)) {
        return value.reduce((size, item) => size + this.approximateSize(item), 0);
      }

      // For objects, estimate based on keys and values
      return Object.entries(value).reduce((size, [key, val]) => {
        return size + key.length * 2 + this.approximateSize(val);
      }, 0);
    }

    return 8; // Default size for other types
  }

  /**
   * Get an item from the cache
   * @param {String} key - Cache key
   * @returns {*} - Cached value or null if not found
   */
  get(key) {
    if (!this.cache.has(key)) {
      this.misses++;
      return null;
    }

    const item = this.cache.get(key);

    // Check if item has expired
    if (item.expiry < Date.now()) {
      this.cache.delete(key);
      this.totalMemoryUsed -= item.size || 0;
      this.expirations++;
      this.misses++;
      return null;
    }

    // Update access time and frequency
    item.lastAccessed = Date.now();
    item.accessCount = (item.accessCount || 0) + 1;
    this.hits++;

    return item.value;
  }

  /**
   * Set an item in the cache
   * @param {String} key - Cache key
   * @param {*} value - Value to cache
   * @param {Object} options - Additional options
   * @param {Number} options.ttl - Custom TTL for this item
   * @param {Number} options.size - Approximate size of the item in bytes
   */
  set(key, value, options = {}) {
    const ttl = options.ttl || this.ttl;

    // Calculate approximate size of the value
    const valueSize = options.size || this.approximateSize(value);

    // Check if adding this item would exceed memory limit
    if (valueSize > this.maxMemorySize) {
      console.warn(`Item too large for cache: ${valueSize} bytes exceeds limit of ${this.maxMemorySize} bytes`);
      return;
    }

    // If we already have this key, remove its size from the total
    if (this.cache.has(key)) {
      const oldItem = this.cache.get(key);
      this.totalMemoryUsed -= oldItem.size || 0;
    }

    // Check if we need to evict items due to memory pressure
    if (this.totalMemoryUsed + valueSize > this.maxMemorySize * this.evictionThreshold) {
      this.evictByMemory(this.totalMemoryUsed + valueSize - (this.maxMemorySize * 0.7));
    }

    // If cache is full by count, remove least recently used item
    if (!this.cache.has(key) && this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    // Add new item
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl,
      lastAccessed: Date.now(),
      size: valueSize,
      accessCount: 0
    });

    this.totalMemoryUsed += valueSize;
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hits: this.hits,
      misses: this.misses,
      evictions: this.evictions,
      expirations: this.expirations,
      hitRatio: this.hits / (this.hits + this.misses || 1),
      memoryUsed: this.totalMemoryUsed,
      memoryLimit: this.maxMemorySize,
      memoryUsageRatio: this.totalMemoryUsed / this.maxMemorySize
    };
  }

  /**
   * Dispose of resources
   */
  dispose() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}
```

## Summary

The Animation Editor is a powerful component that provides a rich set of features for creating and managing animations. It includes:

1. **User-Friendly Interface**: Intuitive timeline-based editing with drag-and-drop functionality
2. **Performance Optimizations**: Virtual scrolling, lazy loading, and caching for handling large datasets
3. **Collaborative Editing**: Real-time collaboration using Y.js and WebSockets
4. **Advanced Animation Features**: Keyframe interpolation, easing functions, and animation blending

For more information, refer to the [Animation Editor User Guide](../user-guides/ANIMATION_EDITOR_GUIDE.md).

### Optimized Rendering

The timeline only renders keyframes in the visible time range:

```javascript
renderVisibleKeyframes() {
  const visibleStart = this.scrollPosition / this.timelineScale;
  const visibleEnd = visibleStart + (this.timelineWidth / this.timelineScale);

  return this.selectedAnimation.tracks.flatMap(track => {
    return track.keyframes
      .filter(keyframe => {
        return keyframe.time >= visibleStart && keyframe.time <= visibleEnd;
      })
      .map(keyframe => {
        // Render keyframe
        return {
          id: keyframe.id,
          position: this.timeToPosition(keyframe.time),
          selected: this.selectedKeyframeId === keyframe.id,
          trackId: track.id
        };
      });
  });
}
```

## Extending the Component

### Adding New Track Types

To add a new track type:

1. Update the track type options in the UI
2. Add a new value template for the track type
3. Create a new property panel section for the track type
4. Implement interpolation for the new value type

Example:

```javascript
// Add to track type options
const trackTypes = [
  { value: 'transform', label: 'Transform' },
  { value: 'visibility', label: 'Visibility' },
  { value: 'material', label: 'Material' },
  { value: 'custom', label: 'Custom' } // New track type
];

// Add value template
getDefaultValueForTrackType(type) {
  switch (type) {
    case 'transform':
      return {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      };
    case 'visibility':
      return { visible: true, opacity: 1 };
    case 'material':
      return { color: '#ffffff', metalness: 0, roughness: 0.5 };
    case 'custom':
      return { value: 0 }; // New value template
    default:
      return {};
  }
}
```

### Adding New Easing Functions

To add a new easing function:

1. Add the function to the `EasingFunctions` object
2. Add the option to the easing selector in the UI

Example:

```javascript
// Add to EasingFunctions
const EasingFunctions = {
  // Existing functions
  linear: t => t,
  easeInQuad: t => t * t,
  // New function
  customSpring: t => {
    const s = 1.70158 * 1.525;
    return t < 0.5
      ? (t * t * ((s + 1) * 2 * t - s)) / 2
      : ((t - 2) * t * ((s + 1) * (t * 2 - 2) + s) + 2) / 2;
  }
};

// Add to easing options in the template
<select id="keyframe-easing" v-model="selectedKeyframe.easing">
  <option value="linear">Linear</option>
  <!-- Other options -->
  <option value="customSpring">Custom Spring</option>
</select>
```

## Collaborative Editing

The Animation Editor supports real-time collaborative editing using Y.js and WebSockets. This allows multiple users to work on the same animation simultaneously.

### Architecture

The collaborative editing system consists of three main components:

1. **Y.js Document**: A shared document that stores the animation data
2. **WebSocket Provider**: Synchronizes the Y.js document between clients
3. **Collaboration UI**: User interface elements for collaboration features

### Y.js Integration

The Animation Editor uses Y.js for conflict-free collaborative editing:

```javascript
// Initialize Y.js document and providers
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import { IndexeddbPersistence } from 'y-indexeddb';

// Create a Y.js document
const doc = new Y.Doc();

// Set up shared data structures
const animationsMap = doc.getMap('animations');
const metadataMap = doc.getMap('metadata');
const cursorsMap = doc.getMap('cursors');
const selectionsMap = doc.getMap('selections');
const chatArray = doc.getArray('chat');

// Set up WebSocket provider
const provider = new WebsocketProvider(
  'wss://collaboration.mvs-vr.com',
  'animation-room-123',
  doc,
  {
    awareness: {
      // Initial local state
      local: {
        userId: 'user-123',
        userName: 'John Doe',
        userColor: '#ff0000',
        cursor: null,
        selection: null
      }
    }
  }
);

// Set up persistence (optional)
const persistence = new IndexeddbPersistence('animation-editor-123', doc);
```

### Awareness Protocol

The awareness protocol is used to share user presence information:

```javascript
// Get the awareness instance from the provider
const awareness = provider.awareness;

// Update local state
awareness.setLocalState({
  userId: 'user-123',
  userName: 'John Doe',
  userColor: '#ff0000',
  cursor: { x: 100, y: 200 },
  selection: { animationId: 'anim-1', keyframeId: 'key-1' }
});

// Listen for remote state changes
awareness.on('change', () => {
  const states = Array.from(awareness.getStates().values());
  // Update UI with user presence information
  updateUserPresence(states);
});
```

### Handling Document Updates

Listen for changes to the shared document:

```javascript
// Listen for changes to the animations map
animationsMap.observe(event => {
  // Update local animations
  updateAnimations(Array.from(animationsMap.values()));
});

// Listen for changes to the chat array
chatArray.observe(event => {
  // Update chat messages
  updateChatMessages(Array.from(chatArray.values()));
});
```

### Conflict Resolution

Y.js automatically handles conflict resolution using Conflict-free Replicated Data Types (CRDTs). However, you may need to implement application-specific conflict resolution:

```javascript
// Example: Handling conflicting animation names
function resolveAnimationNameConflict(animation) {
  const existingNames = new Set(
    Array.from(animationsMap.values()).map(a => a.name)
  );

  if (existingNames.has(animation.name)) {
    let counter = 1;
    let newName = `${animation.name} (${counter})`;

    while (existingNames.has(newName)) {
      counter++;
      newName = `${animation.name} (${counter})`;
    }

    animation.name = newName;
  }

  return animation;
}
```

### Collaborative UI Components

The Animation Editor includes several UI components for collaboration:

1. **User Presence**: Shows avatars of connected users
2. **Cursor Tracking**: Displays remote users' cursors
3. **Selection Highlighting**: Highlights elements selected by other users
4. **Chat Panel**: Allows users to communicate in real-time
5. **Conflict Indicators**: Shows when conflicts are detected and resolved

### WebSocket Server

The WebSocket server is responsible for relaying messages between clients:

```javascript
// Server-side code (Node.js)
const WebSocket = require('ws');
const http = require('http');
const y = require('y-websocket/bin/utils');

const server = http.createServer((request, response) => {
  response.writeHead(200, { 'Content-Type': 'text/plain' });
  response.end('Y.js WebSocket server');
});

const wss = new WebSocket.Server({ server });

y.setupWSConnection(wss, {
  authenticate: (request, callback) => {
    // Implement authentication logic here
    callback(null, true); // Allow connection
  },
  onConnect: (doc, conn) => {
    console.log('Client connected');
  },
  onDisconnect: (doc, conn) => {
    console.log('Client disconnected');
  }
});

server.listen(1234);
```
