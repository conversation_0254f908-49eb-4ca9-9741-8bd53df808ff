/**
 * Query Parameter Validation Middleware
 * 
 * This middleware validates query parameters using Zod schemas.
 */

const { z } = require('zod');
const { fromZodError } = require('zod-validation-error');

/**
 * Create validation middleware for query parameters
 * @param {z.ZodSchema} schema - Zod schema for validation
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function validateQuery(schema, options = {}) {
  const {
    stripUnknown = true,
    errorStatus = 400,
    errorHandler = null
  } = options;
  
  return async (req, res, next) => {
    try {
      // Parse and validate query parameters
      const validatedQuery = await schema.parseAsync(req.query);
      
      // Replace req.query with validated query
      req.query = validatedQuery;
      
      next();
    } catch (error) {
      if (errorHandler) {
        return errorHandler(error, req, res, next);
      }
      
      // Convert Zod error to user-friendly format
      const validationError = fromZodError(error);
      
      res.status(errorStatus).json({
        error: 'Validation Error',
        message: 'Invalid query parameters',
        details: validationError.details
      });
    }
  };
}

/**
 * Common validation schemas
 */
const commonSchemas = {
  // Pagination
  pagination: z.object({
    page: z.coerce.number().int().positive().optional().default(1),
    limit: z.coerce.number().int().positive().max(100).optional().default(20)
  }),
  
  // Sorting
  sorting: z.object({
    sort_by: z.string().optional(),
    sort_order: z.enum(['asc', 'desc']).optional().default('asc')
  }),
  
  // Date range
  dateRange: z.object({
    start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional()
  }),
  
  // Search
  search: z.object({
    q: z.string().optional(),
    fields: z.string().optional()
  }),
  
  // Filtering
  filtering: z.object({
    filter: z.string().optional(),
    filter_value: z.string().optional()
  }),
  
  // IDs
  ids: z.object({
    ids: z.string().transform(val => val.split(',').map(id => id.trim())).optional()
  }),
  
  // Boolean flags
  booleanFlags: z.object({
    include_deleted: z.enum(['true', 'false']).transform(val => val === 'true').optional().default('false'),
    include_inactive: z.enum(['true', 'false']).transform(val => val === 'true').optional().default('false')
  })
};

/**
 * Combine multiple schemas
 * @param {Array<z.ZodSchema>} schemas - Array of Zod schemas
 * @returns {z.ZodSchema} Combined schema
 */
function combineSchemas(...schemas) {
  return schemas.reduce((merged, schema) => merged.merge(schema), z.object({}));
}

module.exports = {
  validateQuery,
  commonSchemas,
  combineSchemas
};
