/**
 * Migration: Add ML Alert Columns
 * 
 * This migration adds ML-related columns to the alerts table.
 */

const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

async function run() {
  try {
    logger.info('Starting migration: Add ML Alert Columns');
    
    // Check if columns already exist
    const { data: columns, error: columnsError } = await supabase.rpc('get_table_columns', {
      table_name: 'alerts'
    });
    
    if (columnsError) {
      logger.error('Error checking columns', { error: columnsError.message });
      return;
    }
    
    const columnNames = columns.map(c => c.column_name);
    
    // Add ml_processed column if it doesn't exist
    if (!columnNames.includes('ml_processed')) {
      const { error: mlProcessedError } = await supabase.rpc('add_column', {
        table_name: 'alerts',
        column_name: 'ml_processed',
        column_type: 'boolean'
      });
      
      if (mlProcessedError) {
        logger.error('Error adding ml_processed column', { error: mlProcessedError.message });
      } else {
        logger.info('Added ml_processed column');
      }
    }
    
    // Add ml_importance column if it doesn't exist
    if (!columnNames.includes('ml_importance')) {
      const { error: mlImportanceError } = await supabase.rpc('add_column', {
        table_name: 'alerts',
        column_name: 'ml_importance',
        column_type: 'float'
      });
      
      if (mlImportanceError) {
        logger.error('Error adding ml_importance column', { error: mlImportanceError.message });
      } else {
        logger.info('Added ml_importance column');
      }
    }
    
    // Add ml_confidence column if it doesn't exist
    if (!columnNames.includes('ml_confidence')) {
      const { error: mlConfidenceError } = await supabase.rpc('add_column', {
        table_name: 'alerts',
        column_name: 'ml_confidence',
        column_type: 'float'
      });
      
      if (mlConfidenceError) {
        logger.error('Error adding ml_confidence column', { error: mlConfidenceError.message });
      } else {
        logger.info('Added ml_confidence column');
      }
    }
    
    // Add ml_reasons column if it doesn't exist
    if (!columnNames.includes('ml_reasons')) {
      const { error: mlReasonsError } = await supabase.rpc('add_column', {
        table_name: 'alerts',
        column_name: 'ml_reasons',
        column_type: 'jsonb'
      });
      
      if (mlReasonsError) {
        logger.error('Error adding ml_reasons column', { error: mlReasonsError.message });
      } else {
        logger.info('Added ml_reasons column');
      }
    }
    
    // Add ml_correlated_alerts column if it doesn't exist
    if (!columnNames.includes('ml_correlated_alerts')) {
      const { error: mlCorrelatedAlertsError } = await supabase.rpc('add_column', {
        table_name: 'alerts',
        column_name: 'ml_correlated_alerts',
        column_type: 'jsonb'
      });
      
      if (mlCorrelatedAlertsError) {
        logger.error('Error adding ml_correlated_alerts column', { error: mlCorrelatedAlertsError.message });
      } else {
        logger.info('Added ml_correlated_alerts column');
      }
    }
    
    // Add ml_system_state column if it doesn't exist
    if (!columnNames.includes('ml_system_state')) {
      const { error: mlSystemStateError } = await supabase.rpc('add_column', {
        table_name: 'alerts',
        column_name: 'ml_system_state',
        column_type: 'jsonb'
      });
      
      if (mlSystemStateError) {
        logger.error('Error adding ml_system_state column', { error: mlSystemStateError.message });
      } else {
        logger.info('Added ml_system_state column');
      }
    }
    
    // Create alert_actions table if it doesn't exist
    const { error: tableExistsError } = await supabase.rpc('table_exists', {
      table_name: 'alert_actions'
    });
    
    if (tableExistsError || tableExistsError === false) {
      const { error: createTableError } = await supabase.rpc('create_table', {
        table_name: 'alert_actions',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'alert_id', type: 'uuid', nullable: false },
          { name: 'action_type', type: 'text', nullable: false },
          { name: 'action_data', type: 'jsonb', nullable: true },
          { name: 'performed_by', type: 'text', nullable: true },
          { name: 'created_at', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createTableError) {
        logger.error('Error creating alert_actions table', { error: createTableError.message });
      } else {
        logger.info('Created alert_actions table');
        
        // Create index on alert_id
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'alert_actions',
          column_name: 'alert_id'
        });
        
        if (indexError) {
          logger.error('Error creating index on alert_id', { error: indexError.message });
        } else {
          logger.info('Created index on alert_id');
        }
      }
    }
    
    // Create system_metrics table if it doesn't exist
    const { error: systemMetricsExistsError } = await supabase.rpc('table_exists', {
      table_name: 'system_metrics'
    });
    
    if (systemMetricsExistsError || systemMetricsExistsError === false) {
      const { error: createSystemMetricsError } = await supabase.rpc('create_table', {
        table_name: 'system_metrics',
        columns: [
          { name: 'id', type: 'uuid', primary: true, default: 'uuid_generate_v4()' },
          { name: 'cpu_usage', type: 'float', nullable: false },
          { name: 'memory_usage', type: 'float', nullable: false },
          { name: 'disk_usage', type: 'float', nullable: false },
          { name: 'network_in', type: 'float', nullable: false },
          { name: 'network_out', type: 'float', nullable: false },
          { name: 'api_requests', type: 'integer', nullable: false },
          { name: 'error_count', type: 'integer', nullable: false },
          { name: 'timestamp', type: 'timestamp with time zone', default: 'now()' }
        ]
      });
      
      if (createSystemMetricsError) {
        logger.error('Error creating system_metrics table', { error: createSystemMetricsError.message });
      } else {
        logger.info('Created system_metrics table');
        
        // Create index on timestamp
        const { error: indexError } = await supabase.rpc('create_index', {
          table_name: 'system_metrics',
          column_name: 'timestamp'
        });
        
        if (indexError) {
          logger.error('Error creating index on timestamp', { error: indexError.message });
        } else {
          logger.info('Created index on timestamp');
        }
      }
    }
    
    logger.info('Migration completed: Add ML Alert Columns');
  } catch (error) {
    logger.error('Error in migration', { error: error.message });
  }
}

// Run migration if called directly
if (require.main === module) {
  run().then(() => {
    process.exit(0);
  }).catch(error => {
    logger.error('Migration failed', { error: error.message });
    process.exit(1);
  });
}

module.exports = { run };
