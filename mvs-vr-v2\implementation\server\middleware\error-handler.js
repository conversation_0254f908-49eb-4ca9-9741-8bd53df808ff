/**
 * Error Handler Middleware
 * 
 * This middleware provides consistent error handling and prevents information disclosure.
 */

const { ZodError } = require('zod');
const { fromZodError } = require('zod-validation-error');
const { ValidationError } = require('joi');
const { JsonWebTokenError, TokenExpiredError } = require('jsonwebtoken');
const { DatabaseError } = require('pg');

// Error types
const ERROR_TYPES = {
  VALIDATION: 'ValidationError',
  AUTHENTICATION: 'AuthenticationError',
  AUTHORIZATION: 'AuthorizationError',
  NOT_FOUND: 'NotFoundError',
  CONFLICT: 'ConflictError',
  RATE_LIMIT: 'RateLimitError',
  DATABASE: 'DatabaseError',
  EXTERNAL_SERVICE: 'ExternalServiceError',
  INTERNAL: 'InternalError'
};

// Error status codes
const ERROR_STATUS_CODES = {
  [ERROR_TYPES.VALIDATION]: 400,
  [ERROR_TYPES.AUTHENTICATION]: 401,
  [ERROR_TYPES.AUTHORIZATION]: 403,
  [ERROR_TYPES.NOT_FOUND]: 404,
  [ERROR_TYPES.CONFLICT]: 409,
  [ERROR_TYPES.RATE_LIMIT]: 429,
  [ERROR_TYPES.DATABASE]: 500,
  [ERROR_TYPES.EXTERNAL_SERVICE]: 502,
  [ERROR_TYPES.INTERNAL]: 500
};

/**
 * Custom error class
 */
class AppError extends Error {
  constructor(type, message, details = null, originalError = null) {
    super(message);
    this.name = type;
    this.details = details;
    this.originalError = originalError;
  }
}

/**
 * Sanitize error details to prevent information disclosure
 * @param {any} details - Error details
 * @param {string} errorType - Error type
 * @returns {any} Sanitized details
 */
function sanitizeErrorDetails(details, errorType) {
  if (!details) {
    return null;
  }
  
  // For validation errors, keep details
  if (errorType === ERROR_TYPES.VALIDATION) {
    return details;
  }
  
  // For database errors, remove sensitive information
  if (errorType === ERROR_TYPES.DATABASE) {
    if (typeof details === 'object') {
      const sanitized = { ...details };
      
      // Remove sensitive fields
      delete sanitized.code;
      delete sanitized.detail;
      delete sanitized.schema;
      delete sanitized.table;
      delete sanitized.column;
      delete sanitized.dataType;
      delete sanitized.constraint;
      
      return sanitized;
    }
    
    return 'Database error occurred';
  }
  
  // For authentication errors, keep minimal details
  if (errorType === ERROR_TYPES.AUTHENTICATION) {
    if (typeof details === 'object') {
      const sanitized = { ...details };
      
      // Remove sensitive fields
      delete sanitized.token;
      delete sanitized.user;
      
      return sanitized;
    }
    
    return details;
  }
  
  // For other errors, return generic message
  return 'An error occurred';
}

/**
 * Map error to standard format
 * @param {Error} error - Error object
 * @returns {Object} Standardized error
 */
function mapErrorToStandard(error) {
  // Already standardized error
  if (error instanceof AppError) {
    return {
      type: error.name,
      message: error.message,
      details: sanitizeErrorDetails(error.details, error.name),
      status: ERROR_STATUS_CODES[error.name] || 500
    };
  }
  
  // Zod validation error
  if (error instanceof ZodError) {
    const validationError = fromZodError(error);
    
    return {
      type: ERROR_TYPES.VALIDATION,
      message: 'Validation error',
      details: validationError.details,
      status: 400
    };
  }
  
  // Joi validation error
  if (error instanceof ValidationError) {
    return {
      type: ERROR_TYPES.VALIDATION,
      message: 'Validation error',
      details: error.details,
      status: 400
    };
  }
  
  // JWT errors
  if (error instanceof JsonWebTokenError) {
    return {
      type: ERROR_TYPES.AUTHENTICATION,
      message: 'Invalid token',
      details: null,
      status: 401
    };
  }
  
  if (error instanceof TokenExpiredError) {
    return {
      type: ERROR_TYPES.AUTHENTICATION,
      message: 'Token expired',
      details: null,
      status: 401
    };
  }
  
  // Database errors
  if (error instanceof DatabaseError) {
    return {
      type: ERROR_TYPES.DATABASE,
      message: 'Database error',
      details: sanitizeErrorDetails({ code: error.code }, ERROR_TYPES.DATABASE),
      status: 500
    };
  }
  
  // Default to internal error
  return {
    type: ERROR_TYPES.INTERNAL,
    message: 'Internal server error',
    details: null,
    status: 500
  };
}

/**
 * Error handler middleware
 */
function errorHandler(options = {}) {
  const {
    logErrors = true,
    includeStackTrace = process.env.NODE_ENV === 'development'
  } = options;
  
  return (error, req, res, next) => {
    // Map error to standard format
    const standardError = mapErrorToStandard(error);
    
    // Log error
    if (logErrors) {
      console.error(`[ERROR] ${standardError.type}: ${standardError.message}`);
      
      if (includeStackTrace && error.stack) {
        console.error(error.stack);
      }
      
      if (error.originalError) {
        console.error('Original error:', error.originalError);
      }
    }
    
    // Send response
    res.status(standardError.status).json({
      error: standardError.type,
      message: standardError.message,
      details: standardError.details,
      ...(includeStackTrace && error.stack ? { stack: error.stack } : {})
    });
  };
}

module.exports = {
  errorHandler,
  AppError,
  ERROR_TYPES,
  ERROR_STATUS_CODES
};
