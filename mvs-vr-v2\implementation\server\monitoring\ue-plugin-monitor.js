/**
 * UE Plugin Monitor
 * 
 * This service collects and processes metrics from the Unreal Engine plugin.
 * It integrates with the existing monitoring system to provide comprehensive
 * client-side performance monitoring.
 */

const { createClient } = require('@supabase/supabase-js');
const promClient = require('prom-client');
const express = require('express');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceKey
);

// Create Express app
const app = express();
app.use(express.json({ limit: '5mb' }));

// Create a Registry for UE plugin metrics
const register = new promClient.Registry();

// Create custom metrics for UE plugin
const metrics = {
  // Performance metrics
  frameRate: new promClient.Gauge({
    name: 'mvs_vr_ue_frame_rate',
    help: 'Frame rate in frames per second',
    labelNames: ['client_id', 'device_type', 'ue_version']
  }),
  
  drawCalls: new promClient.Gauge({
    name: 'mvs_vr_ue_draw_calls',
    help: 'Number of draw calls per frame',
    labelNames: ['client_id', 'device_type', 'ue_version']
  }),
  
  triangleCount: new promClient.Gauge({
    name: 'mvs_vr_ue_triangle_count',
    help: 'Number of triangles rendered',
    labelNames: ['client_id', 'device_type', 'ue_version']
  }),
  
  gpuMemoryUsage: new promClient.Gauge({
    name: 'mvs_vr_ue_gpu_memory_usage_mb',
    help: 'GPU memory usage in megabytes',
    labelNames: ['client_id', 'device_type', 'ue_version']
  }),
  
  cpuUsage: new promClient.Gauge({
    name: 'mvs_vr_ue_cpu_usage_percent',
    help: 'CPU usage in percent',
    labelNames: ['client_id', 'device_type', 'ue_version']
  }),
  
  memoryUsage: new promClient.Gauge({
    name: 'mvs_vr_ue_memory_usage_mb',
    help: 'Memory usage in megabytes',
    labelNames: ['client_id', 'device_type', 'ue_version']
  }),
  
  // Asset loading metrics
  assetLoadTime: new promClient.Histogram({
    name: 'mvs_vr_ue_asset_load_time_seconds',
    help: 'Asset loading time in seconds',
    labelNames: ['client_id', 'asset_type', 'ue_version'],
    buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
  }),
  
  assetCacheHitRate: new promClient.Gauge({
    name: 'mvs_vr_ue_asset_cache_hit_rate_percent',
    help: 'Asset cache hit rate in percent',
    labelNames: ['client_id', 'asset_type', 'ue_version']
  }),
  
  assetLoadCount: new promClient.Counter({
    name: 'mvs_vr_ue_asset_load_count',
    help: 'Number of assets loaded',
    labelNames: ['client_id', 'asset_type', 'ue_version', 'status']
  }),
  
  // Network metrics
  networkLatency: new promClient.Histogram({
    name: 'mvs_vr_ue_network_latency_ms',
    help: 'Network latency in milliseconds',
    labelNames: ['client_id', 'endpoint', 'ue_version'],
    buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000]
  }),
  
  networkBandwidth: new promClient.Gauge({
    name: 'mvs_vr_ue_network_bandwidth_kbps',
    help: 'Network bandwidth in kilobits per second',
    labelNames: ['client_id', 'direction', 'ue_version']
  }),
  
  // Offline mode metrics
  offlineModeStatus: new promClient.Gauge({
    name: 'mvs_vr_ue_offline_mode_status',
    help: 'Offline mode status (1 = active, 0 = inactive)',
    labelNames: ['client_id', 'ue_version']
  }),
  
  offlineCacheSize: new promClient.Gauge({
    name: 'mvs_vr_ue_offline_cache_size_mb',
    help: 'Offline cache size in megabytes',
    labelNames: ['client_id', 'ue_version']
  }),
  
  networkQuality: new promClient.Gauge({
    name: 'mvs_vr_ue_network_quality',
    help: 'Network quality score (0-100)',
    labelNames: ['client_id', 'ue_version']
  }),
  
  // User interaction metrics
  interactionCount: new promClient.Counter({
    name: 'mvs_vr_ue_interaction_count',
    help: 'Number of user interactions',
    labelNames: ['client_id', 'interaction_type', 'ue_version']
  }),
  
  interactionLatency: new promClient.Histogram({
    name: 'mvs_vr_ue_interaction_latency_ms',
    help: 'Interaction latency in milliseconds',
    labelNames: ['client_id', 'interaction_type', 'ue_version'],
    buckets: [10, 20, 50, 100, 200, 500, 1000]
  }),
  
  // LLM integration metrics
  llmRequestCount: new promClient.Counter({
    name: 'mvs_vr_ue_llm_request_count',
    help: 'Number of LLM requests',
    labelNames: ['client_id', 'model', 'ue_version', 'status']
  }),
  
  llmResponseTime: new promClient.Histogram({
    name: 'mvs_vr_ue_llm_response_time_seconds',
    help: 'LLM response time in seconds',
    labelNames: ['client_id', 'model', 'ue_version'],
    buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
  }),
  
  llmTokenUsage: new promClient.Counter({
    name: 'mvs_vr_ue_llm_token_usage',
    help: 'Number of tokens used in LLM requests',
    labelNames: ['client_id', 'model', 'ue_version', 'token_type']
  }),
  
  // Error metrics
  errorCount: new promClient.Counter({
    name: 'mvs_vr_ue_error_count',
    help: 'Number of errors',
    labelNames: ['client_id', 'error_type', 'ue_version', 'component']
  })
};

// Register all metrics
Object.values(metrics).forEach(metric => register.registerMetric(metric));

/**
 * Process UE plugin metrics
 * 
 * @param {Object} data - Metrics data from UE plugin
 */
async function processUEPluginMetrics(data) {
  try {
    const { clientId, deviceType, ueVersion, timestamp, metrics: metricsData } = data;
    
    // Process performance metrics
    if (metricsData.performance) {
      const perf = metricsData.performance;
      
      if (perf.frameRate !== undefined) {
        metrics.frameRate.set({ client_id: clientId, device_type: deviceType, ue_version: ueVersion }, perf.frameRate);
      }
      
      if (perf.drawCalls !== undefined) {
        metrics.drawCalls.set({ client_id: clientId, device_type: deviceType, ue_version: ueVersion }, perf.drawCalls);
      }
      
      if (perf.triangleCount !== undefined) {
        metrics.triangleCount.set({ client_id: clientId, device_type: deviceType, ue_version: ueVersion }, perf.triangleCount);
      }
      
      if (perf.gpuMemoryUsage !== undefined) {
        metrics.gpuMemoryUsage.set({ client_id: clientId, device_type: deviceType, ue_version: ueVersion }, perf.gpuMemoryUsage);
      }
      
      if (perf.cpuUsage !== undefined) {
        metrics.cpuUsage.set({ client_id: clientId, device_type: deviceType, ue_version: ueVersion }, perf.cpuUsage);
      }
      
      if (perf.memoryUsage !== undefined) {
        metrics.memoryUsage.set({ client_id: clientId, device_type: deviceType, ue_version: ueVersion }, perf.memoryUsage);
      }
    }
    
    // Process asset loading metrics
    if (metricsData.assetLoading) {
      const assets = metricsData.assetLoading;
      
      if (assets.loadTimes) {
        Object.entries(assets.loadTimes).forEach(([assetType, loadTime]) => {
          metrics.assetLoadTime.observe({ client_id: clientId, asset_type: assetType, ue_version: ueVersion }, loadTime);
        });
      }
      
      if (assets.cacheHitRates) {
        Object.entries(assets.cacheHitRates).forEach(([assetType, hitRate]) => {
          metrics.assetCacheHitRate.set({ client_id: clientId, asset_type: assetType, ue_version: ueVersion }, hitRate);
        });
      }
      
      if (assets.loadCounts) {
        Object.entries(assets.loadCounts).forEach(([assetType, counts]) => {
          if (counts.success) {
            metrics.assetLoadCount.inc({ client_id: clientId, asset_type: assetType, ue_version: ueVersion, status: 'success' }, counts.success);
          }
          
          if (counts.failure) {
            metrics.assetLoadCount.inc({ client_id: clientId, asset_type: assetType, ue_version: ueVersion, status: 'failure' }, counts.failure);
          }
        });
      }
    }
    
    // Process network metrics
    if (metricsData.network) {
      const network = metricsData.network;
      
      if (network.latencies) {
        Object.entries(network.latencies).forEach(([endpoint, latency]) => {
          metrics.networkLatency.observe({ client_id: clientId, endpoint, ue_version: ueVersion }, latency);
        });
      }
      
      if (network.bandwidth) {
        if (network.bandwidth.download !== undefined) {
          metrics.networkBandwidth.set({ client_id: clientId, direction: 'download', ue_version: ueVersion }, network.bandwidth.download);
        }
        
        if (network.bandwidth.upload !== undefined) {
          metrics.networkBandwidth.set({ client_id: clientId, direction: 'upload', ue_version: ueVersion }, network.bandwidth.upload);
        }
      }
    }
    
    // Process offline mode metrics
    if (metricsData.offlineMode) {
      const offline = metricsData.offlineMode;
      
      if (offline.active !== undefined) {
        metrics.offlineModeStatus.set({ client_id: clientId, ue_version: ueVersion }, offline.active ? 1 : 0);
      }
      
      if (offline.cacheSize !== undefined) {
        metrics.offlineCacheSize.set({ client_id: clientId, ue_version: ueVersion }, offline.cacheSize);
      }
      
      if (offline.networkQuality !== undefined) {
        metrics.networkQuality.set({ client_id: clientId, ue_version: ueVersion }, offline.networkQuality);
      }
    }
    
    // Process user interaction metrics
    if (metricsData.interactions) {
      const interactions = metricsData.interactions;
      
      if (interactions.counts) {
        Object.entries(interactions.counts).forEach(([type, count]) => {
          metrics.interactionCount.inc({ client_id: clientId, interaction_type: type, ue_version: ueVersion }, count);
        });
      }
      
      if (interactions.latencies) {
        Object.entries(interactions.latencies).forEach(([type, latency]) => {
          metrics.interactionLatency.observe({ client_id: clientId, interaction_type: type, ue_version: ueVersion }, latency);
        });
      }
    }
    
    // Process LLM integration metrics
    if (metricsData.llm) {
      const llm = metricsData.llm;
      
      if (llm.requests) {
        Object.entries(llm.requests).forEach(([model, counts]) => {
          if (counts.success) {
            metrics.llmRequestCount.inc({ client_id: clientId, model, ue_version: ueVersion, status: 'success' }, counts.success);
          }
          
          if (counts.failure) {
            metrics.llmRequestCount.inc({ client_id: clientId, model, ue_version: ueVersion, status: 'failure' }, counts.failure);
          }
        });
      }
      
      if (llm.responseTimes) {
        Object.entries(llm.responseTimes).forEach(([model, time]) => {
          metrics.llmResponseTime.observe({ client_id: clientId, model, ue_version: ueVersion }, time);
        });
      }
      
      if (llm.tokenUsage) {
        Object.entries(llm.tokenUsage).forEach(([model, usage]) => {
          if (usage.prompt) {
            metrics.llmTokenUsage.inc({ client_id: clientId, model, ue_version: ueVersion, token_type: 'prompt' }, usage.prompt);
          }
          
          if (usage.completion) {
            metrics.llmTokenUsage.inc({ client_id: clientId, model, ue_version: ueVersion, token_type: 'completion' }, usage.completion);
          }
        });
      }
    }
    
    // Process error metrics
    if (metricsData.errors) {
      Object.entries(metricsData.errors).forEach(([errorType, components]) => {
        Object.entries(components).forEach(([component, count]) => {
          metrics.errorCount.inc({ client_id: clientId, error_type: errorType, ue_version: ueVersion, component }, count);
        });
      });
    }
    
    // Store metrics in database for historical analysis
    await storeMetricsInDatabase(data);
    
    logger.info(`Processed UE plugin metrics from client ${clientId}`);
  } catch (error) {
    logger.error('Error processing UE plugin metrics', { error: error.message });
  }
}

/**
 * Store metrics in database for historical analysis
 * 
 * @param {Object} data - Metrics data from UE plugin
 */
async function storeMetricsInDatabase(data) {
  try {
    const { clientId, deviceType, ueVersion, timestamp, metrics: metricsData } = data;
    
    // Store in database
    const { error } = await supabase
      .from('ue_plugin_metrics')
      .insert({
        client_id: clientId,
        device_type: deviceType,
        ue_version: ueVersion,
        timestamp: timestamp || new Date().toISOString(),
        metrics_data: metricsData
      });
      
    if (error) {
      logger.error('Error storing UE plugin metrics in database', { error: error.message });
    }
  } catch (error) {
    logger.error('Error in storeMetricsInDatabase', { error: error.message });
  }
}

// API endpoint for receiving metrics from UE plugin
app.post('/api/metrics', async (req, res) => {
  try {
    const data = req.body;
    
    // Validate required fields
    if (!data.clientId || !data.metrics) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Process metrics
    await processUEPluginMetrics(data);
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error in POST /api/metrics', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error serving UE plugin metrics', { error: error.message });
    res.status(500).send('Error collecting UE plugin metrics');
  }
});

// Start server
const PORT = process.env.UE_PLUGIN_MONITOR_PORT || 9100;
app.listen(PORT, () => {
  logger.info(`UE Plugin Monitor listening on port ${PORT}`);
});

module.exports = {
  processUEPluginMetrics,
  storeMetricsInDatabase
};
