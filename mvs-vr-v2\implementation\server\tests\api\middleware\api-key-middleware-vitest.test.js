/**
 * API Key Middleware Tests (Vitest Version)
 *
 * This file contains tests for the API key authentication middleware.
 */

const { describe, it, expect, beforeEach, vi } = require('vitest');
const { createClient } = require('@supabase/supabase-js');
const {
  authenticateApiKey,
  hasRequiredPermissions,
  hasRequiredScopes,
} = require('../../../api/middleware/api-key-middleware.js');

// Mock dependencies before importing the modules
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(),
}));

vi.mock('../../../api/middleware/auth-middleware.js', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
  redis: {
    hincrby: vi.fn().mockResolvedValue(1),
    lpush: vi.fn().mockResolvedValue(1),
    ltrim: vi.fn().mockResolvedValue(1),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    incr: vi.fn().mockResolvedValue(1),
    expire: vi.fn().mockResolvedValue(1),
  },
}));

// Mock crypto
vi.mock('crypto', () => ({
  createHash: vi.fn().mockReturnValue({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn().mockReturnValue('hashed_api_key'),
  }),
}));

describe('API Key Middleware', () => {
  let req;
  let res;
  let next;
  let mockSupabase;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock request, response, and next function
    req = {
      headers: {
        'x-api-key': 'test_api_key',
      },
      ip: '127.0.0.1',
      originalUrl: '/api/test',
      method: 'GET',
    };

    res = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
    };

    next = vi.fn();

    // Mock Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      update: vi.fn().mockReturnThis(),
    };

    vi.mocked(createClient).mockReturnValue(mockSupabase);
  });

  describe('authenticateApiKey', () => {
    it('should continue if API key is not required', async () => {
      // Remove API key from request
      req.headers['x-api-key'] = undefined;

      // Call middleware with required: false
      await authenticateApiKey({ required: false })(req, res, next);

      // Expect next to be called
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is required but not provided', async () => {
      // Remove API key from request
      req.headers['x-api-key'] = undefined;

      // Call middleware with required: true
      await authenticateApiKey({ required: true })(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'API_KEY_REQUIRED',
          message: 'API key is required',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is invalid', async () => {
      // Mock Supabase response for invalid API key
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: { message: 'API key not found' },
      });

      // Call middleware
      await authenticateApiKey()(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_API_KEY',
          message: 'Invalid API key',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is disabled', async () => {
      // Mock Supabase response for disabled API key
      mockSupabase.single.mockResolvedValue({
        data: {
          id: 'api_key_id',
          user_id: 'user_id',
          permissions: ['test:read'],
          scopes: ['api'],
          expires_at: null,
          enabled: false,
        },
        error: null,
      });

      // Call middleware
      await authenticateApiKey()(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'API_KEY_DISABLED',
          message: 'API key is disabled',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is expired', async () => {
      // Mock Supabase response for expired API key
      mockSupabase.single.mockResolvedValue({
        data: {
          id: 'api_key_id',
          user_id: 'user_id',
          permissions: ['test:read'],
          scopes: ['api'],
          expires_at: '2020-01-01T00:00:00Z',
          enabled: true,
        },
        error: null,
      });

      // Call middleware
      await authenticateApiKey()(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'API_KEY_EXPIRED',
          message: 'API key is expired',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if API key does not have required permissions', async () => {
      // Mock Supabase response for API key with insufficient permissions
      mockSupabase.single.mockResolvedValue({
        data: {
          id: 'api_key_id',
          user_id: 'user_id',
          permissions: ['test:read'],
          scopes: ['api'],
          expires_at: null,
          enabled: true,
        },
        error: null,
      });

      // Call middleware with required permissions
      await authenticateApiKey({ permissions: ['test:write'] })(req, res, next);

      // Expect 403 response
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'API key does not have required permissions',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if API key does not have required scopes', async () => {
      // Mock Supabase response for API key with insufficient scopes
      mockSupabase.single.mockResolvedValue({
        data: {
          id: 'api_key_id',
          user_id: 'user_id',
          permissions: ['test:read', 'test:write'],
          scopes: ['api'],
          expires_at: null,
          enabled: true,
        },
        error: null,
      });

      // Call middleware with required scopes
      await authenticateApiKey({ scopes: ['admin'] })(req, res, next);

      // Expect 403 response
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_SCOPES',
          message: 'API key does not have required scopes',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should continue if API key is valid and has required permissions and scopes', async () => {
      // Mock Supabase responses for valid API key and user
      mockSupabase.single.mockImplementation(_options => {
        if (mockSupabase.eq.mock.calls[0][0] === 'key_hash') {
          // First call - API key lookup
          return Promise.resolve({
            data: {
              id: 'api_key_id',
              user_id: 'user_id',
              permissions: ['test:read', 'test:write'],
              scopes: ['api', 'admin'],
              expires_at: null,
              enabled: true,
            },
            error: null,
          });
        } else {
          // Second call - User lookup
          return Promise.resolve({
            data: {
              id: 'user_id',
              email: '<EMAIL>',
              role: 'admin',
              vendor_id: 'vendor_id',
            },
            error: null,
          });
        }
      });

      // Call middleware with required permissions and scopes
      await authenticateApiKey({
        permissions: ['test:read'],
        scopes: ['api'],
      })(req, res, next);

      // Expect next to be called
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();

      // Expect user and API key to be added to request
      expect(req.apiKey).toEqual({
        id: 'api_key_id',
        permissions: ['test:read', 'test:write'],
        scopes: ['api', 'admin'],
      });

      expect(req.user).toEqual({
        id: 'user_id',
        email: '<EMAIL>',
        role: 'admin',
        vendor_id: 'vendor_id',
      });
    });
  });

  // Additional tests for hasRequiredPermissions and hasRequiredScopes
  describe('hasRequiredPermissions', () => {
    it('should return true if no permissions are required', () => {
      expect(hasRequiredPermissions(['test:read'], [])).toBe(true);
    });

    it('should return true if key has wildcard permission', () => {
      expect(hasRequiredPermissions(['*'], ['test:read'])).toBe(true);
    });

    it('should return true if key has all required permissions', () => {
      expect(hasRequiredPermissions(['test:read', 'test:write'], ['test:read'])).toBe(true);
    });

    it('should return false if key does not have required permissions', () => {
      expect(hasRequiredPermissions(['test:read'], ['test:write'])).toBe(false);
    });
  });

  describe('hasRequiredScopes', () => {
    it('should return true if no scopes are required', () => {
      expect(hasRequiredScopes(['api'], [])).toBe(true);
    });

    it('should return true if key has wildcard scope', () => {
      expect(hasRequiredScopes(['*'], ['api'])).toBe(true);
    });

    it('should return true if key has all required scopes', () => {
      expect(hasRequiredScopes(['api', 'admin'], ['api'])).toBe(true);
    });

    it('should return false if key does not have required scopes', () => {
      expect(hasRequiredScopes(['api'], ['admin'])).toBe(false);
    });
  });
});
