/**
 * Rate Limiting Middleware Tests
 * 
 * This file contains tests for the rate limiting middleware.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import Redis from 'ioredis';

// Mock dependencies
vi.mock('express-rate-limit', () => {
  return vi.fn().mockImplementation((options) => {
    return (req: Request, res: Response, next: NextFunction) => {
      // Simple mock implementation that calls the handler if keyGenerator returns a specific test key
      const key = options.keyGenerator(req);
      if (key.includes('rate-limited')) {
        options.handler(req, res, next, { 
          statusCode: 429, 
          message: options.message 
        });
        return;
      }
      next();
    };
  });
});

vi.mock('rate-limit-redis', () => {
  return {
    default: vi.fn().mockImplementation(() => ({})),
  };
});

vi.mock('ioredis', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
      hincrby: vi.fn(),
      hset: vi.fn(),
      lpush: vi.fn(),
      ltrim: vi.fn(),
      expire: vi.fn(),
      call: vi.fn(),
    })),
  };
});

// Mock logger
const mockLogger = {
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock auth-middleware
vi.mock('../../api/middleware/auth-middleware.js', () => ({
  logger: mockLogger,
  redis: new Redis(),
}));

// Mock monitoring service
vi.mock('../../services/monitoring/rate-limit-monitor.js', () => ({
  trackRateLimitEvent: vi.fn(),
}));

// Import the module under test
const rateLimitMiddleware = require('../../api/middleware/rate-limit-middleware.js');
const { 
  createRateLimiter, 
  isIpBlocked, 
  trackRateLimitAbuse 
} = rateLimitMiddleware;

describe('Rate Limit Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;
  let mockRedis: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Setup mock request and response
    mockRequest = {
      ip: '127.0.0.1',
      originalUrl: '/api/test',
      method: 'GET',
      user: { id: 'user-id' },
      apiKey: { id: 'key-id' },
    };

    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };

    nextFunction = vi.fn();

    // Get Redis mock from auth-middleware
    mockRedis = require('../../api/middleware/auth-middleware.js').redis;
  });

  describe('createRateLimiter', () => {
    it('should create a rate limiter with default options', () => {
      const limiter = createRateLimiter();
      expect(limiter).toBeDefined();
    });

    it('should create a rate limiter with custom options', () => {
      const limiter = createRateLimiter({
        windowMs: 60 * 1000,
        max: 10,
      });
      expect(limiter).toBeDefined();
    });

    it('should skip rate limiting for trusted IPs', () => {
      process.env.TRUSTED_IPS = '127.0.0.1,***********';
      
      const limiter = createRateLimiter();
      
      // @ts-ignore - Accessing private property for testing
      const skip = limiter.options.skip;
      
      expect(skip(mockRequest as Request)).toBe(true);
      
      // Cleanup
      delete process.env.TRUSTED_IPS;
    });

    it('should use user ID in key if available', () => {
      const limiter = createRateLimiter();
      
      // @ts-ignore - Accessing private property for testing
      const keyGenerator = limiter.options.keyGenerator;
      
      const key = keyGenerator(mockRequest as Request);
      
      expect(key).toBe('127.0.0.1:user-id:/api/test');
    });

    it('should use API key ID in key if available and no user', () => {
      mockRequest.user = undefined;
      
      const limiter = createRateLimiter();
      
      // @ts-ignore - Accessing private property for testing
      const keyGenerator = limiter.options.keyGenerator;
      
      const key = keyGenerator(mockRequest as Request);
      
      expect(key).toBe('127.0.0.1:apikey:key-id:/api/test');
    });

    it('should use IP in key if no user or API key', () => {
      mockRequest.user = undefined;
      mockRequest.apiKey = undefined;
      
      const limiter = createRateLimiter();
      
      // @ts-ignore - Accessing private property for testing
      const keyGenerator = limiter.options.keyGenerator;
      
      const key = keyGenerator(mockRequest as Request);
      
      expect(key).toBe('127.0.0.1:/api/test');
    });
  });

  describe('isIpBlocked', () => {
    it('should return true if IP is blocked', async () => {
      mockRedis.get.mockResolvedValue('true');
      
      const result = await isIpBlocked('127.0.0.1');
      
      expect(mockRedis.get).toHaveBeenCalledWith('ip:blocked:127.0.0.1');
      expect(result).toBe(true);
    });

    it('should return false if IP is not blocked', async () => {
      mockRedis.get.mockResolvedValue(null);
      
      const result = await isIpBlocked('127.0.0.1');
      
      expect(result).toBe(false);
    });

    it('should return false on error', async () => {
      mockRedis.get.mockRejectedValue(new Error('Redis error'));
      
      const result = await isIpBlocked('127.0.0.1');
      
      expect(mockLogger.error).toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('trackRateLimitAbuse', () => {
    it('should track abuse and set expiry on first abuse', async () => {
      mockRedis.hincrby.mockResolvedValue(1);
      
      await trackRateLimitAbuse('127.0.0.1', '/api/test');
      
      expect(mockRedis.hincrby).toHaveBeenCalledWith('rate-limit:abuse:127.0.0.1', 'count', 1);
      expect(mockRedis.hset).toHaveBeenCalled();
      expect(mockRedis.lpush).toHaveBeenCalled();
      expect(mockRedis.ltrim).toHaveBeenCalled();
      expect(mockRedis.expire).toHaveBeenCalledTimes(2);
    });

    it('should block IP after 10 abuses', async () => {
      mockRedis.hincrby.mockResolvedValue(10);
      
      await trackRateLimitAbuse('127.0.0.1', '/api/test');
      
      expect(mockRedis.set).toHaveBeenCalledWith('ip:blocked:127.0.0.1', 'true', 'EX', 24 * 60 * 60);
      expect(mockLogger.warn).toHaveBeenCalled();
    });
  });
});
