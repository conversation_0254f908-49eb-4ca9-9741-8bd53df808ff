/**
 * Recovery Time Objective (RTO) Measurement
 * 
 * This script measures recovery time for system components and compares against defined RTOs.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const rtoDefinitions = require('./rto-definitions');
const { logger } = require('../shared/utils/logger');

// Database for storing recovery time measurements
const MEASUREMENTS_DB_PATH = path.join(__dirname, '../../logs/recovery-measurements.json');

/**
 * Recovery time measurement class
 */
class RecoveryTimeMeasurement {
  constructor() {
    this.measurements = this.loadMeasurements();
    this.activeRecoveries = new Map();
  }

  /**
   * Load existing measurements from file
   */
  loadMeasurements() {
    try {
      if (fs.existsSync(MEASUREMENTS_DB_PATH)) {
        const data = fs.readFileSync(MEASUREMENTS_DB_PATH, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading measurements:', error);
    }
    return { recoveries: [] };
  }

  /**
   * Save measurements to file
   */
  async saveMeasurements() {
    try {
      await writeFileAsync(
        MEASUREMENTS_DB_PATH,
        JSON.stringify(this.measurements, null, 2),
        'utf8'
      );
    } catch (error) {
      console.error('Error saving measurements:', error);
    }
  }

  /**
   * Start recovery time measurement for a component
   * @param {string} componentPath - Dot notation path to component in rtoDefinitions
   * @param {string} incidentId - Unique identifier for the incident
   * @param {string} description - Description of the recovery event
   * @returns {string} Recovery ID
   */
  startRecovery(componentPath, incidentId, description) {
    const recoveryId = `${incidentId}-${componentPath}-${Date.now()}`;
    const component = this.getComponentByPath(componentPath);
    
    if (!component) {
      throw new Error(`Component not found: ${componentPath}`);
    }

    const recovery = {
      id: recoveryId,
      componentPath,
      incidentId,
      description,
      startTime: new Date().toISOString(),
      endTime: null,
      duration: null,
      rto: component.rto,
      status: 'in-progress',
      metRto: null
    };

    this.activeRecoveries.set(recoveryId, recovery);
    logger.info(`Started recovery measurement for ${componentPath} (ID: ${recoveryId});`);
    return recoveryId;
  }

  /**
   * End recovery time measurement for a component
   * @param {string} recoveryId - Recovery ID returned from startRecovery
   * @param {string} status - Status of the recovery (success, partial, failed)
   * @param {string} notes - Additional notes about the recovery
   * @returns {Object} Recovery measurement
   */
  async endRecovery(recoveryId, status = 'success', notes = '') {
    if (!this.activeRecoveries.has(recoveryId)) {
      throw new Error(`Recovery not found: ${recoveryId}`);
    }

    const recovery = this.activeRecoveries.get(recoveryId);
    const endTime = new Date();
    const startTime = new Date(recovery.startTime);
    const durationMinutes = (endTime - startTime) / (1000 * 60);

    recovery.endTime = endTime.toISOString();
    recovery.duration = durationMinutes;
    recovery.status = status;
    recovery.notes = notes;
    recovery.metRto = durationMinutes <= recovery.rto;

    this.activeRecoveries.delete(recoveryId);
    this.measurements.recoveries.push(recovery);
    await this.saveMeasurements();

    logger.info(`Ended recovery measurement for ${recovery.componentPath} (ID: ${recoveryId});`);
    logger.info(`Duration: ${durationMinutes.toFixed(2);} minutes, RTO: ${recovery.rto} minutes`);
    logger.info(`Met RTO: ${recovery.metRto ? 'Yes' : 'No'}`);

    return recovery;
  }

  /**
   * Get component by path
   * @param {string} componentPath - Dot notation path to component in rtoDefinitions
   * @returns {Object} Component definition
   */
  getComponentByPath(componentPath) {
    const parts = componentPath.split('.');
    let current = rtoDefinitions;
    
    for (const part of parts) {
      if (!current[part]) {
        return null;
      }
      current = current[part];
    }
    
    return current;
  }

  /**
   * Generate RTO compliance report
   * @param {string} incidentId - Optional incident ID to filter by
   * @returns {Object} RTO compliance report
   */
  generateRtoReport(incidentId = null) {
    const filteredRecoveries = incidentId
      ? this.measurements.recoveries.filter(r => r.incidentId === incidentId)
      : this.measurements.recoveries;

    const report = {
      totalRecoveries: filteredRecoveries.length,
      metRto: filteredRecoveries.filter(r => r.metRto).length,
      missedRto: filteredRecoveries.filter(r => !r.metRto).length,
      complianceRate: 0,
      byComponent: {},
      byPriority: {
        critical: { total: 0, met: 0, rate: 0 },
        high: { total: 0, met: 0, rate: 0 },
        medium: { total: 0, met: 0, rate: 0 },
        low: { total: 0, met: 0, rate: 0 }
      },
      recoveries: filteredRecoveries
    };

    if (report.totalRecoveries > 0) {
      report.complianceRate = (report.metRto / report.totalRecoveries) * 100;
    }

    // Group by component
    filteredRecoveries.forEach(recovery => {
      const component = this.getComponentByPath(recovery.componentPath);
      if (!component) return;

      if (!report.byComponent[recovery.componentPath]) {
        report.byComponent[recovery.componentPath] = {
          total: 0,
          met: 0,
          rate: 0,
          rto: component.rto,
          priority: component.priority
        };
      }

      report.byComponent[recovery.componentPath].total++;
      if (recovery.metRto) {
        report.byComponent[recovery.componentPath].met++;
      }

      // Update priority stats
      report.byPriority[component.priority].total++;
      if (recovery.metRto) {
        report.byPriority[component.priority].met++;
      }
    });

    // Calculate rates
    Object.keys(report.byComponent).forEach(key => {
      const comp = report.byComponent[key];
      comp.rate = comp.total > 0 ? (comp.met / comp.total) * 100 : 0;
    });

    Object.keys(report.byPriority).forEach(key => {
      const priority = report.byPriority[key];
      priority.rate = priority.total > 0 ? (priority.met / priority.total) * 100 : 0;
    });

    return report;
  }
}

module.exports = new RecoveryTimeMeasurement();
